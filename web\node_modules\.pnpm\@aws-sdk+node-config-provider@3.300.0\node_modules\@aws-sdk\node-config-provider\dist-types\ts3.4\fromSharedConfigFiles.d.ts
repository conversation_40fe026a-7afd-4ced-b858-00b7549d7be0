import { SourceProfileInit } from "@aws-sdk/shared-ini-file-loader";
import { Profile, Provider } from "@aws-sdk/types";
export interface SharedConfigInit extends SourceProfileInit {
  preferredFile?: "config" | "credentials";
}
export type GetterFromConfig<T> = (profile: Profile) => T | undefined;
export declare const fromSharedConfigFiles: <T = string>(
  configSelector: GetterFromConfig<T>,
  { preferredFile, ...init }?: SharedConfigInit
) => Provider<T>;
