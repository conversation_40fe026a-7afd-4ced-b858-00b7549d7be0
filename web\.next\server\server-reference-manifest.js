self.__RSC_SERVER_MANIFEST="{\n  \"node\": {\n    \"138b27991ba1a7854f74bc6ecfb24098f0c1c89a\": {\n      \"workers\": {\n        \"app/page\": \"(rsc)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cuser.actions.ts%22%2C%5B%22getUserByEmail%22%2C%22deleteUser%22%2C%22getAvatarUploadUrl%22%2C%22updateUserProfile%22%2C%22getUserById%22%5D%5D%5D&__client_imported__=!\"\n      },\n      \"layer\": {\n        \"app/page\": \"rsc\"\n      }\n    },\n    \"17ffd57ca0253de6b0b24817288a845fe8b216d3\": {\n      \"workers\": {\n        \"app/page\": \"(rsc)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cuser.actions.ts%22%2C%5B%22getUserByEmail%22%2C%22deleteUser%22%2C%22getAvatarUploadUrl%22%2C%22updateUserProfile%22%2C%22getUserById%22%5D%5D%5D&__client_imported__=!\"\n      },\n      \"layer\": {\n        \"app/page\": \"rsc\"\n      }\n    },\n    \"cab1a8e8f736a5a13ead6cfaea1c00dd01fed889\": {\n      \"workers\": {\n        \"app/page\": \"(rsc)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cuser.actions.ts%22%2C%5B%22getUserByEmail%22%2C%22deleteUser%22%2C%22getAvatarUploadUrl%22%2C%22updateUserProfile%22%2C%22getUserById%22%5D%5D%5D&__client_imported__=!\"\n      },\n      \"layer\": {\n        \"app/page\": \"rsc\"\n      }\n    },\n    \"e4aae0411117d44bb320d005d0a976a49f8ce3f3\": {\n      \"workers\": {\n        \"app/page\": \"(rsc)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cuser.actions.ts%22%2C%5B%22getUserByEmail%22%2C%22deleteUser%22%2C%22getAvatarUploadUrl%22%2C%22updateUserProfile%22%2C%22getUserById%22%5D%5D%5D&__client_imported__=!\"\n      },\n      \"layer\": {\n        \"app/page\": \"rsc\"\n      }\n    },\n    \"f8bf0ac93593ae5c883312fc236552552fae3f3a\": {\n      \"workers\": {\n        \"app/page\": \"(rsc)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cuser.actions.ts%22%2C%5B%22getUserByEmail%22%2C%22deleteUser%22%2C%22getAvatarUploadUrl%22%2C%22updateUserProfile%22%2C%22getUserById%22%5D%5D%5D&__client_imported__=!\"\n      },\n      \"layer\": {\n        \"app/page\": \"rsc\"\n      }\n    },\n    \"1588230eecaa4a1ed048e7d0e58ecd104947e617\": {\n      \"workers\": {\n        \"app/page\": \"(action-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cversion.actions.ts%22%2C%5B%22getVersion%22%2C%22deleteVersions%22%2C%22updateVersion%22%2C%22getCurrentVersion%22%2C%22getVersions%22%2C%22createVersion%22%2C%22deleteVersion%22%5D%5D%2C%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cworkspace.actions.ts%22%2C%5B%22getWorkspaceUploadUrl%22%2C%22removeMember%22%2C%22getWorkspace%22%2C%22leaveWorkspace%22%2C%22UploadFileToS3%22%2C%22deleteWorkspace%22%2C%22createWorkspace%22%2C%22getWorkspacePendingInvites%22%2C%22getWorkspaceMembers%22%2C%22updateWorkspaceUploadStatus%22%2C%22updateWorkspace%22%2C%22updateRole%22%2C%22deleteWorkspaces%22%2C%22getWorkspaces%22%5D%5D%2C%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cinvite.actions.ts%22%2C%5B%22acceptInvite%22%2C%22cancelInvite%22%2C%22getReceivedInvites%22%2C%22rejectInvite%22%2C%22inviteUser%22%5D%5D%5D&__client_imported__=true!\",\n        \"app/projects/[id]/edit/page\": \"(action-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cversion.actions.ts%22%2C%5B%22getVersion%22%2C%22deleteVersions%22%2C%22updateVersion%22%2C%22getCurrentVersion%22%2C%22getVersions%22%2C%22createVersion%22%2C%22deleteVersion%22%5D%5D%5D&__client_imported__=true!\"\n      },\n      \"layer\": {\n        \"app/page\": \"action-browser\",\n        \"app/projects/[id]/edit/page\": \"action-browser\"\n      }\n    },\n    \"1ebda6f92187b6ead3547bf016316466b7426668\": {\n      \"workers\": {\n        \"app/page\": \"(action-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cversion.actions.ts%22%2C%5B%22getVersion%22%2C%22deleteVersions%22%2C%22updateVersion%22%2C%22getCurrentVersion%22%2C%22getVersions%22%2C%22createVersion%22%2C%22deleteVersion%22%5D%5D%2C%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cworkspace.actions.ts%22%2C%5B%22getWorkspaceUploadUrl%22%2C%22removeMember%22%2C%22getWorkspace%22%2C%22leaveWorkspace%22%2C%22UploadFileToS3%22%2C%22deleteWorkspace%22%2C%22createWorkspace%22%2C%22getWorkspacePendingInvites%22%2C%22getWorkspaceMembers%22%2C%22updateWorkspaceUploadStatus%22%2C%22updateWorkspace%22%2C%22updateRole%22%2C%22deleteWorkspaces%22%2C%22getWorkspaces%22%5D%5D%2C%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cinvite.actions.ts%22%2C%5B%22acceptInvite%22%2C%22cancelInvite%22%2C%22getReceivedInvites%22%2C%22rejectInvite%22%2C%22inviteUser%22%5D%5D%5D&__client_imported__=true!\",\n        \"app/projects/[id]/edit/page\": \"(action-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cversion.actions.ts%22%2C%5B%22getVersion%22%2C%22deleteVersions%22%2C%22updateVersion%22%2C%22getCurrentVersion%22%2C%22getVersions%22%2C%22createVersion%22%2C%22deleteVersion%22%5D%5D%5D&__client_imported__=true!\"\n      },\n      \"layer\": {\n        \"app/page\": \"action-browser\",\n        \"app/projects/[id]/edit/page\": \"action-browser\"\n      }\n    },\n    \"63f0822cd70fc7903171d68dc2b728ae27cc61c0\": {\n      \"workers\": {\n        \"app/page\": \"(action-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cversion.actions.ts%22%2C%5B%22getVersion%22%2C%22deleteVersions%22%2C%22updateVersion%22%2C%22getCurrentVersion%22%2C%22getVersions%22%2C%22createVersion%22%2C%22deleteVersion%22%5D%5D%2C%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cworkspace.actions.ts%22%2C%5B%22getWorkspaceUploadUrl%22%2C%22removeMember%22%2C%22getWorkspace%22%2C%22leaveWorkspace%22%2C%22UploadFileToS3%22%2C%22deleteWorkspace%22%2C%22createWorkspace%22%2C%22getWorkspacePendingInvites%22%2C%22getWorkspaceMembers%22%2C%22updateWorkspaceUploadStatus%22%2C%22updateWorkspace%22%2C%22updateRole%22%2C%22deleteWorkspaces%22%2C%22getWorkspaces%22%5D%5D%2C%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cinvite.actions.ts%22%2C%5B%22acceptInvite%22%2C%22cancelInvite%22%2C%22getReceivedInvites%22%2C%22rejectInvite%22%2C%22inviteUser%22%5D%5D%5D&__client_imported__=true!\",\n        \"app/projects/[id]/edit/page\": \"(action-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cversion.actions.ts%22%2C%5B%22getVersion%22%2C%22deleteVersions%22%2C%22updateVersion%22%2C%22getCurrentVersion%22%2C%22getVersions%22%2C%22createVersion%22%2C%22deleteVersion%22%5D%5D%5D&__client_imported__=true!\"\n      },\n      \"layer\": {\n        \"app/page\": \"action-browser\",\n        \"app/projects/[id]/edit/page\": \"action-browser\"\n      }\n    },\n    \"8cf3c1e9c02f69d9bc9e438c38e23f006a00e150\": {\n      \"workers\": {\n        \"app/page\": \"(action-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cversion.actions.ts%22%2C%5B%22getVersion%22%2C%22deleteVersions%22%2C%22updateVersion%22%2C%22getCurrentVersion%22%2C%22getVersions%22%2C%22createVersion%22%2C%22deleteVersion%22%5D%5D%2C%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cworkspace.actions.ts%22%2C%5B%22getWorkspaceUploadUrl%22%2C%22removeMember%22%2C%22getWorkspace%22%2C%22leaveWorkspace%22%2C%22UploadFileToS3%22%2C%22deleteWorkspace%22%2C%22createWorkspace%22%2C%22getWorkspacePendingInvites%22%2C%22getWorkspaceMembers%22%2C%22updateWorkspaceUploadStatus%22%2C%22updateWorkspace%22%2C%22updateRole%22%2C%22deleteWorkspaces%22%2C%22getWorkspaces%22%5D%5D%2C%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cinvite.actions.ts%22%2C%5B%22acceptInvite%22%2C%22cancelInvite%22%2C%22getReceivedInvites%22%2C%22rejectInvite%22%2C%22inviteUser%22%5D%5D%5D&__client_imported__=true!\",\n        \"app/projects/[id]/edit/page\": \"(action-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cversion.actions.ts%22%2C%5B%22getVersion%22%2C%22deleteVersions%22%2C%22updateVersion%22%2C%22getCurrentVersion%22%2C%22getVersions%22%2C%22createVersion%22%2C%22deleteVersion%22%5D%5D%5D&__client_imported__=true!\"\n      },\n      \"layer\": {\n        \"app/page\": \"action-browser\",\n        \"app/projects/[id]/edit/page\": \"action-browser\"\n      }\n    },\n    \"a21520244c8e1c66d665bd83d070bc956ca70652\": {\n      \"workers\": {\n        \"app/page\": \"(action-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cversion.actions.ts%22%2C%5B%22getVersion%22%2C%22deleteVersions%22%2C%22updateVersion%22%2C%22getCurrentVersion%22%2C%22getVersions%22%2C%22createVersion%22%2C%22deleteVersion%22%5D%5D%2C%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cworkspace.actions.ts%22%2C%5B%22getWorkspaceUploadUrl%22%2C%22removeMember%22%2C%22getWorkspace%22%2C%22leaveWorkspace%22%2C%22UploadFileToS3%22%2C%22deleteWorkspace%22%2C%22createWorkspace%22%2C%22getWorkspacePendingInvites%22%2C%22getWorkspaceMembers%22%2C%22updateWorkspaceUploadStatus%22%2C%22updateWorkspace%22%2C%22updateRole%22%2C%22deleteWorkspaces%22%2C%22getWorkspaces%22%5D%5D%2C%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cinvite.actions.ts%22%2C%5B%22acceptInvite%22%2C%22cancelInvite%22%2C%22getReceivedInvites%22%2C%22rejectInvite%22%2C%22inviteUser%22%5D%5D%5D&__client_imported__=true!\",\n        \"app/projects/[id]/edit/page\": \"(action-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cversion.actions.ts%22%2C%5B%22getVersion%22%2C%22deleteVersions%22%2C%22updateVersion%22%2C%22getCurrentVersion%22%2C%22getVersions%22%2C%22createVersion%22%2C%22deleteVersion%22%5D%5D%5D&__client_imported__=true!\"\n      },\n      \"layer\": {\n        \"app/page\": \"action-browser\",\n        \"app/projects/[id]/edit/page\": \"action-browser\"\n      }\n    },\n    \"aa9866299ce2a298d0554a97e2609694ab8d9623\": {\n      \"workers\": {\n        \"app/page\": \"(action-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cversion.actions.ts%22%2C%5B%22getVersion%22%2C%22deleteVersions%22%2C%22updateVersion%22%2C%22getCurrentVersion%22%2C%22getVersions%22%2C%22createVersion%22%2C%22deleteVersion%22%5D%5D%2C%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cworkspace.actions.ts%22%2C%5B%22getWorkspaceUploadUrl%22%2C%22removeMember%22%2C%22getWorkspace%22%2C%22leaveWorkspace%22%2C%22UploadFileToS3%22%2C%22deleteWorkspace%22%2C%22createWorkspace%22%2C%22getWorkspacePendingInvites%22%2C%22getWorkspaceMembers%22%2C%22updateWorkspaceUploadStatus%22%2C%22updateWorkspace%22%2C%22updateRole%22%2C%22deleteWorkspaces%22%2C%22getWorkspaces%22%5D%5D%2C%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cinvite.actions.ts%22%2C%5B%22acceptInvite%22%2C%22cancelInvite%22%2C%22getReceivedInvites%22%2C%22rejectInvite%22%2C%22inviteUser%22%5D%5D%5D&__client_imported__=true!\",\n        \"app/projects/[id]/edit/page\": \"(action-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cversion.actions.ts%22%2C%5B%22getVersion%22%2C%22deleteVersions%22%2C%22updateVersion%22%2C%22getCurrentVersion%22%2C%22getVersions%22%2C%22createVersion%22%2C%22deleteVersion%22%5D%5D%5D&__client_imported__=true!\"\n      },\n      \"layer\": {\n        \"app/page\": \"action-browser\",\n        \"app/projects/[id]/edit/page\": \"action-browser\"\n      }\n    },\n    \"d48acca2d9db9bc855b3e319333a4eb3b7a3cc5b\": {\n      \"workers\": {\n        \"app/page\": \"(action-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cversion.actions.ts%22%2C%5B%22getVersion%22%2C%22deleteVersions%22%2C%22updateVersion%22%2C%22getCurrentVersion%22%2C%22getVersions%22%2C%22createVersion%22%2C%22deleteVersion%22%5D%5D%2C%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cworkspace.actions.ts%22%2C%5B%22getWorkspaceUploadUrl%22%2C%22removeMember%22%2C%22getWorkspace%22%2C%22leaveWorkspace%22%2C%22UploadFileToS3%22%2C%22deleteWorkspace%22%2C%22createWorkspace%22%2C%22getWorkspacePendingInvites%22%2C%22getWorkspaceMembers%22%2C%22updateWorkspaceUploadStatus%22%2C%22updateWorkspace%22%2C%22updateRole%22%2C%22deleteWorkspaces%22%2C%22getWorkspaces%22%5D%5D%2C%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cinvite.actions.ts%22%2C%5B%22acceptInvite%22%2C%22cancelInvite%22%2C%22getReceivedInvites%22%2C%22rejectInvite%22%2C%22inviteUser%22%5D%5D%5D&__client_imported__=true!\",\n        \"app/projects/[id]/edit/page\": \"(action-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cversion.actions.ts%22%2C%5B%22getVersion%22%2C%22deleteVersions%22%2C%22updateVersion%22%2C%22getCurrentVersion%22%2C%22getVersions%22%2C%22createVersion%22%2C%22deleteVersion%22%5D%5D%5D&__client_imported__=true!\"\n      },\n      \"layer\": {\n        \"app/page\": \"action-browser\",\n        \"app/projects/[id]/edit/page\": \"action-browser\"\n      }\n    },\n    \"0c7e64e2188c37210784fd7673e22ca0087fa099\": {\n      \"workers\": {\n        \"app/page\": \"(action-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cversion.actions.ts%22%2C%5B%22getVersion%22%2C%22deleteVersions%22%2C%22updateVersion%22%2C%22getCurrentVersion%22%2C%22getVersions%22%2C%22createVersion%22%2C%22deleteVersion%22%5D%5D%2C%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cworkspace.actions.ts%22%2C%5B%22getWorkspaceUploadUrl%22%2C%22removeMember%22%2C%22getWorkspace%22%2C%22leaveWorkspace%22%2C%22UploadFileToS3%22%2C%22deleteWorkspace%22%2C%22createWorkspace%22%2C%22getWorkspacePendingInvites%22%2C%22getWorkspaceMembers%22%2C%22updateWorkspaceUploadStatus%22%2C%22updateWorkspace%22%2C%22updateRole%22%2C%22deleteWorkspaces%22%2C%22getWorkspaces%22%5D%5D%2C%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cinvite.actions.ts%22%2C%5B%22acceptInvite%22%2C%22cancelInvite%22%2C%22getReceivedInvites%22%2C%22rejectInvite%22%2C%22inviteUser%22%5D%5D%5D&__client_imported__=true!\",\n        \"app/projects/[id]/page\": \"(rsc)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cworkspace.actions.ts%22%2C%5B%22getWorkspaceUploadUrl%22%2C%22removeMember%22%2C%22getWorkspace%22%2C%22leaveWorkspace%22%2C%22UploadFileToS3%22%2C%22deleteWorkspace%22%2C%22createWorkspace%22%2C%22getWorkspacePendingInvites%22%2C%22getWorkspaceMembers%22%2C%22updateWorkspaceUploadStatus%22%2C%22updateWorkspace%22%2C%22updateRole%22%2C%22deleteWorkspaces%22%2C%22getWorkspaces%22%5D%5D%5D&__client_imported__=!\",\n        \"app/projects/[id]/edit/page\": \"(rsc)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cworkspace.actions.ts%22%2C%5B%22getWorkspaceUploadUrl%22%2C%22removeMember%22%2C%22getWorkspace%22%2C%22leaveWorkspace%22%2C%22UploadFileToS3%22%2C%22deleteWorkspace%22%2C%22createWorkspace%22%2C%22getWorkspacePendingInvites%22%2C%22getWorkspaceMembers%22%2C%22updateWorkspaceUploadStatus%22%2C%22updateWorkspace%22%2C%22updateRole%22%2C%22deleteWorkspaces%22%2C%22getWorkspaces%22%5D%5D%5D&__client_imported__=!\"\n      },\n      \"layer\": {\n        \"app/page\": \"action-browser\",\n        \"app/projects/[id]/page\": \"rsc\",\n        \"app/projects/[id]/edit/page\": \"rsc\"\n      }\n    },\n    \"24f996c95795f80c6178d74a519dda14ecdd03d1\": {\n      \"workers\": {\n        \"app/page\": \"(action-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cversion.actions.ts%22%2C%5B%22getVersion%22%2C%22deleteVersions%22%2C%22updateVersion%22%2C%22getCurrentVersion%22%2C%22getVersions%22%2C%22createVersion%22%2C%22deleteVersion%22%5D%5D%2C%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cworkspace.actions.ts%22%2C%5B%22getWorkspaceUploadUrl%22%2C%22removeMember%22%2C%22getWorkspace%22%2C%22leaveWorkspace%22%2C%22UploadFileToS3%22%2C%22deleteWorkspace%22%2C%22createWorkspace%22%2C%22getWorkspacePendingInvites%22%2C%22getWorkspaceMembers%22%2C%22updateWorkspaceUploadStatus%22%2C%22updateWorkspace%22%2C%22updateRole%22%2C%22deleteWorkspaces%22%2C%22getWorkspaces%22%5D%5D%2C%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cinvite.actions.ts%22%2C%5B%22acceptInvite%22%2C%22cancelInvite%22%2C%22getReceivedInvites%22%2C%22rejectInvite%22%2C%22inviteUser%22%5D%5D%5D&__client_imported__=true!\",\n        \"app/projects/[id]/page\": \"(rsc)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cworkspace.actions.ts%22%2C%5B%22getWorkspaceUploadUrl%22%2C%22removeMember%22%2C%22getWorkspace%22%2C%22leaveWorkspace%22%2C%22UploadFileToS3%22%2C%22deleteWorkspace%22%2C%22createWorkspace%22%2C%22getWorkspacePendingInvites%22%2C%22getWorkspaceMembers%22%2C%22updateWorkspaceUploadStatus%22%2C%22updateWorkspace%22%2C%22updateRole%22%2C%22deleteWorkspaces%22%2C%22getWorkspaces%22%5D%5D%5D&__client_imported__=!\",\n        \"app/projects/[id]/edit/page\": \"(rsc)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cworkspace.actions.ts%22%2C%5B%22getWorkspaceUploadUrl%22%2C%22removeMember%22%2C%22getWorkspace%22%2C%22leaveWorkspace%22%2C%22UploadFileToS3%22%2C%22deleteWorkspace%22%2C%22createWorkspace%22%2C%22getWorkspacePendingInvites%22%2C%22getWorkspaceMembers%22%2C%22updateWorkspaceUploadStatus%22%2C%22updateWorkspace%22%2C%22updateRole%22%2C%22deleteWorkspaces%22%2C%22getWorkspaces%22%5D%5D%5D&__client_imported__=!\"\n      },\n      \"layer\": {\n        \"app/page\": \"action-browser\",\n        \"app/projects/[id]/page\": \"rsc\",\n        \"app/projects/[id]/edit/page\": \"rsc\"\n      }\n    },\n    \"2bff3a8dff3a5d7f3fe14a0a823a21bc8fdf5ee0\": {\n      \"workers\": {\n        \"app/page\": \"(action-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cversion.actions.ts%22%2C%5B%22getVersion%22%2C%22deleteVersions%22%2C%22updateVersion%22%2C%22getCurrentVersion%22%2C%22getVersions%22%2C%22createVersion%22%2C%22deleteVersion%22%5D%5D%2C%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cworkspace.actions.ts%22%2C%5B%22getWorkspaceUploadUrl%22%2C%22removeMember%22%2C%22getWorkspace%22%2C%22leaveWorkspace%22%2C%22UploadFileToS3%22%2C%22deleteWorkspace%22%2C%22createWorkspace%22%2C%22getWorkspacePendingInvites%22%2C%22getWorkspaceMembers%22%2C%22updateWorkspaceUploadStatus%22%2C%22updateWorkspace%22%2C%22updateRole%22%2C%22deleteWorkspaces%22%2C%22getWorkspaces%22%5D%5D%2C%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cinvite.actions.ts%22%2C%5B%22acceptInvite%22%2C%22cancelInvite%22%2C%22getReceivedInvites%22%2C%22rejectInvite%22%2C%22inviteUser%22%5D%5D%5D&__client_imported__=true!\",\n        \"app/projects/[id]/page\": \"(rsc)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cworkspace.actions.ts%22%2C%5B%22getWorkspaceUploadUrl%22%2C%22removeMember%22%2C%22getWorkspace%22%2C%22leaveWorkspace%22%2C%22UploadFileToS3%22%2C%22deleteWorkspace%22%2C%22createWorkspace%22%2C%22getWorkspacePendingInvites%22%2C%22getWorkspaceMembers%22%2C%22updateWorkspaceUploadStatus%22%2C%22updateWorkspace%22%2C%22updateRole%22%2C%22deleteWorkspaces%22%2C%22getWorkspaces%22%5D%5D%5D&__client_imported__=!\",\n        \"app/projects/[id]/edit/page\": \"(rsc)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cworkspace.actions.ts%22%2C%5B%22getWorkspaceUploadUrl%22%2C%22removeMember%22%2C%22getWorkspace%22%2C%22leaveWorkspace%22%2C%22UploadFileToS3%22%2C%22deleteWorkspace%22%2C%22createWorkspace%22%2C%22getWorkspacePendingInvites%22%2C%22getWorkspaceMembers%22%2C%22updateWorkspaceUploadStatus%22%2C%22updateWorkspace%22%2C%22updateRole%22%2C%22deleteWorkspaces%22%2C%22getWorkspaces%22%5D%5D%5D&__client_imported__=!\"\n      },\n      \"layer\": {\n        \"app/page\": \"action-browser\",\n        \"app/projects/[id]/page\": \"rsc\",\n        \"app/projects/[id]/edit/page\": \"rsc\"\n      }\n    },\n    \"2e843e559e768b70c5438f5508ea04d1f7e79a31\": {\n      \"workers\": {\n        \"app/page\": \"(action-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cversion.actions.ts%22%2C%5B%22getVersion%22%2C%22deleteVersions%22%2C%22updateVersion%22%2C%22getCurrentVersion%22%2C%22getVersions%22%2C%22createVersion%22%2C%22deleteVersion%22%5D%5D%2C%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cworkspace.actions.ts%22%2C%5B%22getWorkspaceUploadUrl%22%2C%22removeMember%22%2C%22getWorkspace%22%2C%22leaveWorkspace%22%2C%22UploadFileToS3%22%2C%22deleteWorkspace%22%2C%22createWorkspace%22%2C%22getWorkspacePendingInvites%22%2C%22getWorkspaceMembers%22%2C%22updateWorkspaceUploadStatus%22%2C%22updateWorkspace%22%2C%22updateRole%22%2C%22deleteWorkspaces%22%2C%22getWorkspaces%22%5D%5D%2C%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cinvite.actions.ts%22%2C%5B%22acceptInvite%22%2C%22cancelInvite%22%2C%22getReceivedInvites%22%2C%22rejectInvite%22%2C%22inviteUser%22%5D%5D%5D&__client_imported__=true!\",\n        \"app/projects/[id]/page\": \"(rsc)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cworkspace.actions.ts%22%2C%5B%22getWorkspaceUploadUrl%22%2C%22removeMember%22%2C%22getWorkspace%22%2C%22leaveWorkspace%22%2C%22UploadFileToS3%22%2C%22deleteWorkspace%22%2C%22createWorkspace%22%2C%22getWorkspacePendingInvites%22%2C%22getWorkspaceMembers%22%2C%22updateWorkspaceUploadStatus%22%2C%22updateWorkspace%22%2C%22updateRole%22%2C%22deleteWorkspaces%22%2C%22getWorkspaces%22%5D%5D%5D&__client_imported__=!\",\n        \"app/projects/[id]/edit/page\": \"(rsc)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cworkspace.actions.ts%22%2C%5B%22getWorkspaceUploadUrl%22%2C%22removeMember%22%2C%22getWorkspace%22%2C%22leaveWorkspace%22%2C%22UploadFileToS3%22%2C%22deleteWorkspace%22%2C%22createWorkspace%22%2C%22getWorkspacePendingInvites%22%2C%22getWorkspaceMembers%22%2C%22updateWorkspaceUploadStatus%22%2C%22updateWorkspace%22%2C%22updateRole%22%2C%22deleteWorkspaces%22%2C%22getWorkspaces%22%5D%5D%5D&__client_imported__=!\"\n      },\n      \"layer\": {\n        \"app/page\": \"action-browser\",\n        \"app/projects/[id]/page\": \"rsc\",\n        \"app/projects/[id]/edit/page\": \"rsc\"\n      }\n    },\n    \"3ba52efe929ed6dede843114b8d4b6a38c17df50\": {\n      \"workers\": {\n        \"app/page\": \"(action-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cversion.actions.ts%22%2C%5B%22getVersion%22%2C%22deleteVersions%22%2C%22updateVersion%22%2C%22getCurrentVersion%22%2C%22getVersions%22%2C%22createVersion%22%2C%22deleteVersion%22%5D%5D%2C%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cworkspace.actions.ts%22%2C%5B%22getWorkspaceUploadUrl%22%2C%22removeMember%22%2C%22getWorkspace%22%2C%22leaveWorkspace%22%2C%22UploadFileToS3%22%2C%22deleteWorkspace%22%2C%22createWorkspace%22%2C%22getWorkspacePendingInvites%22%2C%22getWorkspaceMembers%22%2C%22updateWorkspaceUploadStatus%22%2C%22updateWorkspace%22%2C%22updateRole%22%2C%22deleteWorkspaces%22%2C%22getWorkspaces%22%5D%5D%2C%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cinvite.actions.ts%22%2C%5B%22acceptInvite%22%2C%22cancelInvite%22%2C%22getReceivedInvites%22%2C%22rejectInvite%22%2C%22inviteUser%22%5D%5D%5D&__client_imported__=true!\",\n        \"app/projects/[id]/page\": \"(rsc)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cworkspace.actions.ts%22%2C%5B%22getWorkspaceUploadUrl%22%2C%22removeMember%22%2C%22getWorkspace%22%2C%22leaveWorkspace%22%2C%22UploadFileToS3%22%2C%22deleteWorkspace%22%2C%22createWorkspace%22%2C%22getWorkspacePendingInvites%22%2C%22getWorkspaceMembers%22%2C%22updateWorkspaceUploadStatus%22%2C%22updateWorkspace%22%2C%22updateRole%22%2C%22deleteWorkspaces%22%2C%22getWorkspaces%22%5D%5D%5D&__client_imported__=!\",\n        \"app/projects/[id]/edit/page\": \"(rsc)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cworkspace.actions.ts%22%2C%5B%22getWorkspaceUploadUrl%22%2C%22removeMember%22%2C%22getWorkspace%22%2C%22leaveWorkspace%22%2C%22UploadFileToS3%22%2C%22deleteWorkspace%22%2C%22createWorkspace%22%2C%22getWorkspacePendingInvites%22%2C%22getWorkspaceMembers%22%2C%22updateWorkspaceUploadStatus%22%2C%22updateWorkspace%22%2C%22updateRole%22%2C%22deleteWorkspaces%22%2C%22getWorkspaces%22%5D%5D%5D&__client_imported__=!\"\n      },\n      \"layer\": {\n        \"app/page\": \"action-browser\",\n        \"app/projects/[id]/page\": \"rsc\",\n        \"app/projects/[id]/edit/page\": \"rsc\"\n      }\n    },\n    \"7ab00e47d6eb7aa6288816fe6e16491af1601b4a\": {\n      \"workers\": {\n        \"app/page\": \"(action-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cversion.actions.ts%22%2C%5B%22getVersion%22%2C%22deleteVersions%22%2C%22updateVersion%22%2C%22getCurrentVersion%22%2C%22getVersions%22%2C%22createVersion%22%2C%22deleteVersion%22%5D%5D%2C%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cworkspace.actions.ts%22%2C%5B%22getWorkspaceUploadUrl%22%2C%22removeMember%22%2C%22getWorkspace%22%2C%22leaveWorkspace%22%2C%22UploadFileToS3%22%2C%22deleteWorkspace%22%2C%22createWorkspace%22%2C%22getWorkspacePendingInvites%22%2C%22getWorkspaceMembers%22%2C%22updateWorkspaceUploadStatus%22%2C%22updateWorkspace%22%2C%22updateRole%22%2C%22deleteWorkspaces%22%2C%22getWorkspaces%22%5D%5D%2C%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cinvite.actions.ts%22%2C%5B%22acceptInvite%22%2C%22cancelInvite%22%2C%22getReceivedInvites%22%2C%22rejectInvite%22%2C%22inviteUser%22%5D%5D%5D&__client_imported__=true!\",\n        \"app/projects/[id]/page\": \"(rsc)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cworkspace.actions.ts%22%2C%5B%22getWorkspaceUploadUrl%22%2C%22removeMember%22%2C%22getWorkspace%22%2C%22leaveWorkspace%22%2C%22UploadFileToS3%22%2C%22deleteWorkspace%22%2C%22createWorkspace%22%2C%22getWorkspacePendingInvites%22%2C%22getWorkspaceMembers%22%2C%22updateWorkspaceUploadStatus%22%2C%22updateWorkspace%22%2C%22updateRole%22%2C%22deleteWorkspaces%22%2C%22getWorkspaces%22%5D%5D%5D&__client_imported__=!\",\n        \"app/projects/[id]/edit/page\": \"(rsc)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cworkspace.actions.ts%22%2C%5B%22getWorkspaceUploadUrl%22%2C%22removeMember%22%2C%22getWorkspace%22%2C%22leaveWorkspace%22%2C%22UploadFileToS3%22%2C%22deleteWorkspace%22%2C%22createWorkspace%22%2C%22getWorkspacePendingInvites%22%2C%22getWorkspaceMembers%22%2C%22updateWorkspaceUploadStatus%22%2C%22updateWorkspace%22%2C%22updateRole%22%2C%22deleteWorkspaces%22%2C%22getWorkspaces%22%5D%5D%5D&__client_imported__=!\"\n      },\n      \"layer\": {\n        \"app/page\": \"action-browser\",\n        \"app/projects/[id]/page\": \"rsc\",\n        \"app/projects/[id]/edit/page\": \"rsc\"\n      }\n    },\n    \"827f5eb9cb6964f97a57b2ef96cf01e7041a5dfc\": {\n      \"workers\": {\n        \"app/page\": \"(action-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cversion.actions.ts%22%2C%5B%22getVersion%22%2C%22deleteVersions%22%2C%22updateVersion%22%2C%22getCurrentVersion%22%2C%22getVersions%22%2C%22createVersion%22%2C%22deleteVersion%22%5D%5D%2C%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cworkspace.actions.ts%22%2C%5B%22getWorkspaceUploadUrl%22%2C%22removeMember%22%2C%22getWorkspace%22%2C%22leaveWorkspace%22%2C%22UploadFileToS3%22%2C%22deleteWorkspace%22%2C%22createWorkspace%22%2C%22getWorkspacePendingInvites%22%2C%22getWorkspaceMembers%22%2C%22updateWorkspaceUploadStatus%22%2C%22updateWorkspace%22%2C%22updateRole%22%2C%22deleteWorkspaces%22%2C%22getWorkspaces%22%5D%5D%2C%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cinvite.actions.ts%22%2C%5B%22acceptInvite%22%2C%22cancelInvite%22%2C%22getReceivedInvites%22%2C%22rejectInvite%22%2C%22inviteUser%22%5D%5D%5D&__client_imported__=true!\",\n        \"app/projects/[id]/page\": \"(rsc)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cworkspace.actions.ts%22%2C%5B%22getWorkspaceUploadUrl%22%2C%22removeMember%22%2C%22getWorkspace%22%2C%22leaveWorkspace%22%2C%22UploadFileToS3%22%2C%22deleteWorkspace%22%2C%22createWorkspace%22%2C%22getWorkspacePendingInvites%22%2C%22getWorkspaceMembers%22%2C%22updateWorkspaceUploadStatus%22%2C%22updateWorkspace%22%2C%22updateRole%22%2C%22deleteWorkspaces%22%2C%22getWorkspaces%22%5D%5D%5D&__client_imported__=!\",\n        \"app/projects/[id]/edit/page\": \"(rsc)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cworkspace.actions.ts%22%2C%5B%22getWorkspaceUploadUrl%22%2C%22removeMember%22%2C%22getWorkspace%22%2C%22leaveWorkspace%22%2C%22UploadFileToS3%22%2C%22deleteWorkspace%22%2C%22createWorkspace%22%2C%22getWorkspacePendingInvites%22%2C%22getWorkspaceMembers%22%2C%22updateWorkspaceUploadStatus%22%2C%22updateWorkspace%22%2C%22updateRole%22%2C%22deleteWorkspaces%22%2C%22getWorkspaces%22%5D%5D%5D&__client_imported__=!\"\n      },\n      \"layer\": {\n        \"app/page\": \"action-browser\",\n        \"app/projects/[id]/page\": \"rsc\",\n        \"app/projects/[id]/edit/page\": \"rsc\"\n      }\n    },\n    \"a6744ef04e770d8c50b7b4bf33cd3a20bf32e1c9\": {\n      \"workers\": {\n        \"app/page\": \"(action-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cversion.actions.ts%22%2C%5B%22getVersion%22%2C%22deleteVersions%22%2C%22updateVersion%22%2C%22getCurrentVersion%22%2C%22getVersions%22%2C%22createVersion%22%2C%22deleteVersion%22%5D%5D%2C%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cworkspace.actions.ts%22%2C%5B%22getWorkspaceUploadUrl%22%2C%22removeMember%22%2C%22getWorkspace%22%2C%22leaveWorkspace%22%2C%22UploadFileToS3%22%2C%22deleteWorkspace%22%2C%22createWorkspace%22%2C%22getWorkspacePendingInvites%22%2C%22getWorkspaceMembers%22%2C%22updateWorkspaceUploadStatus%22%2C%22updateWorkspace%22%2C%22updateRole%22%2C%22deleteWorkspaces%22%2C%22getWorkspaces%22%5D%5D%2C%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cinvite.actions.ts%22%2C%5B%22acceptInvite%22%2C%22cancelInvite%22%2C%22getReceivedInvites%22%2C%22rejectInvite%22%2C%22inviteUser%22%5D%5D%5D&__client_imported__=true!\",\n        \"app/projects/[id]/page\": \"(rsc)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cworkspace.actions.ts%22%2C%5B%22getWorkspaceUploadUrl%22%2C%22removeMember%22%2C%22getWorkspace%22%2C%22leaveWorkspace%22%2C%22UploadFileToS3%22%2C%22deleteWorkspace%22%2C%22createWorkspace%22%2C%22getWorkspacePendingInvites%22%2C%22getWorkspaceMembers%22%2C%22updateWorkspaceUploadStatus%22%2C%22updateWorkspace%22%2C%22updateRole%22%2C%22deleteWorkspaces%22%2C%22getWorkspaces%22%5D%5D%5D&__client_imported__=!\",\n        \"app/projects/[id]/edit/page\": \"(rsc)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cworkspace.actions.ts%22%2C%5B%22getWorkspaceUploadUrl%22%2C%22removeMember%22%2C%22getWorkspace%22%2C%22leaveWorkspace%22%2C%22UploadFileToS3%22%2C%22deleteWorkspace%22%2C%22createWorkspace%22%2C%22getWorkspacePendingInvites%22%2C%22getWorkspaceMembers%22%2C%22updateWorkspaceUploadStatus%22%2C%22updateWorkspace%22%2C%22updateRole%22%2C%22deleteWorkspaces%22%2C%22getWorkspaces%22%5D%5D%5D&__client_imported__=!\"\n      },\n      \"layer\": {\n        \"app/page\": \"action-browser\",\n        \"app/projects/[id]/page\": \"rsc\",\n        \"app/projects/[id]/edit/page\": \"rsc\"\n      }\n    },\n    \"a82515a0ba8f7f0bd29468b0cc28cff936225a04\": {\n      \"workers\": {\n        \"app/page\": \"(action-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cversion.actions.ts%22%2C%5B%22getVersion%22%2C%22deleteVersions%22%2C%22updateVersion%22%2C%22getCurrentVersion%22%2C%22getVersions%22%2C%22createVersion%22%2C%22deleteVersion%22%5D%5D%2C%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cworkspace.actions.ts%22%2C%5B%22getWorkspaceUploadUrl%22%2C%22removeMember%22%2C%22getWorkspace%22%2C%22leaveWorkspace%22%2C%22UploadFileToS3%22%2C%22deleteWorkspace%22%2C%22createWorkspace%22%2C%22getWorkspacePendingInvites%22%2C%22getWorkspaceMembers%22%2C%22updateWorkspaceUploadStatus%22%2C%22updateWorkspace%22%2C%22updateRole%22%2C%22deleteWorkspaces%22%2C%22getWorkspaces%22%5D%5D%2C%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cinvite.actions.ts%22%2C%5B%22acceptInvite%22%2C%22cancelInvite%22%2C%22getReceivedInvites%22%2C%22rejectInvite%22%2C%22inviteUser%22%5D%5D%5D&__client_imported__=true!\",\n        \"app/projects/[id]/page\": \"(rsc)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cworkspace.actions.ts%22%2C%5B%22getWorkspaceUploadUrl%22%2C%22removeMember%22%2C%22getWorkspace%22%2C%22leaveWorkspace%22%2C%22UploadFileToS3%22%2C%22deleteWorkspace%22%2C%22createWorkspace%22%2C%22getWorkspacePendingInvites%22%2C%22getWorkspaceMembers%22%2C%22updateWorkspaceUploadStatus%22%2C%22updateWorkspace%22%2C%22updateRole%22%2C%22deleteWorkspaces%22%2C%22getWorkspaces%22%5D%5D%5D&__client_imported__=!\",\n        \"app/projects/[id]/edit/page\": \"(rsc)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cworkspace.actions.ts%22%2C%5B%22getWorkspaceUploadUrl%22%2C%22removeMember%22%2C%22getWorkspace%22%2C%22leaveWorkspace%22%2C%22UploadFileToS3%22%2C%22deleteWorkspace%22%2C%22createWorkspace%22%2C%22getWorkspacePendingInvites%22%2C%22getWorkspaceMembers%22%2C%22updateWorkspaceUploadStatus%22%2C%22updateWorkspace%22%2C%22updateRole%22%2C%22deleteWorkspaces%22%2C%22getWorkspaces%22%5D%5D%5D&__client_imported__=!\"\n      },\n      \"layer\": {\n        \"app/page\": \"action-browser\",\n        \"app/projects/[id]/page\": \"rsc\",\n        \"app/projects/[id]/edit/page\": \"rsc\"\n      }\n    },\n    \"c3cfa10258a88db0db275ad7f4e4409d452cd2c6\": {\n      \"workers\": {\n        \"app/page\": \"(action-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cversion.actions.ts%22%2C%5B%22getVersion%22%2C%22deleteVersions%22%2C%22updateVersion%22%2C%22getCurrentVersion%22%2C%22getVersions%22%2C%22createVersion%22%2C%22deleteVersion%22%5D%5D%2C%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cworkspace.actions.ts%22%2C%5B%22getWorkspaceUploadUrl%22%2C%22removeMember%22%2C%22getWorkspace%22%2C%22leaveWorkspace%22%2C%22UploadFileToS3%22%2C%22deleteWorkspace%22%2C%22createWorkspace%22%2C%22getWorkspacePendingInvites%22%2C%22getWorkspaceMembers%22%2C%22updateWorkspaceUploadStatus%22%2C%22updateWorkspace%22%2C%22updateRole%22%2C%22deleteWorkspaces%22%2C%22getWorkspaces%22%5D%5D%2C%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cinvite.actions.ts%22%2C%5B%22acceptInvite%22%2C%22cancelInvite%22%2C%22getReceivedInvites%22%2C%22rejectInvite%22%2C%22inviteUser%22%5D%5D%5D&__client_imported__=true!\",\n        \"app/projects/[id]/page\": \"(rsc)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cworkspace.actions.ts%22%2C%5B%22getWorkspaceUploadUrl%22%2C%22removeMember%22%2C%22getWorkspace%22%2C%22leaveWorkspace%22%2C%22UploadFileToS3%22%2C%22deleteWorkspace%22%2C%22createWorkspace%22%2C%22getWorkspacePendingInvites%22%2C%22getWorkspaceMembers%22%2C%22updateWorkspaceUploadStatus%22%2C%22updateWorkspace%22%2C%22updateRole%22%2C%22deleteWorkspaces%22%2C%22getWorkspaces%22%5D%5D%5D&__client_imported__=!\",\n        \"app/projects/[id]/edit/page\": \"(rsc)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cworkspace.actions.ts%22%2C%5B%22getWorkspaceUploadUrl%22%2C%22removeMember%22%2C%22getWorkspace%22%2C%22leaveWorkspace%22%2C%22UploadFileToS3%22%2C%22deleteWorkspace%22%2C%22createWorkspace%22%2C%22getWorkspacePendingInvites%22%2C%22getWorkspaceMembers%22%2C%22updateWorkspaceUploadStatus%22%2C%22updateWorkspace%22%2C%22updateRole%22%2C%22deleteWorkspaces%22%2C%22getWorkspaces%22%5D%5D%5D&__client_imported__=!\"\n      },\n      \"layer\": {\n        \"app/page\": \"action-browser\",\n        \"app/projects/[id]/page\": \"rsc\",\n        \"app/projects/[id]/edit/page\": \"rsc\"\n      }\n    },\n    \"ce4a8abea8adec88075d6c1ff32fdc8d3a9ad618\": {\n      \"workers\": {\n        \"app/page\": \"(action-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cversion.actions.ts%22%2C%5B%22getVersion%22%2C%22deleteVersions%22%2C%22updateVersion%22%2C%22getCurrentVersion%22%2C%22getVersions%22%2C%22createVersion%22%2C%22deleteVersion%22%5D%5D%2C%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cworkspace.actions.ts%22%2C%5B%22getWorkspaceUploadUrl%22%2C%22removeMember%22%2C%22getWorkspace%22%2C%22leaveWorkspace%22%2C%22UploadFileToS3%22%2C%22deleteWorkspace%22%2C%22createWorkspace%22%2C%22getWorkspacePendingInvites%22%2C%22getWorkspaceMembers%22%2C%22updateWorkspaceUploadStatus%22%2C%22updateWorkspace%22%2C%22updateRole%22%2C%22deleteWorkspaces%22%2C%22getWorkspaces%22%5D%5D%2C%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cinvite.actions.ts%22%2C%5B%22acceptInvite%22%2C%22cancelInvite%22%2C%22getReceivedInvites%22%2C%22rejectInvite%22%2C%22inviteUser%22%5D%5D%5D&__client_imported__=true!\",\n        \"app/projects/[id]/page\": \"(rsc)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cworkspace.actions.ts%22%2C%5B%22getWorkspaceUploadUrl%22%2C%22removeMember%22%2C%22getWorkspace%22%2C%22leaveWorkspace%22%2C%22UploadFileToS3%22%2C%22deleteWorkspace%22%2C%22createWorkspace%22%2C%22getWorkspacePendingInvites%22%2C%22getWorkspaceMembers%22%2C%22updateWorkspaceUploadStatus%22%2C%22updateWorkspace%22%2C%22updateRole%22%2C%22deleteWorkspaces%22%2C%22getWorkspaces%22%5D%5D%5D&__client_imported__=!\",\n        \"app/projects/[id]/edit/page\": \"(rsc)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cworkspace.actions.ts%22%2C%5B%22getWorkspaceUploadUrl%22%2C%22removeMember%22%2C%22getWorkspace%22%2C%22leaveWorkspace%22%2C%22UploadFileToS3%22%2C%22deleteWorkspace%22%2C%22createWorkspace%22%2C%22getWorkspacePendingInvites%22%2C%22getWorkspaceMembers%22%2C%22updateWorkspaceUploadStatus%22%2C%22updateWorkspace%22%2C%22updateRole%22%2C%22deleteWorkspaces%22%2C%22getWorkspaces%22%5D%5D%5D&__client_imported__=!\"\n      },\n      \"layer\": {\n        \"app/page\": \"action-browser\",\n        \"app/projects/[id]/page\": \"rsc\",\n        \"app/projects/[id]/edit/page\": \"rsc\"\n      }\n    },\n    \"d2dc946dcf99188b3be50e8be7ac7abcd2b27450\": {\n      \"workers\": {\n        \"app/page\": \"(action-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cversion.actions.ts%22%2C%5B%22getVersion%22%2C%22deleteVersions%22%2C%22updateVersion%22%2C%22getCurrentVersion%22%2C%22getVersions%22%2C%22createVersion%22%2C%22deleteVersion%22%5D%5D%2C%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cworkspace.actions.ts%22%2C%5B%22getWorkspaceUploadUrl%22%2C%22removeMember%22%2C%22getWorkspace%22%2C%22leaveWorkspace%22%2C%22UploadFileToS3%22%2C%22deleteWorkspace%22%2C%22createWorkspace%22%2C%22getWorkspacePendingInvites%22%2C%22getWorkspaceMembers%22%2C%22updateWorkspaceUploadStatus%22%2C%22updateWorkspace%22%2C%22updateRole%22%2C%22deleteWorkspaces%22%2C%22getWorkspaces%22%5D%5D%2C%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cinvite.actions.ts%22%2C%5B%22acceptInvite%22%2C%22cancelInvite%22%2C%22getReceivedInvites%22%2C%22rejectInvite%22%2C%22inviteUser%22%5D%5D%5D&__client_imported__=true!\",\n        \"app/projects/[id]/page\": \"(rsc)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cworkspace.actions.ts%22%2C%5B%22getWorkspaceUploadUrl%22%2C%22removeMember%22%2C%22getWorkspace%22%2C%22leaveWorkspace%22%2C%22UploadFileToS3%22%2C%22deleteWorkspace%22%2C%22createWorkspace%22%2C%22getWorkspacePendingInvites%22%2C%22getWorkspaceMembers%22%2C%22updateWorkspaceUploadStatus%22%2C%22updateWorkspace%22%2C%22updateRole%22%2C%22deleteWorkspaces%22%2C%22getWorkspaces%22%5D%5D%5D&__client_imported__=!\",\n        \"app/projects/[id]/edit/page\": \"(rsc)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cworkspace.actions.ts%22%2C%5B%22getWorkspaceUploadUrl%22%2C%22removeMember%22%2C%22getWorkspace%22%2C%22leaveWorkspace%22%2C%22UploadFileToS3%22%2C%22deleteWorkspace%22%2C%22createWorkspace%22%2C%22getWorkspacePendingInvites%22%2C%22getWorkspaceMembers%22%2C%22updateWorkspaceUploadStatus%22%2C%22updateWorkspace%22%2C%22updateRole%22%2C%22deleteWorkspaces%22%2C%22getWorkspaces%22%5D%5D%5D&__client_imported__=!\"\n      },\n      \"layer\": {\n        \"app/page\": \"action-browser\",\n        \"app/projects/[id]/page\": \"rsc\",\n        \"app/projects/[id]/edit/page\": \"rsc\"\n      }\n    },\n    \"f65cf45c8116cec7cdbc55ecd40091e3837a4579\": {\n      \"workers\": {\n        \"app/page\": \"(action-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cversion.actions.ts%22%2C%5B%22getVersion%22%2C%22deleteVersions%22%2C%22updateVersion%22%2C%22getCurrentVersion%22%2C%22getVersions%22%2C%22createVersion%22%2C%22deleteVersion%22%5D%5D%2C%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cworkspace.actions.ts%22%2C%5B%22getWorkspaceUploadUrl%22%2C%22removeMember%22%2C%22getWorkspace%22%2C%22leaveWorkspace%22%2C%22UploadFileToS3%22%2C%22deleteWorkspace%22%2C%22createWorkspace%22%2C%22getWorkspacePendingInvites%22%2C%22getWorkspaceMembers%22%2C%22updateWorkspaceUploadStatus%22%2C%22updateWorkspace%22%2C%22updateRole%22%2C%22deleteWorkspaces%22%2C%22getWorkspaces%22%5D%5D%2C%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cinvite.actions.ts%22%2C%5B%22acceptInvite%22%2C%22cancelInvite%22%2C%22getReceivedInvites%22%2C%22rejectInvite%22%2C%22inviteUser%22%5D%5D%5D&__client_imported__=true!\",\n        \"app/projects/[id]/page\": \"(rsc)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cworkspace.actions.ts%22%2C%5B%22getWorkspaceUploadUrl%22%2C%22removeMember%22%2C%22getWorkspace%22%2C%22leaveWorkspace%22%2C%22UploadFileToS3%22%2C%22deleteWorkspace%22%2C%22createWorkspace%22%2C%22getWorkspacePendingInvites%22%2C%22getWorkspaceMembers%22%2C%22updateWorkspaceUploadStatus%22%2C%22updateWorkspace%22%2C%22updateRole%22%2C%22deleteWorkspaces%22%2C%22getWorkspaces%22%5D%5D%5D&__client_imported__=!\",\n        \"app/projects/[id]/edit/page\": \"(rsc)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cworkspace.actions.ts%22%2C%5B%22getWorkspaceUploadUrl%22%2C%22removeMember%22%2C%22getWorkspace%22%2C%22leaveWorkspace%22%2C%22UploadFileToS3%22%2C%22deleteWorkspace%22%2C%22createWorkspace%22%2C%22getWorkspacePendingInvites%22%2C%22getWorkspaceMembers%22%2C%22updateWorkspaceUploadStatus%22%2C%22updateWorkspace%22%2C%22updateRole%22%2C%22deleteWorkspaces%22%2C%22getWorkspaces%22%5D%5D%5D&__client_imported__=!\"\n      },\n      \"layer\": {\n        \"app/page\": \"action-browser\",\n        \"app/projects/[id]/page\": \"rsc\",\n        \"app/projects/[id]/edit/page\": \"rsc\"\n      }\n    },\n    \"fcdc0978a1a457f97e675f6718e9cd0441a5fb8a\": {\n      \"workers\": {\n        \"app/page\": \"(action-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cversion.actions.ts%22%2C%5B%22getVersion%22%2C%22deleteVersions%22%2C%22updateVersion%22%2C%22getCurrentVersion%22%2C%22getVersions%22%2C%22createVersion%22%2C%22deleteVersion%22%5D%5D%2C%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cworkspace.actions.ts%22%2C%5B%22getWorkspaceUploadUrl%22%2C%22removeMember%22%2C%22getWorkspace%22%2C%22leaveWorkspace%22%2C%22UploadFileToS3%22%2C%22deleteWorkspace%22%2C%22createWorkspace%22%2C%22getWorkspacePendingInvites%22%2C%22getWorkspaceMembers%22%2C%22updateWorkspaceUploadStatus%22%2C%22updateWorkspace%22%2C%22updateRole%22%2C%22deleteWorkspaces%22%2C%22getWorkspaces%22%5D%5D%2C%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cinvite.actions.ts%22%2C%5B%22acceptInvite%22%2C%22cancelInvite%22%2C%22getReceivedInvites%22%2C%22rejectInvite%22%2C%22inviteUser%22%5D%5D%5D&__client_imported__=true!\",\n        \"app/projects/[id]/page\": \"(rsc)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cworkspace.actions.ts%22%2C%5B%22getWorkspaceUploadUrl%22%2C%22removeMember%22%2C%22getWorkspace%22%2C%22leaveWorkspace%22%2C%22UploadFileToS3%22%2C%22deleteWorkspace%22%2C%22createWorkspace%22%2C%22getWorkspacePendingInvites%22%2C%22getWorkspaceMembers%22%2C%22updateWorkspaceUploadStatus%22%2C%22updateWorkspace%22%2C%22updateRole%22%2C%22deleteWorkspaces%22%2C%22getWorkspaces%22%5D%5D%5D&__client_imported__=!\",\n        \"app/projects/[id]/edit/page\": \"(rsc)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cworkspace.actions.ts%22%2C%5B%22getWorkspaceUploadUrl%22%2C%22removeMember%22%2C%22getWorkspace%22%2C%22leaveWorkspace%22%2C%22UploadFileToS3%22%2C%22deleteWorkspace%22%2C%22createWorkspace%22%2C%22getWorkspacePendingInvites%22%2C%22getWorkspaceMembers%22%2C%22updateWorkspaceUploadStatus%22%2C%22updateWorkspace%22%2C%22updateRole%22%2C%22deleteWorkspaces%22%2C%22getWorkspaces%22%5D%5D%5D&__client_imported__=!\"\n      },\n      \"layer\": {\n        \"app/page\": \"action-browser\",\n        \"app/projects/[id]/page\": \"rsc\",\n        \"app/projects/[id]/edit/page\": \"rsc\"\n      }\n    },\n    \"16d6a264ee2b817c3fd8eea0ea96130325530824\": {\n      \"workers\": {\n        \"app/page\": \"(action-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cversion.actions.ts%22%2C%5B%22getVersion%22%2C%22deleteVersions%22%2C%22updateVersion%22%2C%22getCurrentVersion%22%2C%22getVersions%22%2C%22createVersion%22%2C%22deleteVersion%22%5D%5D%2C%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cworkspace.actions.ts%22%2C%5B%22getWorkspaceUploadUrl%22%2C%22removeMember%22%2C%22getWorkspace%22%2C%22leaveWorkspace%22%2C%22UploadFileToS3%22%2C%22deleteWorkspace%22%2C%22createWorkspace%22%2C%22getWorkspacePendingInvites%22%2C%22getWorkspaceMembers%22%2C%22updateWorkspaceUploadStatus%22%2C%22updateWorkspace%22%2C%22updateRole%22%2C%22deleteWorkspaces%22%2C%22getWorkspaces%22%5D%5D%2C%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cinvite.actions.ts%22%2C%5B%22acceptInvite%22%2C%22cancelInvite%22%2C%22getReceivedInvites%22%2C%22rejectInvite%22%2C%22inviteUser%22%5D%5D%5D&__client_imported__=true!\",\n        \"app/projects/[id]/page\": \"(action-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cinvite.actions.ts%22%2C%5B%22acceptInvite%22%2C%22cancelInvite%22%2C%22getReceivedInvites%22%2C%22rejectInvite%22%2C%22inviteUser%22%5D%5D%2C%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Ccomment.actions.ts%22%2C%5B%22deleteComment%22%2C%22updateCommentReply%22%2C%22deleteCommentReply%22%2C%22updateComment%22%2C%22createCommentReply%22%2C%22getWorkspaceComments%22%2C%22getCommentReplies%22%2C%22createComment%22%2C%22reactToComment%22%2C%22resolveComment%22%5D%5D%5D&__client_imported__=true!\"\n      },\n      \"layer\": {\n        \"app/page\": \"action-browser\",\n        \"app/projects/[id]/page\": \"action-browser\"\n      }\n    },\n    \"7d5dd7a2acb747e361797e864f3dae447898c17b\": {\n      \"workers\": {\n        \"app/page\": \"(action-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cversion.actions.ts%22%2C%5B%22getVersion%22%2C%22deleteVersions%22%2C%22updateVersion%22%2C%22getCurrentVersion%22%2C%22getVersions%22%2C%22createVersion%22%2C%22deleteVersion%22%5D%5D%2C%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cworkspace.actions.ts%22%2C%5B%22getWorkspaceUploadUrl%22%2C%22removeMember%22%2C%22getWorkspace%22%2C%22leaveWorkspace%22%2C%22UploadFileToS3%22%2C%22deleteWorkspace%22%2C%22createWorkspace%22%2C%22getWorkspacePendingInvites%22%2C%22getWorkspaceMembers%22%2C%22updateWorkspaceUploadStatus%22%2C%22updateWorkspace%22%2C%22updateRole%22%2C%22deleteWorkspaces%22%2C%22getWorkspaces%22%5D%5D%2C%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cinvite.actions.ts%22%2C%5B%22acceptInvite%22%2C%22cancelInvite%22%2C%22getReceivedInvites%22%2C%22rejectInvite%22%2C%22inviteUser%22%5D%5D%5D&__client_imported__=true!\",\n        \"app/projects/[id]/page\": \"(action-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cinvite.actions.ts%22%2C%5B%22acceptInvite%22%2C%22cancelInvite%22%2C%22getReceivedInvites%22%2C%22rejectInvite%22%2C%22inviteUser%22%5D%5D%2C%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Ccomment.actions.ts%22%2C%5B%22deleteComment%22%2C%22updateCommentReply%22%2C%22deleteCommentReply%22%2C%22updateComment%22%2C%22createCommentReply%22%2C%22getWorkspaceComments%22%2C%22getCommentReplies%22%2C%22createComment%22%2C%22reactToComment%22%2C%22resolveComment%22%5D%5D%5D&__client_imported__=true!\"\n      },\n      \"layer\": {\n        \"app/page\": \"action-browser\",\n        \"app/projects/[id]/page\": \"action-browser\"\n      }\n    },\n    \"b3fbbbd59222832786f212e49fa3da5ce2e45107\": {\n      \"workers\": {\n        \"app/page\": \"(action-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cversion.actions.ts%22%2C%5B%22getVersion%22%2C%22deleteVersions%22%2C%22updateVersion%22%2C%22getCurrentVersion%22%2C%22getVersions%22%2C%22createVersion%22%2C%22deleteVersion%22%5D%5D%2C%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cworkspace.actions.ts%22%2C%5B%22getWorkspaceUploadUrl%22%2C%22removeMember%22%2C%22getWorkspace%22%2C%22leaveWorkspace%22%2C%22UploadFileToS3%22%2C%22deleteWorkspace%22%2C%22createWorkspace%22%2C%22getWorkspacePendingInvites%22%2C%22getWorkspaceMembers%22%2C%22updateWorkspaceUploadStatus%22%2C%22updateWorkspace%22%2C%22updateRole%22%2C%22deleteWorkspaces%22%2C%22getWorkspaces%22%5D%5D%2C%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cinvite.actions.ts%22%2C%5B%22acceptInvite%22%2C%22cancelInvite%22%2C%22getReceivedInvites%22%2C%22rejectInvite%22%2C%22inviteUser%22%5D%5D%5D&__client_imported__=true!\",\n        \"app/projects/[id]/page\": \"(action-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cinvite.actions.ts%22%2C%5B%22acceptInvite%22%2C%22cancelInvite%22%2C%22getReceivedInvites%22%2C%22rejectInvite%22%2C%22inviteUser%22%5D%5D%2C%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Ccomment.actions.ts%22%2C%5B%22deleteComment%22%2C%22updateCommentReply%22%2C%22deleteCommentReply%22%2C%22updateComment%22%2C%22createCommentReply%22%2C%22getWorkspaceComments%22%2C%22getCommentReplies%22%2C%22createComment%22%2C%22reactToComment%22%2C%22resolveComment%22%5D%5D%5D&__client_imported__=true!\"\n      },\n      \"layer\": {\n        \"app/page\": \"action-browser\",\n        \"app/projects/[id]/page\": \"action-browser\"\n      }\n    },\n    \"c80a225c2bdfbb1a12b20f5fbb94382c0caf354c\": {\n      \"workers\": {\n        \"app/page\": \"(action-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cversion.actions.ts%22%2C%5B%22getVersion%22%2C%22deleteVersions%22%2C%22updateVersion%22%2C%22getCurrentVersion%22%2C%22getVersions%22%2C%22createVersion%22%2C%22deleteVersion%22%5D%5D%2C%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cworkspace.actions.ts%22%2C%5B%22getWorkspaceUploadUrl%22%2C%22removeMember%22%2C%22getWorkspace%22%2C%22leaveWorkspace%22%2C%22UploadFileToS3%22%2C%22deleteWorkspace%22%2C%22createWorkspace%22%2C%22getWorkspacePendingInvites%22%2C%22getWorkspaceMembers%22%2C%22updateWorkspaceUploadStatus%22%2C%22updateWorkspace%22%2C%22updateRole%22%2C%22deleteWorkspaces%22%2C%22getWorkspaces%22%5D%5D%2C%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cinvite.actions.ts%22%2C%5B%22acceptInvite%22%2C%22cancelInvite%22%2C%22getReceivedInvites%22%2C%22rejectInvite%22%2C%22inviteUser%22%5D%5D%5D&__client_imported__=true!\",\n        \"app/projects/[id]/page\": \"(action-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cinvite.actions.ts%22%2C%5B%22acceptInvite%22%2C%22cancelInvite%22%2C%22getReceivedInvites%22%2C%22rejectInvite%22%2C%22inviteUser%22%5D%5D%2C%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Ccomment.actions.ts%22%2C%5B%22deleteComment%22%2C%22updateCommentReply%22%2C%22deleteCommentReply%22%2C%22updateComment%22%2C%22createCommentReply%22%2C%22getWorkspaceComments%22%2C%22getCommentReplies%22%2C%22createComment%22%2C%22reactToComment%22%2C%22resolveComment%22%5D%5D%5D&__client_imported__=true!\"\n      },\n      \"layer\": {\n        \"app/page\": \"action-browser\",\n        \"app/projects/[id]/page\": \"action-browser\"\n      }\n    },\n    \"db956847609128c7881ae1b3db53bba31ce80267\": {\n      \"workers\": {\n        \"app/page\": \"(action-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cversion.actions.ts%22%2C%5B%22getVersion%22%2C%22deleteVersions%22%2C%22updateVersion%22%2C%22getCurrentVersion%22%2C%22getVersions%22%2C%22createVersion%22%2C%22deleteVersion%22%5D%5D%2C%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cworkspace.actions.ts%22%2C%5B%22getWorkspaceUploadUrl%22%2C%22removeMember%22%2C%22getWorkspace%22%2C%22leaveWorkspace%22%2C%22UploadFileToS3%22%2C%22deleteWorkspace%22%2C%22createWorkspace%22%2C%22getWorkspacePendingInvites%22%2C%22getWorkspaceMembers%22%2C%22updateWorkspaceUploadStatus%22%2C%22updateWorkspace%22%2C%22updateRole%22%2C%22deleteWorkspaces%22%2C%22getWorkspaces%22%5D%5D%2C%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cinvite.actions.ts%22%2C%5B%22acceptInvite%22%2C%22cancelInvite%22%2C%22getReceivedInvites%22%2C%22rejectInvite%22%2C%22inviteUser%22%5D%5D%5D&__client_imported__=true!\",\n        \"app/projects/[id]/page\": \"(action-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cinvite.actions.ts%22%2C%5B%22acceptInvite%22%2C%22cancelInvite%22%2C%22getReceivedInvites%22%2C%22rejectInvite%22%2C%22inviteUser%22%5D%5D%2C%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Ccomment.actions.ts%22%2C%5B%22deleteComment%22%2C%22updateCommentReply%22%2C%22deleteCommentReply%22%2C%22updateComment%22%2C%22createCommentReply%22%2C%22getWorkspaceComments%22%2C%22getCommentReplies%22%2C%22createComment%22%2C%22reactToComment%22%2C%22resolveComment%22%5D%5D%5D&__client_imported__=true!\"\n      },\n      \"layer\": {\n        \"app/page\": \"action-browser\",\n        \"app/projects/[id]/page\": \"action-browser\"\n      }\n    },\n    \"1efbf9471a6741b62a3b3c3131cbe5c2bd459a09\": {\n      \"workers\": {\n        \"app/projects/[id]/page\": \"(action-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cinvite.actions.ts%22%2C%5B%22acceptInvite%22%2C%22cancelInvite%22%2C%22getReceivedInvites%22%2C%22rejectInvite%22%2C%22inviteUser%22%5D%5D%2C%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Ccomment.actions.ts%22%2C%5B%22deleteComment%22%2C%22updateCommentReply%22%2C%22deleteCommentReply%22%2C%22updateComment%22%2C%22createCommentReply%22%2C%22getWorkspaceComments%22%2C%22getCommentReplies%22%2C%22createComment%22%2C%22reactToComment%22%2C%22resolveComment%22%5D%5D%5D&__client_imported__=true!\"\n      },\n      \"layer\": {\n        \"app/projects/[id]/page\": \"action-browser\"\n      }\n    },\n    \"261a845629a28e2d5e3c4f502c0f5743fafc2f1b\": {\n      \"workers\": {\n        \"app/projects/[id]/page\": \"(action-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cinvite.actions.ts%22%2C%5B%22acceptInvite%22%2C%22cancelInvite%22%2C%22getReceivedInvites%22%2C%22rejectInvite%22%2C%22inviteUser%22%5D%5D%2C%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Ccomment.actions.ts%22%2C%5B%22deleteComment%22%2C%22updateCommentReply%22%2C%22deleteCommentReply%22%2C%22updateComment%22%2C%22createCommentReply%22%2C%22getWorkspaceComments%22%2C%22getCommentReplies%22%2C%22createComment%22%2C%22reactToComment%22%2C%22resolveComment%22%5D%5D%5D&__client_imported__=true!\"\n      },\n      \"layer\": {\n        \"app/projects/[id]/page\": \"action-browser\"\n      }\n    },\n    \"4d33945c4142696a92218919b9d8ea2dc518c95b\": {\n      \"workers\": {\n        \"app/projects/[id]/page\": \"(action-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cinvite.actions.ts%22%2C%5B%22acceptInvite%22%2C%22cancelInvite%22%2C%22getReceivedInvites%22%2C%22rejectInvite%22%2C%22inviteUser%22%5D%5D%2C%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Ccomment.actions.ts%22%2C%5B%22deleteComment%22%2C%22updateCommentReply%22%2C%22deleteCommentReply%22%2C%22updateComment%22%2C%22createCommentReply%22%2C%22getWorkspaceComments%22%2C%22getCommentReplies%22%2C%22createComment%22%2C%22reactToComment%22%2C%22resolveComment%22%5D%5D%5D&__client_imported__=true!\"\n      },\n      \"layer\": {\n        \"app/projects/[id]/page\": \"action-browser\"\n      }\n    },\n    \"59bf5f393051cf3360abd1705d8c74557d5e6e68\": {\n      \"workers\": {\n        \"app/projects/[id]/page\": \"(action-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cinvite.actions.ts%22%2C%5B%22acceptInvite%22%2C%22cancelInvite%22%2C%22getReceivedInvites%22%2C%22rejectInvite%22%2C%22inviteUser%22%5D%5D%2C%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Ccomment.actions.ts%22%2C%5B%22deleteComment%22%2C%22updateCommentReply%22%2C%22deleteCommentReply%22%2C%22updateComment%22%2C%22createCommentReply%22%2C%22getWorkspaceComments%22%2C%22getCommentReplies%22%2C%22createComment%22%2C%22reactToComment%22%2C%22resolveComment%22%5D%5D%5D&__client_imported__=true!\"\n      },\n      \"layer\": {\n        \"app/projects/[id]/page\": \"action-browser\"\n      }\n    },\n    \"633519cf59e6d1a28b43cdb3391a0bdb74b6a7da\": {\n      \"workers\": {\n        \"app/projects/[id]/page\": \"(action-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cinvite.actions.ts%22%2C%5B%22acceptInvite%22%2C%22cancelInvite%22%2C%22getReceivedInvites%22%2C%22rejectInvite%22%2C%22inviteUser%22%5D%5D%2C%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Ccomment.actions.ts%22%2C%5B%22deleteComment%22%2C%22updateCommentReply%22%2C%22deleteCommentReply%22%2C%22updateComment%22%2C%22createCommentReply%22%2C%22getWorkspaceComments%22%2C%22getCommentReplies%22%2C%22createComment%22%2C%22reactToComment%22%2C%22resolveComment%22%5D%5D%5D&__client_imported__=true!\"\n      },\n      \"layer\": {\n        \"app/projects/[id]/page\": \"action-browser\"\n      }\n    },\n    \"739d45e4b556279ceb7fd4ab4e4bbb55456c5838\": {\n      \"workers\": {\n        \"app/projects/[id]/page\": \"(action-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cinvite.actions.ts%22%2C%5B%22acceptInvite%22%2C%22cancelInvite%22%2C%22getReceivedInvites%22%2C%22rejectInvite%22%2C%22inviteUser%22%5D%5D%2C%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Ccomment.actions.ts%22%2C%5B%22deleteComment%22%2C%22updateCommentReply%22%2C%22deleteCommentReply%22%2C%22updateComment%22%2C%22createCommentReply%22%2C%22getWorkspaceComments%22%2C%22getCommentReplies%22%2C%22createComment%22%2C%22reactToComment%22%2C%22resolveComment%22%5D%5D%5D&__client_imported__=true!\"\n      },\n      \"layer\": {\n        \"app/projects/[id]/page\": \"action-browser\"\n      }\n    },\n    \"a11af139797873682ecd7faa04d1fd6f9b827370\": {\n      \"workers\": {\n        \"app/projects/[id]/page\": \"(action-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cinvite.actions.ts%22%2C%5B%22acceptInvite%22%2C%22cancelInvite%22%2C%22getReceivedInvites%22%2C%22rejectInvite%22%2C%22inviteUser%22%5D%5D%2C%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Ccomment.actions.ts%22%2C%5B%22deleteComment%22%2C%22updateCommentReply%22%2C%22deleteCommentReply%22%2C%22updateComment%22%2C%22createCommentReply%22%2C%22getWorkspaceComments%22%2C%22getCommentReplies%22%2C%22createComment%22%2C%22reactToComment%22%2C%22resolveComment%22%5D%5D%5D&__client_imported__=true!\"\n      },\n      \"layer\": {\n        \"app/projects/[id]/page\": \"action-browser\"\n      }\n    },\n    \"a8d418c3d5d7b6ef88bb466aa41a8373f811a251\": {\n      \"workers\": {\n        \"app/projects/[id]/page\": \"(action-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cinvite.actions.ts%22%2C%5B%22acceptInvite%22%2C%22cancelInvite%22%2C%22getReceivedInvites%22%2C%22rejectInvite%22%2C%22inviteUser%22%5D%5D%2C%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Ccomment.actions.ts%22%2C%5B%22deleteComment%22%2C%22updateCommentReply%22%2C%22deleteCommentReply%22%2C%22updateComment%22%2C%22createCommentReply%22%2C%22getWorkspaceComments%22%2C%22getCommentReplies%22%2C%22createComment%22%2C%22reactToComment%22%2C%22resolveComment%22%5D%5D%5D&__client_imported__=true!\"\n      },\n      \"layer\": {\n        \"app/projects/[id]/page\": \"action-browser\"\n      }\n    },\n    \"bf1954c0b6a3a4cf11315183f07b0c3d8913897a\": {\n      \"workers\": {\n        \"app/projects/[id]/page\": \"(action-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cinvite.actions.ts%22%2C%5B%22acceptInvite%22%2C%22cancelInvite%22%2C%22getReceivedInvites%22%2C%22rejectInvite%22%2C%22inviteUser%22%5D%5D%2C%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Ccomment.actions.ts%22%2C%5B%22deleteComment%22%2C%22updateCommentReply%22%2C%22deleteCommentReply%22%2C%22updateComment%22%2C%22createCommentReply%22%2C%22getWorkspaceComments%22%2C%22getCommentReplies%22%2C%22createComment%22%2C%22reactToComment%22%2C%22resolveComment%22%5D%5D%5D&__client_imported__=true!\"\n      },\n      \"layer\": {\n        \"app/projects/[id]/page\": \"action-browser\"\n      }\n    },\n    \"c61905810501c15c12005fdbd8db6ffc60b38353\": {\n      \"workers\": {\n        \"app/projects/[id]/page\": \"(action-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cinvite.actions.ts%22%2C%5B%22acceptInvite%22%2C%22cancelInvite%22%2C%22getReceivedInvites%22%2C%22rejectInvite%22%2C%22inviteUser%22%5D%5D%2C%5B%22G%3A%5C%5Cagape%5C%5Cagape-newFrontend%5C%5Cweb%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Ccomment.actions.ts%22%2C%5B%22deleteComment%22%2C%22updateCommentReply%22%2C%22deleteCommentReply%22%2C%22updateComment%22%2C%22createCommentReply%22%2C%22getWorkspaceComments%22%2C%22getCommentReplies%22%2C%22createComment%22%2C%22reactToComment%22%2C%22resolveComment%22%5D%5D%5D&__client_imported__=true!\"\n      },\n      \"layer\": {\n        \"app/projects/[id]/page\": \"action-browser\"\n      }\n    }\n  },\n  \"edge\": {},\n  \"encryptionKey\": \"tBMrZbb8e0m6XYA5WRZwwVzS+HxqglGM3fV5nLr/y8Q=\"\n}"