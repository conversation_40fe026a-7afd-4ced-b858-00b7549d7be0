"use client";

import { useState, useEffect } from "react";
import * as THREE from "three";
import { ChromePicker } from "react-color";
import { DiamondShader } from "../../utils/ArtistTool/refraction_materials/Diamond_Shader.js";
import { NormalCube } from "../../utils/ArtistTool/refraction_materials/NormalCube.js";
import { useLoader } from "@react-three/fiber";
import { EXRLoader } from "three/examples/jsm/loaders/EXRLoader.js";
import { useHistoryDebounce } from "@/hooks/useDebounce";
// import { RGBELoader } from "three/examples/jsm/loaders/RGBELoader.js";

// Global cache for diamond data to persist across component re-renders
const globalDiamondDataCache = new Map();

// Define material presets
const MATERIAL_PRESETS = {
  gems: [
    {
      name: "Diamond",
      color: "#ffffff",
      roughness: 0.05,
      metalness: 0.1,
      clearcoat: 1.0,
      clearcoatRoughness: 0.02,
      transparent: false,
      opacity: 1.0,
      envMapIntensity: 1.5,
    },
    {
      name: "Emerald",
      color: "#22dfa3",
      roughness: 0.1,
      metalness: 0.0,
      clearcoat: 0.8,
      clearcoatRoughness: 0.05,
      transparent: true,
      opacity: 0.85,
      envMapIntensity: 1.2,
    },
    {
      name: "Ruby",
      color: "#e14276",
      roughness: 0.08,
      metalness: 0.0,
      clearcoat: 0.9,
      clearcoatRoughness: 0.03,
      transparent: true,
      opacity: 0.9,
      envMapIntensity: 1.3,
    },
  ],
  metals: [
    {
      name: "Platinum",
      color: "#e5e4e2",
      roughness: 0.15,
      metalness: 1.0,
      clearcoat: 0.3,
      clearcoatRoughness: 0.1,
      transparent: false,
      opacity: 1.0,
      envMapIntensity: 1.0,
    },
    {
      name: "Yellow Gold",
      color: "#E5b377",
      roughness: 0.12,
      metalness: 1.0,
      clearcoat: 0.2,
      clearcoatRoughness: 0.08,
      transparent: false,
      opacity: 1.0,
      envMapIntensity: 0.9,
    },
    {
      name: "Rose Gold",
      color: "#e8b4a0",
      roughness: 0.14,
      metalness: 1.0,
      clearcoat: 0.25,
      clearcoatRoughness: 0.09,
      transparent: false,
      opacity: 1.0,
      envMapIntensity: 0.95,
    },
  ],
};

export function MaterialPanel({
  selectedObjects,
  useDiamondShader,
  setUseDiamondShader,
  usePremiumWhiteGold,
  setUsePremiumWhiteGold,
  usePremiumRoseGold,
  setUsePremiumRoseGold,
  renderer,
  forceUpdate,
  setMaterialVersion,
  addToHistory,
}) {
  const [materialType, setMaterialType] = useState("standard");
  const [materialProps, setMaterialProps] = useState({
    color: "#ffffff",
    roughness: 0.5,
    metalness: 0.5,
    clearcoat: 0.5,
    clearcoatRoughness: 0.5,
    wireframe: false,
    transparent: false,
    opacity: 1.0,
    // Diamond shader specific properties
    ior: 2.4,
    bounces: 3,
    aberrationStrength: 0.01,
    fresnel: 1.0,
  });
  const [displayColorPicker, setDisplayColorPicker] = useState(false);
  const [isLoadingDiamondShader, setIsLoadingDiamondShader] = useState(false);
  const [diamondDataCache, setDiamondDataCache] = useState(new Map());

  // Cleanup function for diamond data cache
  useEffect(() => {
    return () => {
      // Only clean up if we're actually unmounting the entire component
      // Don't clean up during normal operation
    };
  }, []);

  // Helper function to check if an object is a decal
  const isDecalObject = (obj) => {
    if (!obj) return false;

    // Check if the object is a decal based on its name or userData
    const isDecalByName =
      obj.name &&
      (obj.name.toLowerCase().includes("decal") ||
        obj.name.toLowerCase().includes("engraving") ||
        obj.name.toLowerCase().includes("text"));

    const isDecalByUserData =
      obj.userData &&
      (obj.userData.isDecal || obj.userData.isEngraving || obj.userData.isText);

    // Check if the object has a parent that's a decal
    const hasDecalParent =
      obj.parent &&
      ((obj.parent.name && obj.parent.name.toLowerCase().includes("decal")) ||
        (obj.parent.userData && obj.parent.userData.isDecal));

    return isDecalByName || isDecalByUserData || hasDecalParent;
  };

  // Helper function to filter out decal objects
  const filterOutDecals = (objects) => {
    return objects.filter((obj) => !isDecalObject(obj));
  };

  // Helper function to check if diamond parts are selected
  const hasDiamondPartsSelected = () => {
    const nonDecalObjects = filterOutDecals(selectedObjects);
    return nonDecalObjects.some(
      (obj) => obj && obj.userData && obj.userData.part === "diamond"
    );
  };

  // Helper function to count diamond parts
  const getDiamondPartsCount = () => {
    const nonDecalObjects = filterOutDecals(selectedObjects);
    return nonDecalObjects.filter(
      (obj) => obj && obj.userData && obj.userData.part === "diamond"
    ).length;
  };

  // Setup debounced history for slider inputs
  const { addToHistoryImmediate, addToHistoryDebounced } = useHistoryDebounce(
    addToHistory || (() => {}),
    900
  );

  // Update local state when selected objects change
  useEffect(() => {
    console.log("MaterialPanel - selectedObjects changed:", selectedObjects);
    console.log(
      "MaterialPanel - selectedObjects length:",
      selectedObjects?.length
    );
    console.log(
      "MaterialPanel - selectedObjects details:",
      selectedObjects?.map((obj) => ({
        name: obj?.name,
        userData: obj?.userData,
        type: obj?.type,
        isMesh: obj?.isMesh,
      }))
    );

    // Ensure selectedObjects is always an array and filter out decals
    const objectsArray = Array.isArray(selectedObjects)
      ? selectedObjects
      : [selectedObjects].filter(Boolean);

    const nonDecalObjects = filterOutDecals(objectsArray);

    if (nonDecalObjects.length === 0) return;

    // Use the first selected non-decal object to determine material type and properties
    const obj = nonDecalObjects[0];
    if (!obj.material) return;

    // Determine material type
    let type = "standard";
    if (obj.material.type === "MeshBasicMaterial") type = "basic";
    else if (obj.material.type === "MeshStandardMaterial") type = "standard";
    else if (obj.material.type === "MeshPhysicalMaterial") type = "physical";
    else if (obj.material.type === "MeshToonMaterial") type = "toon";
    else if (obj.material.type === "MeshNormalMaterial") type = "normal";
    else if (
      obj.material.type === "ShaderMaterial" &&
      obj.material.userData?.isDiamondShader
    ) {
      type = "diamond";
      // Ensure diamond shader state is set
      setUseDiamondShader(true);
    }

    setMaterialType(type);

    // Update material properties
    const props = {
      color: obj.material.color
        ? "#" + obj.material.color.getHexString()
        : "#ffffff",
      roughness:
        obj.material.roughness !== undefined ? obj.material.roughness : 0.5,
      metalness:
        obj.material.metalness !== undefined ? obj.material.metalness : 0.5,
      clearcoat:
        obj.material.clearcoat !== undefined ? obj.material.clearcoat : 0.5,
      clearcoatRoughness:
        obj.material.clearcoatRoughness !== undefined
          ? obj.material.clearcoatRoughness
          : 0.5,
      wireframe: obj.material.wireframe || false,
      transparent: obj.material.transparent || false,
      opacity: obj.material.opacity !== undefined ? obj.material.opacity : 1.0,
      // Diamond shader properties
      ior: obj.material.uniforms?.ior?.value || 2.4,
      bounces: obj.material.uniforms?.bounces?.value || 3,
      aberrationStrength:
        obj.material.uniforms?.aberrationStrength?.value || 0.01,
      fresnel: obj.material.uniforms?.fresnel?.value || 1.0,
    };

    setMaterialProps(props);

    // Check if this is a diamond shader material that needs restoration
    if (
      obj.material.type === "ShaderMaterial" &&
      obj.material.userData?.isDiamondShader
    ) {
      // Check if the normal cube texture is missing or invalid
      const normalEnvMap = obj.material.uniforms.normalEnvMap.value;
      if (
        !normalEnvMap ||
        !normalEnvMap.isTexture ||
        normalEnvMap.type === "CubeTexture"
      ) {
        console.log("Restoring diamond shader material for", obj.name);

        // Recreate the diamond data and update the material
        if (obj.geometry && renderer) {
          const diamondData = createDiamondData(obj.geometry, renderer);
          obj.material.uniforms.normalEnvMap.value =
            diamondData.normalCube.texture;
          obj.material.uniforms.diamondOriginCenter.value = diamondData.center;
          obj.material.uniforms.diamondOriginRadius.value = diamondData.radius;
          obj.material.needsUpdate = true;
        }
      }
    }

    // Check if any selected object has diamond shader to maintain state
    const hasDiamondShader = nonDecalObjects.some(
      (obj) =>
        obj.material.type === "ShaderMaterial" &&
        obj.material.userData?.isDiamondShader
    );

    if (hasDiamondShader && !useDiamondShader) {
      setUseDiamondShader(true);
    }
  }, [selectedObjects, renderer]);

  if (filterOutDecals(selectedObjects).length === 0) {
    return (
      <div className="p-2 text-center text-gray-600">
        Select an object to edit its material
      </div>
    );
  }

  const applyAdvancedPreset = (preset) => {
    console.log("Applying advanced preset:", preset);
    console.log("Selected objects:", selectedObjects);

    if (preset.type === "diamond") {
      // Apply diamond shader by setting the state
      setUseDiamondShader(true);
      setMaterialType("diamond");

      // Also clear other advanced material flags
      setUsePremiumWhiteGold(false);
      setUsePremiumRoseGold(false);
    } else if (preset.type === "premium_metal") {
      // Don't clear diamond shader when applying premium metals - preserve existing state

      if (preset.name === "Premium White Gold") {
        setUsePremiumWhiteGold(true);
        setUsePremiumRoseGold(false);
      } else if (preset.name === "Premium Rose Gold") {
        setUsePremiumRoseGold(true);
        setUsePremiumWhiteGold(false);
      }

      // Update local state
      setMaterialProps(preset);

      // Apply premium material properties to selected objects (excluding decals)
      const nonDecalObjects = filterOutDecals(selectedObjects);
      nonDecalObjects.forEach((obj) => {
        if (!obj || !obj.material) return;

        const material = obj.material;

        // Update color if the material has a color property
        if (material.color) {
          material.color.set(preset.color);
        }

        // Update properties that exist on the material
        if (material.roughness !== undefined) {
          material.roughness = preset.roughness;
        }
        if (material.metalness !== undefined) {
          material.metalness = preset.metalness;
        }
        if (material.envMapIntensity !== undefined) {
          material.envMapIntensity = preset.envMapIntensity || 1.0;
        }

        // Mark material for update
        material.needsUpdate = true;
      });
    }
  };

  const applyPreset = (preset) => {
    if (preset.isAdvanced) {
      applyAdvancedPreset(preset);
      return;
    }

    // Only clear diamond shader when applying gem presets, not metal presets
    const isGemPreset = MATERIAL_PRESETS.gems.some(
      (gem) => gem.name === preset.name
    );
    const isMetalPreset = MATERIAL_PRESETS.metals.some(
      (metal) => metal.name === preset.name
    );

    // Check if we have any diamond parts selected
    const selectedDiamondParts = selectedObjects.filter(
      (obj) => obj && obj.userData && obj.userData.part === "diamond"
    );
    const selectedNonDiamondParts = selectedObjects.filter(
      (obj) => !obj || !obj.userData || obj.userData.part !== "diamond"
    );

    // Only clear diamond shader if we're applying gem presets to non-diamond parts
    // Keep diamond shader active for diamond parts
    if (
      isGemPreset &&
      selectedNonDiamondParts.length > 0 &&
      selectedDiamondParts.length === 0
    ) {
      // Only clear diamond shader if no diamond parts are selected
      setUseDiamondShader(false);
    }

    // Clear premium material flags when applying regular presets (both gems and metals)
    setUsePremiumWhiteGold(false);
    setUsePremiumRoseGold(false);

    // Update local state
    setMaterialProps(preset);

    // Update selected objects (excluding decals)
    const nonDecalObjects = filterOutDecals(selectedObjects);
    nonDecalObjects.forEach((obj) => {
      if (!obj || !obj.material) return;

      const material = obj.material;

      // Special handling for diamond shader materials
      if (materialType === "diamond" && material.uniforms) {
        console.log(`Updating diamond shader with ${preset.name} preset`);

        // Update diamond shader uniforms
        if (material.uniforms.color) {
          material.uniforms.color.value.set(preset.color);
        }
        if (material.uniforms.opacity && preset.opacity !== undefined) {
          material.uniforms.opacity.value = preset.opacity;
        }
        if (preset.transparent !== undefined) {
          material.transparent = preset.transparent;
        }

        material.needsUpdate = true;
        return;
      }

      // Legacy handling for diamond parts (keep for compatibility)
      if (isGemPreset && obj.userData && obj.userData.part === "diamond") {
        console.log(
          `Updating diamond shader color for ${preset.name} on diamond part`
        );

        if (material.uniforms && material.uniforms.color) {
          material.uniforms.color.value.set(preset.color);
          material.needsUpdate = true;
        } else if (material.color) {
          material.color.set(preset.color);
          material.needsUpdate = true;
        }
        return;
      }

      // Handle diamond shader materials specifically
      if (materialType === "diamond" && material.uniforms) {
        console.log(`Updating diamond shader with ${preset.name} preset`);

        // Update diamond shader uniforms with preset values
        if (material.uniforms.color) {
          material.uniforms.color.value.set(preset.color);
        }
        if (material.uniforms.opacity && preset.opacity !== undefined) {
          material.uniforms.opacity.value = preset.opacity;
        }
        if (preset.transparent !== undefined) {
          material.transparent = preset.transparent;
        }
        // Update IOR if it's a gem preset
        if (isGemPreset && material.uniforms.ior) {
          // Different gems have different IOR values
          const iorValues = {
            Diamond: 2.4,
            Emerald: 1.57,
            Ruby: 1.76,
          };
          material.uniforms.ior.value = iorValues[preset.name] || 2.4;
        }

        material.needsUpdate = true;
        return;
      }

      // Standard material property updates
      if (material.color) {
        material.color.set(preset.color);
      }

      // Update properties that exist on the material
      if (material.roughness !== undefined) {
        material.roughness = preset.roughness;
      }
      if (material.metalness !== undefined) {
        material.metalness = preset.metalness;
      }
      if (material.clearcoat !== undefined) {
        material.clearcoat = preset.clearcoat;
      }
      if (material.clearcoatRoughness !== undefined) {
        material.clearcoatRoughness = preset.clearcoatRoughness;
      }
      if (material.transparent !== undefined) {
        material.transparent = preset.transparent;
      }
      if (material.opacity !== undefined) {
        material.opacity = preset.opacity;
      }
      if (material.envMapIntensity !== undefined) {
        material.envMapIntensity = preset.envMapIntensity || 1.0;
      }

      // Mark material for update
      material.needsUpdate = true;
    });
    if (forceUpdate) forceUpdate();
    if (setMaterialVersion) setMaterialVersion((v) => v + 1);

    // Add to history for preset application
    if (addToHistory) {
      addToHistoryImmediate();
    }
  };

  const updateMaterialProperty = (property, value, isSlider = false) => {
    // Update local state
    setMaterialProps((prev) => ({
      ...prev,
      [property]: value,
    }));

    // Update all selected objects (excluding decals)
    const nonDecalObjects = filterOutDecals(selectedObjects);
    nonDecalObjects.forEach((obj) => {
      if (obj && obj.material) {
        // Special handling for diamond shader materials
        if (materialType === "diamond" && obj.material.uniforms) {
          // Handle diamond shader uniform updates
          if (property === "color" && obj.material.uniforms.color) {
            obj.material.uniforms.color.value.set(value);
          } else if (property === "opacity" && obj.material.uniforms.opacity) {
            obj.material.uniforms.opacity.value = value;
          } else if (property === "transparent") {
            obj.material.transparent = value;
          } else if (property === "wireframe") {
            obj.material.wireframe = value;
          } else if (property === "ior" && obj.material.uniforms.ior) {
            obj.material.uniforms.ior.value = value;
          } else if (property === "bounces" && obj.material.uniforms.bounces) {
            obj.material.uniforms.bounces.value = value;
          } else if (
            property === "aberrationStrength" &&
            obj.material.uniforms.aberrationStrength
          ) {
            obj.material.uniforms.aberrationStrength.value = value;
          } else if (property === "fresnel" && obj.material.uniforms.fresnel) {
            obj.material.uniforms.fresnel.value = value;
          }
        } else {
          // Standard material property update
          if (property === "color") {
            if (obj.material.color) {
              obj.material.color.set(value);
            }
          } else {
            if (obj.material[property] !== undefined) {
              obj.material[property] = value;
            }
          }
        }
        obj.material.needsUpdate = true;
      }
    });
    if (forceUpdate) forceUpdate();
    if (setMaterialVersion) setMaterialVersion((v) => v + 1);

    // Add to history with appropriate debouncing
    if (addToHistory) {
      if (isSlider) {
        addToHistoryDebounced();
      } else {
        addToHistoryImmediate();
      }
    }
  };

  // Function to create diamond data with proper normal cube texture
  const createDiamondData = (geometry, renderer) => {
    // Create a cache key based on geometry UUID or a hash of its properties
    const cacheKey =
      geometry.uuid ||
      `${geometry.attributes.position.count}_${geometry.index?.count || 0}`;

    // Check if we already have diamond data for this geometry (check both local and global cache)
    if (diamondDataCache.has(cacheKey)) {
      console.log("Using local cached diamond data for geometry:", cacheKey);
      const cachedData = diamondDataCache.get(cacheKey);
      // Ensure the normal cube texture is still valid
      if (cachedData.normalCube && cachedData.normalCube.texture) {
        return cachedData;
      }
    }

    if (globalDiamondDataCache.has(cacheKey)) {
      console.log("Using global cached diamond data for geometry:", cacheKey);
      const cachedData = globalDiamondDataCache.get(cacheKey);
      // Ensure the normal cube texture is still valid
      if (cachedData.normalCube && cachedData.normalCube.texture) {
        // Also update local cache
        setDiamondDataCache((prev) => new Map(prev).set(cacheKey, cachedData));
        return cachedData;
      }
    }

    // Clone and prepare geometry
    const geo = geometry.clone();
    geo.computeBoundingBox();
    geo.computeBoundingSphere();

    // Get center and radius
    const center = new THREE.Vector3();
    geo.boundingBox.getCenter(center);
    const radius = geo.boundingSphere.radius;

    // Create normal cube for this geometry
    const normalCube = new NormalCube({ renderer, geo });

    const diamondData = { geometry: geo, center, radius, normalCube };

    // Cache the diamond data in both local and global cache
    setDiamondDataCache((prev) => new Map(prev).set(cacheKey, diamondData));
    globalDiamondDataCache.set(cacheKey, diamondData);

    return diamondData;
  };

  // Function to create and apply diamond shader material
  const createDiamondShaderMaterial = async (
    currentColor = "#ffffff",
    diamondData = null
  ) => {
    try {
      console.log("Loading diamond shader textures...");

      // Load HDRIs
      const envMapLoader = new EXRLoader();
      const reflectionMapLoader = new EXRLoader();

      let envMap, reflectionMap;

      try {
        envMap = await new Promise((resolve, reject) => {
          envMapLoader.load(
            "/hdris/env_gem_001_b9c4533e70.exr",
            resolve,
            undefined,
            reject
          );
        });
        console.log("Successfully loaded env_gem_001.exr");
      } catch (error) {
        console.warn("Failed to load env_gem_001.exr, using fallback:", error);
        // Create a simple fallback texture
        envMap = new THREE.CubeTexture();
        envMap.mapping = THREE.EquirectangularReflectionMapping;
      }

      try {
        reflectionMap = await new Promise((resolve, reject) => {
          reflectionMapLoader.load(
            "/hdris/env_gem_002_30251392af.exr",
            resolve,
            undefined,
            reject
          );
        });
        console.log("Successfully loaded env_gem_002_30251392af.exr");
      } catch (error) {
        console.warn(
          "Failed to load env_gem_002_30251392af.exr, using envMap as fallback:",
          error
        );
        reflectionMap = envMap; // Use envMap as fallback
      }

      // Configure textures
      if (envMap.mapping !== undefined) {
        envMap.mapping = THREE.EquirectangularReflectionMapping;
      }
      if (reflectionMap.mapping !== undefined) {
        reflectionMap.mapping = THREE.EquirectangularReflectionMapping;
      }

      // Create diamond shader material using the advanced DiamondRefraction shader
      const diamondMaterial = new THREE.ShaderMaterial({
        transparent: true,
        side: THREE.DoubleSide,
        uniforms: {
          envMap: { value: envMap },
          reflectionMap: { value: reflectionMap },
          normalEnvMap: { value: diamondData?.normalCube?.texture || envMap },
          ior: { value: 2.4 },
          bounces: { value: 3 },
          correctMips: { value: true },
          aberrationStrength: { value: 0.01 },
          colorEnvMapRotY: { value: Math.PI * 0.25 },
          fresnel: { value: 1.0 },
          fresnelstrength: { value: 0 },
          diamondOriginRadius: { value: diamondData?.radius || 1.0 },
          diamondOriginCenter: {
            value: diamondData?.center || new THREE.Vector3(0, 0, 0),
          },
          color: { value: new THREE.Color(currentColor) },
          fresnelcolor: { value: new THREE.Color("white") },
          opacity: { value: 1.0 },
          resolution: {
            value: new THREE.Vector2(window.innerWidth, window.innerHeight),
          },
          viewMatrixInverse: { value: new THREE.Matrix4() },
          projectionMatrixInverse: { value: new THREE.Matrix4() },
          u_useReflection: { value: true },
        },
        // Store the diamond data reference in the material for persistence
        userData: {
          isDiamondShader: true,
          diamondData: diamondData,
        },
        // Use the advanced vertex shader from DiamondRefraction
        vertexShader: `
          uniform mat4 viewMatrixInverse;
          varying vec3 vWorldPosition;
          varying vec3 vNormal;
          varying mat4 vModelMatrixInverse;
          varying vec2 v_uv;
          
          void main() {
            v_uv = uv;
            vec4 transformedNormal = vec4(normal, 0.0);
            vec4 transformedPosition = vec4(position, 1.0);
            
            vModelMatrixInverse = inverse(modelMatrix);
            vWorldPosition = (modelMatrix * transformedPosition).xyz;
            vNormal = normalize((viewMatrixInverse * vec4(normalMatrix * transformedNormal.xyz, 0.0)).xyz);
            gl_Position = projectionMatrix * viewMatrix * modelMatrix * transformedPosition;
          }
        `,
        // Use the advanced fragment shader from DiamondRefraction
        fragmentShader: `
          #define ENVMAP_TYPE_CUBE_UV
          precision highp isampler2D;
          precision highp usampler2D;
          varying vec3 vWorldPosition;
          varying vec3 vNormal;
          varying vec2 v_uv;
          varying mat4 vModelMatrixInverse;
          uniform samplerCube normalEnvMap;

          #ifdef ENVMAP_TYPE_CUBEM
            uniform samplerCube envMap;
            uniform samplerCube reflectionMap;
          #else
            uniform sampler2D envMap;
            uniform sampler2D reflectionMap;
          #endif

          uniform float bounces;
          uniform float ior;
          uniform bool correctMips;
          uniform bool u_useReflection;
          uniform vec2 resolution;
          uniform float fresnel;
          uniform float fresnelstrength;
          uniform mat4 modelMatrix;
          uniform mat4 projectionMatrixInverse;
          uniform mat4 viewMatrixInverse;
          uniform float aberrationStrength;
          uniform float diamondOriginRadius;
          uniform vec3 diamondOriginCenter;
          uniform vec3 color;
          uniform vec3 fresnelcolor;
          uniform float opacity;
          uniform float colorEnvMapRotY;

          mat3 rotateZ(float theta) {
            float c = cos(theta);
            float s = sin(theta);
            return mat3(
                vec3(c, -s, 0),
                vec3(s, c, 0),
                vec3(0, 0, 1)
            );
          }

          float fresnelFunc(vec3 viewDirection, vec3 worldNormal) {
            return pow( 1.0 + dot( viewDirection, worldNormal), 10.0 );
          }

          vec3 getCameraDirectionHighDetail () {
            vec3 directionCamPerfect;
            directionCamPerfect = (projectionMatrixInverse * vec4(v_uv * 2.0 - 1.0, 0.0, 1.0)).xyz;
            directionCamPerfect = (viewMatrixInverse * vec4(directionCamPerfect, 0.0)).xyz;
            directionCamPerfect = normalize(directionCamPerfect);
            return directionCamPerfect;
          }

          vec4 getPackedData(vec3 n){
            vec3 directionCamPerfect = getCameraDirectionHighDetail();
            vec4 data = textureGrad(normalEnvMap, n, dFdx(directionCamPerfect), dFdy(directionCamPerfect));
            data.rgb = data.rgb * 2.0 - 1.0;
            data.rgb = normalize(-data.rgb);
            data.rgb *= -1.0;
            data.a = data.a * diamondOriginRadius;
            return data;
          }

          vec3 hitSphere(vec3 origin, vec3 direction, vec3 center, float radius) {
            vec3 oc = origin - center;
            float a = dot(direction, direction);
            float b = 2.0 * dot(oc, direction);
            float c = dot(oc, oc) - (radius * radius);
            float discriminant = (b * b) - ( a * c);

            if (discriminant > 0.0) {
                float sqrtDiscriminant = sqrt(discriminant);
                float t = (-b + sqrtDiscriminant) / a;
                return origin + direction * t;
            }
            return vec3(0.0);
          }

          vec3 hitPlane(vec3 linePoint, vec3 lineDir, vec3 planePoint, vec3 planeDir) {
            vec3 normalizedLineDir = normalize(lineDir);
            vec3 normalizedPlaneDir = normalize(planeDir);
            float dotProduct = dot(normalizedPlaneDir, planePoint - linePoint);
            float dotProductLine = dot(normalizedPlaneDir, normalizedLineDir);
            float howFar = dotProduct / dotProductLine;
            vec3 position = normalizedLineDir * howFar;
            vec3 intersectionPoint = linePoint + position;
            return intersectionPoint;
          }

          vec3 hitRay(vec3 rayOrigin, vec3 rayDirection) {
            vec3 sphereHitPoint = hitSphere(rayOrigin, rayDirection, diamondOriginCenter, diamondOriginRadius);
            vec3 sphereCenterOffset = diamondOriginCenter;
            vec3 dir1 = normalize(sphereHitPoint - sphereCenterOffset);
            vec4 faceNormal1 = getPackedData(dir1);
            float dist1 = faceNormal1.a;
            vec3 point1 = sphereCenterOffset + dir1 * dist1;
            vec3 normal1 = faceNormal1.rgb;
            vec3 hit1 = hitPlane(rayOrigin, rayDirection, point1, normal1);
            return hit1;
          }

          vec3 totalInternalReflection(vec3 ro, vec3 rd, vec3 normal, float ior, mat4 modelMatrixInverse) {
            vec3 rayOrigin = ro;
            vec3 rayDirection = rd;
            rayDirection = refract(rayDirection, normal, 1.0 / ior);
            rayOrigin = vWorldPosition + rayDirection * 0.1;
            rayOrigin = (modelMatrixInverse * vec4(rayOrigin, 1.0)).xyz;
            rayDirection = normalize((modelMatrixInverse * vec4(rayDirection, 0.0)).xyz);
            for(float i = 0.0; i < bounces; i++) {
                vec3 hitPos = hitRay(rayOrigin, rayDirection);
                vec3 pureDirectionToRaycast = normalize(hitPos - diamondOriginCenter);
                vec4 faceNormal = getPackedData(pureDirectionToRaycast);
                vec3 diamondWallCastPoint = diamondOriginCenter + pureDirectionToRaycast * faceNormal.a;
                rayOrigin = diamondWallCastPoint;
                vec3 tempDir = refract(rayDirection, faceNormal.rgb, ior);
                if (length(tempDir) != 0.0) {
                    rayDirection = tempDir;
                    continue;
                }
                rayDirection = reflect(rayDirection, faceNormal.rgb);
            }
            rayDirection = normalize((modelMatrix * vec4(rayDirection, 0.0)).xyz);
            return rayDirection;
          }

          vec2 equirectUv(vec3 dir) {
            float u = atan(dir.z, dir.x) * 0.15915494309189535 + 0.5;
            float v = asin(clamp(dir.y, -1.0, 1.0)) * 0.3183098861837907 + 0.5;
            return vec2(u, v);
          }

          #ifdef ENVMAP_TYPE_CUBEM
            vec4 textureGradient(samplerCube envMap, vec3 rayDirection, vec3 directionCamPerfect) {
              return textureGrad(envMap, rayDirection, dFdx(correctMips ? directionCamPerfect: rayDirection), dFdy(correctMips ? directionCamPerfect: rayDirection));
            }
          #else
            vec4 textureGradient(sampler2D envMap, vec3 rayDirection, vec3 directionCamPerfect) {
              vec2 uvv = equirectUv( rayDirection );
              vec2 smoothUv = equirectUv( directionCamPerfect );
              return textureGrad(envMap, uvv, dFdx(correctMips ? smoothUv : uvv), dFdy(correctMips ? smoothUv : uvv));
            }
          #endif

          void main() {
            vec3 directionCamPerfect = getCameraDirectionHighDetail();
            vec3 normal = vNormal;
            vec3 rayOrigin = cameraPosition;
            vec3 rayDirection = normalize(vWorldPosition - cameraPosition);

            vec4 diffuseColor = vec4(color, opacity);

            vec3 rayDirectionG = totalInternalReflection(rayOrigin, rayDirection, normal, max(ior, 1.0), vModelMatrixInverse);
            vec3 rayDirectionR = normalize(rayDirectionG + 1.0 * vec3(aberrationStrength / 2.0));
            vec3 rayDirectionB = normalize(rayDirectionG - 1.0 * vec3(aberrationStrength / 2.0));
            float finalColorR = textureGradient(envMap, rayDirectionR, directionCamPerfect).r;
            float finalColorG = textureGradient(envMap, rayDirectionG, directionCamPerfect).g;
            float finalColorB = textureGradient(envMap, rayDirectionB, directionCamPerfect).b;
            diffuseColor.rgb *= vec3(finalColorR, finalColorG, finalColorB);

            vec3 reflectionDir = reflect(rayDirection, normal);
            vec3 reflectionColor = textureGradient(reflectionMap, reflectionDir, directionCamPerfect).rgb;
            float brightness = dot(reflectionColor, vec3(0.299, 0.587, 0.114));
            float reflectionStrength = smoothstep(0.9, 1.0, brightness);
            diffuseColor.rgb = mix(diffuseColor.rgb, reflectionColor, reflectionStrength * 0.5);

            gl_FragColor = vec4(diffuseColor.rgb, 1.0);
          }
        `,
      });

      return diamondMaterial;
    } catch (error) {
      console.error("Failed to load diamond shader textures:", error);
      // Fallback to a basic diamond-like material
      return new THREE.MeshPhysicalMaterial({
        color: currentColor,
        metalness: 0.0,
        roughness: 0.0,
        transmission: 0.9,
        transparent: true,
        thickness: 0.5,
        ior: 2.3,
      });
    }
  };

  const changeMaterialType = async (type) => {
    // Check if we have any diamond parts selected
    const selectedDiamondObjects = selectedObjects.filter(
      (obj) => obj && obj.userData && obj.userData.part === "diamond"
    );

    if (type === "diamond") {
      // Set diamond shader state
      setUseDiamondShader(true);
      console.log("Applying diamond shader to selected objects");
    } else {
      // If switching away from diamond type, clear diamond shader
      setUseDiamondShader(false);
    }

    // Clear premium material flags when changing material type
    setUsePremiumWhiteGold(false);
    setUsePremiumRoseGold(false);

    // Update local state
    setMaterialType(type);

    // Update selected objects (excluding decals)
    const nonDecalObjects = filterOutDecals(selectedObjects);

    for (const obj of nonDecalObjects) {
      if (!obj) continue;

      const currentColor =
        obj.material && obj.material.color
          ? obj.material.color.getHexString()
          : "ffffff";

      let newMaterial;

      switch (type) {
        case "basic":
          newMaterial = new THREE.MeshBasicMaterial({
            color: `#${currentColor}`,
          });
          break;
        case "standard":
          newMaterial = new THREE.MeshStandardMaterial({
            color: `#${currentColor}`,
            roughness: materialProps.roughness,
            metalness: materialProps.metalness,
          });
          break;
        case "physical":
          newMaterial = new THREE.MeshPhysicalMaterial({
            color: `#${currentColor}`,
            roughness: materialProps.roughness,
            metalness: materialProps.metalness,
            clearcoat: materialProps.clearcoat,
            clearcoatRoughness: materialProps.clearcoatRoughness,
          });
          break;
        case "toon":
          newMaterial = new THREE.MeshToonMaterial({
            color: `#${currentColor}`,
          });
          break;
        case "normal":
          newMaterial = new THREE.MeshNormalMaterial();
          break;
        case "diamond":
          // Create and apply diamond shader material
          setIsLoadingDiamondShader(true);
          try {
            // Create diamond data for this specific object
            let diamondData = null;
            if (obj.geometry && renderer) {
              diamondData = createDiamondData(obj.geometry, renderer);
              console.log(
                "Created diamond data for",
                obj.name || "unnamed object",
                {
                  center: diamondData.center,
                  radius: diamondData.radius,
                  hasNormalCube: !!diamondData.normalCube,
                }
              );
            } else if (!renderer) {
              console.warn(
                "Renderer not available for diamond shader, using fallback material"
              );
            }

            newMaterial = await createDiamondShaderMaterial(
              `#${currentColor}`,
              diamondData
            );
            console.log(
              "Diamond shader material created successfully for",
              obj.name || "unnamed object"
            );
          } catch (error) {
            console.error("Failed to create diamond shader material:", error);
            // Fallback to physical material with diamond-like properties
            newMaterial = new THREE.MeshPhysicalMaterial({
              color: `#${currentColor}`,
              metalness: 0.0,
              roughness: 0.0,
              transmission: 0.9,
              transparent: true,
              thickness: 0.5,
              ior: 2.3,
            });
          } finally {
            setIsLoadingDiamondShader(false);
          }
          break;
        default:
          newMaterial = new THREE.MeshStandardMaterial({
            color: `#${currentColor}`,
          });
      }

      // Copy other properties for non-diamond materials
      if (type !== "diamond") {
        newMaterial.wireframe = materialProps.wireframe;
        newMaterial.transparent = materialProps.transparent;
        newMaterial.opacity = materialProps.opacity;
      }

      obj.material = newMaterial;
    }
    if (forceUpdate) forceUpdate();
    if (setMaterialVersion) setMaterialVersion((v) => v + 1);

    // Add to history for material type changes
    if (addToHistory) {
      addToHistoryImmediate();
    }
  };

  const handleColorClick = () => {
    setDisplayColorPicker(!displayColorPicker);
  };

  const handleColorClose = () => {
    setDisplayColorPicker(false);
  };

  const handleColorChange = (color) => {
    const hexColor = color.hex;
    setMaterialProps((prev) => ({ ...prev, color: hexColor }));
    updateMaterialProperty("color", hexColor);
  };

  return (
    <div className="w-full overflow-y-auto scrollbar-none text-[10px] md:text-xs">
      <div className="mb-4">
        <div className="mb-2 border-b border-gray-700 text-[#FDE9CE]">
          Material{" "}
          {(() => {
            const nonDecalObjects = filterOutDecals(selectedObjects);
            if (nonDecalObjects.length > 1) {
              return `(${nonDecalObjects.length} objects)`;
            } else if (nonDecalObjects.length === 1) {
              return `(${nonDecalObjects[0].name || "Unnamed object"})`;
            }
            return "";
          })()}
        </div>

        {/* Material Presets Section */}
        <div className="mb-4 p-4 bg-gradient-to-br from-[#2A2D3A] to-[#1A1D2A] rounded-xl border border-[#A3A3A3]/10 shadow-lg">
          <h3 className="text-[#FDE9CE] font-medium mb-4 text-center text-xs md:text-sm tracking-wide">
            Material Presets
          </h3>

          {/* Gems Section */}
          <div className="mb-5">
            <h4 className="text-[#A3A3A3] text-[10px] uppercase tracking-wider mb-3 font-semibold flex items-center">
              <span className="w-2 h-2 rounded-full bg-emerald-500 mr-2"></span>
              Gems
            </h4>
            <div className="grid grid-cols-3 gap-3">
              {MATERIAL_PRESETS.gems.map((preset) => (
                <button
                  key={preset.name}
                  onClick={() => applyPreset(preset)}
                  className="group relative overflow-hidden rounded-xl border border-[#A3A3A3]/20 hover:border-[#FDE9CE]/50 transition-all duration-300 transform hover:scale-105 hover:shadow-lg hover:shadow-[#FDE9CE]/5"
                >
                  <div
                    className="h-10 w-full relative"
                    style={{
                      background: `linear-gradient(135deg, ${preset.color} 0%, ${preset.color}dd 50%, ${preset.color}bb 100%)`,
                      boxShadow: `inset 0 1px 2px rgba(255,255,255,0.3), inset 0 -1px 2px rgba(0,0,0,0.3)`,
                    }}
                  >
                    <div className="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent opacity-60"></div>
                    {preset.transparent && (
                      <div className="absolute inset-0 bg-gradient-to-t from-black/10 to-transparent"></div>
                    )}
                    <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      <span className="text-[10px] font-bold text-white bg-black/30 px-2 py-1 rounded-full">
                        Apply
                      </span>
                    </div>
                  </div>
                  <div className="p-1.5 bg-gradient-to-b from-[#2A2D3A] to-[#1F222F]">
                    <span className="text-[10px] text-[#FDE9CE] group-hover:text-white transition-colors duration-200 font-medium">
                      {preset.name}
                    </span>
                  </div>
                </button>
              ))}
            </div>
          </div>

          {/* Metals Section */}
          <div>
            <h4 className="text-[#A3A3A3] text-[10px] uppercase tracking-wider mb-3 font-semibold flex items-center">
              <span className="w-2 h-2 rounded-full bg-amber-500 mr-2"></span>
              Metals
            </h4>
            <div className="grid grid-cols-3 gap-3">
              {MATERIAL_PRESETS.metals.map((preset) => (
                <button
                  key={preset.name}
                  onClick={() => applyPreset(preset)}
                  className="group relative overflow-hidden rounded-xl border border-[#A3A3A3]/20 hover:border-[#FDE9CE]/50 transition-all duration-300 transform hover:scale-105 hover:shadow-lg hover:shadow-[#FDE9CE]/5"
                >
                  <div
                    className="h-10 w-full relative"
                    style={{
                      background: `linear-gradient(135deg, ${preset.color} 0%, ${preset.color}cc 50%, ${preset.color}aa 100%)`,
                      boxShadow: `inset 0 1px 2px rgba(255,255,255,0.4), inset 0 -1px 2px rgba(0,0,0,0.4)`,
                    }}
                  >
                    <div className="absolute inset-0 bg-gradient-to-br from-white/30 to-transparent opacity-50"></div>
                    <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-30"></div>
                    <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      <span className="text-[10px] font-bold text-white bg-black/30 px-2 py-1 rounded-full">
                        Apply
                      </span>
                    </div>
                  </div>
                  <div className="p-1.5 bg-gradient-to-b from-[#2A2D3A] to-[#1F222F]">
                    <span className="text-[10px] text-[#FDE9CE] group-hover:text-white transition-colors duration-200 font-medium text-nowrap">
                      {preset.name}
                    </span>
                  </div>
                </button>
              ))}
            </div>
          </div>
        </div>

        <div className="mb-3 w-[95%]">
          <label className="text-[#FDE9CE]">Material Type</label>
          <div className="relative">
            <select
              value={materialType}
              onChange={(e) => {
                const newType = e.target.value;
                changeMaterialType(newType).catch((error) => {
                  console.error("Error changing material type:", error);
                });
              }}
              disabled={isLoadingDiamondShader}
              className="ml-2 bg-[#1A1A1A] border border-gray-700 rounded px-2 py-1 w-full mt-1 text-[#FDE9CE] text-xs disabled:opacity-50"
            >
              <option value="standard">Standard</option>
              <option value="physical">Physical</option>
              <option value="basic">Basic</option>
              <option value="toon">Toon</option>
              <option value="normal">Normal</option>
              <option value="diamond">Diamond Shader</option>
            </select>
            {isLoadingDiamondShader && (
              <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
                <div className="animate-spin h-4 w-4 border-2 border-[#FDE9CE] border-t-transparent rounded-full"></div>
              </div>
            )}
          </div>
          {isLoadingDiamondShader && (
            <div className="text-[#FDE9CE] text-xs mt-1 opacity-75">
              Loading diamond shader...
            </div>
          )}
        </div>

        <div className="mb-3">
          <label className="text-[#FDE9CE]">Color</label>
          <div className="flex items-center gap-2 mt-1">
            <div className="relative">
              <div
                className="w-12 h-12 rounded cursor-pointer border border-gray-700"
                style={{ backgroundColor: materialProps.color || "#ffffff" }}
                onClick={handleColorClick}
              />
              {displayColorPicker && (
                <div style={{ position: "absolute", zIndex: "2" }}>
                  <div
                    style={{
                      position: "fixed",
                      top: "0px",
                      right: "0px",
                      bottom: "0px",
                      left: "0px",
                    }}
                    onClick={handleColorClose}
                  />
                  <ChromePicker
                    color={materialProps.color || "#ffffff"}
                    onChange={handleColorChange}
                    disableAlpha
                  />
                </div>
              )}
            </div>
            <input
              type="text"
              value={materialProps.color || "#ffffff"}
              onChange={(e) => {
                const value = e.target.value;
                setMaterialProps((prev) => ({ ...prev, color: value }));
                if (/^#[0-9A-Fa-f]{6}$/.test(value)) {
                  updateMaterialProperty("color", value);
                }
              }}
              onBlur={(e) => {
                const value = e.target.value;
                if (!/^#[0-9A-Fa-f]{6}$/.test(value)) {
                  setMaterialProps((prev) => ({
                    ...prev,
                    color: materialProps.color || "#ffffff",
                  }));
                }
              }}
              className="bg-[#1A1A1A] border border-gray-700 rounded px-2 py-1 w-24 text-[#FDE9CE] text-sm"
              placeholder="#FFFFFF"
            />
          </div>
        </div>

        {(materialType === "standard" || materialType === "physical") && (
          <>
            <div className="mb-3">
              <label className="text-[#FDE9CE]">Roughness</label>
              <div className="flex items-center gap-2 mt-1">
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.01"
                  value={materialProps.roughness}
                  onChange={(e) =>
                    updateMaterialProperty(
                      "roughness",
                      parseFloat(e.target.value),
                      true
                    )
                  }
                  className="w-full accent-[#FDE9CE]"
                />
                <span className="w-10 text-right text-[#FDE9CE]">
                  {materialProps.roughness.toFixed(2)}
                </span>
              </div>
            </div>

            <div className="mb-3">
              <label className="text-[#FDE9CE]">Metalness</label>
              <div className="flex items-center gap-2 mt-1">
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.01"
                  value={materialProps.metalness}
                  onChange={(e) =>
                    updateMaterialProperty(
                      "metalness",
                      parseFloat(e.target.value),
                      true
                    )
                  }
                  className="w-full accent-[#FDE9CE]"
                />
                <span className="w-10 text-right text-[#FDE9CE]">
                  {materialProps.metalness.toFixed(2)}
                </span>
              </div>
            </div>
          </>
        )}

        {materialType === "physical" && (
          <>
            <div className="mb-3">
              <label className="text-[#FDE9CE]">Clearcoat</label>
              <div className="flex items-center gap-2 mt-1">
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.01"
                  value={materialProps.clearcoat}
                  onChange={(e) =>
                    updateMaterialProperty(
                      "clearcoat",
                      parseFloat(e.target.value),
                      true
                    )
                  }
                  className="w-full accent-[#FDE9CE]"
                />
                <span className="w-10 text-right text-[#FDE9CE]">
                  {materialProps.clearcoat.toFixed(2)}
                </span>
              </div>
            </div>

            <div className="mb-3">
              <label className="text-[#FDE9CE]">Clearcoat Roughness</label>
              <div className="flex items-center gap-2 mt-1">
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.01"
                  value={materialProps.clearcoatRoughness}
                  onChange={(e) =>
                    updateMaterialProperty(
                      "clearcoatRoughness",
                      parseFloat(e.target.value),
                      true
                    )
                  }
                  className="w-full accent-[#FDE9CE]"
                />
                <span className="w-10 text-right text-[#FDE9CE]">
                  {materialProps.clearcoatRoughness.toFixed(2)}
                </span>
              </div>
            </div>
          </>
        )}

        <div className="mb-3">
          <label className="flex items-center gap-2 text-[#FDE9CE]">
            <input
              type="checkbox"
              checked={materialProps.wireframe || false}
              onChange={(e) =>
                updateMaterialProperty("wireframe", e.target.checked)
              }
              className="accent-[#FDE9CE]"
            />
            Wireframe
          </label>
        </div>

        <div className="mb-3">
          <label className="flex items-center gap-2 text-[#FDE9CE]">
            <input
              type="checkbox"
              checked={materialProps.transparent || false}
              onChange={(e) =>
                updateMaterialProperty("transparent", e.target.checked)
              }
              className="accent-[#FDE9CE]"
            />
            Transparent
          </label>
        </div>

        {materialProps.transparent && (
          <div className="mb-3">
            <label className="text-[#FDE9CE]">Opacity</label>
            <div className="flex items-center gap-2 mt-1">
              <input
                type="range"
                min="0"
                max="1"
                step="0.01"
                value={materialProps.opacity || 1.0}
                onChange={(e) =>
                  updateMaterialProperty(
                    "opacity",
                    parseFloat(e.target.value),
                    true
                  )
                }
                className="w-full accent-[#FDE9CE]"
              />
              <span className="w-10 text-right text-[#FDE9CE]">
                {(materialProps.opacity || 1.0).toFixed(2)}
              </span>
            </div>
          </div>
        )}

        {/* Diamond Shader Advanced Controls */}
        {materialType === "diamond" && (
          <>
            <div className="mb-3 p-3 bg-blue-900/20 rounded-lg border border-blue-500/30">
              <div className="text-xs text-blue-300 mb-2">
                <strong>Advanced Diamond Shader</strong>
              </div>
              <div className="text-[10px] text-blue-200 opacity-80">
                Using geometry-specific normal cube textures for realistic
                refraction and reflection calculations.
              </div>
            </div>
            <div className="mb-3">
              <label className="text-[#FDE9CE]">
                Index of Refraction (IOR)
              </label>
              <div className="flex items-center gap-2 mt-1">
                <input
                  type="range"
                  min="1.0"
                  max="3.0"
                  step="0.1"
                  value={materialProps.ior || 2.4}
                  onChange={(e) =>
                    updateMaterialProperty("ior", parseFloat(e.target.value))
                  }
                  className="w-full accent-[#FDE9CE]"
                />
                <span className="w-10 text-right text-[#FDE9CE]">
                  {(materialProps.ior || 2.4).toFixed(1)}
                </span>
              </div>
            </div>

            <div className="mb-3">
              <label className="text-[#FDE9CE]">Reflection Bounces</label>
              <div className="flex items-center gap-2 mt-1">
                <input
                  type="range"
                  min="1"
                  max="8"
                  step="1"
                  value={materialProps.bounces || 3}
                  onChange={(e) =>
                    updateMaterialProperty("bounces", parseInt(e.target.value))
                  }
                  className="w-full accent-[#FDE9CE]"
                />
                <span className="w-10 text-right text-[#FDE9CE]">
                  {materialProps.bounces || 3}
                </span>
              </div>
            </div>

            <div className="mb-3">
              <label className="text-[#FDE9CE]">Chromatic Aberration</label>
              <div className="flex items-center gap-2 mt-1">
                <input
                  type="range"
                  min="0"
                  max="0.05"
                  step="0.001"
                  value={materialProps.aberrationStrength || 0.01}
                  onChange={(e) =>
                    updateMaterialProperty(
                      "aberrationStrength",
                      parseFloat(e.target.value)
                    )
                  }
                  className="w-full accent-[#FDE9CE]"
                />
                <span className="w-10 text-right text-[#FDE9CE]">
                  {(materialProps.aberrationStrength || 0.01).toFixed(3)}
                </span>
              </div>
            </div>

            <div className="mb-3">
              <label className="text-[#FDE9CE]">Fresnel Strength</label>
              <div className="flex items-center gap-2 mt-1">
                <input
                  type="range"
                  min="0"
                  max="2"
                  step="0.1"
                  value={materialProps.fresnel || 1.0}
                  onChange={(e) =>
                    updateMaterialProperty(
                      "fresnel",
                      parseFloat(e.target.value)
                    )
                  }
                  className="w-full accent-[#FDE9CE]"
                />
                <span className="w-10 text-right text-[#FDE9CE]">
                  {(materialProps.fresnel || 1.0).toFixed(1)}
                </span>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
}
