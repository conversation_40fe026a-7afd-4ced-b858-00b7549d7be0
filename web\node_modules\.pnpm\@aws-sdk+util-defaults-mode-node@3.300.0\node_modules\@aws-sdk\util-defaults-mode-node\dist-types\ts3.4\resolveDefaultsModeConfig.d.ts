import { DefaultsMode, ResolvedDefaultsMode } from "@aws-sdk/smithy-client";
import { Provider } from "@aws-sdk/types";
export interface ResolveDefaultsModeConfigOptions {
  defaultsMode?: DefaultsMode | Provider<DefaultsMode>;
  region?: string | Provider<string>;
}
export declare const resolveDefaultsModeConfig: ({
  region,
  defaultsMode,
}?: ResolveDefaultsModeConfigOptions) => Provider<ResolvedDefaultsMode>;
