"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/projects/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/ArtistTool/Environment.tsx":
/*!***************************************************!*\
  !*** ./src/components/ArtistTool/Environment.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Environment: function() { return /* binding */ Environment; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/Environment.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! three */ \"(app-pages-browser)/./node_modules/.pnpm/three@0.167.1/node_modules/three/build/three.module.js\");\n\nvar _s = $RefreshSig$();\n\n\n\nfunction Environment(param) {\n    let { preset = \"sunset\", background = false, bgColor = \"#000000\", customHdri = null, intensity = 1, envRotation = 0, blur = 0.1, envTextureCache = {} } = param;\n    _s();\n    const [backgroundColor, setBackgroundColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (bgColor) {\n            setBackgroundColor(new three__WEBPACK_IMPORTED_MODULE_2__.Color(bgColor));\n        }\n    }, [\n        bgColor\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            backgroundColor && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"color\", {\n                attach: \"background\",\n                args: [\n                    backgroundColor\n                ]\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Environment.tsx\",\n                lineNumber: 59,\n                columnNumber: 9\n            }, this),\n            preset !== \"none\" && (customHdri ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_3__.Environment, {\n                files: customHdri,\n                background: background,\n                environmentIntensity: intensity,\n                environmentRotation: [\n                    0,\n                    envRotation,\n                    0\n                ],\n                blur: background ? blur : 0\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Environment.tsx\",\n                lineNumber: 64,\n                columnNumber: 11\n            }, this) : preset === \"hdri_15\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_3__.Environment, {\n                map: envTextureCache[\"hdri_15\"],\n                files: \"/hdris/hdri_15.hdr\",\n                background: background,\n                environmentIntensity: intensity,\n                environmentRotation: [\n                    0,\n                    envRotation,\n                    0\n                ],\n                blur: background ? blur : 0\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Environment.tsx\",\n                lineNumber: 72,\n                columnNumber: 11\n            }, this) : preset === \"hdri_metal\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_3__.Environment, {\n                map: envTextureCache[\"hdri_metal\"],\n                files: \"/hdris/hdri_metal.exr\",\n                background: background,\n                environmentIntensity: intensity,\n                environmentRotation: [\n                    0,\n                    envRotation,\n                    0\n                ],\n                blur: background ? blur : 0\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Environment.tsx\",\n                lineNumber: 81,\n                columnNumber: 11\n            }, this) : preset === \"hdri_metal2\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_3__.Environment, {\n                map: envTextureCache[\"hdri_metal2\"],\n                files: \"/hdris/metal_hdri2.hdr\",\n                background: background,\n                environmentIntensity: intensity,\n                environmentRotation: [\n                    0,\n                    envRotation,\n                    0\n                ],\n                blur: background ? blur : 0\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Environment.tsx\",\n                lineNumber: 90,\n                columnNumber: 11\n            }, this) : preset === \"hdri_metal3\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_3__.Environment, {\n                map: envTextureCache[\"hdri_metal3\"],\n                files: \"/hdris/metal_hdri3.hdr\",\n                background: background,\n                environmentIntensity: intensity,\n                environmentRotation: [\n                    0,\n                    envRotation,\n                    0\n                ],\n                blur: background ? blur : 0\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Environment.tsx\",\n                lineNumber: 99,\n                columnNumber: 11\n            }, this) : preset === \"hdri_gem\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_3__.Environment, {\n                map: envTextureCache[\"hdri_gem\"],\n                files: \"/hdris/env_gem_002_30251392af.exr\",\n                background: background,\n                environmentIntensity: intensity,\n                environmentRotation: [\n                    0,\n                    envRotation,\n                    0\n                ],\n                blur: background ? blur : 0\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Environment.tsx\",\n                lineNumber: 108,\n                columnNumber: 11\n            }, this) : preset === \"studio_small\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_3__.Environment, {\n                map: envTextureCache[\"studio_small\"],\n                files: \"/hdris/studio_small_02_2k.hdr\",\n                background: background,\n                environmentIntensity: intensity,\n                environmentRotation: [\n                    0,\n                    envRotation,\n                    0\n                ],\n                blur: background ? blur : 0\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Environment.tsx\",\n                lineNumber: 117,\n                columnNumber: 11\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_3__.Environment, {\n                preset: preset,\n                background: background,\n                environmentIntensity: intensity,\n                environmentRotation: [\n                    0,\n                    envRotation,\n                    0\n                ],\n                blur: background ? blur : 0\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Environment.tsx\",\n                lineNumber: 126,\n                columnNumber: 11\n            }, this))\n        ]\n    }, void 0, true);\n}\n_s(Environment, \"8dfVRfhsIeo/uEzTOufHhYu5Oto=\");\n_c = Environment;\nvar _c;\n$RefreshReg$(_c, \"Environment\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ArtistTool/Environment.tsx\n"));

/***/ })

});