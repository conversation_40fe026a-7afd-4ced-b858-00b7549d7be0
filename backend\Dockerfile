# Use the latest Node.js image as the base
FROM node:20.10.0-alpine


# Set the working directory inside the container
WORKDIR /app

RUN npm install -g pnpm


# Copy all files from the current directory to the /app directory in the container
COPY . /app


# Install dependencies (assuming package.json and package-lock.json are present)
RUN pnpm install


# Ignore the node_modules folder
EXPOSE 3001


# Run the command to start the application
CMD ["pnpm", "run", "start"]