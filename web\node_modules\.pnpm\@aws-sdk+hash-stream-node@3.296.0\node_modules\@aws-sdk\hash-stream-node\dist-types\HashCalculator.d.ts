/// <reference types="node" />
/// <reference types="node" />
import { Checksum, Hash } from "@aws-sdk/types";
import { Writable, WritableOptions } from "stream";
export declare class HashCalculator extends Writable {
    readonly hash: Checksum | Hash;
    constructor(hash: Checksum | Hash, options?: WritableOptions);
    _write(chunk: Buffer, encoding: string, callback: (err?: Error) => void): void;
}
