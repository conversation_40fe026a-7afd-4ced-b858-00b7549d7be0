"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/projects/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/ArtistTool/EditorExperience.jsx":
/*!********************************************************!*\
  !*** ./src/components/ArtistTool/EditorExperience.jsx ***!
  \********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ EditorExperience; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/web/Html.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/web/Loader.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/OrbitControls.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/TransformControls.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/MeshReflectorMaterial.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/GizmoHelper.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/GizmoViewport.js\");\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @react-three/fiber */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_three@0.167.1/node_modules/@react-three/fiber/dist/events-776716bd.esm.js\");\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @react-three/fiber */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_three@0.167.1/node_modules/@react-three/fiber/dist/react-three-fiber.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/.pnpm/react-toastify@11.0.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/react-toastify/dist/index.mjs\");\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! three */ \"(app-pages-browser)/./node_modules/.pnpm/three@0.167.1/node_modules/three/build/three.module.js\");\n/* harmony import */ var three_examples_jsm_loaders_EXRLoader_js__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! three/examples/jsm/loaders/EXRLoader.js */ \"(app-pages-browser)/./node_modules/.pnpm/three@0.167.1/node_modules/three/examples/jsm/loaders/EXRLoader.js\");\n/* harmony import */ var three_examples_jsm_loaders_RGBELoader_js__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! three/examples/jsm/loaders/RGBELoader.js */ \"(app-pages-browser)/./node_modules/.pnpm/three@0.167.1/node_modules/three/examples/jsm/loaders/RGBELoader.js\");\n/* harmony import */ var _Environment__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Environment */ \"(app-pages-browser)/./src/components/ArtistTool/Environment.tsx\");\n/* harmony import */ var _EnvironmentPanel__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./EnvironmentPanel */ \"(app-pages-browser)/./src/components/ArtistTool/EnvironmentPanel.jsx\");\n/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Header */ \"(app-pages-browser)/./src/components/ArtistTool/Header.jsx\");\n/* harmony import */ var _LightPanel__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./LightPanel */ \"(app-pages-browser)/./src/components/ArtistTool/LightPanel.jsx\");\n/* harmony import */ var _MaterialPanel__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./MaterialPanel */ \"(app-pages-browser)/./src/components/ArtistTool/MaterialPanel.jsx\");\n/* harmony import */ var _ModelStats__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./ModelStats */ \"(app-pages-browser)/./src/components/ArtistTool/ModelStats.jsx\");\n/* harmony import */ var _Outliner__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./Outliner */ \"(app-pages-browser)/./src/components/ArtistTool/Outliner.jsx\");\n/* harmony import */ var _PostProcessingPanel__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./PostProcessingPanel */ \"(app-pages-browser)/./src/components/ArtistTool/PostProcessingPanel.jsx\");\n/* harmony import */ var _SceneLights__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./SceneLights */ \"(app-pages-browser)/./src/components/ArtistTool/SceneLights.jsx\");\n/* harmony import */ var _Toolbar__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./Toolbar */ \"(app-pages-browser)/./src/components/ArtistTool/Toolbar.jsx\");\n/* harmony import */ var _TransformPanel__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./TransformPanel */ \"(app-pages-browser)/./src/components/ArtistTool/TransformPanel.jsx\");\n/* harmony import */ var _utils_ArtistTool_cameraUtils__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../../utils/ArtistTool/cameraUtils */ \"(app-pages-browser)/./src/utils/ArtistTool/cameraUtils.js\");\n/* harmony import */ var _utils_ArtistTool_materialUtils__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../../utils/ArtistTool/materialUtils */ \"(app-pages-browser)/./src/utils/ArtistTool/materialUtils.js\");\n/* harmony import */ var _utils_ArtistTool_sceneUtils__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../../utils/ArtistTool/sceneUtils */ \"(app-pages-browser)/./src/utils/ArtistTool/sceneUtils.js\");\n/* harmony import */ var _animations_CameraAnimations__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./animations/CameraAnimations */ \"(app-pages-browser)/./src/components/ArtistTool/animations/CameraAnimations.js\");\n/* harmony import */ var _EngravingPanel__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./EngravingPanel */ \"(app-pages-browser)/./src/components/ArtistTool/EngravingPanel.jsx\");\n/* harmony import */ var _InitialOverlayPanel__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./InitialOverlayPanel */ \"(app-pages-browser)/./src/components/ArtistTool/InitialOverlayPanel.jsx\");\n/* harmony import */ var _ModelStage__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./ModelStage */ \"(app-pages-browser)/./src/components/ArtistTool/ModelStage.jsx\");\n/* harmony import */ var _PostProcessingEffects__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./PostProcessingEffects */ \"(app-pages-browser)/./src/components/ArtistTool/PostProcessingEffects.jsx\");\n/* harmony import */ var _SelectionOutline__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ./SelectionOutline */ \"(app-pages-browser)/./src/components/ArtistTool/SelectionOutline.jsx\");\n/* harmony import */ var _lib_actions_version_actions__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/lib/actions/version.actions */ \"(app-pages-browser)/./src/lib/actions/version.actions.ts\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/.pnpm/sonner@1.5.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _lib_states__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @/lib/states */ \"(app-pages-browser)/./src/lib/states.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst ScreenshotTaker = (param)=>{\n    let { onScreenshotRef } = param;\n    _s();\n    const { gl, scene, camera } = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.D)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (typeof onScreenshotRef === \"function\") {\n            onScreenshotRef(()=>{\n                try {\n                    // Render the scene\n                    gl.render(scene, camera);\n                    // Get the canvas data URL\n                    const dataURL = gl.domElement.toDataURL(\"image/png\");\n                    // Create and trigger a download\n                    const link = document.createElement(\"a\");\n                    const timestamp = new Date().toISOString().replace(/:/g, \"-\").substring(0, 19);\n                    link.download = \"agape-scene-\".concat(timestamp, \".png\");\n                    link.href = dataURL;\n                    document.body.appendChild(link);\n                    link.click();\n                    // Clean up\n                    setTimeout(()=>{\n                        document.body.removeChild(link);\n                    }, 100);\n                    console.log(\"Screenshot captured successfully\");\n                    return true;\n                } catch (error) {\n                    console.error(\"Error capturing screenshot:\", error);\n                    return false;\n                }\n            });\n        }\n    }, [\n        gl,\n        scene,\n        camera,\n        onScreenshotRef\n    ]);\n    return null;\n};\n_s(ScreenshotTaker, \"RVTFHuczdlyvGFUsZJPSpjurI/Y=\", false, function() {\n    return [\n        _react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.D\n    ];\n});\n_c = ScreenshotTaker;\n// Component to provide renderer to MaterialPanel\nconst MaterialPanelWrapper = (param)=>{\n    let { renderer, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MaterialPanel__WEBPACK_IMPORTED_MODULE_7__.MaterialPanel, {\n        renderer: renderer,\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n        lineNumber: 95,\n        columnNumber: 10\n    }, undefined);\n};\n_c1 = MaterialPanelWrapper;\n// Component to get renderer from Canvas context\nconst RendererProvider = (param)=>{\n    let { children, onRendererReady } = param;\n    _s1();\n    const { gl } = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.D)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (onRendererReady) {\n            onRendererReady(gl);\n        }\n    }, [\n        gl,\n        onRendererReady\n    ]);\n    return null;\n};\n_s1(RendererProvider, \"iTWbOa64ZxlA5nuyNExgWK+vjCI=\", false, function() {\n    return [\n        _react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.D\n    ];\n});\n_c2 = RendererProvider;\nfunction EditorExperience(param) {\n    let { modelUrl, project, user } = param;\n    _s2();\n    const { addVersion } = (0,_lib_states__WEBPACK_IMPORTED_MODULE_25__.useVersionState)();\n    const configRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const workingConfigRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [selectedModel, setSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"uploaded\");\n    const [lastSelectedModel, setLastSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [uploadedModel, setUploadedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(modelUrl || null);\n    const [modelStats, setModelStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        vertices: 0,\n        triangles: 0,\n        materials: 0,\n        animations: 0\n    });\n    const [lights, setLights] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: \"defaultDirectional\",\n            type: \"directional\",\n            position: [\n                2.5,\n                5.5,\n                5\n            ],\n            intensity: 1.0,\n            color: \"#ffffff\",\n            name: \"Main Directional Light\",\n            castShadow: true,\n            helperVisible: false\n        },\n        {\n            id: \"keyLight\",\n            type: \"point\",\n            position: [\n                5,\n                5,\n                5\n            ],\n            intensity: 8.0,\n            color: \"#ffffff\",\n            name: \"Key Light\"\n        },\n        {\n            id: \"fillLight\",\n            type: \"point\",\n            position: [\n                -5,\n                5,\n                -5\n            ],\n            intensity: 4.0,\n            color: \"#ffffff\",\n            name: \"Fill Light\"\n        },\n        {\n            id: \"backLight\",\n            type: \"point\",\n            position: [\n                0,\n                5,\n                -5\n            ],\n            intensity: 2.0,\n            color: \"#ffffff\",\n            name: \"Back Light\"\n        }\n    ]);\n    const lightRefs = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({});\n    const [selectedObjects, setSelectedObjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedLight, setSelectedLight] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [lightPositionHistory, setLightPositionHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [sceneObjects, setSceneObjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [transformMode, setTransformMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"translate\");\n    const selectionGroupRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(new three__WEBPACK_IMPORTED_MODULE_27__.Group());\n    console.log(\"renderingggg editor experience\");\n    const [rightPanelTab, setRightPanelTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Decal placement state\n    const [isPlacingDecal, setIsPlacingDecal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showDecalDebug, setShowDecalDebug] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Scene controls state\n    const [showGrid, setShowGrid] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [wireframe, setWireframe] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [groundType, setGroundType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"none\");\n    const [envPreset, setEnvPreset] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"hdri_15\");\n    const [bgColor, setBgColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"#272631\");\n    const [showEnvironment, setShowEnvironment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showLightSpheres, setShowLightSpheres] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [customHdri, setCustomHdri] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [envIntensity, setEnvIntensity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1.85);\n    const [envBlur, setEnvBlur] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0.35);\n    const [envRotation, setEnvRotation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [postProcessingEnabled, setPostProcessingEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [postProcessingSettings, setPostProcessingSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        autofocus: {\n            enabled: false,\n            bokehScale: 18\n        },\n        bloom: {\n            enabled: true,\n            intensity: 0.22,\n            threshold: 1,\n            radius: 0.5\n        },\n        dof: {\n            enabled: false,\n            focusDistance: 0,\n            aperture: 0.01,\n            bokehScale: 3\n        },\n        vignette: {\n            enabled: true,\n            darkness: 0.5,\n            offset: 0.1\n        }\n    });\n    // Store initial state\n    const initialStateRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        selectedModel: null,\n        lights: [],\n        envPreset: null,\n        bgColor: null,\n        postProcessingSettings: null,\n        showGrid: null,\n        wireframe: null,\n        groundType: null,\n        showEnvironment: null,\n        showLightSpheres: null,\n        envIntensity: null,\n        envBlur: null,\n        cameraPosition: null,\n        cameraTarget: null\n    });\n    // Add a ref to store the initial state of model objects\n    const modelInitialStateRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        objects: []\n    });\n    // Capture initial state when component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        initialStateRef.current = {\n            selectedModel,\n            lights: [\n                ...lights\n            ],\n            envPreset,\n            bgColor,\n            postProcessingSettings: {\n                ...postProcessingSettings\n            },\n            showGrid,\n            wireframe,\n            groundType,\n            showEnvironment,\n            showLightSpheres,\n            envIntensity,\n            envBlur,\n            cameraPosition: new three__WEBPACK_IMPORTED_MODULE_27__.Vector3(5, 2, 5),\n            cameraTarget: new three__WEBPACK_IMPORTED_MODULE_27__.Vector3(0, 1, 0)\n        };\n    }, []);\n    const orbitControlsRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const sceneRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const [renderer, setRenderer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // History state for undo/redo\n    const [history, setHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [historyIndex, setHistoryIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(-1);\n    const isUndoRedoActionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const skipHistoryTrackingRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const isUndoRedoInProgressRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const [showModelStats, setShowModelStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Debug function to inspect history state\n    const debugHistory = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        console.log(\"=== HISTORY DEBUG ===\");\n        console.log(\"Current historyIndex:\", historyIndex);\n        console.log(\"History length:\", history.length);\n        console.log(\"Can undo:\", historyIndex > 0);\n        console.log(\"Can redo:\", historyIndex < history.length - 1);\n        console.log(\"isUndoRedoActionRef.current:\", isUndoRedoActionRef.current);\n        console.log(\"skipHistoryTrackingRef.current:\", skipHistoryTrackingRef.current);\n        console.log(\"isUndoRedoInProgressRef.current:\", isUndoRedoInProgressRef.current);\n        console.log(\"History entries:\", history.map((_, i)=>\"\".concat(i).concat(i === historyIndex ? \" (current)\" : \"\")));\n        console.log(\"==================\");\n    }, [\n        historyIndex,\n        history\n    ]);\n    // Expose debug function to window for testing\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        window.debugHistory = debugHistory;\n        return ()=>{\n            delete window.debugHistory;\n        };\n    }, [\n        debugHistory\n    ]);\n    // Get full configuration for history\n    const getFullConfig = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        return JSON.parse(JSON.stringify({\n            selectedModel,\n            uploadedModel,\n            lights,\n            envPreset,\n            bgColor,\n            showEnvironment,\n            showLightSpheres,\n            customHdri,\n            envIntensity,\n            envBlur,\n            envRotation,\n            postProcessingEnabled,\n            postProcessingSettings,\n            showGrid,\n            wireframe,\n            groundType,\n            selectedObjects: selectedObjects.map((obj)=>obj.uuid),\n            // Store object states for transforms and materials\n            objectStates: sceneObjects.map((obj)=>({\n                    uuid: obj.uuid,\n                    name: obj.name,\n                    position: obj.position.toArray(),\n                    rotation: obj.rotation.toArray(),\n                    scale: obj.scale.toArray(),\n                    material: obj.material ? {\n                        type: obj.material.type,\n                        color: obj.material.color ? obj.material.color.getHexString() : \"ffffff\",\n                        roughness: obj.material.roughness,\n                        metalness: obj.material.metalness,\n                        clearcoat: obj.material.clearcoat,\n                        clearcoatRoughness: obj.material.clearcoatRoughness,\n                        wireframe: obj.material.wireframe,\n                        transparent: obj.material.transparent,\n                        opacity: obj.material.opacity,\n                        envMapIntensity: obj.material.envMapIntensity\n                    } : null\n                }))\n        }));\n    }, [\n        selectedModel,\n        uploadedModel,\n        lights,\n        envPreset,\n        bgColor,\n        showEnvironment,\n        showLightSpheres,\n        customHdri,\n        envIntensity,\n        envBlur,\n        envRotation,\n        postProcessingEnabled,\n        postProcessingSettings,\n        showGrid,\n        wireframe,\n        groundType,\n        selectedObjects,\n        sceneObjects\n    ]);\n    // Add initial state to history when component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (history.length === 0) {\n            console.log(\"[Initial] Adding initial state to history\");\n            const initialState = getFullConfig();\n            setHistory([\n                initialState\n            ]);\n            setHistoryIndex(0);\n        }\n    }, [\n        history.length,\n        getFullConfig\n    ]);\n    // Add a new state to the history\n    const addToHistory = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (isUndoRedoActionRef.current || skipHistoryTrackingRef.current) {\n            console.log(\"[addToHistory] Skipping - undo/redo action in progress\");\n            return;\n        }\n        const newState = getFullConfig();\n        // Check if the new state is different from the current state\n        if (history.length > 0 && historyIndex >= 0) {\n            const currentState = history[historyIndex];\n            if (JSON.stringify(currentState) === JSON.stringify(newState)) {\n                console.log(\"[addToHistory] Skipping - state unchanged\");\n                return;\n            }\n        }\n        const newHistory = history.slice(0, historyIndex + 1);\n        setHistory([\n            ...newHistory,\n            newState\n        ]);\n        const newIndex = historyIndex + 1;\n        setHistoryIndex(newIndex);\n        console.log(\"[addToHistory] Added state. New historyIndex:\", newIndex, \"History length:\", newHistory.length + 1, \"Can redo:\", newIndex < newHistory.length);\n    }, [\n        history,\n        historyIndex,\n        getFullConfig\n    ]);\n    const pendingSelectedObjectsUUIDs = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Define forceUpdate and version setters early so they can be used in restoreConfig\n    const [_, setForceUpdate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const forceUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>setForceUpdate((f)=>f + 1), []);\n    const [transformVersion, setTransformVersion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [materialVersion, setMaterialVersion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const restoreConfig = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((config)=>{\n        setSelectedModel(config.selectedModel);\n        setUploadedModel(config.uploadedModel);\n        setLights(config.lights);\n        setEnvPreset(config.envPreset);\n        setBgColor(config.bgColor);\n        setShowEnvironment(config.showEnvironment);\n        setShowLightSpheres(config.showLightSpheres);\n        setCustomHdri(config.customHdri);\n        setEnvIntensity(config.envIntensity);\n        setEnvBlur(config.envBlur);\n        setEnvRotation(config.envRotation);\n        setPostProcessingEnabled(config.postProcessingEnabled);\n        setPostProcessingSettings(config.postProcessingSettings);\n        setShowGrid(config.showGrid);\n        setWireframe(config.wireframe);\n        setGroundType(config.groundType);\n        // Restore object states (transforms and materials)\n        if (config.objectStates && sceneObjects.length > 0) {\n            config.objectStates.forEach((savedState)=>{\n                const obj = sceneObjects.find((o)=>o.uuid === savedState.uuid);\n                if (obj) {\n                    // Restore transform\n                    obj.position.fromArray(savedState.position);\n                    obj.rotation.fromArray(savedState.rotation);\n                    obj.scale.fromArray(savedState.scale);\n                    // Restore material properties\n                    if (savedState.material && obj.material) {\n                        const material = obj.material;\n                        if (material.color) {\n                            material.color.setHex(parseInt(savedState.material.color, 16));\n                        }\n                        if (savedState.material.roughness !== undefined) {\n                            material.roughness = savedState.material.roughness;\n                        }\n                        if (savedState.material.metalness !== undefined) {\n                            material.metalness = savedState.material.metalness;\n                        }\n                        if (savedState.material.clearcoat !== undefined) {\n                            material.clearcoat = savedState.material.clearcoat;\n                        }\n                        if (savedState.material.clearcoatRoughness !== undefined) {\n                            material.clearcoatRoughness = savedState.material.clearcoatRoughness;\n                        }\n                        if (savedState.material.wireframe !== undefined) {\n                            material.wireframe = savedState.material.wireframe;\n                        }\n                        if (savedState.material.transparent !== undefined) {\n                            material.transparent = savedState.material.transparent;\n                        }\n                        if (savedState.material.opacity !== undefined) {\n                            material.opacity = savedState.material.opacity;\n                        }\n                        if (savedState.material.envMapIntensity !== undefined) {\n                            material.envMapIntensity = savedState.material.envMapIntensity;\n                        }\n                        material.needsUpdate = true;\n                    }\n                }\n            });\n            // Force update to reflect changes\n            forceUpdate();\n            // Don't increment versions during restore to avoid triggering history loop\n            if (!isUndoRedoActionRef.current) {\n                setMaterialVersion((v)=>v + 1);\n                setTransformVersion((v)=>v + 1);\n            }\n        }\n        // Store selectedObjects UUIDs to be restored after sceneObjects update\n        pendingSelectedObjectsUUIDs.current = config.selectedObjects || [];\n        console.log(\"[restoreConfig] Restoring config\", config);\n    }, [\n        sceneObjects,\n        forceUpdate,\n        setMaterialVersion,\n        setTransformVersion\n    ]);\n    // After sceneObjects update, restore selectedObjects from UUIDs\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (pendingSelectedObjectsUUIDs.current && sceneObjects.length > 0) {\n            const uuids = pendingSelectedObjectsUUIDs.current;\n            const newSelected = sceneObjects.filter((obj)=>uuids.includes(obj.uuid));\n            setSelectedObjects(newSelected);\n            pendingSelectedObjectsUUIDs.current = null;\n            console.log(\"[restoreConfig] Restored selectedObjects\", newSelected);\n        }\n    }, [\n        sceneObjects\n    ]);\n    // Undo action\n    const undoAction = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        // Prevent rapid undo operations\n        if (isUndoRedoInProgressRef.current) {\n            console.log(\"[undoAction] Skipping - undo/redo already in progress\");\n            return;\n        }\n        console.log(\"[undoAction] Current historyIndex:\", historyIndex, \"History length:\", history.length);\n        if (historyIndex > 0) {\n            console.log(\"[undoAction] Performing undo to index:\", historyIndex - 1);\n            // Set all flags to prevent interference\n            isUndoRedoInProgressRef.current = true;\n            isUndoRedoActionRef.current = true;\n            skipHistoryTrackingRef.current = true;\n            const prevConfig = history[historyIndex - 1];\n            restoreConfig(prevConfig);\n            setHistoryIndex(historyIndex - 1);\n            // Reset flags after operation completes\n            setTimeout(()=>{\n                isUndoRedoActionRef.current = false;\n                skipHistoryTrackingRef.current = false;\n                isUndoRedoInProgressRef.current = false;\n                console.log(\"[undoAction] Undo completed, all flags reset\");\n            }, 1000);\n        } else {\n            console.log(\"[undoAction] Cannot undo - at beginning of history\");\n        }\n    }, [\n        historyIndex,\n        history,\n        restoreConfig\n    ]);\n    // Redo action\n    const redoAction = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        // Prevent rapid redo operations\n        if (isUndoRedoInProgressRef.current) {\n            console.log(\"[redoAction] Skipping - undo/redo already in progress\");\n            return;\n        }\n        debugHistory();\n        console.log(\"[redoAction] Current historyIndex:\", historyIndex, \"History length:\", history.length);\n        if (historyIndex < history.length - 1) {\n            console.log(\"[redoAction] Performing redo to index:\", historyIndex + 1);\n            // Set all flags to prevent interference\n            isUndoRedoInProgressRef.current = true;\n            isUndoRedoActionRef.current = true;\n            skipHistoryTrackingRef.current = true;\n            const nextConfig = history[historyIndex + 1];\n            console.log(\"[redoAction] Next config:\", nextConfig);\n            restoreConfig(nextConfig);\n            setHistoryIndex(historyIndex + 1);\n            // Reset flags after operation completes\n            setTimeout(()=>{\n                isUndoRedoActionRef.current = false;\n                skipHistoryTrackingRef.current = false;\n                isUndoRedoInProgressRef.current = false;\n                console.log(\"[redoAction] Redo completed, all flags reset\");\n            }, 1000);\n        } else {\n            console.log(\"[redoAction] Cannot redo - at end of history\");\n        }\n    }, [\n        historyIndex,\n        history,\n        restoreConfig,\n        debugHistory\n    ]);\n    // Track changes to objects and lights for history\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const trackChanges = ()=>{\n            if (selectedObjects.length > 0 || lights.length > 0) {\n                addToHistory({\n                    selectedObjects,\n                    lights\n                });\n            }\n        };\n        // Debounce to avoid too many history entries\n        const timeoutId = setTimeout(trackChanges, 500);\n        return ()=>clearTimeout(timeoutId);\n    }, [\n        selectedObjects,\n        lights,\n        addToHistory\n    ]);\n    const handleObjectClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e)=>{\n        e.stopPropagation();\n        // If placing decal, let the decal placement handler do its job (triggered by model's onClick)\n        if (isPlacingDecal) {\n            return;\n        }\n        console.log(\"Object clicked:\", e.object.name, e.object);\n        if (e.shiftKey) {\n            setSelectedObjects((prev)=>{\n                // Ensure prev is always an array\n                const prevArray = Array.isArray(prev) ? prev : [\n                    prev\n                ].filter(Boolean);\n                if (prevArray.includes(e.object)) {\n                    return prevArray.filter((obj)=>obj !== e.object);\n                }\n                return [\n                    ...prevArray,\n                    e.object\n                ];\n            });\n        } else {\n            setSelectedObjects([\n                e.object\n            ]);\n        }\n    }, [\n        isPlacingDecal,\n        setSelectedObjects\n    ]);\n    // Add debugging for selectedObjects changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"ExperienceNew - selectedObjects changed:\", selectedObjects);\n        console.log(\"ExperienceNew - selectedObjects length:\", selectedObjects === null || selectedObjects === void 0 ? void 0 : selectedObjects.length);\n    }, [\n        selectedObjects\n    ]);\n    // Add debugging for sceneObjects changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"ExperienceNew - sceneObjects changed:\", sceneObjects);\n        console.log(\"ExperienceNew - sceneObjects length:\", sceneObjects === null || sceneObjects === void 0 ? void 0 : sceneObjects.length);\n        console.log(\"ExperienceNew - sceneObjects details:\", sceneObjects === null || sceneObjects === void 0 ? void 0 : sceneObjects.map((obj)=>({\n                name: obj === null || obj === void 0 ? void 0 : obj.name,\n                type: obj === null || obj === void 0 ? void 0 : obj.type,\n                userData: obj === null || obj === void 0 ? void 0 : obj.userData\n            })));\n    }, [\n        sceneObjects\n    ]);\n    const handlePointerMissed = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        // If in decal placement mode, a \"missed\" click (on the canvas background)\n        // should probably cancel placement mode, or do nothing to allow model click-through.\n        // For now, let's prevent it from deselecting objects if placing decal.\n        if (isPlacingDecal) {\n            // setIsPlacingDecal(false); // Optionally, cancel placement on missed click\n            return;\n        }\n        setSelectedObjects([]);\n        setSelectedLight(null);\n    // Do not close the rightPanelTab here, as that's usually handled by clicking outside the panel itself.\n    }, [\n        isPlacingDecal,\n        setIsPlacingDecal\n    ]);\n    // Add the selection group to the scene\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // This effect runs once to add the selection group to the scene\n        return ()=>{\n            // Clean up on unmount\n            if (selectionGroupRef.current && selectionGroupRef.current.parent) {\n                selectionGroupRef.current.parent.remove(selectionGroupRef.current);\n            }\n        };\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Ensure selectedObjects is always an array\n        const objectsArray = Array.isArray(selectedObjects) ? selectedObjects : [\n            selectedObjects\n        ].filter(Boolean);\n        while(selectionGroupRef.current.children.length > 0){\n            selectionGroupRef.current.remove(selectionGroupRef.current.children[0]);\n        }\n        if (objectsArray.length > 0) {\n            const center = new three__WEBPACK_IMPORTED_MODULE_27__.Vector3();\n            objectsArray.forEach((obj)=>{\n                center.add(obj.position);\n            });\n            center.divideScalar(objectsArray.length);\n            selectionGroupRef.current.position.copy(center);\n            objectsArray.forEach((obj)=>{\n                const clone = obj.clone();\n                // Make the clone invisible while keeping its geometry for transforms\n                clone.traverse((child)=>{\n                    if (child.isMesh) {\n                        child.visible = false;\n                    }\n                });\n                clone.position.sub(center);\n                selectionGroupRef.current.add(clone);\n            });\n        }\n    }, [\n        selectedObjects\n    ]);\n    const updateObjectsFromGroup = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        // Ensure selectedObjects is always an array\n        const objectsArray = Array.isArray(selectedObjects) ? selectedObjects : [\n            selectedObjects\n        ].filter(Boolean);\n        if (objectsArray.length > 0 && selectionGroupRef.current) {\n            objectsArray.forEach((obj, index)=>{\n                if (selectionGroupRef.current.children[index]) {\n                    const worldPos = new three__WEBPACK_IMPORTED_MODULE_27__.Vector3();\n                    selectionGroupRef.current.children[index].getWorldPosition(worldPos);\n                    obj.position.copy(worldPos);\n                    obj.rotation.copy(selectionGroupRef.current.rotation);\n                    obj.scale.copy(selectionGroupRef.current.scale);\n                }\n            });\n        }\n    }, [\n        selectedObjects\n    ]);\n    const handleSceneUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((scene)=>{\n        var _scene_children;\n        console.log(\"handleSceneUpdate called with scene:\", scene);\n        console.log(\"Scene type:\", scene.type);\n        console.log(\"Scene children count:\", (_scene_children = scene.children) === null || _scene_children === void 0 ? void 0 : _scene_children.length);\n        setSceneObjects([]);\n        setSelectedObjects([]);\n        const stats = (0,_utils_ArtistTool_sceneUtils__WEBPACK_IMPORTED_MODULE_16__.calculateModelStats)(scene);\n        setModelStats(stats);\n        (0,_utils_ArtistTool_sceneUtils__WEBPACK_IMPORTED_MODULE_16__.setShadowsOnModel)(scene);\n        const objects = [];\n        const initialObjects = [];\n        scene.traverse((object)=>{\n            console.log(\"Traversing object:\", object.name, object.type, \"isMesh:\", object.isMesh, \"userData:\", object.userData);\n            if (object.isMesh) {\n                if (!object.name) {\n                    object.name = \"Mesh_\".concat(object.id);\n                }\n                console.log(\"Adding mesh to objects:\", object.name, \"userData:\", object.userData);\n                objects.push(object);\n                initialObjects.push({\n                    id: object.uuid,\n                    name: object.name,\n                    position: object.position.clone(),\n                    rotation: object.rotation.clone(),\n                    scale: object.scale.clone(),\n                    material: object.material ? {\n                        color: object.material.color ? object.material.color.getHex() : 0xffffff,\n                        roughness: object.material.roughness,\n                        metalness: object.material.metalness,\n                        wireframe: object.material.wireframe,\n                        transparent: object.material.transparent,\n                        opacity: object.material.opacity\n                    } : null\n                });\n            }\n        });\n        console.log(\"Found\", objects.length, \"mesh objects:\", objects.map((obj)=>({\n                name: obj.name,\n                userData: obj.userData\n            })));\n        modelInitialStateRef.current.objects = initialObjects;\n        setSceneObjects(objects);\n    }, []);\n    // Add this effect to detect model changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedModel !== lastSelectedModel) {\n            // Clear the scene objects when model changes\n            setSceneObjects([]);\n            setSelectedObjects([]);\n            setLastSelectedModel(selectedModel);\n        }\n    }, [\n        selectedModel,\n        lastSelectedModel\n    ]);\n    const handleLightMoved = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((lightId, newPosition)=>{\n        setLightPositionHistory((prev)=>({\n                ...prev,\n                [lightId]: [\n                    ...prev[lightId] || [],\n                    newPosition\n                ]\n            }));\n    }, []);\n    const undoLightMove = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((lightId)=>{\n        setLightPositionHistory((prev)=>{\n            const history = prev[lightId] || [];\n            if (history.length <= 1) return prev;\n            const newHistory = history.slice(0, -1);\n            const lastPosition = newHistory[newHistory.length - 1];\n            if (lightRefs.current[lightId]) {\n                lightRefs.current[lightId].position.copy(lastPosition);\n            }\n            return {\n                ...prev,\n                [lightId]: newHistory\n            };\n        });\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleKeyDown = (event)=>{\n            // Handle undo/redo keyboard shortcuts\n            if (event.ctrlKey) {\n                if (event.key === \"z\" && !event.shiftKey) {\n                    event.preventDefault();\n                    undoAction();\n                    return;\n                }\n                if (event.key === \"y\" || event.key === \"z\" && event.shiftKey) {\n                    event.preventDefault();\n                    redoAction();\n                    return;\n                }\n            }\n            // Handle Escape key for decal placement\n            if (event.key === \"Escape\" && isPlacingDecal) {\n                setIsPlacingDecal(false);\n                return;\n            }\n            // Handle light undo\n            if (event.ctrlKey && event.key === \"z\" && selectedLight) {\n                undoLightMove(selectedLight);\n            }\n            // Handle transform mode shortcuts\n            if (selectedObjects.length > 0) {\n                switch(event.key){\n                    case \"g\":\n                        setTransformMode(\"translate\");\n                        break;\n                    case \"r\":\n                        setTransformMode(\"rotate\");\n                        break;\n                    case \"s\":\n                        setTransformMode(\"scale\");\n                        break;\n                    case \"Escape\":\n                        setSelectedObjects([]);\n                        break;\n                }\n            }\n            // Handle select all\n            if (event.ctrlKey && event.key === \"a\") {\n                event.preventDefault();\n                if (sceneObjects.length > 0) {\n                    setSelectedObjects([\n                        ...sceneObjects\n                    ]);\n                }\n            }\n        };\n        window.addEventListener(\"keydown\", handleKeyDown);\n        return ()=>window.removeEventListener(\"keydown\", handleKeyDown);\n    }, [\n        selectedLight,\n        undoLightMove,\n        sceneObjects,\n        selectedObjects.length,\n        undoAction,\n        redoAction,\n        isPlacingDecal,\n        setIsPlacingDecal\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        return ()=>{\n            if (uploadedModel) {\n                URL.revokeObjectURL(uploadedModel);\n            }\n            if (customHdri) {\n                URL.revokeObjectURL(customHdri);\n            }\n        };\n    }, [\n        uploadedModel,\n        customHdri\n    ]);\n    const handleCameraView = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((view)=>{\n        (0,_utils_ArtistTool_cameraUtils__WEBPACK_IMPORTED_MODULE_14__.setCameraView)(orbitControlsRef.current, view);\n    }, []);\n    // Add the panelRef to ExperienceNew instead of in Toolbar\n    const panelRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [activeAnimation, setActiveAnimation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [animationStartTime, setAnimationStartTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const requestRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    // Animation loop handler\n    const animateCamera = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((timestamp)=>{\n        if (!activeAnimation || !orbitControlsRef.current) {\n            return;\n        }\n        // Calculate animation progress (0 to 1)\n        const animation = _animations_CameraAnimations__WEBPACK_IMPORTED_MODULE_17__.predefinedAnimations.find((a)=>a.id === activeAnimation);\n        if (!animation) return;\n        if (animationStartTime === 0) {\n            setAnimationStartTime(timestamp);\n        }\n        const elapsed = timestamp - animationStartTime;\n        const progress = elapsed % animation.duration / animation.duration;\n        // Get the target position (center of selected objects or scene center)\n        const target = new three__WEBPACK_IMPORTED_MODULE_27__.Vector3(0, 0, 0);\n        if (selectedObjects.length > 0) {\n            // Calculate center of selected objects\n            const objectsArray = Array.isArray(selectedObjects) ? selectedObjects : [\n                selectedObjects\n            ].filter(Boolean);\n            if (objectsArray.length > 0) {\n                const center = new three__WEBPACK_IMPORTED_MODULE_27__.Vector3();\n                objectsArray.forEach((obj)=>{\n                    center.add(obj.position);\n                });\n                center.divideScalar(objectsArray.length);\n                target.copy(center);\n            }\n        }\n        // Animate camera using the animation function\n        animation.animate(orbitControlsRef.current.object, orbitControlsRef.current, progress, target);\n        requestRef.current = requestAnimationFrame(animateCamera);\n    }, [\n        activeAnimation,\n        orbitControlsRef,\n        animationStartTime,\n        selectedObjects\n    ]);\n    // Setup animation loop\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (activeAnimation) {\n            setAnimationStartTime(0);\n            requestRef.current = requestAnimationFrame(animateCamera);\n            // Disable orbit controls during animation\n            if (orbitControlsRef.current) {\n                orbitControlsRef.current.enabled = false;\n            }\n        } else {\n            // Re-enable orbit controls when animation stops\n            if (orbitControlsRef.current && selectedObjects.length === 0) {\n                orbitControlsRef.current.enabled = true;\n            }\n        }\n        return ()=>{\n            if (requestRef.current) {\n                cancelAnimationFrame(requestRef.current);\n            }\n        };\n    }, [\n        activeAnimation,\n        animateCamera,\n        selectedObjects\n    ]);\n    // Handle animation playback from Outliner\n    const handlePlayAnimation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((animationId)=>{\n        setActiveAnimation(animationId);\n    }, []);\n    const getEnvironmentPreset = ()=>{\n        return envPreset;\n    };\n    // Reset function\n    const handleReset = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        // Check if there's a saved scene in localStorage\n        const savedScene = localStorage.getItem(\"agape_saved_scene\");\n        if (savedScene) {\n            const confirmReset = window.confirm(\"There is a saved scene in your browser storage. Resetting will clear this saved scene. Do you want to continue?\");\n            if (!confirmReset) {\n                return;\n            }\n            // Clear the saved scene from localStorage\n            localStorage.removeItem(\"agape_saved_scene\");\n        }\n        console.log(\"Resetting scene to initial state\");\n        setSceneObjects([]);\n        setSelectedObjects([]);\n        // Reset lights to default configuration\n        setLights([\n            {\n                id: \"defaultDirectional\",\n                type: \"directional\",\n                position: [\n                    2.5,\n                    5.5,\n                    5\n                ],\n                intensity: 1.0,\n                color: \"#ffffff\",\n                name: \"Main Directional Light\",\n                castShadow: true,\n                helperVisible: false\n            },\n            {\n                id: \"keyLight\",\n                type: \"point\",\n                position: [\n                    5,\n                    5,\n                    5\n                ],\n                intensity: 8.0,\n                color: \"#ffffff\",\n                name: \"Key Light\"\n            },\n            {\n                id: \"fillLight\",\n                type: \"point\",\n                position: [\n                    -5,\n                    5,\n                    -5\n                ],\n                intensity: 4.0,\n                color: \"#ffffff\",\n                name: \"Fill Light\"\n            },\n            {\n                id: \"backLight\",\n                type: \"point\",\n                position: [\n                    0,\n                    5,\n                    -5\n                ],\n                intensity: 2.0,\n                color: \"#ffffff\",\n                name: \"Back Light\"\n            }\n        ]);\n        // Reset environment settings\n        setEnvPreset(\"hdri_15\");\n        setBgColor(\"#202020\");\n        setShowEnvironment(false);\n        setShowLightSpheres(true);\n        setCustomHdri(null);\n        setEnvIntensity(1.5);\n        setEnvBlur(0.35);\n        setEnvRotation(0);\n        // Reset post-processing settings\n        setPostProcessingEnabled(false);\n        setPostProcessingSettings({\n            autofocus: {\n                enabled: false,\n                bokehScale: 18\n            },\n            bloom: {\n                enabled: true,\n                intensity: 0.12,\n                threshold: 0.8,\n                radius: 0.4\n            },\n            dof: {\n                enabled: false,\n                focusDistance: 0,\n                aperture: 0.01,\n                bokehScale: 3\n            },\n            vignette: {\n                enabled: true,\n                darkness: 0.5,\n                offset: 0.1\n            }\n        });\n        // Reset scene controls\n        setShowGrid(true);\n        setWireframe(false);\n        setGroundType(\"none\");\n        // Reset camera position\n        if (orbitControlsRef.current) {\n            orbitControlsRef.current.object.position.set(5, 2, 5);\n            orbitControlsRef.current.target.set(0, 1, 0);\n            orbitControlsRef.current.update();\n        }\n        // Reset transform state\n        setTransformMode(\"translate\");\n        setSelectedLight(null);\n        // Reset material shaders\n        setUseDiamondShader(false);\n        setUsePremiumWhiteGold(false);\n        setUsePremiumRoseGold(false);\n        // Clear history\n        setHistory([]);\n        setHistoryIndex(-1);\n        setLightPositionHistory({});\n        // Reset model stats\n        setModelStats({\n            vertices: 0,\n            triangles: 0,\n            materials: 0,\n            animations: 0\n        });\n        // Reset right panel tab\n        setRightPanelTab(null);\n        // Reset active animation\n        setActiveAnimation(null);\n        setAnimationStartTime(0);\n        if (requestRef.current) {\n            cancelAnimationFrame(requestRef.current);\n        }\n        // Clear any custom HDRI or uploaded model URLs\n        if (uploadedModel) {\n            URL.revokeObjectURL(uploadedModel);\n        }\n        if (customHdri) {\n            URL.revokeObjectURL(customHdri);\n        }\n        // Force a model reload by setting selectedModel to null first\n        setSelectedModel(null);\n        // Then set it back to uploaded after a small delay\n        setTimeout(()=>{\n            setSelectedModel(\"uploaded\");\n        }, 100);\n    }, []);\n    const screenshotRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const captureScreenshot = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (screenshotRef.current) {\n            console.log(\"Taking screenshot\");\n            screenshotRef.current();\n        } else {\n            console.error(\"Screenshot function not available yet\");\n        }\n    }, []);\n    // Load saved scene from localStorage on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadSavedScene = ()=>{\n            const savedScene = localStorage.getItem(\"agape_saved_scene\");\n            if (savedScene) {\n                try {\n                    const sceneConfig = JSON.parse(savedScene);\n                    console.log(\"Loading saved scene:\", sceneConfig);\n                    // Only load the saved configuration on initial load\n                    if (!lastSelectedModel) {\n                        console.log(\"Initial load - setting saved model:\", sceneConfig.model);\n                        setSelectedModel(\"uploaded\"); // Always use uploaded for S3 models\n                        // Load the rest of the saved scene configuration\n                        setLights(sceneConfig.lights);\n                        setEnvPreset(sceneConfig.environment.preset);\n                        setBgColor(sceneConfig.environment.bgColor);\n                        setShowEnvironment(sceneConfig.environment.showEnvironment);\n                        setShowLightSpheres(sceneConfig.environment.showLightSpheres);\n                        setEnvIntensity(sceneConfig.environment.intensity);\n                        setEnvBlur(sceneConfig.environment.blur);\n                        setEnvRotation(sceneConfig.environment.rotation);\n                        setPostProcessingEnabled(sceneConfig.postProcessing.enabled);\n                        setPostProcessingSettings(sceneConfig.postProcessing.settings);\n                        setShowGrid(sceneConfig.sceneControls.showGrid);\n                        setWireframe(sceneConfig.sceneControls.wireframe);\n                        setGroundType(sceneConfig.sceneControls.groundType);\n                        // Restore camera position after a short delay to ensure scene is loaded\n                        setTimeout(()=>{\n                            if (orbitControlsRef.current && sceneConfig.camera) {\n                                orbitControlsRef.current.object.position.fromArray(sceneConfig.camera.position);\n                                orbitControlsRef.current.target.fromArray(sceneConfig.camera.target);\n                                orbitControlsRef.current.update();\n                            }\n                        }, 1000);\n                    } else if (configRef.current) {\n                        // For explicit loads with configRef, do a complete load\n                        console.log(\"Explicit load with configRef\");\n                        handleLoadScene(sceneConfig);\n                    }\n                    // Create a function to restore materials\n                    const restoreMaterials = ()=>{\n                        if (!sceneConfig.materials || !sceneObjects.length) {\n                            console.log(\"No materials to restore or scene objects not ready\");\n                            return;\n                        }\n                        console.log(\"Attempting to restore materials for objects:\", sceneObjects);\n                        console.log(\"Saved materials:\", sceneConfig.materials);\n                        const savedMaterialsMap = new Map(sceneConfig.materials.map((m)=>[\n                                m.name,\n                                m.material\n                            ]));\n                        sceneObjects.forEach((obj)=>{\n                            if (obj.material && savedMaterialsMap.has(obj.name)) {\n                                const savedMaterial = savedMaterialsMap.get(obj.name);\n                                console.log(\"Restoring material for object:\", obj.name, savedMaterial);\n                                (0,_utils_ArtistTool_materialUtils__WEBPACK_IMPORTED_MODULE_15__.applyMaterialToObject)(obj, savedMaterial);\n                            } else {\n                                console.log(\"Could not find saved material for object:\", obj.name);\n                            }\n                        });\n                    };\n                    // Set up an interval to check for scene objects\n                    const checkInterval = setInterval(()=>{\n                        if (sceneObjects.length > 0) {\n                            clearInterval(checkInterval);\n                            // Add a small delay to ensure all materials are properly initialized\n                            setTimeout(restoreMaterials, 500);\n                        }\n                    }, 100);\n                    // Clean up interval after 5 seconds if scene objects never become available\n                    setTimeout(()=>{\n                        clearInterval(checkInterval);\n                    }, 5000);\n                    // Restore camera position after a short delay to ensure scene is loaded\n                    setTimeout(()=>{\n                        if (orbitControlsRef.current && sceneConfig.camera) {\n                            orbitControlsRef.current.object.position.fromArray(sceneConfig.camera.position);\n                            orbitControlsRef.current.target.fromArray(sceneConfig.camera.target);\n                            orbitControlsRef.current.update();\n                        }\n                    }, 1000);\n                } catch (error) {\n                    console.error(\"Error loading saved scene:\", error);\n                }\n            }\n        };\n        // Load saved scene immediately on mount\n        loadSavedScene();\n        // Also set up an interval to check for scene readiness\n        const checkSceneReady = setInterval(()=>{\n            if (sceneRef.current && orbitControlsRef.current) {\n                clearInterval(checkSceneReady);\n                loadSavedScene();\n            }\n        }, 100);\n        return ()=>clearInterval(checkSceneReady);\n    }, [\n        sceneObjects,\n        selectedModel\n    ]);\n    const [configState, setConfigState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (sceneObjects.length > 0 && configState && configState.materials) {\n            const applyMaterials = ()=>{\n                console.log(\"Applying materials to scene objects:\", sceneObjects);\n                console.log(\"Saved materials configuration:\", configState.materials);\n                sceneObjects.forEach((obj)=>{\n                    const materialConfig = configState.materials.find((m)=>m.name === obj.name || m.id === obj.uuid);\n                    if (materialConfig && materialConfig.material) {\n                        console.log(\"Found material config for object:\", obj.name, materialConfig.material);\n                        (0,_utils_ArtistTool_materialUtils__WEBPACK_IMPORTED_MODULE_15__.applyMaterialToObject)(obj, materialConfig.material);\n                    } else {\n                        console.log(\"No material config found for object:\", obj.name);\n                    }\n                });\n            };\n            // Add a small delay to ensure all materials are properly initialized\n            const timeoutId = setTimeout(applyMaterials, 500);\n            return ()=>clearTimeout(timeoutId);\n        }\n    }, [\n        sceneObjects,\n        configState\n    ]);\n    const handleLoadScene = (config)=>{\n        try {\n            console.log(\"Loading scene configuration:\", config);\n            // Store the config in the ref\n            configRef.current = config;\n            workingConfigRef.current = config; // Always keep this in sync with last applied config\n            setConfigState(config); // <-- keep in sync for effect\n            // Clear scene objects and selected objects first\n            setSceneObjects([]);\n            setSelectedObjects([]);\n            setLastSelectedModel(null);\n            // Restore scene settings\n            setSelectedModel(\"uploaded\");\n            setLights(config.lights);\n            setEnvPreset(config.environment.preset);\n            setBgColor(config.environment.bgColor);\n            setShowEnvironment(config.environment.showEnvironment);\n            setShowLightSpheres(config.environment.showLightSpheres);\n            setEnvIntensity(config.environment.intensity);\n            setEnvBlur(config.environment.blur);\n            setEnvRotation(config.environment.rotation);\n            setPostProcessingEnabled(config.postProcessing.enabled);\n            setPostProcessingSettings(config.postProcessing.settings);\n            setShowGrid(config.sceneControls.showGrid);\n            setWireframe(config.sceneControls.wireframe);\n            setGroundType(config.sceneControls.groundType);\n            // Wait for the scene to update before applying camera position\n            setTimeout(()=>{\n                if (orbitControlsRef.current && config.camera) {\n                    orbitControlsRef.current.object.position.fromArray(config.camera.position);\n                    orbitControlsRef.current.target.fromArray(config.camera.target);\n                    orbitControlsRef.current.update();\n                }\n            }, 1000);\n            // Restore materials after sceneObjects are ready\n            const restoreMaterials = ()=>{\n                if (!config.materials || !sceneObjects.length) {\n                    console.log(\"No materials to restore or scene objects not ready\");\n                    return;\n                }\n                console.log(\"Restoring materials for objects:\", sceneObjects);\n                const savedMaterialsMap = new Map(config.materials.map((m)=>[\n                        m.name,\n                        m.material\n                    ]));\n                sceneObjects.forEach((obj)=>{\n                    if (obj.material && savedMaterialsMap.has(obj.name)) {\n                        const savedMaterial = savedMaterialsMap.get(obj.name);\n                        console.log(\"Restoring material for object:\", obj.name, savedMaterial);\n                        (0,_utils_ArtistTool_materialUtils__WEBPACK_IMPORTED_MODULE_15__.applyMaterialToObject)(obj, savedMaterial);\n                    } else {\n                        console.log(\"Could not find saved material for object:\", obj.name);\n                    }\n                });\n            };\n            if (sceneObjects.length > 0) {\n                setTimeout(restoreMaterials, 500);\n            } else {\n                const checkInterval = setInterval(()=>{\n                    if (sceneObjects.length > 0) {\n                        clearInterval(checkInterval);\n                        setTimeout(restoreMaterials, 500);\n                    }\n                }, 100);\n                setTimeout(()=>{\n                    clearInterval(checkInterval);\n                }, 5000);\n            }\n        } catch (error) {\n            console.error(\"Error loading scene configuration:\", error);\n        }\n    };\n    const handleSaveScene = async (sceneName, description, saveType)=>{\n        // Use workingConfigRef if it exists and matches the current state, otherwise build from state\n        let sceneConfig;\n        if (workingConfigRef.current) {\n            // Use a shallow compare for top-level keys (or always use workingConfigRef for now)\n            sceneConfig = {\n                ...workingConfigRef.current,\n                name: sceneName,\n                timestamp: new Date().toISOString()\n            };\n        } else {\n            var _orbitControlsRef_current, _orbitControlsRef_current1;\n            // fallback to building from state\n            sceneConfig = {\n                name: sceneName,\n                timestamp: new Date().toISOString(),\n                model: selectedModel,\n                lights: lights.map((light)=>{\n                    var _lightRefs_current_light_id;\n                    return {\n                        ...light,\n                        position: ((_lightRefs_current_light_id = lightRefs.current[light.id]) === null || _lightRefs_current_light_id === void 0 ? void 0 : _lightRefs_current_light_id.position.toArray()) || light.position\n                    };\n                }),\n                environment: {\n                    preset: envPreset,\n                    intensity: envIntensity,\n                    blur: envBlur,\n                    rotation: envRotation,\n                    showEnvironment,\n                    bgColor,\n                    customHdri\n                },\n                postProcessing: {\n                    enabled: postProcessingEnabled,\n                    settings: postProcessingSettings\n                },\n                sceneControls: {\n                    showGrid,\n                    wireframe,\n                    groundType,\n                    showLightSpheres\n                },\n                camera: {\n                    position: (_orbitControlsRef_current = orbitControlsRef.current) === null || _orbitControlsRef_current === void 0 ? void 0 : _orbitControlsRef_current.object.position.toArray(),\n                    target: (_orbitControlsRef_current1 = orbitControlsRef.current) === null || _orbitControlsRef_current1 === void 0 ? void 0 : _orbitControlsRef_current1.target.toArray()\n                },\n                materials: sceneObjects.map((obj)=>{\n                    const material = obj.material;\n                    if (!material) return {\n                        id: obj.uuid,\n                        name: obj.name,\n                        material: null\n                    };\n                    return {\n                        id: obj.uuid,\n                        name: obj.name,\n                        material: {\n                            type: material.type,\n                            color: material.color ? material.color.getHexString() : \"ffffff\",\n                            roughness: material.roughness,\n                            metalness: material.metalness,\n                            wireframe: material.wireframe,\n                            transparent: material.transparent,\n                            opacity: material.opacity,\n                            envMapIntensity: material.envMapIntensity,\n                            ...material.type === \"MeshRefractionMaterial\" && {\n                                aberrationStrength: material.aberrationStrength,\n                                toneMapped: material.toneMapped,\n                                ior: material.ior,\n                                colorEnvMapRotY: material.colorEnvMapRotY\n                            }\n                        }\n                    };\n                })\n            };\n        }\n        if (saveType === \"local\") {\n            // Save to localStorage\n            localStorage.setItem(\"agape_saved_scene\", JSON.stringify(sceneConfig));\n        } else if (saveType === \"db\") {\n            // Save to DB (cloud)\n            try {\n                const version = await (0,_lib_actions_version_actions__WEBPACK_IMPORTED_MODULE_23__.createVersion)({\n                    userId: user.id,\n                    workspaceId: project._id,\n                    name: sceneName,\n                    config: sceneConfig,\n                    description: description\n                });\n                console.log(\"version\", version);\n                if (version) {\n                    addVersion(project._id, version);\n                }\n                sonner__WEBPACK_IMPORTED_MODULE_24__.toast.success(\"Scene saved to cloud!\");\n            } catch (err) {\n                sonner__WEBPACK_IMPORTED_MODULE_24__.toast.error(\"Failed to save scene to cloud\");\n            }\n        } else {\n            // Export as file\n            const blob = new Blob([\n                JSON.stringify(sceneConfig, null, 2)\n            ], {\n                type: \"application/json\"\n            });\n            const url = URL.createObjectURL(blob);\n            const a = document.createElement(\"a\");\n            a.href = url;\n            a.download = \"\".concat(sceneName.replace(/\\s+/g, \"_\"), \".json\");\n            document.body.appendChild(a);\n            a.click();\n            document.body.removeChild(a);\n            URL.revokeObjectURL(url);\n        }\n    };\n    // Engraving state\n    const [engravingText, setEngravingText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [decalPosition, setDecalPosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        0,\n        1.68,\n        0\n    ]);\n    const [decalScale, setDecalScale] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        2.8,\n        0.9,\n        1.5\n    ]);\n    const [decalRotation, setDecalRotation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        -Math.PI / 2,\n        0,\n        0\n    ]);\n    const [decalColor, setDecalColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"#cbcbcb\");\n    const [selectedFont, setSelectedFont] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"/fonts/ttf/RobotoSerif-Regular.ttf\");\n    // Diamond shader state\n    const [useDiamondShader, setUseDiamondShader] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [usePremiumWhiteGold, setUsePremiumWhiteGold] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [usePremiumRoseGold, setUsePremiumRoseGold] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleEngraveText = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((text)=>{\n        setEngravingText(text);\n        // Store in localStorage to make it persist across panel changes and page reloads\n        localStorage.setItem(\"engravingText\", text);\n    }, []);\n    const handleFontChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((font)=>{\n        setSelectedFont(font);\n        localStorage.setItem(\"engravingFont\", font);\n    }, []);\n    // Load saved engraving text on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const savedEngravingText = localStorage.getItem(\"engravingText\");\n        if (savedEngravingText) {\n            setEngravingText(savedEngravingText);\n        }\n    }, []);\n    const handleDecalPlacement = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((event)=>{\n        handleDecalPlacement(event, setIsPlacingDecal, setDecalPosition, setDecalRotation);\n    }, [\n        setDecalPosition,\n        setDecalRotation,\n        setIsPlacingDecal\n    ]);\n    // Get predefined decal positions based on selected model\n    const getDefaultDecalPosition = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((modelId)=>{\n        const modelConfigs = {\n            chosenring: [\n                0,\n                1.68,\n                0\n            ],\n            testring: [\n                0,\n                1.68,\n                0\n            ],\n            agapering1: [\n                0,\n                1.2,\n                0\n            ],\n            agapering2: [\n                0,\n                1.2,\n                0\n            ],\n            ringa: [\n                0,\n                0.26,\n                0\n            ],\n            ringb: [\n                0,\n                0.5,\n                0\n            ],\n            ringc: [\n                0,\n                0.26,\n                0\n            ]\n        };\n        return modelConfigs[modelId] || [\n            0,\n            1.68,\n            0\n        ];\n    }, []);\n    const getDefaultDecalRotation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((modelId)=>{\n        const modelConfigs = {\n            chosenring: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            testring: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            agapering1: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            agapering2: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            ringa: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            ringb: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            ringc: [\n                -Math.PI / 2,\n                0,\n                0\n            ]\n        };\n        return modelConfigs[modelId] || [\n            -Math.PI / 2,\n            0,\n            0\n        ];\n    }, []);\n    const getDefaultDecalScale = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((modelId)=>{\n        const modelConfigs = {\n            chosenring: [\n                6.0,\n                2.0,\n                1.5\n            ],\n            testring: [\n                6.0,\n                2.0,\n                1.5\n            ],\n            agapering1: [\n                3.2,\n                1.8,\n                1.0\n            ],\n            agapering2: [\n                3.2,\n                1.8,\n                1.0\n            ],\n            ringa: [\n                1.8,\n                2.0,\n                0.8\n            ],\n            ringb: [\n                1.8,\n                2.0,\n                0.8\n            ],\n            ringc: [\n                1.8,\n                2.0,\n                0.8\n            ]\n        };\n        return modelConfigs[modelId] || [\n            4.2,\n            2.0,\n            1.5\n        ];\n    }, []);\n    // Calculate dynamic scale based on text length and model type\n    const calculateDynamicScale = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((baseScale, text, modelId)=>{\n        if (!text || text.length === 0) return baseScale;\n        const textLength = text.length;\n        let [scaleX, scaleY, scaleZ] = baseScale;\n        // Calculate scale multiplier based on text length\n        let scaleMultiplier = 1.0;\n        if (textLength > 10) {\n            // For very long text, scale up more aggressively\n            scaleMultiplier = 1.3 + (textLength - 10) * 0.08;\n        } else if (textLength > 6) {\n            // For medium length text, scale up moderately\n            scaleMultiplier = 1.0 + (textLength - 6) * 0.08;\n        } else if (textLength < 4) {\n            // For short text, scale down slightly to prevent oversized appearance\n            scaleMultiplier = 0.8 + textLength * 0.05;\n        }\n        // Apply text length scaling to X-axis (width) primarily\n        scaleX = scaleX * scaleMultiplier;\n        // For very long text, also adjust the height slightly\n        if (textLength > 8) {\n            scaleY = scaleY * Math.min(1.2, 1.0 + (textLength - 8) * 0.02);\n        }\n        // Cap maximum scale to prevent excessive sizing (updated for larger Y values)\n        const maxScale = {\n            chosenring: [\n                12.0,\n                2.5,\n                2.0\n            ],\n            testring: [\n                12.0,\n                2.5,\n                2.0\n            ],\n            agapering1: [\n                6.5,\n                2.2,\n                1.5\n            ],\n            agapering2: [\n                6.5,\n                2.2,\n                1.5\n            ],\n            ringa: [\n                4.0,\n                2.5,\n                1.2\n            ],\n            ringb: [\n                4.0,\n                2.5,\n                1.2\n            ],\n            ringc: [\n                4.0,\n                2.5,\n                1.2\n            ]\n        };\n        const limits = maxScale[modelId] || [\n            8.5,\n            2.5,\n            2.0\n        ];\n        scaleX = Math.min(scaleX, limits[0]);\n        scaleY = Math.min(scaleY, limits[1]);\n        scaleZ = Math.min(scaleZ, limits[2]);\n        return [\n            scaleX,\n            scaleY,\n            scaleZ\n        ];\n    }, []);\n    // Update scale when text changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedModel && engravingText) {\n            const baseScale = getDefaultDecalScale(selectedModel);\n            const dynamicScale = calculateDynamicScale(baseScale, engravingText, selectedModel);\n            setDecalScale(dynamicScale);\n        }\n    }, [\n        engravingText,\n        selectedModel,\n        getDefaultDecalScale,\n        calculateDynamicScale\n    ]);\n    // Initialize decal position based on selected model\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedModel) {\n            setDecalPosition(getDefaultDecalPosition(selectedModel));\n            setDecalRotation(getDefaultDecalRotation(selectedModel));\n            // Only set initial scale if there's no engraving text yet\n            if (!engravingText) {\n                setDecalScale(getDefaultDecalScale(selectedModel));\n            }\n        }\n    }, [\n        selectedModel,\n        getDefaultDecalPosition,\n        getDefaultDecalRotation,\n        getDefaultDecalScale,\n        engravingText\n    ]);\n    const [isFullscreen, setIsFullscreen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Get camera position based on device type\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const checkMobile = ()=>{\n            setIsMobile(window.innerWidth < 768);\n        };\n        checkMobile();\n        window.addEventListener(\"resize\", checkMobile);\n        return ()=>window.removeEventListener(\"resize\", checkMobile);\n    }, []);\n    // Add state to control outliner collapsed state - initialize based on device type\n    const [outlinerCollapsed, setOutlinerCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        // Initialize based on device type - closed on mobile, open on desktop\n        if (true) {\n            return window.innerWidth < 768;\n        }\n        return false; // Default to open on server-side rendering\n    });\n    // Effect to close outliner when toolbar panels are opened on mobile\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isMobile && rightPanelTab) {\n            setOutlinerCollapsed(true);\n        }\n    }, [\n        isMobile,\n        rightPanelTab\n    ]);\n    // Effect to update outliner collapsed state when mobile state changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Update outliner state based on device type - closed on mobile, open on desktop\n        setOutlinerCollapsed(isMobile);\n    }, [\n        isMobile\n    ]);\n    const getCameraPosition = ()=>{\n        if (isMobile) {\n            return [\n                -1.85,\n                3.25,\n                4.5\n            ];\n        }\n        return [\n            -1.85,\n            1.55,\n            3.5\n        ];\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // On initial mount, fetch and apply the current version from DB\n        const fetchAndApplyCurrentVersion = async ()=>{\n            try {\n                const currentVersion = await (0,_lib_actions_version_actions__WEBPACK_IMPORTED_MODULE_23__.getCurrentVersion)({\n                    userId: user.id,\n                    workspaceId: project._id\n                });\n                if (currentVersion && currentVersion.config) {\n                    handleLoadScene(currentVersion.config);\n                }\n            } catch (err) {\n                console.error(\"Failed to load current version from DB\", err);\n            }\n        };\n        fetchAndApplyCurrentVersion();\n    // Only run on mount\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, []);\n    // Preload environment HDRIs/EXRs\n    const envTextureCache = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({});\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // List of all environment files to preload\n        const envFiles = [\n            {\n                key: \"hdri_15\",\n                url: \"/hdris/hdri_15.hdr\",\n                loader: \"rgbe\"\n            },\n            {\n                key: \"hdri_metal\",\n                url: \"/hdris/hdri_metal.exr\",\n                loader: \"exr\"\n            },\n            {\n                key: \"hdri_metal2\",\n                url: \"/hdris/metal_hdri2.hdr\",\n                loader: \"rgbe\"\n            },\n            {\n                key: \"hdri_metal3\",\n                url: \"/hdris/metal_hdri3.hdr\",\n                loader: \"rgbe\"\n            },\n            {\n                key: \"hdri_gem\",\n                url: \"/hdris/env_gem_002_30251392af.exr\",\n                loader: \"exr\"\n            },\n            {\n                key: \"studio_small\",\n                url: \"/hdris/studio_small_02_2k.hdr\",\n                loader: \"rgbe\"\n            }\n        ];\n        envFiles.forEach((param)=>{\n            let { key, url, loader } = param;\n            if (!envTextureCache.current[key]) {\n                if (loader === \"exr\") {\n                    new three_examples_jsm_loaders_EXRLoader_js__WEBPACK_IMPORTED_MODULE_28__.EXRLoader().load(url, (texture)=>{\n                        envTextureCache.current[key] = texture;\n                    });\n                } else if (loader === \"rgbe\") {\n                    new three_examples_jsm_loaders_RGBELoader_js__WEBPACK_IMPORTED_MODULE_29__.RGBELoader().load(url, (texture)=>{\n                        envTextureCache.current[key] = texture;\n                    });\n                }\n            }\n        });\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_toastify__WEBPACK_IMPORTED_MODULE_2__.ToastContainer, {}, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1806,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Header__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                onReset: handleReset,\n                onScreenshot: captureScreenshot,\n                onSaveScene: handleSaveScene,\n                onLoadScene: handleLoadScene,\n                isFullscreen: isFullscreen,\n                onToggleFullscreen: ()=>setIsFullscreen(!isFullscreen),\n                project: project,\n                user: user,\n                onUndo: undoAction,\n                onRedo: redoAction\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1807,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_InitialOverlayPanel__WEBPACK_IMPORTED_MODULE_19__.InitialOverlayPanel, {}, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1820,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed left-5 top-1/2 transform -translate-y-1/2 z-[1000] transition-all duration-300 \".concat(isFullscreen ? \"opacity-0 pointer-events-none\" : \"opacity-100\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Toolbar__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    transformMode: transformMode,\n                    setTransformMode: setTransformMode,\n                    undoAction: undoAction,\n                    redoAction: redoAction,\n                    rightPanelTab: rightPanelTab,\n                    setRightPanelTab: setRightPanelTab,\n                    orientation: \"vertical\",\n                    sceneObjects: sceneObjects,\n                    setSelectedObjects: setSelectedObjects,\n                    selectedObjects: selectedObjects,\n                    panelRef: panelRef,\n                    isMobile: isMobile,\n                    setOutlinerCollapsed: setOutlinerCollapsed\n                }, void 0, false, {\n                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                    lineNumber: 1828,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1823,\n                columnNumber: 7\n            }, this),\n            rightPanelTab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: panelRef,\n                className: \"fixed left-20 top-1/2 transform -translate-y-1/2 bg-gradient-to-tl from-[#32343D] to-[#14192D] p-4 rounded-lg shadow-lg z-[1000] max-h-[80vh] overflow-y-auto w-[250px] md:w-[300px] border border-[#A3A3A3]/30 scrollbar-none\",\n                children: [\n                    rightPanelTab === \"transform\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TransformPanel__WEBPACK_IMPORTED_MODULE_13__.TransformPanel, {\n                        selectedObjects: selectedObjects,\n                        transformMode: transformMode,\n                        setTransformMode: setTransformMode,\n                        undoAction: undoAction,\n                        redoAction: redoAction,\n                        addToHistory: addToHistory,\n                        invalidate: _react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.m\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1852,\n                        columnNumber: 13\n                    }, this),\n                    rightPanelTab === \"material\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MaterialPanel__WEBPACK_IMPORTED_MODULE_7__.MaterialPanel, {\n                        selectedObjects: selectedObjects,\n                        useDiamondShader: useDiamondShader,\n                        setUseDiamondShader: setUseDiamondShader,\n                        usePremiumWhiteGold: usePremiumWhiteGold,\n                        setUsePremiumWhiteGold: setUsePremiumWhiteGold,\n                        usePremiumRoseGold: usePremiumRoseGold,\n                        setUsePremiumRoseGold: setUsePremiumRoseGold,\n                        renderer: renderer,\n                        forceUpdate: forceUpdate,\n                        setMaterialVersion: setMaterialVersion,\n                        invalidate: _react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.m,\n                        addToHistory: addToHistory\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1864,\n                        columnNumber: 13\n                    }, this),\n                    rightPanelTab === \"postprocessing\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PostProcessingPanel__WEBPACK_IMPORTED_MODULE_10__.PostProcessingPanel, {\n                        postProcessingEnabled: postProcessingEnabled,\n                        setPostProcessingEnabled: setPostProcessingEnabled,\n                        postProcessingSettings: postProcessingSettings,\n                        setPostProcessingSettings: setPostProcessingSettings\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1881,\n                        columnNumber: 13\n                    }, this),\n                    rightPanelTab === \"environment\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EnvironmentPanel__WEBPACK_IMPORTED_MODULE_4__.EnvironmentPanel, {\n                        envPreset: envPreset,\n                        setEnvPreset: setEnvPreset,\n                        bgColor: bgColor,\n                        setBgColor: setBgColor,\n                        showEnvironment: showEnvironment,\n                        setShowEnvironment: setShowEnvironment,\n                        customHdri: customHdri,\n                        setCustomHdri: setCustomHdri,\n                        envIntensity: envIntensity,\n                        setEnvIntensity: setEnvIntensity,\n                        showModelStats: showModelStats,\n                        setShowModelStats: setShowModelStats,\n                        envBlur: envBlur,\n                        setEnvBlur: setEnvBlur,\n                        envRotation: envRotation,\n                        setEnvRotation: setEnvRotation,\n                        envTextureCache: envTextureCache.current\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1890,\n                        columnNumber: 13\n                    }, this),\n                    rightPanelTab === \"lights\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LightPanel__WEBPACK_IMPORTED_MODULE_6__.LightPanel, {\n                        lights: lights,\n                        setLights: setLights,\n                        lightRefs: lightRefs,\n                        showGrid: showGrid,\n                        setShowGrid: setShowGrid,\n                        wireframe: wireframe,\n                        setWireframe: setWireframe,\n                        showLightSpheres: showLightSpheres,\n                        setShowLightSpheres: setShowLightSpheres,\n                        onResetLights: ()=>{\n                            // Reset lights to default configuration\n                            const defaultLights = [\n                                {\n                                    id: \"defaultDirectional\",\n                                    type: \"directional\",\n                                    position: [\n                                        2.5,\n                                        5.5,\n                                        5\n                                    ],\n                                    intensity: 1.0,\n                                    color: \"#ffffff\",\n                                    name: \"Main Directional Light\",\n                                    castShadow: true,\n                                    helperVisible: false\n                                },\n                                {\n                                    id: \"keyLight\",\n                                    type: \"point\",\n                                    position: [\n                                        5,\n                                        5,\n                                        5\n                                    ],\n                                    intensity: 8.0,\n                                    color: \"#ffffff\",\n                                    name: \"Key Light\"\n                                },\n                                {\n                                    id: \"fillLight\",\n                                    type: \"point\",\n                                    position: [\n                                        -5,\n                                        5,\n                                        -5\n                                    ],\n                                    intensity: 4.0,\n                                    color: \"#ffffff\",\n                                    name: \"Fill Light\"\n                                },\n                                {\n                                    id: \"backLight\",\n                                    type: \"point\",\n                                    position: [\n                                        0,\n                                        5,\n                                        -5\n                                    ],\n                                    intensity: 2.0,\n                                    color: \"#ffffff\",\n                                    name: \"Back Light\"\n                                }\n                            ];\n                            setLights(defaultLights);\n                            // Clear selected light\n                            setSelectedLight(null);\n                            // Clear light position history\n                            setLightPositionHistory({});\n                        }\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1912,\n                        columnNumber: 13\n                    }, this),\n                    rightPanelTab === \"engraving\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EngravingPanel__WEBPACK_IMPORTED_MODULE_18__.EngravingPanel, {\n                        onEngraveText: handleEngraveText,\n                        onDecalPositionChange: setDecalPosition,\n                        onDecalScaleChange: setDecalScale,\n                        onDecalRotationChange: setDecalRotation,\n                        decalPosition: decalPosition,\n                        decalScale: decalScale,\n                        decalRotation: decalRotation,\n                        decalColor: decalColor,\n                        onDecalColorChange: setDecalColor,\n                        onFontChange: handleFontChange,\n                        selectedFont: selectedFont,\n                        isPlacingDecal: isPlacingDecal,\n                        setIsPlacingDecal: setIsPlacingDecal,\n                        showDebug: showDecalDebug,\n                        onShowDebugChange: setShowDecalDebug\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1970,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1847,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed right-5 top-24 z-[999] transition-all duration-300 \".concat(isFullscreen ? \"opacity-0 pointer-events-none\" : \"opacity-100\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-tl from-[#32343D] to-[#14192D] shadow-lg rounded-lg overflow-visible scrollbar-none\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Outliner__WEBPACK_IMPORTED_MODULE_9__.Outliner, {\n                        sceneObjects: sceneObjects,\n                        selectedObjects: selectedObjects,\n                        onSelectObject: setSelectedObjects,\n                        onCameraView: handleCameraView,\n                        onPlayAnimation: handlePlayAnimation,\n                        onSaveScene: handleSaveScene,\n                        groundType: groundType,\n                        setGroundType: setGroundType,\n                        collapsed: outlinerCollapsed,\n                        setCollapsed: setOutlinerCollapsed\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1998,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                    lineNumber: 1997,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1992,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-full\",\n                style: {\n                    background: \"linear-gradient(to bottom right, #363643, #5C5C6E)\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_fiber__WEBPACK_IMPORTED_MODULE_30__.Canvas, {\n                    className: \"w-full h-full\",\n                    camera: {\n                        position: getCameraPosition(),\n                        fov: 45,\n                        near: 0.1,\n                        far: 50\n                    },\n                    shadows: true,\n                    ref: sceneRef,\n                    gl: {\n                        antialias: true,\n                        preserveDrawingBuffer: true\n                    },\n                    onPointerMissed: handlePointerMissed,\n                    raycaster: {\n                        computeOffsets: (e)=>({\n                                offsetX: e.clientX,\n                                offsetY: e.clientY\n                            })\n                    },\n                    frameloop: \"demand\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n                        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_31__.Html, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_32__.Loader, {}, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2043,\n                                columnNumber: 17\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                            lineNumber: 2042,\n                            columnNumber: 15\n                        }, void 0),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ScreenshotTaker, {\n                                onScreenshotRef: (fn)=>{\n                                    screenshotRef.current = fn;\n                                }\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2049,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RendererProvider, {\n                                onRendererReady: (gl)=>{\n                                    setRenderer(gl);\n                                }\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2055,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SceneLights__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                lights: lights,\n                                lightRefs: lightRefs,\n                                selectedLight: selectedLight,\n                                onLightSelect: setSelectedLight,\n                                onLightMoved: handleLightMoved,\n                                showLightSpheres: showLightSpheres,\n                                threePointLighting: true\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2061,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Environment__WEBPACK_IMPORTED_MODULE_3__.Environment, {\n                                preset: getEnvironmentPreset(),\n                                background: showEnvironment,\n                                bgColor: bgColor,\n                                customHdri: customHdri,\n                                intensity: envIntensity,\n                                blur: envBlur,\n                                envRotation: envRotation,\n                                envTextureCache: envTextureCache.current\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2070,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SelectionOutline__WEBPACK_IMPORTED_MODULE_22__.SelectionOutline, {\n                                selectedObjects: selectedObjects,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ModelStage__WEBPACK_IMPORTED_MODULE_20__.ModelStage, {\n                                    selectedModel: selectedModel,\n                                    wireframe: wireframe,\n                                    handleSceneUpdate: handleSceneUpdate,\n                                    handleObjectClick: handleObjectClick,\n                                    uploadedModel: uploadedModel,\n                                    groundType: groundType,\n                                    engravingText: engravingText,\n                                    activeAnimation: activeAnimation,\n                                    decalPosition: decalPosition,\n                                    decalScale: decalScale,\n                                    decalRotation: decalRotation,\n                                    decalColor: decalColor,\n                                    font: selectedFont,\n                                    isPlacingDecal: isPlacingDecal,\n                                    onDecalPlacement: handleDecalPlacement,\n                                    showDecalDebug: showDecalDebug,\n                                    sceneObjects: sceneObjects,\n                                    useDiamondShader: useDiamondShader,\n                                    usePremiumWhiteGold: usePremiumWhiteGold,\n                                    usePremiumRoseGold: usePremiumRoseGold\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                    lineNumber: 2082,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2081,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_33__.OrbitControls, {\n                                ref: orbitControlsRef,\n                                target: [\n                                    0,\n                                    0.5,\n                                    0\n                                ],\n                                makeDefault: true,\n                                enableDamping: true,\n                                // minPolarAngle={0}\n                                // maxPolarAngle={Math.PI / 2.25}\n                                // minDistance={2.5}\n                                maxDistance: 20\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2106,\n                                columnNumber: 13\n                            }, this),\n                            selectedObjects.length > 0 && rightPanelTab === \"transform\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_34__.TransformControls, {\n                                object: selectedObjects.length === 1 ? selectedObjects[0] : selectionGroupRef.current,\n                                mode: transformMode,\n                                onMouseDown: ()=>{\n                                    if (orbitControlsRef.current) orbitControlsRef.current.enabled = false;\n                                },\n                                onMouseUp: ()=>{\n                                    if (orbitControlsRef.current) orbitControlsRef.current.enabled = true;\n                                    if (selectedObjects.length > 1) updateObjectsFromGroup();\n                                    forceUpdate(); // Ensure immediate update after transform\n                                    (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.m)(); // Force R3F to re-render after transform\n                                },\n                                onObjectChange: ()=>{\n                                    if (selectedObjects.length > 1) updateObjectsFromGroup();\n                                    forceUpdate(); // Ensure immediate update after transform\n                                    (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.m)(); // Force R3F to re-render after transform\n                                }\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2118,\n                                columnNumber: 15\n                            }, this),\n                            groundType === \"grid\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"gridHelper\", {\n                                args: [\n                                    50,\n                                    50,\n                                    \"#666666\",\n                                    \"#333333\"\n                                ],\n                                receiveShadow: true\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2144,\n                                columnNumber: 15\n                            }, this),\n                            groundType === \"solid\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n                                \"rotation-x\": -Math.PI / 2,\n                                receiveShadow: true,\n                                position: [\n                                    0,\n                                    -0.01,\n                                    0\n                                ],\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"planeGeometry\", {\n                                        args: [\n                                            200,\n                                            200\n                                        ]\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2152,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meshStandardMaterial\", {\n                                        color: \"#5d5d5d\",\n                                        wireframe: wireframe,\n                                        transparent: true,\n                                        opacity: 0.7\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2153,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2147,\n                                columnNumber: 15\n                            }, this),\n                            groundType === \"reflective\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n                                receiveShadow: true,\n                                rotation: [\n                                    -Math.PI / 2,\n                                    0,\n                                    0\n                                ],\n                                position: [\n                                    0,\n                                    -0.01,\n                                    0\n                                ],\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"planeGeometry\", {\n                                        args: [\n                                            80,\n                                            80\n                                        ]\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2167,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_35__.MeshReflectorMaterial, {\n                                        blur: [\n                                            800,\n                                            800\n                                        ],\n                                        resolution: 2048,\n                                        mixBlur: 0.5,\n                                        mixStrength: 10,\n                                        roughness: 1,\n                                        depthScale: 1,\n                                        minDepthThreshold: 0.4,\n                                        maxDepthThreshold: 1.2,\n                                        color: \"#363643\",\n                                        metalness: 0.01,\n                                        mirror: 0.3,\n                                        transparent: true,\n                                        opacity: 0.5\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2168,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2162,\n                                columnNumber: 15\n                            }, this),\n                            groundType === \"none\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n                                receiveShadow: true,\n                                rotation: [\n                                    -Math.PI / 2,\n                                    0,\n                                    0\n                                ],\n                                position: [\n                                    0,\n                                    -0.01,\n                                    0\n                                ],\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"planeGeometry\", {\n                                        args: [\n                                            200,\n                                            200\n                                        ]\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2191,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"primitive\", {\n                                        object: new three__WEBPACK_IMPORTED_MODULE_27__.ShadowMaterial({\n                                            opacity: 0.1,\n                                            color: new three__WEBPACK_IMPORTED_MODULE_27__.Color(0x000000),\n                                            transparent: true\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2192,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2186,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_36__.GizmoHelper, {\n                                alignment: \"bottom-right\",\n                                margin: [\n                                    80,\n                                    80\n                                ],\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_37__.GizmoViewport, {\n                                    labelColor: \"white\",\n                                    axisColors: [\n                                        \"red\",\n                                        \"green\",\n                                        \"blue\"\n                                    ]\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                    lineNumber: 2204,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2203,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"primitive\", {\n                                object: selectionGroupRef.current\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2210,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PostProcessingEffects__WEBPACK_IMPORTED_MODULE_21__.PostProcessingEffects, {\n                                enabled: postProcessingEnabled,\n                                settings: postProcessingSettings\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2212,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 2040,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                    lineNumber: 2020,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 2014,\n                columnNumber: 7\n            }, this),\n            showModelStats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ModelStats__WEBPACK_IMPORTED_MODULE_8__.ModelStats, {\n                stats: modelStats\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 2220,\n                columnNumber: 26\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s2(EditorExperience, \"JxrURUqzlmQjBBZjlm5WfzcQTGM=\", false, function() {\n    return [\n        _lib_states__WEBPACK_IMPORTED_MODULE_25__.useVersionState\n    ];\n});\n_c3 = EditorExperience;\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"ScreenshotTaker\");\n$RefreshReg$(_c1, \"MaterialPanelWrapper\");\n$RefreshReg$(_c2, \"RendererProvider\");\n$RefreshReg$(_c3, \"EditorExperience\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ArtistTool/EditorExperience.jsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/three@0.167.1/node_modules/three/examples/jsm/loaders/RGBELoader.js":
/*!************************************************************************************************!*\
  !*** ./node_modules/.pnpm/three@0.167.1/node_modules/three/examples/jsm/loaders/RGBELoader.js ***!
  \************************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RGBELoader: function() { return /* binding */ RGBELoader; }\n/* harmony export */ });\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! three */ \"(app-pages-browser)/./node_modules/.pnpm/three@0.167.1/node_modules/three/build/three.module.js\");\n\n\n// https://github.com/mrdoob/three.js/issues/5552\n// http://en.wikipedia.org/wiki/RGBE_image_format\n\nclass RGBELoader extends three__WEBPACK_IMPORTED_MODULE_0__.DataTextureLoader {\n\n\tconstructor( manager ) {\n\n\t\tsuper( manager );\n\n\t\tthis.type = three__WEBPACK_IMPORTED_MODULE_0__.HalfFloatType;\n\n\t}\n\n\t// adapted from http://www.graphics.cornell.edu/~bjw/rgbe.html\n\n\tparse( buffer ) {\n\n\t\tconst\n\t\t\t/* default error routine.  change this to change error handling */\n\t\t\trgbe_read_error = 1,\n\t\t\trgbe_write_error = 2,\n\t\t\trgbe_format_error = 3,\n\t\t\trgbe_memory_error = 4,\n\t\t\trgbe_error = function ( rgbe_error_code, msg ) {\n\n\t\t\t\tswitch ( rgbe_error_code ) {\n\n\t\t\t\t\tcase rgbe_read_error: throw new Error( 'THREE.RGBELoader: Read Error: ' + ( msg || '' ) );\n\t\t\t\t\tcase rgbe_write_error: throw new Error( 'THREE.RGBELoader: Write Error: ' + ( msg || '' ) );\n\t\t\t\t\tcase rgbe_format_error: throw new Error( 'THREE.RGBELoader: Bad File Format: ' + ( msg || '' ) );\n\t\t\t\t\tdefault:\n\t\t\t\t\tcase rgbe_memory_error: throw new Error( 'THREE.RGBELoader: Memory Error: ' + ( msg || '' ) );\n\n\t\t\t\t}\n\n\t\t\t},\n\n\t\t\t/* offsets to red, green, and blue components in a data (float) pixel */\n\t\t\t//RGBE_DATA_RED = 0,\n\t\t\t//RGBE_DATA_GREEN = 1,\n\t\t\t//RGBE_DATA_BLUE = 2,\n\n\t\t\t/* number of floats per pixel, use 4 since stored in rgba image format */\n\t\t\t//RGBE_DATA_SIZE = 4,\n\n\t\t\t/* flags indicating which fields in an rgbe_header_info are valid */\n\t\t\tRGBE_VALID_PROGRAMTYPE = 1,\n\t\t\tRGBE_VALID_FORMAT = 2,\n\t\t\tRGBE_VALID_DIMENSIONS = 4,\n\n\t\t\tNEWLINE = '\\n',\n\n\t\t\tfgets = function ( buffer, lineLimit, consume ) {\n\n\t\t\t\tconst chunkSize = 128;\n\n\t\t\t\tlineLimit = ! lineLimit ? 1024 : lineLimit;\n\t\t\t\tlet p = buffer.pos,\n\t\t\t\t\ti = - 1, len = 0, s = '',\n\t\t\t\t\tchunk = String.fromCharCode.apply( null, new Uint16Array( buffer.subarray( p, p + chunkSize ) ) );\n\n\t\t\t\twhile ( ( 0 > ( i = chunk.indexOf( NEWLINE ) ) ) && ( len < lineLimit ) && ( p < buffer.byteLength ) ) {\n\n\t\t\t\t\ts += chunk; len += chunk.length;\n\t\t\t\t\tp += chunkSize;\n\t\t\t\t\tchunk += String.fromCharCode.apply( null, new Uint16Array( buffer.subarray( p, p + chunkSize ) ) );\n\n\t\t\t\t}\n\n\t\t\t\tif ( - 1 < i ) {\n\n\t\t\t\t\t/*for (i=l-1; i>=0; i--) {\n\t\t\t\t\t\tbyteCode = m.charCodeAt(i);\n\t\t\t\t\t\tif (byteCode > 0x7f && byteCode <= 0x7ff) byteLen++;\n\t\t\t\t\t\telse if (byteCode > 0x7ff && byteCode <= 0xffff) byteLen += 2;\n\t\t\t\t\t\tif (byteCode >= 0xDC00 && byteCode <= 0xDFFF) i--; //trail surrogate\n\t\t\t\t\t}*/\n\t\t\t\t\tif ( false !== consume ) buffer.pos += len + i + 1;\n\t\t\t\t\treturn s + chunk.slice( 0, i );\n\n\t\t\t\t}\n\n\t\t\t\treturn false;\n\n\t\t\t},\n\n\t\t\t/* minimal header reading.  modify if you want to parse more information */\n\t\t\tRGBE_ReadHeader = function ( buffer ) {\n\n\n\t\t\t\t// regexes to parse header info fields\n\t\t\t\tconst magic_token_re = /^#\\?(\\S+)/,\n\t\t\t\t\tgamma_re = /^\\s*GAMMA\\s*=\\s*(\\d+(\\.\\d+)?)\\s*$/,\n\t\t\t\t\texposure_re = /^\\s*EXPOSURE\\s*=\\s*(\\d+(\\.\\d+)?)\\s*$/,\n\t\t\t\t\tformat_re = /^\\s*FORMAT=(\\S+)\\s*$/,\n\t\t\t\t\tdimensions_re = /^\\s*\\-Y\\s+(\\d+)\\s+\\+X\\s+(\\d+)\\s*$/,\n\n\t\t\t\t\t// RGBE format header struct\n\t\t\t\t\theader = {\n\n\t\t\t\t\t\tvalid: 0, /* indicate which fields are valid */\n\n\t\t\t\t\t\tstring: '', /* the actual header string */\n\n\t\t\t\t\t\tcomments: '', /* comments found in header */\n\n\t\t\t\t\t\tprogramtype: 'RGBE', /* listed at beginning of file to identify it after \"#?\". defaults to \"RGBE\" */\n\n\t\t\t\t\t\tformat: '', /* RGBE format, default 32-bit_rle_rgbe */\n\n\t\t\t\t\t\tgamma: 1.0, /* image has already been gamma corrected with given gamma. defaults to 1.0 (no correction) */\n\n\t\t\t\t\t\texposure: 1.0, /* a value of 1.0 in an image corresponds to <exposure> watts/steradian/m^2. defaults to 1.0 */\n\n\t\t\t\t\t\twidth: 0, height: 0 /* image dimensions, width/height */\n\n\t\t\t\t\t};\n\n\t\t\t\tlet line, match;\n\n\t\t\t\tif ( buffer.pos >= buffer.byteLength || ! ( line = fgets( buffer ) ) ) {\n\n\t\t\t\t\trgbe_error( rgbe_read_error, 'no header found' );\n\n\t\t\t\t}\n\n\t\t\t\t/* if you want to require the magic token then uncomment the next line */\n\t\t\t\tif ( ! ( match = line.match( magic_token_re ) ) ) {\n\n\t\t\t\t\trgbe_error( rgbe_format_error, 'bad initial token' );\n\n\t\t\t\t}\n\n\t\t\t\theader.valid |= RGBE_VALID_PROGRAMTYPE;\n\t\t\t\theader.programtype = match[ 1 ];\n\t\t\t\theader.string += line + '\\n';\n\n\t\t\t\twhile ( true ) {\n\n\t\t\t\t\tline = fgets( buffer );\n\t\t\t\t\tif ( false === line ) break;\n\t\t\t\t\theader.string += line + '\\n';\n\n\t\t\t\t\tif ( '#' === line.charAt( 0 ) ) {\n\n\t\t\t\t\t\theader.comments += line + '\\n';\n\t\t\t\t\t\tcontinue; // comment line\n\n\t\t\t\t\t}\n\n\t\t\t\t\tif ( match = line.match( gamma_re ) ) {\n\n\t\t\t\t\t\theader.gamma = parseFloat( match[ 1 ] );\n\n\t\t\t\t\t}\n\n\t\t\t\t\tif ( match = line.match( exposure_re ) ) {\n\n\t\t\t\t\t\theader.exposure = parseFloat( match[ 1 ] );\n\n\t\t\t\t\t}\n\n\t\t\t\t\tif ( match = line.match( format_re ) ) {\n\n\t\t\t\t\t\theader.valid |= RGBE_VALID_FORMAT;\n\t\t\t\t\t\theader.format = match[ 1 ];//'32-bit_rle_rgbe';\n\n\t\t\t\t\t}\n\n\t\t\t\t\tif ( match = line.match( dimensions_re ) ) {\n\n\t\t\t\t\t\theader.valid |= RGBE_VALID_DIMENSIONS;\n\t\t\t\t\t\theader.height = parseInt( match[ 1 ], 10 );\n\t\t\t\t\t\theader.width = parseInt( match[ 2 ], 10 );\n\n\t\t\t\t\t}\n\n\t\t\t\t\tif ( ( header.valid & RGBE_VALID_FORMAT ) && ( header.valid & RGBE_VALID_DIMENSIONS ) ) break;\n\n\t\t\t\t}\n\n\t\t\t\tif ( ! ( header.valid & RGBE_VALID_FORMAT ) ) {\n\n\t\t\t\t\trgbe_error( rgbe_format_error, 'missing format specifier' );\n\n\t\t\t\t}\n\n\t\t\t\tif ( ! ( header.valid & RGBE_VALID_DIMENSIONS ) ) {\n\n\t\t\t\t\trgbe_error( rgbe_format_error, 'missing image size specifier' );\n\n\t\t\t\t}\n\n\t\t\t\treturn header;\n\n\t\t\t},\n\n\t\t\tRGBE_ReadPixels_RLE = function ( buffer, w, h ) {\n\n\t\t\t\tconst scanline_width = w;\n\n\t\t\t\tif (\n\t\t\t\t\t// run length encoding is not allowed so read flat\n\t\t\t\t\t( ( scanline_width < 8 ) || ( scanline_width > 0x7fff ) ) ||\n\t\t\t\t\t// this file is not run length encoded\n\t\t\t\t\t( ( 2 !== buffer[ 0 ] ) || ( 2 !== buffer[ 1 ] ) || ( buffer[ 2 ] & 0x80 ) )\n\t\t\t\t) {\n\n\t\t\t\t\t// return the flat buffer\n\t\t\t\t\treturn new Uint8Array( buffer );\n\n\t\t\t\t}\n\n\t\t\t\tif ( scanline_width !== ( ( buffer[ 2 ] << 8 ) | buffer[ 3 ] ) ) {\n\n\t\t\t\t\trgbe_error( rgbe_format_error, 'wrong scanline width' );\n\n\t\t\t\t}\n\n\t\t\t\tconst data_rgba = new Uint8Array( 4 * w * h );\n\n\t\t\t\tif ( ! data_rgba.length ) {\n\n\t\t\t\t\trgbe_error( rgbe_memory_error, 'unable to allocate buffer space' );\n\n\t\t\t\t}\n\n\t\t\t\tlet offset = 0, pos = 0;\n\n\t\t\t\tconst ptr_end = 4 * scanline_width;\n\t\t\t\tconst rgbeStart = new Uint8Array( 4 );\n\t\t\t\tconst scanline_buffer = new Uint8Array( ptr_end );\n\t\t\t\tlet num_scanlines = h;\n\n\t\t\t\t// read in each successive scanline\n\t\t\t\twhile ( ( num_scanlines > 0 ) && ( pos < buffer.byteLength ) ) {\n\n\t\t\t\t\tif ( pos + 4 > buffer.byteLength ) {\n\n\t\t\t\t\t\trgbe_error( rgbe_read_error );\n\n\t\t\t\t\t}\n\n\t\t\t\t\trgbeStart[ 0 ] = buffer[ pos ++ ];\n\t\t\t\t\trgbeStart[ 1 ] = buffer[ pos ++ ];\n\t\t\t\t\trgbeStart[ 2 ] = buffer[ pos ++ ];\n\t\t\t\t\trgbeStart[ 3 ] = buffer[ pos ++ ];\n\n\t\t\t\t\tif ( ( 2 != rgbeStart[ 0 ] ) || ( 2 != rgbeStart[ 1 ] ) || ( ( ( rgbeStart[ 2 ] << 8 ) | rgbeStart[ 3 ] ) != scanline_width ) ) {\n\n\t\t\t\t\t\trgbe_error( rgbe_format_error, 'bad rgbe scanline format' );\n\n\t\t\t\t\t}\n\n\t\t\t\t\t// read each of the four channels for the scanline into the buffer\n\t\t\t\t\t// first red, then green, then blue, then exponent\n\t\t\t\t\tlet ptr = 0, count;\n\n\t\t\t\t\twhile ( ( ptr < ptr_end ) && ( pos < buffer.byteLength ) ) {\n\n\t\t\t\t\t\tcount = buffer[ pos ++ ];\n\t\t\t\t\t\tconst isEncodedRun = count > 128;\n\t\t\t\t\t\tif ( isEncodedRun ) count -= 128;\n\n\t\t\t\t\t\tif ( ( 0 === count ) || ( ptr + count > ptr_end ) ) {\n\n\t\t\t\t\t\t\trgbe_error( rgbe_format_error, 'bad scanline data' );\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif ( isEncodedRun ) {\n\n\t\t\t\t\t\t\t// a (encoded) run of the same value\n\t\t\t\t\t\t\tconst byteValue = buffer[ pos ++ ];\n\t\t\t\t\t\t\tfor ( let i = 0; i < count; i ++ ) {\n\n\t\t\t\t\t\t\t\tscanline_buffer[ ptr ++ ] = byteValue;\n\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t//ptr += count;\n\n\t\t\t\t\t\t} else {\n\n\t\t\t\t\t\t\t// a literal-run\n\t\t\t\t\t\t\tscanline_buffer.set( buffer.subarray( pos, pos + count ), ptr );\n\t\t\t\t\t\t\tptr += count; pos += count;\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t}\n\n\n\t\t\t\t\t// now convert data from buffer into rgba\n\t\t\t\t\t// first red, then green, then blue, then exponent (alpha)\n\t\t\t\t\tconst l = scanline_width; //scanline_buffer.byteLength;\n\t\t\t\t\tfor ( let i = 0; i < l; i ++ ) {\n\n\t\t\t\t\t\tlet off = 0;\n\t\t\t\t\t\tdata_rgba[ offset ] = scanline_buffer[ i + off ];\n\t\t\t\t\t\toff += scanline_width; //1;\n\t\t\t\t\t\tdata_rgba[ offset + 1 ] = scanline_buffer[ i + off ];\n\t\t\t\t\t\toff += scanline_width; //1;\n\t\t\t\t\t\tdata_rgba[ offset + 2 ] = scanline_buffer[ i + off ];\n\t\t\t\t\t\toff += scanline_width; //1;\n\t\t\t\t\t\tdata_rgba[ offset + 3 ] = scanline_buffer[ i + off ];\n\t\t\t\t\t\toffset += 4;\n\n\t\t\t\t\t}\n\n\t\t\t\t\tnum_scanlines --;\n\n\t\t\t\t}\n\n\t\t\t\treturn data_rgba;\n\n\t\t\t};\n\n\t\tconst RGBEByteToRGBFloat = function ( sourceArray, sourceOffset, destArray, destOffset ) {\n\n\t\t\tconst e = sourceArray[ sourceOffset + 3 ];\n\t\t\tconst scale = Math.pow( 2.0, e - 128.0 ) / 255.0;\n\n\t\t\tdestArray[ destOffset + 0 ] = sourceArray[ sourceOffset + 0 ] * scale;\n\t\t\tdestArray[ destOffset + 1 ] = sourceArray[ sourceOffset + 1 ] * scale;\n\t\t\tdestArray[ destOffset + 2 ] = sourceArray[ sourceOffset + 2 ] * scale;\n\t\t\tdestArray[ destOffset + 3 ] = 1;\n\n\t\t};\n\n\t\tconst RGBEByteToRGBHalf = function ( sourceArray, sourceOffset, destArray, destOffset ) {\n\n\t\t\tconst e = sourceArray[ sourceOffset + 3 ];\n\t\t\tconst scale = Math.pow( 2.0, e - 128.0 ) / 255.0;\n\n\t\t\t// clamping to 65504, the maximum representable value in float16\n\t\t\tdestArray[ destOffset + 0 ] = three__WEBPACK_IMPORTED_MODULE_0__.DataUtils.toHalfFloat( Math.min( sourceArray[ sourceOffset + 0 ] * scale, 65504 ) );\n\t\t\tdestArray[ destOffset + 1 ] = three__WEBPACK_IMPORTED_MODULE_0__.DataUtils.toHalfFloat( Math.min( sourceArray[ sourceOffset + 1 ] * scale, 65504 ) );\n\t\t\tdestArray[ destOffset + 2 ] = three__WEBPACK_IMPORTED_MODULE_0__.DataUtils.toHalfFloat( Math.min( sourceArray[ sourceOffset + 2 ] * scale, 65504 ) );\n\t\t\tdestArray[ destOffset + 3 ] = three__WEBPACK_IMPORTED_MODULE_0__.DataUtils.toHalfFloat( 1 );\n\n\t\t};\n\n\t\tconst byteArray = new Uint8Array( buffer );\n\t\tbyteArray.pos = 0;\n\t\tconst rgbe_header_info = RGBE_ReadHeader( byteArray );\n\n\t\tconst w = rgbe_header_info.width,\n\t\t\th = rgbe_header_info.height,\n\t\t\timage_rgba_data = RGBE_ReadPixels_RLE( byteArray.subarray( byteArray.pos ), w, h );\n\n\n\t\tlet data, type;\n\t\tlet numElements;\n\n\t\tswitch ( this.type ) {\n\n\t\t\tcase three__WEBPACK_IMPORTED_MODULE_0__.FloatType:\n\n\t\t\t\tnumElements = image_rgba_data.length / 4;\n\t\t\t\tconst floatArray = new Float32Array( numElements * 4 );\n\n\t\t\t\tfor ( let j = 0; j < numElements; j ++ ) {\n\n\t\t\t\t\tRGBEByteToRGBFloat( image_rgba_data, j * 4, floatArray, j * 4 );\n\n\t\t\t\t}\n\n\t\t\t\tdata = floatArray;\n\t\t\t\ttype = three__WEBPACK_IMPORTED_MODULE_0__.FloatType;\n\t\t\t\tbreak;\n\n\t\t\tcase three__WEBPACK_IMPORTED_MODULE_0__.HalfFloatType:\n\n\t\t\t\tnumElements = image_rgba_data.length / 4;\n\t\t\t\tconst halfArray = new Uint16Array( numElements * 4 );\n\n\t\t\t\tfor ( let j = 0; j < numElements; j ++ ) {\n\n\t\t\t\t\tRGBEByteToRGBHalf( image_rgba_data, j * 4, halfArray, j * 4 );\n\n\t\t\t\t}\n\n\t\t\t\tdata = halfArray;\n\t\t\t\ttype = three__WEBPACK_IMPORTED_MODULE_0__.HalfFloatType;\n\t\t\t\tbreak;\n\n\t\t\tdefault:\n\n\t\t\t\tthrow new Error( 'THREE.RGBELoader: Unsupported type: ' + this.type );\n\t\t\t\tbreak;\n\n\t\t}\n\n\t\treturn {\n\t\t\twidth: w, height: h,\n\t\t\tdata: data,\n\t\t\theader: rgbe_header_info.string,\n\t\t\tgamma: rgbe_header_info.gamma,\n\t\t\texposure: rgbe_header_info.exposure,\n\t\t\ttype: type\n\t\t};\n\n\t}\n\n\tsetDataType( value ) {\n\n\t\tthis.type = value;\n\t\treturn this;\n\n\t}\n\n\tload( url, onLoad, onProgress, onError ) {\n\n\t\tfunction onLoadCallback( texture, texData ) {\n\n\t\t\tswitch ( texture.type ) {\n\n\t\t\t\tcase three__WEBPACK_IMPORTED_MODULE_0__.FloatType:\n\t\t\t\tcase three__WEBPACK_IMPORTED_MODULE_0__.HalfFloatType:\n\n\t\t\t\t\ttexture.colorSpace = three__WEBPACK_IMPORTED_MODULE_0__.LinearSRGBColorSpace;\n\t\t\t\t\ttexture.minFilter = three__WEBPACK_IMPORTED_MODULE_0__.LinearFilter;\n\t\t\t\t\ttexture.magFilter = three__WEBPACK_IMPORTED_MODULE_0__.LinearFilter;\n\t\t\t\t\ttexture.generateMipmaps = false;\n\t\t\t\t\ttexture.flipY = true;\n\n\t\t\t\t\tbreak;\n\n\t\t\t}\n\n\t\t\tif ( onLoad ) onLoad( texture, texData );\n\n\t\t}\n\n\t\treturn super.load( url, onLoadCallback, onProgress, onError );\n\n\t}\n\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/three@0.167.1/node_modules/three/examples/jsm/loaders/RGBELoader.js\n"));

/***/ })

});