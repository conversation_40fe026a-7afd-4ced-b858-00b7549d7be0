"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/projects/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/ArtistTool/EditorExperience.jsx":
/*!********************************************************!*\
  !*** ./src/components/ArtistTool/EditorExperience.jsx ***!
  \********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ EditorExperience; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/web/Html.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/web/Loader.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/OrbitControls.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/TransformControls.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/MeshReflectorMaterial.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/GizmoHelper.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/GizmoViewport.js\");\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @react-three/fiber */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_three@0.167.1/node_modules/@react-three/fiber/dist/events-776716bd.esm.js\");\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @react-three/fiber */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_three@0.167.1/node_modules/@react-three/fiber/dist/react-three-fiber.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/.pnpm/react-toastify@11.0.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/react-toastify/dist/index.mjs\");\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! three */ \"(app-pages-browser)/./node_modules/.pnpm/three@0.167.1/node_modules/three/build/three.module.js\");\n/* harmony import */ var _Environment__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Environment */ \"(app-pages-browser)/./src/components/ArtistTool/Environment.tsx\");\n/* harmony import */ var _EnvironmentPanel__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./EnvironmentPanel */ \"(app-pages-browser)/./src/components/ArtistTool/EnvironmentPanel.jsx\");\n/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Header */ \"(app-pages-browser)/./src/components/ArtistTool/Header.jsx\");\n/* harmony import */ var _LightPanel__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./LightPanel */ \"(app-pages-browser)/./src/components/ArtistTool/LightPanel.jsx\");\n/* harmony import */ var _MaterialPanel__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./MaterialPanel */ \"(app-pages-browser)/./src/components/ArtistTool/MaterialPanel.jsx\");\n/* harmony import */ var _ModelStats__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./ModelStats */ \"(app-pages-browser)/./src/components/ArtistTool/ModelStats.jsx\");\n/* harmony import */ var _Outliner__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./Outliner */ \"(app-pages-browser)/./src/components/ArtistTool/Outliner.jsx\");\n/* harmony import */ var _PostProcessingPanel__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./PostProcessingPanel */ \"(app-pages-browser)/./src/components/ArtistTool/PostProcessingPanel.jsx\");\n/* harmony import */ var _SceneLights__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./SceneLights */ \"(app-pages-browser)/./src/components/ArtistTool/SceneLights.jsx\");\n/* harmony import */ var _Toolbar__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./Toolbar */ \"(app-pages-browser)/./src/components/ArtistTool/Toolbar.jsx\");\n/* harmony import */ var _TransformPanel__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./TransformPanel */ \"(app-pages-browser)/./src/components/ArtistTool/TransformPanel.jsx\");\n/* harmony import */ var _utils_ArtistTool_cameraUtils__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../../utils/ArtistTool/cameraUtils */ \"(app-pages-browser)/./src/utils/ArtistTool/cameraUtils.js\");\n/* harmony import */ var _utils_ArtistTool_materialUtils__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../../utils/ArtistTool/materialUtils */ \"(app-pages-browser)/./src/utils/ArtistTool/materialUtils.js\");\n/* harmony import */ var _utils_ArtistTool_sceneUtils__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../../utils/ArtistTool/sceneUtils */ \"(app-pages-browser)/./src/utils/ArtistTool/sceneUtils.js\");\n/* harmony import */ var _animations_CameraAnimations__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./animations/CameraAnimations */ \"(app-pages-browser)/./src/components/ArtistTool/animations/CameraAnimations.js\");\n/* harmony import */ var _EngravingPanel__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./EngravingPanel */ \"(app-pages-browser)/./src/components/ArtistTool/EngravingPanel.jsx\");\n/* harmony import */ var _InitialOverlayPanel__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./InitialOverlayPanel */ \"(app-pages-browser)/./src/components/ArtistTool/InitialOverlayPanel.jsx\");\n/* harmony import */ var _ModelStage__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./ModelStage */ \"(app-pages-browser)/./src/components/ArtistTool/ModelStage.jsx\");\n/* harmony import */ var _PostProcessingEffects__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./PostProcessingEffects */ \"(app-pages-browser)/./src/components/ArtistTool/PostProcessingEffects.jsx\");\n/* harmony import */ var _SelectionOutline__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ./SelectionOutline */ \"(app-pages-browser)/./src/components/ArtistTool/SelectionOutline.jsx\");\n/* harmony import */ var _lib_actions_version_actions__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/lib/actions/version.actions */ \"(app-pages-browser)/./src/lib/actions/version.actions.ts\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/.pnpm/sonner@1.5.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _lib_states__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @/lib/states */ \"(app-pages-browser)/./src/lib/states.ts\");\n/* harmony import */ var three_examples_jsm_loaders_RGBELoader_js__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! three/examples/jsm/loaders/RGBELoader.js */ \"(app-pages-browser)/./node_modules/.pnpm/three@0.167.1/node_modules/three/examples/jsm/loaders/RGBELoader.js\");\n/* harmony import */ var three_examples_jsm_loaders_EXRLoader_js__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! three/examples/jsm/loaders/EXRLoader.js */ \"(app-pages-browser)/./node_modules/.pnpm/three@0.167.1/node_modules/three/examples/jsm/loaders/EXRLoader.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst ScreenshotTaker = (param)=>{\n    let { onScreenshotRef } = param;\n    _s();\n    const { gl, scene, camera } = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.D)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (typeof onScreenshotRef === \"function\") {\n            onScreenshotRef(()=>{\n                try {\n                    // Render the scene\n                    gl.render(scene, camera);\n                    // Get the canvas data URL\n                    const dataURL = gl.domElement.toDataURL(\"image/png\");\n                    // Create and trigger a download\n                    const link = document.createElement(\"a\");\n                    const timestamp = new Date().toISOString().replace(/:/g, \"-\").substring(0, 19);\n                    link.download = \"agape-scene-\".concat(timestamp, \".png\");\n                    link.href = dataURL;\n                    document.body.appendChild(link);\n                    link.click();\n                    // Clean up\n                    setTimeout(()=>{\n                        document.body.removeChild(link);\n                    }, 100);\n                    console.log(\"Screenshot captured successfully\");\n                    return true;\n                } catch (error) {\n                    console.error(\"Error capturing screenshot:\", error);\n                    return false;\n                }\n            });\n        }\n    }, [\n        gl,\n        scene,\n        camera,\n        onScreenshotRef\n    ]);\n    return null;\n};\n_s(ScreenshotTaker, \"RVTFHuczdlyvGFUsZJPSpjurI/Y=\", false, function() {\n    return [\n        _react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.D\n    ];\n});\n_c = ScreenshotTaker;\n// Component to provide renderer to MaterialPanel\nconst MaterialPanelWrapper = (param)=>{\n    let { renderer, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MaterialPanel__WEBPACK_IMPORTED_MODULE_7__.MaterialPanel, {\n        renderer: renderer,\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n        lineNumber: 96,\n        columnNumber: 10\n    }, undefined);\n};\n_c1 = MaterialPanelWrapper;\n// Component to get renderer from Canvas context\nconst RendererProvider = (param)=>{\n    let { children, onRendererReady } = param;\n    _s1();\n    const { gl } = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.D)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (onRendererReady) {\n            onRendererReady(gl);\n        }\n    }, [\n        gl,\n        onRendererReady\n    ]);\n    return null;\n};\n_s1(RendererProvider, \"iTWbOa64ZxlA5nuyNExgWK+vjCI=\", false, function() {\n    return [\n        _react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.D\n    ];\n});\n_c2 = RendererProvider;\nfunction EditorExperience(param) {\n    let { modelUrl, project, user } = param;\n    _s2();\n    const { addVersion } = (0,_lib_states__WEBPACK_IMPORTED_MODULE_25__.useVersionState)();\n    const configRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const workingConfigRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [selectedModel, setSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"uploaded\");\n    const [lastSelectedModel, setLastSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [uploadedModel, setUploadedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(modelUrl || null);\n    const [modelStats, setModelStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        vertices: 0,\n        triangles: 0,\n        materials: 0,\n        animations: 0\n    });\n    const [lights, setLights] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: \"defaultDirectional\",\n            type: \"directional\",\n            position: [\n                2.5,\n                5.5,\n                5\n            ],\n            intensity: 1.0,\n            color: \"#ffffff\",\n            name: \"Main Directional Light\",\n            castShadow: true,\n            helperVisible: false\n        },\n        {\n            id: \"keyLight\",\n            type: \"point\",\n            position: [\n                5,\n                5,\n                5\n            ],\n            intensity: 8.0,\n            color: \"#ffffff\",\n            name: \"Key Light\"\n        },\n        {\n            id: \"fillLight\",\n            type: \"point\",\n            position: [\n                -5,\n                5,\n                -5\n            ],\n            intensity: 4.0,\n            color: \"#ffffff\",\n            name: \"Fill Light\"\n        },\n        {\n            id: \"backLight\",\n            type: \"point\",\n            position: [\n                0,\n                5,\n                -5\n            ],\n            intensity: 2.0,\n            color: \"#ffffff\",\n            name: \"Back Light\"\n        }\n    ]);\n    const lightRefs = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({});\n    const [selectedObjects, setSelectedObjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedLight, setSelectedLight] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [lightPositionHistory, setLightPositionHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [sceneObjects, setSceneObjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [transformMode, setTransformMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"translate\");\n    const selectionGroupRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(new three__WEBPACK_IMPORTED_MODULE_27__.Group());\n    console.log(\"renderingggg editor experience\");\n    const [rightPanelTab, setRightPanelTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Decal placement state\n    const [isPlacingDecal, setIsPlacingDecal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showDecalDebug, setShowDecalDebug] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Scene controls state\n    const [showGrid, setShowGrid] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [wireframe, setWireframe] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [groundType, setGroundType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"none\");\n    const [envPreset, setEnvPreset] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"hdri_15\");\n    const [bgColor, setBgColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"#272631\");\n    const [showEnvironment, setShowEnvironment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showLightSpheres, setShowLightSpheres] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [customHdri, setCustomHdri] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [envIntensity, setEnvIntensity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1.85);\n    const [envBlur, setEnvBlur] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0.35);\n    const [envRotation, setEnvRotation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [postProcessingEnabled, setPostProcessingEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [postProcessingSettings, setPostProcessingSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        autofocus: {\n            enabled: false,\n            bokehScale: 18\n        },\n        bloom: {\n            enabled: true,\n            intensity: 0.22,\n            threshold: 1,\n            radius: 0.5\n        },\n        dof: {\n            enabled: false,\n            focusDistance: 0,\n            aperture: 0.01,\n            bokehScale: 3\n        },\n        vignette: {\n            enabled: true,\n            darkness: 0.5,\n            offset: 0.1\n        }\n    });\n    // Store initial state\n    const initialStateRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        selectedModel: null,\n        lights: [],\n        envPreset: null,\n        bgColor: null,\n        postProcessingSettings: null,\n        showGrid: null,\n        wireframe: null,\n        groundType: null,\n        showEnvironment: null,\n        showLightSpheres: null,\n        envIntensity: null,\n        envBlur: null,\n        cameraPosition: null,\n        cameraTarget: null\n    });\n    // Add a ref to store the initial state of model objects\n    const modelInitialStateRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        objects: []\n    });\n    // Capture initial state when component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        initialStateRef.current = {\n            selectedModel,\n            lights: [\n                ...lights\n            ],\n            envPreset,\n            bgColor,\n            postProcessingSettings: {\n                ...postProcessingSettings\n            },\n            showGrid,\n            wireframe,\n            groundType,\n            showEnvironment,\n            showLightSpheres,\n            envIntensity,\n            envBlur,\n            cameraPosition: new three__WEBPACK_IMPORTED_MODULE_27__.Vector3(5, 2, 5),\n            cameraTarget: new three__WEBPACK_IMPORTED_MODULE_27__.Vector3(0, 1, 0)\n        };\n    }, []);\n    const orbitControlsRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const sceneRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const [renderer, setRenderer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // History state for undo/redo\n    const [history, setHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [historyIndex, setHistoryIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(-1);\n    const isUndoRedoActionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const skipHistoryTrackingRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const isUndoRedoInProgressRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const [showModelStats, setShowModelStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Debug function to inspect history state\n    const debugHistory = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        console.log(\"=== HISTORY DEBUG ===\");\n        console.log(\"Current historyIndex:\", historyIndex);\n        console.log(\"History length:\", history.length);\n        console.log(\"Can undo:\", historyIndex > 0);\n        console.log(\"Can redo:\", historyIndex < history.length - 1);\n        console.log(\"isUndoRedoActionRef.current:\", isUndoRedoActionRef.current);\n        console.log(\"skipHistoryTrackingRef.current:\", skipHistoryTrackingRef.current);\n        console.log(\"isUndoRedoInProgressRef.current:\", isUndoRedoInProgressRef.current);\n        console.log(\"History entries:\", history.map((_, i)=>\"\".concat(i).concat(i === historyIndex ? \" (current)\" : \"\")));\n        console.log(\"==================\");\n    }, [\n        historyIndex,\n        history\n    ]);\n    // Expose debug function to window for testing\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        window.debugHistory = debugHistory;\n        return ()=>{\n            delete window.debugHistory;\n        };\n    }, [\n        debugHistory\n    ]);\n    // Get full configuration for history\n    const getFullConfig = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        return JSON.parse(JSON.stringify({\n            selectedModel,\n            uploadedModel,\n            lights,\n            envPreset,\n            bgColor,\n            showEnvironment,\n            showLightSpheres,\n            customHdri,\n            envIntensity,\n            envBlur,\n            envRotation,\n            postProcessingEnabled,\n            postProcessingSettings,\n            showGrid,\n            wireframe,\n            groundType,\n            selectedObjects: selectedObjects.map((obj)=>obj.uuid),\n            // Store object states for transforms and materials\n            objectStates: sceneObjects.map((obj)=>({\n                    uuid: obj.uuid,\n                    name: obj.name,\n                    position: obj.position.toArray(),\n                    rotation: obj.rotation.toArray(),\n                    scale: obj.scale.toArray(),\n                    material: obj.material ? {\n                        type: obj.material.type,\n                        color: obj.material.color ? obj.material.color.getHexString() : \"ffffff\",\n                        roughness: obj.material.roughness,\n                        metalness: obj.material.metalness,\n                        clearcoat: obj.material.clearcoat,\n                        clearcoatRoughness: obj.material.clearcoatRoughness,\n                        wireframe: obj.material.wireframe,\n                        transparent: obj.material.transparent,\n                        opacity: obj.material.opacity,\n                        envMapIntensity: obj.material.envMapIntensity\n                    } : null\n                }))\n        }));\n    }, [\n        selectedModel,\n        uploadedModel,\n        lights,\n        envPreset,\n        bgColor,\n        showEnvironment,\n        showLightSpheres,\n        customHdri,\n        envIntensity,\n        envBlur,\n        envRotation,\n        postProcessingEnabled,\n        postProcessingSettings,\n        showGrid,\n        wireframe,\n        groundType,\n        selectedObjects,\n        sceneObjects\n    ]);\n    // Add initial state to history when component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (history.length === 0) {\n            console.log(\"[Initial] Adding initial state to history\");\n            const initialState = getFullConfig();\n            setHistory([\n                initialState\n            ]);\n            setHistoryIndex(0);\n        }\n    }, [\n        history.length,\n        getFullConfig\n    ]);\n    // Add a new state to the history\n    const addToHistory = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (isUndoRedoActionRef.current || skipHistoryTrackingRef.current) {\n            console.log(\"[addToHistory] Skipping - undo/redo action in progress\");\n            return;\n        }\n        const newState = getFullConfig();\n        // Check if the new state is different from the current state\n        if (history.length > 0 && historyIndex >= 0) {\n            const currentState = history[historyIndex];\n            if (JSON.stringify(currentState) === JSON.stringify(newState)) {\n                console.log(\"[addToHistory] Skipping - state unchanged\");\n                return;\n            }\n        }\n        const newHistory = history.slice(0, historyIndex + 1);\n        setHistory([\n            ...newHistory,\n            newState\n        ]);\n        const newIndex = historyIndex + 1;\n        setHistoryIndex(newIndex);\n        console.log(\"[addToHistory] Added state. New historyIndex:\", newIndex, \"History length:\", newHistory.length + 1, \"Can redo:\", newIndex < newHistory.length);\n    }, [\n        history,\n        historyIndex,\n        getFullConfig\n    ]);\n    const pendingSelectedObjectsUUIDs = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Define forceUpdate and version setters early so they can be used in restoreConfig\n    const [_, setForceUpdate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const forceUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>setForceUpdate((f)=>f + 1), []);\n    const [transformVersion, setTransformVersion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [materialVersion, setMaterialVersion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const restoreConfig = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((config)=>{\n        setSelectedModel(config.selectedModel);\n        setUploadedModel(config.uploadedModel);\n        setLights(config.lights);\n        setEnvPreset(config.envPreset);\n        setBgColor(config.bgColor);\n        setShowEnvironment(config.showEnvironment);\n        setShowLightSpheres(config.showLightSpheres);\n        setCustomHdri(config.customHdri);\n        setEnvIntensity(config.envIntensity);\n        setEnvBlur(config.envBlur);\n        setEnvRotation(config.envRotation);\n        setPostProcessingEnabled(config.postProcessingEnabled);\n        setPostProcessingSettings(config.postProcessingSettings);\n        setShowGrid(config.showGrid);\n        setWireframe(config.wireframe);\n        setGroundType(config.groundType);\n        // Restore object states (transforms and materials)\n        if (config.objectStates && sceneObjects.length > 0) {\n            config.objectStates.forEach((savedState)=>{\n                const obj = sceneObjects.find((o)=>o.uuid === savedState.uuid);\n                if (obj) {\n                    // Restore transform\n                    obj.position.fromArray(savedState.position);\n                    obj.rotation.fromArray(savedState.rotation);\n                    obj.scale.fromArray(savedState.scale);\n                    // Restore material properties\n                    if (savedState.material && obj.material) {\n                        const material = obj.material;\n                        if (material.color) {\n                            material.color.setHex(parseInt(savedState.material.color, 16));\n                        }\n                        if (savedState.material.roughness !== undefined) {\n                            material.roughness = savedState.material.roughness;\n                        }\n                        if (savedState.material.metalness !== undefined) {\n                            material.metalness = savedState.material.metalness;\n                        }\n                        if (savedState.material.clearcoat !== undefined) {\n                            material.clearcoat = savedState.material.clearcoat;\n                        }\n                        if (savedState.material.clearcoatRoughness !== undefined) {\n                            material.clearcoatRoughness = savedState.material.clearcoatRoughness;\n                        }\n                        if (savedState.material.wireframe !== undefined) {\n                            material.wireframe = savedState.material.wireframe;\n                        }\n                        if (savedState.material.transparent !== undefined) {\n                            material.transparent = savedState.material.transparent;\n                        }\n                        if (savedState.material.opacity !== undefined) {\n                            material.opacity = savedState.material.opacity;\n                        }\n                        if (savedState.material.envMapIntensity !== undefined) {\n                            material.envMapIntensity = savedState.material.envMapIntensity;\n                        }\n                        material.needsUpdate = true;\n                    }\n                }\n            });\n            // Force update to reflect changes\n            forceUpdate();\n            // Don't increment versions during restore to avoid triggering history loop\n            if (!isUndoRedoActionRef.current) {\n                setMaterialVersion((v)=>v + 1);\n                setTransformVersion((v)=>v + 1);\n            }\n        }\n        // Store selectedObjects UUIDs to be restored after sceneObjects update\n        pendingSelectedObjectsUUIDs.current = config.selectedObjects || [];\n        console.log(\"[restoreConfig] Restoring config\", config);\n    }, [\n        sceneObjects,\n        forceUpdate,\n        setMaterialVersion,\n        setTransformVersion\n    ]);\n    // After sceneObjects update, restore selectedObjects from UUIDs\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (pendingSelectedObjectsUUIDs.current && sceneObjects.length > 0) {\n            const uuids = pendingSelectedObjectsUUIDs.current;\n            const newSelected = sceneObjects.filter((obj)=>uuids.includes(obj.uuid));\n            setSelectedObjects(newSelected);\n            pendingSelectedObjectsUUIDs.current = null;\n            console.log(\"[restoreConfig] Restored selectedObjects\", newSelected);\n        }\n    }, [\n        sceneObjects\n    ]);\n    // Undo action\n    const undoAction = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        // Prevent rapid undo operations\n        if (isUndoRedoInProgressRef.current) {\n            console.log(\"[undoAction] Skipping - undo/redo already in progress\");\n            return;\n        }\n        console.log(\"[undoAction] Current historyIndex:\", historyIndex, \"History length:\", history.length);\n        if (historyIndex > 0) {\n            console.log(\"[undoAction] Performing undo to index:\", historyIndex - 1);\n            // Set all flags to prevent interference\n            isUndoRedoInProgressRef.current = true;\n            isUndoRedoActionRef.current = true;\n            skipHistoryTrackingRef.current = true;\n            const prevConfig = history[historyIndex - 1];\n            restoreConfig(prevConfig);\n            setHistoryIndex(historyIndex - 1);\n            // Reset flags after operation completes\n            setTimeout(()=>{\n                isUndoRedoActionRef.current = false;\n                skipHistoryTrackingRef.current = false;\n                isUndoRedoInProgressRef.current = false;\n                console.log(\"[undoAction] Undo completed, all flags reset\");\n            }, 1000);\n        } else {\n            console.log(\"[undoAction] Cannot undo - at beginning of history\");\n        }\n    }, [\n        historyIndex,\n        history,\n        restoreConfig\n    ]);\n    // Redo action\n    const redoAction = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        // Prevent rapid redo operations\n        if (isUndoRedoInProgressRef.current) {\n            console.log(\"[redoAction] Skipping - undo/redo already in progress\");\n            return;\n        }\n        debugHistory();\n        console.log(\"[redoAction] Current historyIndex:\", historyIndex, \"History length:\", history.length);\n        if (historyIndex < history.length - 1) {\n            console.log(\"[redoAction] Performing redo to index:\", historyIndex + 1);\n            // Set all flags to prevent interference\n            isUndoRedoInProgressRef.current = true;\n            isUndoRedoActionRef.current = true;\n            skipHistoryTrackingRef.current = true;\n            const nextConfig = history[historyIndex + 1];\n            console.log(\"[redoAction] Next config:\", nextConfig);\n            restoreConfig(nextConfig);\n            setHistoryIndex(historyIndex + 1);\n            // Reset flags after operation completes\n            setTimeout(()=>{\n                isUndoRedoActionRef.current = false;\n                skipHistoryTrackingRef.current = false;\n                isUndoRedoInProgressRef.current = false;\n                console.log(\"[redoAction] Redo completed, all flags reset\");\n            }, 1000);\n        } else {\n            console.log(\"[redoAction] Cannot redo - at end of history\");\n        }\n    }, [\n        historyIndex,\n        history,\n        restoreConfig,\n        debugHistory\n    ]);\n    // Track changes to objects and lights for history\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const trackChanges = ()=>{\n            if (selectedObjects.length > 0 || lights.length > 0) {\n                addToHistory({\n                    selectedObjects,\n                    lights\n                });\n            }\n        };\n        // Debounce to avoid too many history entries\n        const timeoutId = setTimeout(trackChanges, 500);\n        return ()=>clearTimeout(timeoutId);\n    }, [\n        selectedObjects,\n        lights,\n        addToHistory\n    ]);\n    const handleObjectClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e)=>{\n        e.stopPropagation();\n        // If placing decal, let the decal placement handler do its job (triggered by model's onClick)\n        if (isPlacingDecal) {\n            return;\n        }\n        console.log(\"Object clicked:\", e.object.name, e.object);\n        if (e.shiftKey) {\n            setSelectedObjects((prev)=>{\n                // Ensure prev is always an array\n                const prevArray = Array.isArray(prev) ? prev : [\n                    prev\n                ].filter(Boolean);\n                if (prevArray.includes(e.object)) {\n                    return prevArray.filter((obj)=>obj !== e.object);\n                }\n                return [\n                    ...prevArray,\n                    e.object\n                ];\n            });\n        } else {\n            setSelectedObjects([\n                e.object\n            ]);\n        }\n    }, [\n        isPlacingDecal,\n        setSelectedObjects\n    ]);\n    // Add debugging for selectedObjects changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"ExperienceNew - selectedObjects changed:\", selectedObjects);\n        console.log(\"ExperienceNew - selectedObjects length:\", selectedObjects === null || selectedObjects === void 0 ? void 0 : selectedObjects.length);\n    }, [\n        selectedObjects\n    ]);\n    // Add debugging for sceneObjects changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"ExperienceNew - sceneObjects changed:\", sceneObjects);\n        console.log(\"ExperienceNew - sceneObjects length:\", sceneObjects === null || sceneObjects === void 0 ? void 0 : sceneObjects.length);\n        console.log(\"ExperienceNew - sceneObjects details:\", sceneObjects === null || sceneObjects === void 0 ? void 0 : sceneObjects.map((obj)=>({\n                name: obj === null || obj === void 0 ? void 0 : obj.name,\n                type: obj === null || obj === void 0 ? void 0 : obj.type,\n                userData: obj === null || obj === void 0 ? void 0 : obj.userData\n            })));\n    }, [\n        sceneObjects\n    ]);\n    const handlePointerMissed = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        // If in decal placement mode, a \"missed\" click (on the canvas background)\n        // should probably cancel placement mode, or do nothing to allow model click-through.\n        // For now, let's prevent it from deselecting objects if placing decal.\n        if (isPlacingDecal) {\n            // setIsPlacingDecal(false); // Optionally, cancel placement on missed click\n            return;\n        }\n        setSelectedObjects([]);\n        setSelectedLight(null);\n    // Do not close the rightPanelTab here, as that's usually handled by clicking outside the panel itself.\n    }, [\n        isPlacingDecal,\n        setIsPlacingDecal\n    ]);\n    // Add the selection group to the scene\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // This effect runs once to add the selection group to the scene\n        return ()=>{\n            // Clean up on unmount\n            if (selectionGroupRef.current && selectionGroupRef.current.parent) {\n                selectionGroupRef.current.parent.remove(selectionGroupRef.current);\n            }\n        };\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Ensure selectedObjects is always an array\n        const objectsArray = Array.isArray(selectedObjects) ? selectedObjects : [\n            selectedObjects\n        ].filter(Boolean);\n        while(selectionGroupRef.current.children.length > 0){\n            selectionGroupRef.current.remove(selectionGroupRef.current.children[0]);\n        }\n        if (objectsArray.length > 0) {\n            const center = new three__WEBPACK_IMPORTED_MODULE_27__.Vector3();\n            objectsArray.forEach((obj)=>{\n                center.add(obj.position);\n            });\n            center.divideScalar(objectsArray.length);\n            selectionGroupRef.current.position.copy(center);\n            objectsArray.forEach((obj)=>{\n                const clone = obj.clone();\n                // Make the clone invisible while keeping its geometry for transforms\n                clone.traverse((child)=>{\n                    if (child.isMesh) {\n                        child.visible = false;\n                    }\n                });\n                clone.position.sub(center);\n                selectionGroupRef.current.add(clone);\n            });\n        }\n    }, [\n        selectedObjects\n    ]);\n    const updateObjectsFromGroup = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        // Ensure selectedObjects is always an array\n        const objectsArray = Array.isArray(selectedObjects) ? selectedObjects : [\n            selectedObjects\n        ].filter(Boolean);\n        if (objectsArray.length > 0 && selectionGroupRef.current) {\n            objectsArray.forEach((obj, index)=>{\n                if (selectionGroupRef.current.children[index]) {\n                    const worldPos = new three__WEBPACK_IMPORTED_MODULE_27__.Vector3();\n                    selectionGroupRef.current.children[index].getWorldPosition(worldPos);\n                    obj.position.copy(worldPos);\n                    obj.rotation.copy(selectionGroupRef.current.rotation);\n                    obj.scale.copy(selectionGroupRef.current.scale);\n                }\n            });\n        }\n    }, [\n        selectedObjects\n    ]);\n    const handleSceneUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((scene)=>{\n        var _scene_children;\n        console.log(\"handleSceneUpdate called with scene:\", scene);\n        console.log(\"Scene type:\", scene.type);\n        console.log(\"Scene children count:\", (_scene_children = scene.children) === null || _scene_children === void 0 ? void 0 : _scene_children.length);\n        setSceneObjects([]);\n        setSelectedObjects([]);\n        const stats = (0,_utils_ArtistTool_sceneUtils__WEBPACK_IMPORTED_MODULE_16__.calculateModelStats)(scene);\n        setModelStats(stats);\n        (0,_utils_ArtistTool_sceneUtils__WEBPACK_IMPORTED_MODULE_16__.setShadowsOnModel)(scene);\n        const objects = [];\n        const initialObjects = [];\n        scene.traverse((object)=>{\n            console.log(\"Traversing object:\", object.name, object.type, \"isMesh:\", object.isMesh, \"userData:\", object.userData);\n            if (object.isMesh) {\n                if (!object.name) {\n                    object.name = \"Mesh_\".concat(object.id);\n                }\n                console.log(\"Adding mesh to objects:\", object.name, \"userData:\", object.userData);\n                objects.push(object);\n                initialObjects.push({\n                    id: object.uuid,\n                    name: object.name,\n                    position: object.position.clone(),\n                    rotation: object.rotation.clone(),\n                    scale: object.scale.clone(),\n                    material: object.material ? {\n                        color: object.material.color ? object.material.color.getHex() : 0xffffff,\n                        roughness: object.material.roughness,\n                        metalness: object.material.metalness,\n                        wireframe: object.material.wireframe,\n                        transparent: object.material.transparent,\n                        opacity: object.material.opacity\n                    } : null\n                });\n            }\n        });\n        console.log(\"Found\", objects.length, \"mesh objects:\", objects.map((obj)=>({\n                name: obj.name,\n                userData: obj.userData\n            })));\n        modelInitialStateRef.current.objects = initialObjects;\n        setSceneObjects(objects);\n    }, []);\n    // Add this effect to detect model changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedModel !== lastSelectedModel) {\n            // Clear the scene objects when model changes\n            setSceneObjects([]);\n            setSelectedObjects([]);\n            setLastSelectedModel(selectedModel);\n        }\n    }, [\n        selectedModel,\n        lastSelectedModel\n    ]);\n    const handleLightMoved = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((lightId, newPosition)=>{\n        setLightPositionHistory((prev)=>({\n                ...prev,\n                [lightId]: [\n                    ...prev[lightId] || [],\n                    newPosition\n                ]\n            }));\n    }, []);\n    const undoLightMove = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((lightId)=>{\n        setLightPositionHistory((prev)=>{\n            const history = prev[lightId] || [];\n            if (history.length <= 1) return prev;\n            const newHistory = history.slice(0, -1);\n            const lastPosition = newHistory[newHistory.length - 1];\n            if (lightRefs.current[lightId]) {\n                lightRefs.current[lightId].position.copy(lastPosition);\n            }\n            return {\n                ...prev,\n                [lightId]: newHistory\n            };\n        });\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleKeyDown = (event)=>{\n            // Handle undo/redo keyboard shortcuts\n            if (event.ctrlKey) {\n                if (event.key === \"z\" && !event.shiftKey) {\n                    event.preventDefault();\n                    undoAction();\n                    return;\n                }\n                if (event.key === \"y\" || event.key === \"z\" && event.shiftKey) {\n                    event.preventDefault();\n                    redoAction();\n                    return;\n                }\n            }\n            // Handle Escape key for decal placement\n            if (event.key === \"Escape\" && isPlacingDecal) {\n                setIsPlacingDecal(false);\n                return;\n            }\n            // Handle light undo\n            if (event.ctrlKey && event.key === \"z\" && selectedLight) {\n                undoLightMove(selectedLight);\n            }\n            // Handle transform mode shortcuts\n            if (selectedObjects.length > 0) {\n                switch(event.key){\n                    case \"g\":\n                        setTransformMode(\"translate\");\n                        break;\n                    case \"r\":\n                        setTransformMode(\"rotate\");\n                        break;\n                    case \"s\":\n                        setTransformMode(\"scale\");\n                        break;\n                    case \"Escape\":\n                        setSelectedObjects([]);\n                        break;\n                }\n            }\n            // Handle select all\n            if (event.ctrlKey && event.key === \"a\") {\n                event.preventDefault();\n                if (sceneObjects.length > 0) {\n                    setSelectedObjects([\n                        ...sceneObjects\n                    ]);\n                }\n            }\n        };\n        window.addEventListener(\"keydown\", handleKeyDown);\n        return ()=>window.removeEventListener(\"keydown\", handleKeyDown);\n    }, [\n        selectedLight,\n        undoLightMove,\n        sceneObjects,\n        selectedObjects.length,\n        undoAction,\n        redoAction,\n        isPlacingDecal,\n        setIsPlacingDecal\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        return ()=>{\n            if (uploadedModel) {\n                URL.revokeObjectURL(uploadedModel);\n            }\n            if (customHdri) {\n                URL.revokeObjectURL(customHdri);\n            }\n        };\n    }, [\n        uploadedModel,\n        customHdri\n    ]);\n    const handleCameraView = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((view)=>{\n        (0,_utils_ArtistTool_cameraUtils__WEBPACK_IMPORTED_MODULE_14__.setCameraView)(orbitControlsRef.current, view);\n    }, []);\n    // Add the panelRef to ExperienceNew instead of in Toolbar\n    const panelRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [activeAnimation, setActiveAnimation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [animationStartTime, setAnimationStartTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const requestRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    // Animation loop handler\n    const animateCamera = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((timestamp)=>{\n        if (!activeAnimation || !orbitControlsRef.current) {\n            return;\n        }\n        // Calculate animation progress (0 to 1)\n        const animation = _animations_CameraAnimations__WEBPACK_IMPORTED_MODULE_17__.predefinedAnimations.find((a)=>a.id === activeAnimation);\n        if (!animation) return;\n        if (animationStartTime === 0) {\n            setAnimationStartTime(timestamp);\n        }\n        const elapsed = timestamp - animationStartTime;\n        const progress = elapsed % animation.duration / animation.duration;\n        // Get the target position (center of selected objects or scene center)\n        const target = new three__WEBPACK_IMPORTED_MODULE_27__.Vector3(0, 0, 0);\n        if (selectedObjects.length > 0) {\n            // Calculate center of selected objects\n            const objectsArray = Array.isArray(selectedObjects) ? selectedObjects : [\n                selectedObjects\n            ].filter(Boolean);\n            if (objectsArray.length > 0) {\n                const center = new three__WEBPACK_IMPORTED_MODULE_27__.Vector3();\n                objectsArray.forEach((obj)=>{\n                    center.add(obj.position);\n                });\n                center.divideScalar(objectsArray.length);\n                target.copy(center);\n            }\n        }\n        // Animate camera using the animation function\n        animation.animate(orbitControlsRef.current.object, orbitControlsRef.current, progress, target);\n        requestRef.current = requestAnimationFrame(animateCamera);\n    }, [\n        activeAnimation,\n        orbitControlsRef,\n        animationStartTime,\n        selectedObjects\n    ]);\n    // Setup animation loop\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (activeAnimation) {\n            setAnimationStartTime(0);\n            requestRef.current = requestAnimationFrame(animateCamera);\n            // Disable orbit controls during animation\n            if (orbitControlsRef.current) {\n                orbitControlsRef.current.enabled = false;\n            }\n        } else {\n            // Re-enable orbit controls when animation stops\n            if (orbitControlsRef.current && selectedObjects.length === 0) {\n                orbitControlsRef.current.enabled = true;\n            }\n        }\n        return ()=>{\n            if (requestRef.current) {\n                cancelAnimationFrame(requestRef.current);\n            }\n        };\n    }, [\n        activeAnimation,\n        animateCamera,\n        selectedObjects\n    ]);\n    // Handle animation playback from Outliner\n    const handlePlayAnimation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((animationId)=>{\n        setActiveAnimation(animationId);\n    }, []);\n    const getEnvironmentPreset = ()=>{\n        return envPreset;\n    };\n    // Reset function\n    const handleReset = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        // Check if there's a saved scene in localStorage\n        const savedScene = localStorage.getItem(\"agape_saved_scene\");\n        if (savedScene) {\n            const confirmReset = window.confirm(\"There is a saved scene in your browser storage. Resetting will clear this saved scene. Do you want to continue?\");\n            if (!confirmReset) {\n                return;\n            }\n            // Clear the saved scene from localStorage\n            localStorage.removeItem(\"agape_saved_scene\");\n        }\n        console.log(\"Resetting scene to initial state\");\n        setSceneObjects([]);\n        setSelectedObjects([]);\n        // Reset lights to default configuration\n        setLights([\n            {\n                id: \"defaultDirectional\",\n                type: \"directional\",\n                position: [\n                    2.5,\n                    5.5,\n                    5\n                ],\n                intensity: 1.0,\n                color: \"#ffffff\",\n                name: \"Main Directional Light\",\n                castShadow: true,\n                helperVisible: false\n            },\n            {\n                id: \"keyLight\",\n                type: \"point\",\n                position: [\n                    5,\n                    5,\n                    5\n                ],\n                intensity: 8.0,\n                color: \"#ffffff\",\n                name: \"Key Light\"\n            },\n            {\n                id: \"fillLight\",\n                type: \"point\",\n                position: [\n                    -5,\n                    5,\n                    -5\n                ],\n                intensity: 4.0,\n                color: \"#ffffff\",\n                name: \"Fill Light\"\n            },\n            {\n                id: \"backLight\",\n                type: \"point\",\n                position: [\n                    0,\n                    5,\n                    -5\n                ],\n                intensity: 2.0,\n                color: \"#ffffff\",\n                name: \"Back Light\"\n            }\n        ]);\n        // Reset environment settings\n        setEnvPreset(\"hdri_15\");\n        setBgColor(\"#202020\");\n        setShowEnvironment(false);\n        setShowLightSpheres(true);\n        setCustomHdri(null);\n        setEnvIntensity(1.5);\n        setEnvBlur(0.35);\n        setEnvRotation(0);\n        // Reset post-processing settings\n        setPostProcessingEnabled(false);\n        setPostProcessingSettings({\n            autofocus: {\n                enabled: false,\n                bokehScale: 18\n            },\n            bloom: {\n                enabled: true,\n                intensity: 0.12,\n                threshold: 0.8,\n                radius: 0.4\n            },\n            dof: {\n                enabled: false,\n                focusDistance: 0,\n                aperture: 0.01,\n                bokehScale: 3\n            },\n            vignette: {\n                enabled: true,\n                darkness: 0.5,\n                offset: 0.1\n            }\n        });\n        // Reset scene controls\n        setShowGrid(true);\n        setWireframe(false);\n        setGroundType(\"none\");\n        // Reset camera position\n        if (orbitControlsRef.current) {\n            orbitControlsRef.current.object.position.set(5, 2, 5);\n            orbitControlsRef.current.target.set(0, 1, 0);\n            orbitControlsRef.current.update();\n        }\n        // Reset transform state\n        setTransformMode(\"translate\");\n        setSelectedLight(null);\n        // Reset material shaders\n        setUseDiamondShader(false);\n        setUsePremiumWhiteGold(false);\n        setUsePremiumRoseGold(false);\n        // Clear history\n        setHistory([]);\n        setHistoryIndex(-1);\n        setLightPositionHistory({});\n        // Reset model stats\n        setModelStats({\n            vertices: 0,\n            triangles: 0,\n            materials: 0,\n            animations: 0\n        });\n        // Reset right panel tab\n        setRightPanelTab(null);\n        // Reset active animation\n        setActiveAnimation(null);\n        setAnimationStartTime(0);\n        if (requestRef.current) {\n            cancelAnimationFrame(requestRef.current);\n        }\n        // Clear any custom HDRI or uploaded model URLs\n        if (uploadedModel) {\n            URL.revokeObjectURL(uploadedModel);\n        }\n        if (customHdri) {\n            URL.revokeObjectURL(customHdri);\n        }\n        // Force a model reload by setting selectedModel to null first\n        setSelectedModel(null);\n        // Then set it back to uploaded after a small delay\n        setTimeout(()=>{\n            setSelectedModel(\"uploaded\");\n        }, 100);\n    }, []);\n    const screenshotRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const captureScreenshot = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (screenshotRef.current) {\n            console.log(\"Taking screenshot\");\n            screenshotRef.current();\n        } else {\n            console.error(\"Screenshot function not available yet\");\n        }\n    }, []);\n    // Load saved scene from localStorage on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadSavedScene = ()=>{\n            const savedScene = localStorage.getItem(\"agape_saved_scene\");\n            if (savedScene) {\n                try {\n                    const sceneConfig = JSON.parse(savedScene);\n                    console.log(\"Loading saved scene:\", sceneConfig);\n                    // Only load the saved configuration on initial load\n                    if (!lastSelectedModel) {\n                        console.log(\"Initial load - setting saved model:\", sceneConfig.model);\n                        setSelectedModel(\"uploaded\"); // Always use uploaded for S3 models\n                        // Load the rest of the saved scene configuration\n                        setLights(sceneConfig.lights);\n                        setEnvPreset(sceneConfig.environment.preset);\n                        setBgColor(sceneConfig.environment.bgColor);\n                        setShowEnvironment(sceneConfig.environment.showEnvironment);\n                        setShowLightSpheres(sceneConfig.environment.showLightSpheres);\n                        setEnvIntensity(sceneConfig.environment.intensity);\n                        setEnvBlur(sceneConfig.environment.blur);\n                        setEnvRotation(sceneConfig.environment.rotation);\n                        setPostProcessingEnabled(sceneConfig.postProcessing.enabled);\n                        setPostProcessingSettings(sceneConfig.postProcessing.settings);\n                        setShowGrid(sceneConfig.sceneControls.showGrid);\n                        setWireframe(sceneConfig.sceneControls.wireframe);\n                        setGroundType(sceneConfig.sceneControls.groundType);\n                        // Restore camera position after a short delay to ensure scene is loaded\n                        setTimeout(()=>{\n                            if (orbitControlsRef.current && sceneConfig.camera) {\n                                orbitControlsRef.current.object.position.fromArray(sceneConfig.camera.position);\n                                orbitControlsRef.current.target.fromArray(sceneConfig.camera.target);\n                                orbitControlsRef.current.update();\n                            }\n                        }, 1000);\n                    } else if (configRef.current) {\n                        // For explicit loads with configRef, do a complete load\n                        console.log(\"Explicit load with configRef\");\n                        handleLoadScene(sceneConfig);\n                    }\n                    // Create a function to restore materials\n                    const restoreMaterials = ()=>{\n                        if (!sceneConfig.materials || !sceneObjects.length) {\n                            console.log(\"No materials to restore or scene objects not ready\");\n                            return;\n                        }\n                        console.log(\"Attempting to restore materials for objects:\", sceneObjects);\n                        console.log(\"Saved materials:\", sceneConfig.materials);\n                        const savedMaterialsMap = new Map(sceneConfig.materials.map((m)=>[\n                                m.name,\n                                m.material\n                            ]));\n                        sceneObjects.forEach((obj)=>{\n                            if (obj.material && savedMaterialsMap.has(obj.name)) {\n                                const savedMaterial = savedMaterialsMap.get(obj.name);\n                                console.log(\"Restoring material for object:\", obj.name, savedMaterial);\n                                (0,_utils_ArtistTool_materialUtils__WEBPACK_IMPORTED_MODULE_15__.applyMaterialToObject)(obj, savedMaterial);\n                            } else {\n                                console.log(\"Could not find saved material for object:\", obj.name);\n                            }\n                        });\n                    };\n                    // Set up an interval to check for scene objects\n                    const checkInterval = setInterval(()=>{\n                        if (sceneObjects.length > 0) {\n                            clearInterval(checkInterval);\n                            // Add a small delay to ensure all materials are properly initialized\n                            setTimeout(restoreMaterials, 500);\n                        }\n                    }, 100);\n                    // Clean up interval after 5 seconds if scene objects never become available\n                    setTimeout(()=>{\n                        clearInterval(checkInterval);\n                    }, 5000);\n                    // Restore camera position after a short delay to ensure scene is loaded\n                    setTimeout(()=>{\n                        if (orbitControlsRef.current && sceneConfig.camera) {\n                            orbitControlsRef.current.object.position.fromArray(sceneConfig.camera.position);\n                            orbitControlsRef.current.target.fromArray(sceneConfig.camera.target);\n                            orbitControlsRef.current.update();\n                        }\n                    }, 1000);\n                } catch (error) {\n                    console.error(\"Error loading saved scene:\", error);\n                }\n            }\n        };\n        // Load saved scene immediately on mount\n        loadSavedScene();\n        // Also set up an interval to check for scene readiness\n        const checkSceneReady = setInterval(()=>{\n            if (sceneRef.current && orbitControlsRef.current) {\n                clearInterval(checkSceneReady);\n                loadSavedScene();\n            }\n        }, 100);\n        return ()=>clearInterval(checkSceneReady);\n    }, [\n        sceneObjects,\n        selectedModel\n    ]);\n    const [configState, setConfigState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (sceneObjects.length > 0 && configState && configState.materials) {\n            const applyMaterials = ()=>{\n                console.log(\"Applying materials to scene objects:\", sceneObjects);\n                console.log(\"Saved materials configuration:\", configState.materials);\n                sceneObjects.forEach((obj)=>{\n                    const materialConfig = configState.materials.find((m)=>m.name === obj.name || m.id === obj.uuid);\n                    if (materialConfig && materialConfig.material) {\n                        console.log(\"Found material config for object:\", obj.name, materialConfig.material);\n                        (0,_utils_ArtistTool_materialUtils__WEBPACK_IMPORTED_MODULE_15__.applyMaterialToObject)(obj, materialConfig.material);\n                    } else {\n                        console.log(\"No material config found for object:\", obj.name);\n                    }\n                });\n            };\n            // Add a small delay to ensure all materials are properly initialized\n            const timeoutId = setTimeout(applyMaterials, 500);\n            return ()=>clearTimeout(timeoutId);\n        }\n    }, [\n        sceneObjects,\n        configState\n    ]);\n    const handleLoadScene = (config)=>{\n        try {\n            console.log(\"Loading scene configuration:\", config);\n            // Store the config in the ref\n            configRef.current = config;\n            workingConfigRef.current = config; // Always keep this in sync with last applied config\n            setConfigState(config); // <-- keep in sync for effect\n            // Clear scene objects and selected objects first\n            setSceneObjects([]);\n            setSelectedObjects([]);\n            setLastSelectedModel(null);\n            // Restore scene settings\n            setSelectedModel(\"uploaded\");\n            setLights(config.lights);\n            setEnvPreset(config.environment.preset);\n            setBgColor(config.environment.bgColor);\n            setShowEnvironment(config.environment.showEnvironment);\n            setShowLightSpheres(config.environment.showLightSpheres);\n            setEnvIntensity(config.environment.intensity);\n            setEnvBlur(config.environment.blur);\n            setEnvRotation(config.environment.rotation);\n            setPostProcessingEnabled(config.postProcessing.enabled);\n            setPostProcessingSettings(config.postProcessing.settings);\n            setShowGrid(config.sceneControls.showGrid);\n            setWireframe(config.sceneControls.wireframe);\n            setGroundType(config.sceneControls.groundType);\n            // Wait for the scene to update before applying camera position\n            setTimeout(()=>{\n                if (orbitControlsRef.current && config.camera) {\n                    orbitControlsRef.current.object.position.fromArray(config.camera.position);\n                    orbitControlsRef.current.target.fromArray(config.camera.target);\n                    orbitControlsRef.current.update();\n                }\n            }, 1000);\n            // Restore materials after sceneObjects are ready\n            const restoreMaterials = ()=>{\n                if (!config.materials || !sceneObjects.length) {\n                    console.log(\"No materials to restore or scene objects not ready\");\n                    return;\n                }\n                console.log(\"Restoring materials for objects:\", sceneObjects);\n                const savedMaterialsMap = new Map(config.materials.map((m)=>[\n                        m.name,\n                        m.material\n                    ]));\n                sceneObjects.forEach((obj)=>{\n                    if (obj.material && savedMaterialsMap.has(obj.name)) {\n                        const savedMaterial = savedMaterialsMap.get(obj.name);\n                        console.log(\"Restoring material for object:\", obj.name, savedMaterial);\n                        (0,_utils_ArtistTool_materialUtils__WEBPACK_IMPORTED_MODULE_15__.applyMaterialToObject)(obj, savedMaterial);\n                    } else {\n                        console.log(\"Could not find saved material for object:\", obj.name);\n                    }\n                });\n            };\n            if (sceneObjects.length > 0) {\n                setTimeout(restoreMaterials, 500);\n            } else {\n                const checkInterval = setInterval(()=>{\n                    if (sceneObjects.length > 0) {\n                        clearInterval(checkInterval);\n                        setTimeout(restoreMaterials, 500);\n                    }\n                }, 100);\n                setTimeout(()=>{\n                    clearInterval(checkInterval);\n                }, 5000);\n            }\n        } catch (error) {\n            console.error(\"Error loading scene configuration:\", error);\n        }\n    };\n    const handleSaveScene = async (sceneName, description, saveType)=>{\n        // Use workingConfigRef if it exists and matches the current state, otherwise build from state\n        let sceneConfig;\n        if (workingConfigRef.current) {\n            // Use a shallow compare for top-level keys (or always use workingConfigRef for now)\n            sceneConfig = {\n                ...workingConfigRef.current,\n                name: sceneName,\n                timestamp: new Date().toISOString()\n            };\n        } else {\n            var _orbitControlsRef_current, _orbitControlsRef_current1;\n            // fallback to building from state\n            sceneConfig = {\n                name: sceneName,\n                timestamp: new Date().toISOString(),\n                model: selectedModel,\n                lights: lights.map((light)=>{\n                    var _lightRefs_current_light_id;\n                    return {\n                        ...light,\n                        position: ((_lightRefs_current_light_id = lightRefs.current[light.id]) === null || _lightRefs_current_light_id === void 0 ? void 0 : _lightRefs_current_light_id.position.toArray()) || light.position\n                    };\n                }),\n                environment: {\n                    preset: envPreset,\n                    intensity: envIntensity,\n                    blur: envBlur,\n                    rotation: envRotation,\n                    showEnvironment,\n                    bgColor,\n                    customHdri\n                },\n                postProcessing: {\n                    enabled: postProcessingEnabled,\n                    settings: postProcessingSettings\n                },\n                sceneControls: {\n                    showGrid,\n                    wireframe,\n                    groundType,\n                    showLightSpheres\n                },\n                camera: {\n                    position: (_orbitControlsRef_current = orbitControlsRef.current) === null || _orbitControlsRef_current === void 0 ? void 0 : _orbitControlsRef_current.object.position.toArray(),\n                    target: (_orbitControlsRef_current1 = orbitControlsRef.current) === null || _orbitControlsRef_current1 === void 0 ? void 0 : _orbitControlsRef_current1.target.toArray()\n                },\n                materials: sceneObjects.map((obj)=>{\n                    const material = obj.material;\n                    if (!material) return {\n                        id: obj.uuid,\n                        name: obj.name,\n                        material: null\n                    };\n                    return {\n                        id: obj.uuid,\n                        name: obj.name,\n                        material: {\n                            type: material.type,\n                            color: material.color ? material.color.getHexString() : \"ffffff\",\n                            roughness: material.roughness,\n                            metalness: material.metalness,\n                            wireframe: material.wireframe,\n                            transparent: material.transparent,\n                            opacity: material.opacity,\n                            envMapIntensity: material.envMapIntensity,\n                            ...material.type === \"MeshRefractionMaterial\" && {\n                                aberrationStrength: material.aberrationStrength,\n                                toneMapped: material.toneMapped,\n                                ior: material.ior,\n                                colorEnvMapRotY: material.colorEnvMapRotY\n                            }\n                        }\n                    };\n                })\n            };\n        }\n        if (saveType === \"local\") {\n            // Save to localStorage\n            localStorage.setItem(\"agape_saved_scene\", JSON.stringify(sceneConfig));\n        } else if (saveType === \"db\") {\n            // Save to DB (cloud)\n            try {\n                const version = await (0,_lib_actions_version_actions__WEBPACK_IMPORTED_MODULE_23__.createVersion)({\n                    userId: user.id,\n                    workspaceId: project._id,\n                    name: sceneName,\n                    config: sceneConfig,\n                    description: description\n                });\n                console.log(\"version\", version);\n                if (version) {\n                    addVersion(project._id, version);\n                }\n                sonner__WEBPACK_IMPORTED_MODULE_24__.toast.success(\"Scene saved to cloud!\");\n            } catch (err) {\n                sonner__WEBPACK_IMPORTED_MODULE_24__.toast.error(\"Failed to save scene to cloud\");\n            }\n        } else {\n            // Export as file\n            const blob = new Blob([\n                JSON.stringify(sceneConfig, null, 2)\n            ], {\n                type: \"application/json\"\n            });\n            const url = URL.createObjectURL(blob);\n            const a = document.createElement(\"a\");\n            a.href = url;\n            a.download = \"\".concat(sceneName.replace(/\\s+/g, \"_\"), \".json\");\n            document.body.appendChild(a);\n            a.click();\n            document.body.removeChild(a);\n            URL.revokeObjectURL(url);\n        }\n    };\n    // Engraving state\n    const [engravingText, setEngravingText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [decalPosition, setDecalPosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        0,\n        1.68,\n        0\n    ]);\n    const [decalScale, setDecalScale] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        2.8,\n        0.9,\n        1.5\n    ]);\n    const [decalRotation, setDecalRotation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        -Math.PI / 2,\n        0,\n        0\n    ]);\n    const [decalColor, setDecalColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"#cbcbcb\");\n    const [selectedFont, setSelectedFont] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"/fonts/ttf/RobotoSerif-Regular.ttf\");\n    // Diamond shader state\n    const [useDiamondShader, setUseDiamondShader] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [usePremiumWhiteGold, setUsePremiumWhiteGold] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [usePremiumRoseGold, setUsePremiumRoseGold] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleEngraveText = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((text)=>{\n        setEngravingText(text);\n        // Store in localStorage to make it persist across panel changes and page reloads\n        localStorage.setItem(\"engravingText\", text);\n    }, []);\n    const handleFontChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((font)=>{\n        setSelectedFont(font);\n        localStorage.setItem(\"engravingFont\", font);\n    }, []);\n    // Load saved engraving text on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const savedEngravingText = localStorage.getItem(\"engravingText\");\n        if (savedEngravingText) {\n            setEngravingText(savedEngravingText);\n        }\n    }, []);\n    const handleDecalPlacement = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((event)=>{\n        handleDecalPlacement(event, setIsPlacingDecal, setDecalPosition, setDecalRotation);\n    }, [\n        setDecalPosition,\n        setDecalRotation,\n        setIsPlacingDecal\n    ]);\n    // Get predefined decal positions based on selected model\n    const getDefaultDecalPosition = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((modelId)=>{\n        const modelConfigs = {\n            chosenring: [\n                0,\n                1.68,\n                0\n            ],\n            testring: [\n                0,\n                1.68,\n                0\n            ],\n            agapering1: [\n                0,\n                1.2,\n                0\n            ],\n            agapering2: [\n                0,\n                1.2,\n                0\n            ],\n            ringa: [\n                0,\n                0.26,\n                0\n            ],\n            ringb: [\n                0,\n                0.5,\n                0\n            ],\n            ringc: [\n                0,\n                0.26,\n                0\n            ]\n        };\n        return modelConfigs[modelId] || [\n            0,\n            1.68,\n            0\n        ];\n    }, []);\n    const getDefaultDecalRotation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((modelId)=>{\n        const modelConfigs = {\n            chosenring: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            testring: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            agapering1: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            agapering2: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            ringa: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            ringb: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            ringc: [\n                -Math.PI / 2,\n                0,\n                0\n            ]\n        };\n        return modelConfigs[modelId] || [\n            -Math.PI / 2,\n            0,\n            0\n        ];\n    }, []);\n    const getDefaultDecalScale = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((modelId)=>{\n        const modelConfigs = {\n            chosenring: [\n                6.0,\n                2.0,\n                1.5\n            ],\n            testring: [\n                6.0,\n                2.0,\n                1.5\n            ],\n            agapering1: [\n                3.2,\n                1.8,\n                1.0\n            ],\n            agapering2: [\n                3.2,\n                1.8,\n                1.0\n            ],\n            ringa: [\n                1.8,\n                2.0,\n                0.8\n            ],\n            ringb: [\n                1.8,\n                2.0,\n                0.8\n            ],\n            ringc: [\n                1.8,\n                2.0,\n                0.8\n            ]\n        };\n        return modelConfigs[modelId] || [\n            4.2,\n            2.0,\n            1.5\n        ];\n    }, []);\n    // Calculate dynamic scale based on text length and model type\n    const calculateDynamicScale = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((baseScale, text, modelId)=>{\n        if (!text || text.length === 0) return baseScale;\n        const textLength = text.length;\n        let [scaleX, scaleY, scaleZ] = baseScale;\n        // Calculate scale multiplier based on text length\n        let scaleMultiplier = 1.0;\n        if (textLength > 10) {\n            // For very long text, scale up more aggressively\n            scaleMultiplier = 1.3 + (textLength - 10) * 0.08;\n        } else if (textLength > 6) {\n            // For medium length text, scale up moderately\n            scaleMultiplier = 1.0 + (textLength - 6) * 0.08;\n        } else if (textLength < 4) {\n            // For short text, scale down slightly to prevent oversized appearance\n            scaleMultiplier = 0.8 + textLength * 0.05;\n        }\n        // Apply text length scaling to X-axis (width) primarily\n        scaleX = scaleX * scaleMultiplier;\n        // For very long text, also adjust the height slightly\n        if (textLength > 8) {\n            scaleY = scaleY * Math.min(1.2, 1.0 + (textLength - 8) * 0.02);\n        }\n        // Cap maximum scale to prevent excessive sizing (updated for larger Y values)\n        const maxScale = {\n            chosenring: [\n                12.0,\n                2.5,\n                2.0\n            ],\n            testring: [\n                12.0,\n                2.5,\n                2.0\n            ],\n            agapering1: [\n                6.5,\n                2.2,\n                1.5\n            ],\n            agapering2: [\n                6.5,\n                2.2,\n                1.5\n            ],\n            ringa: [\n                4.0,\n                2.5,\n                1.2\n            ],\n            ringb: [\n                4.0,\n                2.5,\n                1.2\n            ],\n            ringc: [\n                4.0,\n                2.5,\n                1.2\n            ]\n        };\n        const limits = maxScale[modelId] || [\n            8.5,\n            2.5,\n            2.0\n        ];\n        scaleX = Math.min(scaleX, limits[0]);\n        scaleY = Math.min(scaleY, limits[1]);\n        scaleZ = Math.min(scaleZ, limits[2]);\n        return [\n            scaleX,\n            scaleY,\n            scaleZ\n        ];\n    }, []);\n    // Update scale when text changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedModel && engravingText) {\n            const baseScale = getDefaultDecalScale(selectedModel);\n            const dynamicScale = calculateDynamicScale(baseScale, engravingText, selectedModel);\n            setDecalScale(dynamicScale);\n        }\n    }, [\n        engravingText,\n        selectedModel,\n        getDefaultDecalScale,\n        calculateDynamicScale\n    ]);\n    // Initialize decal position based on selected model\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedModel) {\n            setDecalPosition(getDefaultDecalPosition(selectedModel));\n            setDecalRotation(getDefaultDecalRotation(selectedModel));\n            // Only set initial scale if there's no engraving text yet\n            if (!engravingText) {\n                setDecalScale(getDefaultDecalScale(selectedModel));\n            }\n        }\n    }, [\n        selectedModel,\n        getDefaultDecalPosition,\n        getDefaultDecalRotation,\n        getDefaultDecalScale,\n        engravingText\n    ]);\n    const [isFullscreen, setIsFullscreen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Get camera position based on device type\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const checkMobile = ()=>{\n            setIsMobile(window.innerWidth < 768);\n        };\n        checkMobile();\n        window.addEventListener(\"resize\", checkMobile);\n        return ()=>window.removeEventListener(\"resize\", checkMobile);\n    }, []);\n    // Add state to control outliner collapsed state - initialize based on device type\n    const [outlinerCollapsed, setOutlinerCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        // Initialize based on device type - closed on mobile, open on desktop\n        if (true) {\n            return window.innerWidth < 768;\n        }\n        return false; // Default to open on server-side rendering\n    });\n    // Effect to close outliner when toolbar panels are opened on mobile\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isMobile && rightPanelTab) {\n            setOutlinerCollapsed(true);\n        }\n    }, [\n        isMobile,\n        rightPanelTab\n    ]);\n    // Effect to update outliner collapsed state when mobile state changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Update outliner state based on device type - closed on mobile, open on desktop\n        setOutlinerCollapsed(isMobile);\n    }, [\n        isMobile\n    ]);\n    const getCameraPosition = ()=>{\n        if (isMobile) {\n            return [\n                -1.85,\n                3.25,\n                4.5\n            ];\n        }\n        return [\n            -1.85,\n            1.55,\n            3.5\n        ];\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // On initial mount, fetch and apply the current version from DB\n        const fetchAndApplyCurrentVersion = async ()=>{\n            try {\n                const currentVersion = await (0,_lib_actions_version_actions__WEBPACK_IMPORTED_MODULE_23__.getCurrentVersion)({\n                    userId: user.id,\n                    workspaceId: project._id\n                });\n                if (currentVersion && currentVersion.config) {\n                    handleLoadScene(currentVersion.config);\n                }\n            } catch (err) {\n                console.error(\"Failed to load current version from DB\", err);\n            }\n        };\n        fetchAndApplyCurrentVersion();\n    // Only run on mount\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, []);\n    // Preload all HDRI/EXR environment textures\n    const envTextureCache = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({});\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const hdriFiles = [\n            {\n                key: \"hdri_15\",\n                path: \"/hdris/hdri_15.hdr\",\n                loader: three_examples_jsm_loaders_RGBELoader_js__WEBPACK_IMPORTED_MODULE_28__.RGBELoader\n            },\n            {\n                key: \"hdri_metal\",\n                path: \"/hdris/hdri_metal.exr\",\n                loader: three_examples_jsm_loaders_EXRLoader_js__WEBPACK_IMPORTED_MODULE_29__.EXRLoader\n            },\n            {\n                key: \"hdri_metal2\",\n                path: \"/hdris/metal_hdri2.hdr\",\n                loader: three_examples_jsm_loaders_RGBELoader_js__WEBPACK_IMPORTED_MODULE_28__.RGBELoader\n            },\n            {\n                key: \"hdri_metal3\",\n                path: \"/hdris/metal_hdri3.hdr\",\n                loader: three_examples_jsm_loaders_RGBELoader_js__WEBPACK_IMPORTED_MODULE_28__.RGBELoader\n            },\n            {\n                key: \"hdri_gem\",\n                path: \"/hdris/env_gem_002_30251392af.exr\",\n                loader: three_examples_jsm_loaders_EXRLoader_js__WEBPACK_IMPORTED_MODULE_29__.EXRLoader\n            },\n            {\n                key: \"studio_small\",\n                path: \"/hdris/studio_small_02_2k.hdr\",\n                loader: three_examples_jsm_loaders_RGBELoader_js__WEBPACK_IMPORTED_MODULE_28__.RGBELoader\n            }\n        ];\n        hdriFiles.forEach((param)=>{\n            let { key, path, loader } = param;\n            if (!envTextureCache.current[key]) {\n                new loader().load(path, (texture)=>{\n                    envTextureCache.current[key] = texture;\n                });\n            }\n        });\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_toastify__WEBPACK_IMPORTED_MODULE_2__.ToastContainer, {}, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1809,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Header__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                onReset: handleReset,\n                onScreenshot: captureScreenshot,\n                onSaveScene: handleSaveScene,\n                onLoadScene: handleLoadScene,\n                isFullscreen: isFullscreen,\n                onToggleFullscreen: ()=>setIsFullscreen(!isFullscreen),\n                project: project,\n                user: user,\n                onUndo: undoAction,\n                onRedo: redoAction\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1810,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_InitialOverlayPanel__WEBPACK_IMPORTED_MODULE_19__.InitialOverlayPanel, {}, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1823,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed left-5 top-1/2 transform -translate-y-1/2 z-[1000] transition-all duration-300 \".concat(isFullscreen ? \"opacity-0 pointer-events-none\" : \"opacity-100\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Toolbar__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    transformMode: transformMode,\n                    setTransformMode: setTransformMode,\n                    undoAction: undoAction,\n                    redoAction: redoAction,\n                    rightPanelTab: rightPanelTab,\n                    setRightPanelTab: setRightPanelTab,\n                    orientation: \"vertical\",\n                    sceneObjects: sceneObjects,\n                    setSelectedObjects: setSelectedObjects,\n                    selectedObjects: selectedObjects,\n                    panelRef: panelRef,\n                    isMobile: isMobile,\n                    setOutlinerCollapsed: setOutlinerCollapsed\n                }, void 0, false, {\n                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                    lineNumber: 1831,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1826,\n                columnNumber: 7\n            }, this),\n            rightPanelTab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: panelRef,\n                className: \"fixed left-20 top-1/2 transform -translate-y-1/2 bg-gradient-to-tl from-[#32343D] to-[#14192D] p-4 rounded-lg shadow-lg z-[1000] max-h-[80vh] overflow-y-auto w-[250px] md:w-[300px] border border-[#A3A3A3]/30 scrollbar-none\",\n                children: [\n                    rightPanelTab === \"transform\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TransformPanel__WEBPACK_IMPORTED_MODULE_13__.TransformPanel, {\n                        selectedObjects: selectedObjects,\n                        transformMode: transformMode,\n                        setTransformMode: setTransformMode,\n                        undoAction: undoAction,\n                        redoAction: redoAction,\n                        addToHistory: addToHistory,\n                        invalidate: _react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.m\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1855,\n                        columnNumber: 13\n                    }, this),\n                    rightPanelTab === \"material\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MaterialPanel__WEBPACK_IMPORTED_MODULE_7__.MaterialPanel, {\n                        selectedObjects: selectedObjects,\n                        useDiamondShader: useDiamondShader,\n                        setUseDiamondShader: setUseDiamondShader,\n                        usePremiumWhiteGold: usePremiumWhiteGold,\n                        setUsePremiumWhiteGold: setUsePremiumWhiteGold,\n                        usePremiumRoseGold: usePremiumRoseGold,\n                        setUsePremiumRoseGold: setUsePremiumRoseGold,\n                        renderer: renderer,\n                        forceUpdate: forceUpdate,\n                        setMaterialVersion: setMaterialVersion,\n                        invalidate: _react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.m,\n                        addToHistory: addToHistory\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1867,\n                        columnNumber: 13\n                    }, this),\n                    rightPanelTab === \"postprocessing\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PostProcessingPanel__WEBPACK_IMPORTED_MODULE_10__.PostProcessingPanel, {\n                        postProcessingEnabled: postProcessingEnabled,\n                        setPostProcessingEnabled: setPostProcessingEnabled,\n                        postProcessingSettings: postProcessingSettings,\n                        setPostProcessingSettings: setPostProcessingSettings\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1884,\n                        columnNumber: 13\n                    }, this),\n                    rightPanelTab === \"environment\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EnvironmentPanel__WEBPACK_IMPORTED_MODULE_4__.EnvironmentPanel, {\n                        envPreset: envPreset,\n                        setEnvPreset: setEnvPreset,\n                        bgColor: bgColor,\n                        setBgColor: setBgColor,\n                        showEnvironment: showEnvironment,\n                        setShowEnvironment: setShowEnvironment,\n                        customHdri: customHdri,\n                        setCustomHdri: setCustomHdri,\n                        envIntensity: envIntensity,\n                        setEnvIntensity: setEnvIntensity,\n                        showModelStats: showModelStats,\n                        setShowModelStats: setShowModelStats,\n                        envBlur: envBlur,\n                        setEnvBlur: setEnvBlur,\n                        envRotation: envRotation,\n                        setEnvRotation: setEnvRotation\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1893,\n                        columnNumber: 13\n                    }, this),\n                    rightPanelTab === \"lights\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LightPanel__WEBPACK_IMPORTED_MODULE_6__.LightPanel, {\n                        lights: lights,\n                        setLights: setLights,\n                        lightRefs: lightRefs,\n                        showGrid: showGrid,\n                        setShowGrid: setShowGrid,\n                        wireframe: wireframe,\n                        setWireframe: setWireframe,\n                        showLightSpheres: showLightSpheres,\n                        setShowLightSpheres: setShowLightSpheres,\n                        onResetLights: ()=>{\n                            // Reset lights to default configuration\n                            const defaultLights = [\n                                {\n                                    id: \"defaultDirectional\",\n                                    type: \"directional\",\n                                    position: [\n                                        2.5,\n                                        5.5,\n                                        5\n                                    ],\n                                    intensity: 1.0,\n                                    color: \"#ffffff\",\n                                    name: \"Main Directional Light\",\n                                    castShadow: true,\n                                    helperVisible: false\n                                },\n                                {\n                                    id: \"keyLight\",\n                                    type: \"point\",\n                                    position: [\n                                        5,\n                                        5,\n                                        5\n                                    ],\n                                    intensity: 8.0,\n                                    color: \"#ffffff\",\n                                    name: \"Key Light\"\n                                },\n                                {\n                                    id: \"fillLight\",\n                                    type: \"point\",\n                                    position: [\n                                        -5,\n                                        5,\n                                        -5\n                                    ],\n                                    intensity: 4.0,\n                                    color: \"#ffffff\",\n                                    name: \"Fill Light\"\n                                },\n                                {\n                                    id: \"backLight\",\n                                    type: \"point\",\n                                    position: [\n                                        0,\n                                        5,\n                                        -5\n                                    ],\n                                    intensity: 2.0,\n                                    color: \"#ffffff\",\n                                    name: \"Back Light\"\n                                }\n                            ];\n                            setLights(defaultLights);\n                            // Clear selected light\n                            setSelectedLight(null);\n                            // Clear light position history\n                            setLightPositionHistory({});\n                        }\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1914,\n                        columnNumber: 13\n                    }, this),\n                    rightPanelTab === \"engraving\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EngravingPanel__WEBPACK_IMPORTED_MODULE_18__.EngravingPanel, {\n                        onEngraveText: handleEngraveText,\n                        onDecalPositionChange: setDecalPosition,\n                        onDecalScaleChange: setDecalScale,\n                        onDecalRotationChange: setDecalRotation,\n                        decalPosition: decalPosition,\n                        decalScale: decalScale,\n                        decalRotation: decalRotation,\n                        decalColor: decalColor,\n                        onDecalColorChange: setDecalColor,\n                        onFontChange: handleFontChange,\n                        selectedFont: selectedFont,\n                        isPlacingDecal: isPlacingDecal,\n                        setIsPlacingDecal: setIsPlacingDecal,\n                        showDebug: showDecalDebug,\n                        onShowDebugChange: setShowDecalDebug\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1972,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1850,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed right-5 top-24 z-[999] transition-all duration-300 \".concat(isFullscreen ? \"opacity-0 pointer-events-none\" : \"opacity-100\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-tl from-[#32343D] to-[#14192D] shadow-lg rounded-lg overflow-visible scrollbar-none\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Outliner__WEBPACK_IMPORTED_MODULE_9__.Outliner, {\n                        sceneObjects: sceneObjects,\n                        selectedObjects: selectedObjects,\n                        onSelectObject: setSelectedObjects,\n                        onCameraView: handleCameraView,\n                        onPlayAnimation: handlePlayAnimation,\n                        onSaveScene: handleSaveScene,\n                        groundType: groundType,\n                        setGroundType: setGroundType,\n                        collapsed: outlinerCollapsed,\n                        setCollapsed: setOutlinerCollapsed\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 2000,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                    lineNumber: 1999,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1994,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-full\",\n                style: {\n                    background: \"linear-gradient(to bottom right, #363643, #5C5C6E)\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_fiber__WEBPACK_IMPORTED_MODULE_30__.Canvas, {\n                    className: \"w-full h-full\",\n                    camera: {\n                        position: getCameraPosition(),\n                        fov: 45,\n                        near: 0.1,\n                        far: 50\n                    },\n                    shadows: true,\n                    ref: sceneRef,\n                    gl: {\n                        antialias: true,\n                        preserveDrawingBuffer: true\n                    },\n                    onPointerMissed: handlePointerMissed,\n                    raycaster: {\n                        computeOffsets: (e)=>({\n                                offsetX: e.clientX,\n                                offsetY: e.clientY\n                            })\n                    },\n                    frameloop: \"demand\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n                        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_31__.Html, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_32__.Loader, {}, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2045,\n                                columnNumber: 17\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                            lineNumber: 2044,\n                            columnNumber: 15\n                        }, void 0),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ScreenshotTaker, {\n                                onScreenshotRef: (fn)=>{\n                                    screenshotRef.current = fn;\n                                }\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2051,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RendererProvider, {\n                                onRendererReady: (gl)=>{\n                                    setRenderer(gl);\n                                }\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2057,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SceneLights__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                lights: lights,\n                                lightRefs: lightRefs,\n                                selectedLight: selectedLight,\n                                onLightSelect: setSelectedLight,\n                                onLightMoved: handleLightMoved,\n                                showLightSpheres: showLightSpheres,\n                                threePointLighting: true\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2063,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Environment__WEBPACK_IMPORTED_MODULE_3__.Environment, {\n                                preset: getEnvironmentPreset(),\n                                background: showEnvironment,\n                                bgColor: bgColor,\n                                customHdri: customHdri,\n                                intensity: envIntensity,\n                                envRotation: envRotation,\n                                blur: envBlur,\n                                envTextureCache: envTextureCache.current\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2072,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SelectionOutline__WEBPACK_IMPORTED_MODULE_22__.SelectionOutline, {\n                                selectedObjects: selectedObjects,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ModelStage__WEBPACK_IMPORTED_MODULE_20__.ModelStage, {\n                                    selectedModel: selectedModel,\n                                    wireframe: wireframe,\n                                    handleSceneUpdate: handleSceneUpdate,\n                                    handleObjectClick: handleObjectClick,\n                                    uploadedModel: uploadedModel,\n                                    groundType: groundType,\n                                    engravingText: engravingText,\n                                    activeAnimation: activeAnimation,\n                                    decalPosition: decalPosition,\n                                    decalScale: decalScale,\n                                    decalRotation: decalRotation,\n                                    decalColor: decalColor,\n                                    font: selectedFont,\n                                    isPlacingDecal: isPlacingDecal,\n                                    onDecalPlacement: handleDecalPlacement,\n                                    showDecalDebug: showDecalDebug,\n                                    sceneObjects: sceneObjects,\n                                    useDiamondShader: useDiamondShader,\n                                    usePremiumWhiteGold: usePremiumWhiteGold,\n                                    usePremiumRoseGold: usePremiumRoseGold\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                    lineNumber: 2084,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2083,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_33__.OrbitControls, {\n                                ref: orbitControlsRef,\n                                target: [\n                                    0,\n                                    0.5,\n                                    0\n                                ],\n                                makeDefault: true,\n                                enableDamping: true,\n                                // minPolarAngle={0}\n                                // maxPolarAngle={Math.PI / 2.25}\n                                // minDistance={2.5}\n                                maxDistance: 20\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2108,\n                                columnNumber: 13\n                            }, this),\n                            selectedObjects.length > 0 && rightPanelTab === \"transform\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_34__.TransformControls, {\n                                object: selectedObjects.length === 1 ? selectedObjects[0] : selectionGroupRef.current,\n                                mode: transformMode,\n                                onMouseDown: ()=>{\n                                    if (orbitControlsRef.current) orbitControlsRef.current.enabled = false;\n                                },\n                                onMouseUp: ()=>{\n                                    if (orbitControlsRef.current) orbitControlsRef.current.enabled = true;\n                                    if (selectedObjects.length > 1) updateObjectsFromGroup();\n                                    forceUpdate(); // Ensure immediate update after transform\n                                    (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.m)(); // Force R3F to re-render after transform\n                                },\n                                onObjectChange: ()=>{\n                                    if (selectedObjects.length > 1) updateObjectsFromGroup();\n                                    forceUpdate(); // Ensure immediate update after transform\n                                    (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.m)(); // Force R3F to re-render after transform\n                                }\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2120,\n                                columnNumber: 15\n                            }, this),\n                            groundType === \"grid\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"gridHelper\", {\n                                args: [\n                                    50,\n                                    50,\n                                    \"#666666\",\n                                    \"#333333\"\n                                ],\n                                receiveShadow: true\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2146,\n                                columnNumber: 15\n                            }, this),\n                            groundType === \"solid\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n                                \"rotation-x\": -Math.PI / 2,\n                                receiveShadow: true,\n                                position: [\n                                    0,\n                                    -0.01,\n                                    0\n                                ],\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"planeGeometry\", {\n                                        args: [\n                                            200,\n                                            200\n                                        ]\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2154,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meshStandardMaterial\", {\n                                        color: \"#5d5d5d\",\n                                        wireframe: wireframe,\n                                        transparent: true,\n                                        opacity: 0.7\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2155,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2149,\n                                columnNumber: 15\n                            }, this),\n                            groundType === \"reflective\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n                                receiveShadow: true,\n                                rotation: [\n                                    -Math.PI / 2,\n                                    0,\n                                    0\n                                ],\n                                position: [\n                                    0,\n                                    -0.01,\n                                    0\n                                ],\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"planeGeometry\", {\n                                        args: [\n                                            80,\n                                            80\n                                        ]\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2169,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_35__.MeshReflectorMaterial, {\n                                        blur: [\n                                            800,\n                                            800\n                                        ],\n                                        resolution: 2048,\n                                        mixBlur: 0.5,\n                                        mixStrength: 10,\n                                        roughness: 1,\n                                        depthScale: 1,\n                                        minDepthThreshold: 0.4,\n                                        maxDepthThreshold: 1.2,\n                                        color: \"#363643\",\n                                        metalness: 0.01,\n                                        mirror: 0.3,\n                                        transparent: true,\n                                        opacity: 0.5\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2170,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2164,\n                                columnNumber: 15\n                            }, this),\n                            groundType === \"none\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n                                receiveShadow: true,\n                                rotation: [\n                                    -Math.PI / 2,\n                                    0,\n                                    0\n                                ],\n                                position: [\n                                    0,\n                                    -0.01,\n                                    0\n                                ],\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"planeGeometry\", {\n                                        args: [\n                                            200,\n                                            200\n                                        ]\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2193,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"primitive\", {\n                                        object: new three__WEBPACK_IMPORTED_MODULE_27__.ShadowMaterial({\n                                            opacity: 0.1,\n                                            color: new three__WEBPACK_IMPORTED_MODULE_27__.Color(0x000000),\n                                            transparent: true\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2194,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2188,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_36__.GizmoHelper, {\n                                alignment: \"bottom-right\",\n                                margin: [\n                                    80,\n                                    80\n                                ],\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_37__.GizmoViewport, {\n                                    labelColor: \"white\",\n                                    axisColors: [\n                                        \"red\",\n                                        \"green\",\n                                        \"blue\"\n                                    ]\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                    lineNumber: 2206,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2205,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"primitive\", {\n                                object: selectionGroupRef.current\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2212,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PostProcessingEffects__WEBPACK_IMPORTED_MODULE_21__.PostProcessingEffects, {\n                                enabled: postProcessingEnabled,\n                                settings: postProcessingSettings\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2214,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 2042,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                    lineNumber: 2022,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 2016,\n                columnNumber: 7\n            }, this),\n            showModelStats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ModelStats__WEBPACK_IMPORTED_MODULE_8__.ModelStats, {\n                stats: modelStats\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 2222,\n                columnNumber: 26\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s2(EditorExperience, \"JxrURUqzlmQjBBZjlm5WfzcQTGM=\", false, function() {\n    return [\n        _lib_states__WEBPACK_IMPORTED_MODULE_25__.useVersionState\n    ];\n});\n_c3 = EditorExperience;\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"ScreenshotTaker\");\n$RefreshReg$(_c1, \"MaterialPanelWrapper\");\n$RefreshReg$(_c2, \"RendererProvider\");\n$RefreshReg$(_c3, \"EditorExperience\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ArtistTool/EditorExperience.jsx\n"));

/***/ })

});