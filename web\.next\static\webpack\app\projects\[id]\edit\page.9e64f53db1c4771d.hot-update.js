"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/projects/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/ArtistTool/EnvironmentPanel.jsx":
/*!********************************************************!*\
  !*** ./src/components/ArtistTool/EnvironmentPanel.jsx ***!
  \********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EnvironmentPanel: function() { return /* binding */ EnvironmentPanel; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_color__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-color */ \"(app-pages-browser)/./node_modules/.pnpm/react-color@2.19.3_react@18.3.1/node_modules/react-color/es/index.js\");\n/* harmony import */ var _EnvironmentPreviewBall__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./EnvironmentPreviewBall */ \"(app-pages-browser)/./src/components/ArtistTool/EnvironmentPreviewBall.jsx\");\n/* harmony import */ var _barrel_optimize_names_Grid3X3_List_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Grid3X3,List!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/list.js\");\n/* harmony import */ var _barrel_optimize_names_Grid3X3_List_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Grid3X3,List!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/grid-3x3.js\");\n/* harmony import */ var _hooks_useDebounce__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useDebounce */ \"(app-pages-browser)/./src/hooks/useDebounce.ts\");\n/* __next_internal_client_entry_do_not_use__ EnvironmentPanel auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction EnvironmentPanel(param) {\n    let { envPreset, setEnvPreset, bgColor, setBgColor, showEnvironment, setShowEnvironment, customHdri, setCustomHdri, envIntensity, setEnvIntensity, showModelStats, setShowModelStats, envBlur, setEnvBlur, envRotation, setEnvRotation, addToHistory } = param;\n    var _this = this;\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"presets\");\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"single\"); // \"single\" or \"grid\"\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [displayBgColorPicker, setDisplayBgColorPicker] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const itemsPerPage = 6;\n    // Check if device is mobile\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const checkMobile = ()=>{\n            setIsMobile(window.innerWidth < 768);\n        };\n        checkMobile();\n        window.addEventListener(\"resize\", checkMobile);\n        return ()=>window.removeEventListener(\"resize\", checkMobile);\n    }, []);\n    // Force single view on mobile\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isMobile) {\n            setViewMode(\"single\");\n        }\n    }, [\n        isMobile\n    ]);\n    // Reset to first page when switching tabs or view modes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setCurrentPage(0);\n    }, [\n        activeTab,\n        viewMode\n    ]);\n    // Setup debounced history for slider inputs\n    const { addToHistoryImmediate, addToHistoryDebounced } = (0,_hooks_useDebounce__WEBPACK_IMPORTED_MODULE_4__.useHistoryDebounce)(addToHistory || (()=>{}), 900);\n    const handleHdriUpload = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((event)=>{\n        const file = event.target.files[0];\n        if (file) {\n            const url = URL.createObjectURL(file);\n            setCustomHdri(url);\n            setEnvPreset(\"custom\");\n            // Add to history for HDRI upload\n            if (addToHistory) {\n                addToHistoryImmediate();\n            }\n        }\n    }, [\n        setCustomHdri,\n        setEnvPreset,\n        addToHistoryImmediate,\n        addToHistory\n    ]);\n    const presets = [\n        {\n            value: \"hdri_15\",\n            label: \"Metal 1\"\n        },\n        {\n            value: \"hdri_metal\",\n            label: \"Metal 2\"\n        },\n        {\n            value: \"hdri_metal2\",\n            label: \"Metal 3\"\n        },\n        {\n            value: \"hdri_metal3\",\n            label: \"Metal 4\"\n        },\n        {\n            value: \"hdri_gem\",\n            label: \"Gem Env\"\n        },\n        {\n            value: \"sunset\",\n            label: \"Sunset\"\n        },\n        {\n            value: \"dawn\",\n            label: \"Dawn\"\n        },\n        {\n            value: \"night\",\n            label: \"Night\"\n        },\n        {\n            value: \"warehouse\",\n            label: \"Warehouse\"\n        },\n        {\n            value: \"forest\",\n            label: \"Forest\"\n        },\n        {\n            value: \"apartment\",\n            label: \"Apartment\"\n        },\n        {\n            value: \"studio\",\n            label: \"Studio\"\n        },\n        {\n            value: \"studio_small\",\n            label: \"Studio Small\"\n        },\n        {\n            value: \"city\",\n            label: \"City\"\n        },\n        {\n            value: \"none\",\n            label: \"None\"\n        }\n    ];\n    // Pagination logic\n    const totalPages = Math.ceil(presets.length / itemsPerPage);\n    const startIndex = currentPage * itemsPerPage;\n    const endIndex = startIndex + itemsPerPage;\n    const currentPresets = presets.slice(startIndex, endIndex);\n    const goToPage = (page)=>{\n        setCurrentPage(Math.max(0, Math.min(page, totalPages - 1)));\n    };\n    const goToNextPage = ()=>{\n        if (currentPage < totalPages - 1) {\n            setCurrentPage(currentPage + 1);\n        }\n    };\n    const goToPrevPage = ()=>{\n        if (currentPage > 0) {\n            setCurrentPage(currentPage - 1);\n        }\n    };\n    const handleBgColorClick = ()=>{\n        setDisplayBgColorPicker(!displayBgColorPicker);\n    };\n    const handleBgColorClose = ()=>{\n        setDisplayBgColorPicker(false);\n    };\n    const handleBgColorChange = (color)=>{\n        const hexColor = color.hex;\n        setBgColor(hexColor);\n    };\n    const renderSlider = function(label, value, onChange, min, max, step) {\n        let unit = arguments.length > 6 && arguments[6] !== void 0 ? arguments[6] : \"\";\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mb-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center mb-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"text-xs md:text-sm font-medium text-gray-200\",\n                            children: label\n                        }, void 0, false, {\n                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                            lineNumber: 142,\n                            columnNumber: 9\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-[10px] md:text-xs text-gray-400\",\n                            children: [\n                                value.toFixed(2),\n                                unit\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                            lineNumber: 145,\n                            columnNumber: 9\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                    lineNumber: 141,\n                    columnNumber: 7\n                }, _this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                    type: \"range\",\n                    min: min,\n                    max: max,\n                    step: step,\n                    value: value,\n                    onChange: (e)=>{\n                        onChange(e);\n                        if (addToHistory) {\n                            addToHistoryDebounced();\n                        }\n                    },\n                    className: \"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer accent-blue-500\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                    lineNumber: 150,\n                    columnNumber: 7\n                }, _this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n            lineNumber: 140,\n            columnNumber: 5\n        }, _this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"text-white w-full overflow-y-auto text-[10px] md:text-xs scrollbar-none\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-base font-semibold mb-2 text-gray-100\",\n                    children: \"Environment\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                    lineNumber: 171,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-1 mb-4 bg-gray-800/30 p-0.5 rounded-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setActiveTab(\"presets\"),\n                            className: \"flex-1 px-2.5 py-1.5 rounded text-[10px] md:text-xs font-medium transition-all duration-200 \".concat(activeTab === \"presets\" ? \"bg-blue-500/90 text-white shadow-sm\" : \"text-gray-300 hover:bg-gray-700/50\"),\n                            children: \"Presets\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                            lineNumber: 177,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setActiveTab(\"settings\"),\n                            className: \"flex-1 px-2.5 py-1.5 rounded text-[10px] md:text-xs font-medium transition-all duration-200 \".concat(activeTab === \"settings\" ? \"bg-blue-500/90 text-white shadow-sm\" : \"text-gray-300 hover:bg-gray-700/50\"),\n                            children: \"Settings\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                            lineNumber: 197,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                    lineNumber: 176,\n                    columnNumber: 9\n                }, this),\n                activeTab === \"presets\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        !isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end items-center mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex bg-gray-800/50 rounded-lg p-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setViewMode(\"single\"),\n                                        className: \"p-2 rounded transition-all duration-200 relative group \".concat(viewMode === \"single\" ? \"bg-blue-500 text-white\" : \"text-gray-300 hover:text-white hover:bg-gray-700/50\"),\n                                        title: \"Single Preview - Shows one large preview with list of preset names\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Grid3X3_List_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-900 text-white text-[10px] md:text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap z-10\",\n                                                children: \"Single Preview\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setViewMode(\"grid\"),\n                                        className: \"p-2 rounded transition-all duration-200 relative group \".concat(viewMode === \"grid\" ? \"bg-blue-500 text-white\" : \"text-gray-300 hover:text-white hover:bg-gray-700/50\"),\n                                        title: \"Grid Preview - Shows all presets as small preview balls\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Grid3X3_List_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-900 text-white text-[10px] md:text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap z-10\",\n                                                children: \"Grid Preview\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                                lineNumber: 240,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                        lineNumber: 230,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                lineNumber: 215,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                            lineNumber: 214,\n                            columnNumber: 15\n                        }, this),\n                        viewMode === \"single\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EnvironmentPreviewBall__WEBPACK_IMPORTED_MODULE_3__.SingleEnvironmentPreview, {\n                                        preset: envPreset,\n                                        customHdri: customHdri,\n                                        intensity: envIntensity\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-1.5\",\n                                    children: currentPresets.map((preset)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                setEnvPreset(preset.value);\n                                                if (addToHistory) {\n                                                    addToHistoryImmediate();\n                                                }\n                                            },\n                                            className: \"w-full flex items-center justify-between p-2 rounded-md transition-all duration-200 \".concat(envPreset === preset.value ? \"bg-blue-500/20 ring-1 ring-blue-500\" : \"bg-gray-800/50 hover:bg-gray-700/50\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-[10px] md:text-xs font-medium text-gray-200\",\n                                                    children: preset.label\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                                    lineNumber: 278,\n                                                    columnNumber: 23\n                                                }, this),\n                                                envPreset === preset.value && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-1.5 h-1.5 bg-blue-500 rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                                    lineNumber: 282,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, preset.value, true, {\n                                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 21\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                    lineNumber: 261,\n                                    columnNumber: 17\n                                }, this),\n                                totalPages > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mt-4 pt-3 border-t border-gray-700/50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: goToPrevPage,\n                                            disabled: currentPage === 0,\n                                            className: \"flex items-center gap-1 px-2 py-1 rounded text-[10px] md:text-xs transition-all duration-200 \".concat(currentPage === 0 ? \"text-gray-500 cursor-not-allowed\" : \"text-gray-300 hover:text-white hover:bg-gray-700/50\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-3 h-3\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M15 19l-7-7 7-7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                                        lineNumber: 306,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                                    lineNumber: 300,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \"Prev\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                            lineNumber: 291,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1\",\n                                            children: Array.from({\n                                                length: totalPages\n                                            }, (_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>goToPage(i),\n                                                    className: \"w-2 h-2 rounded-full transition-all duration-200 \".concat(currentPage === i ? \"bg-blue-500\" : \"bg-gray-600 hover:bg-gray-500\")\n                                                }, i, false, {\n                                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                                    lineNumber: 318,\n                                                    columnNumber: 25\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                            lineNumber: 316,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: goToNextPage,\n                                            disabled: currentPage === totalPages - 1,\n                                            className: \"flex items-center gap-1 px-2 py-1 rounded text-[10px] md:text-xs transition-all duration-200 \".concat(currentPage === totalPages - 1 ? \"text-gray-500 cursor-not-allowed\" : \"text-gray-300 hover:text-white hover:bg-gray-700/50\"),\n                                            children: [\n                                                \"Next\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-3 h-3\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9 5l7 7-7 7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                                        lineNumber: 346,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                                    lineNumber: 340,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                            lineNumber: 330,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                    lineNumber: 290,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                            lineNumber: 250,\n                            columnNumber: 15\n                        }, this),\n                        viewMode === \"grid\" && !isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-3 gap-3\",\n                            children: presets.map((preset)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        setEnvPreset(preset.value);\n                                        if (addToHistory) {\n                                            addToHistoryImmediate();\n                                        }\n                                    },\n                                    className: \"group relative flex flex-col items-center p-2 rounded-lg transition-all duration-200 \".concat(envPreset === preset.value ? \"bg-blue-500/20 ring-2 ring-blue-500\" : \"bg-gray-800/50 hover:bg-gray-700/50\"),\n                                    children: [\n                                        preset.value !== \"none\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EnvironmentPreviewBall__WEBPACK_IMPORTED_MODULE_3__.EnvironmentPreviewBall, {\n                                            preset: preset.value,\n                                            intensity: envIntensity\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                            lineNumber: 379,\n                                            columnNumber: 23\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 rounded-lg bg-gray-800 flex items-center justify-center text-[10px]\",\n                                            children: \"None\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                            lineNumber: 384,\n                                            columnNumber: 23\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"mt-2 text-[10px] md:text-xs font-medium text-gray-200\",\n                                            children: preset.label\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                            lineNumber: 388,\n                                            columnNumber: 21\n                                        }, this),\n                                        envPreset === preset.value && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-1 right-1 w-2 h-2 bg-blue-500 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                            lineNumber: 392,\n                                            columnNumber: 23\n                                        }, this)\n                                    ]\n                                }, preset.value, true, {\n                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                    lineNumber: 363,\n                                    columnNumber: 19\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                            lineNumber: 361,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                    lineNumber: 211,\n                    columnNumber: 11\n                }, this),\n                activeTab === \"settings\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-800/50 rounded-lg p-1 md:p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xs md:text-sm font-medium text-gray-200 mb-4\",\n                                    children: \"Display Settings\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                    lineNumber: 465,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"flex items-center gap-2 cursor-pointer\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"checkbox\",\n                                                    checked: showEnvironment,\n                                                    onChange: (e)=>setShowEnvironment(e.target.checked),\n                                                    className: \"form-checkbox h-4 w-4 text-blue-500 rounded border-gray-600 focus:ring-blue-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                                    lineNumber: 470,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs md:text-sm text-gray-200\",\n                                                    children: \"Show as Background\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                                    lineNumber: 476,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                            lineNumber: 469,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-xs md:text-sm font-medium text-gray-200 mb-2\",\n                                                    children: \"Background Color\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                                    lineNumber: 482,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-10 h-10 rounded cursor-pointer border border-gray-600\",\n                                                                    style: {\n                                                                        backgroundColor: bgColor\n                                                                    },\n                                                                    onClick: handleBgColorClick\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                                                    lineNumber: 487,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                displayBgColorPicker && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        position: \"absolute\",\n                                                                        zIndex: \"2\"\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            style: {\n                                                                                position: \"fixed\",\n                                                                                top: \"0px\",\n                                                                                right: \"0px\",\n                                                                                bottom: \"0px\",\n                                                                                left: \"0px\"\n                                                                            },\n                                                                            onClick: handleBgColorClose\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                                                            lineNumber: 494,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_color__WEBPACK_IMPORTED_MODULE_2__.ChromePicker, {\n                                                                            color: bgColor,\n                                                                            onChange: handleBgColorChange,\n                                                                            disableAlpha: true\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                                                            lineNumber: 504,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                                                    lineNumber: 493,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                                            lineNumber: 486,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: bgColor,\n                                                            onChange: (e)=>{\n                                                                const value = e.target.value;\n                                                                // Allow typing and validate hex format\n                                                                if (value.match(/^#[0-9A-Fa-f]{0,6}$/) || value === \"\") {\n                                                                    setBgColor(value);\n                                                                    if (addToHistory) {\n                                                                        addToHistoryImmediate();\n                                                                    }\n                                                                }\n                                                            },\n                                                            placeholder: \"#ffffff\",\n                                                            className: \"px-2 py-1 bg-gray-700 border border-gray-600 rounded text-[10px] md:text-xs text-gray-200 focus:border-blue-500 focus:outline-none w-20\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                                            lineNumber: 512,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                                    lineNumber: 485,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                            lineNumber: 481,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                    lineNumber: 468,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                            lineNumber: 464,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-800/50 rounded-lg p-1 md:p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xs md:text-sm font-medium text-gray-200 mb-4\",\n                                    children: \"Environment Adjustments\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                    lineNumber: 538,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        renderSlider(\"Environment Intensity\", envIntensity, (e)=>setEnvIntensity(parseFloat(e.target.value)), 0, 10, 0.01),\n                                        renderSlider(\"Environment Rotation\", envRotation, (e)=>{\n                                            const newValue = Number(e.target.value);\n                                            setEnvRotation(newValue >= 0 ? newValue % 360 : 360 + newValue % 360);\n                                        }, 0, Math.PI * 2, 0.01, \"\\xb0\"),\n                                        renderSlider(\"Background Blur\", envBlur, (e)=>setEnvBlur(parseFloat(e.target.value)), 0, 1, 0.01)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                    lineNumber: 541,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                            lineNumber: 537,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-800/50 rounded-lg p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"flex items-center gap-2 cursor-pointer\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        checked: showModelStats,\n                                        onChange: (e)=>{\n                                            setShowModelStats(e.target.checked);\n                                            if (addToHistory) {\n                                                addToHistoryImmediate();\n                                            }\n                                        },\n                                        className: \"form-checkbox h-4 w-4 text-blue-500 rounded border-gray-600 focus:ring-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                        lineNumber: 580,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs md:text-sm text-gray-200\",\n                                        children: \"Show Model Stats\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                        lineNumber: 592,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                lineNumber: 579,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                            lineNumber: 578,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                    lineNumber: 462,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n            lineNumber: 170,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n        lineNumber: 169,\n        columnNumber: 5\n    }, this);\n}\n_s(EnvironmentPanel, \"4keMXT2cxP25tdnE/bIhsIOGlJg=\", false, function() {\n    return [\n        _hooks_useDebounce__WEBPACK_IMPORTED_MODULE_4__.useHistoryDebounce\n    ];\n});\n_c = EnvironmentPanel;\nvar _c;\n$RefreshReg$(_c, \"EnvironmentPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL0FydGlzdFRvb2wvRW52aXJvbm1lbnRQYW5lbC5qc3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFFeUQ7QUFDZDtBQUlUO0FBQ1c7QUFDWTtBQUVsRCxTQUFTUyxpQkFBaUIsS0FrQmhDO1FBbEJnQyxFQUMvQkMsU0FBUyxFQUNUQyxZQUFZLEVBQ1pDLE9BQU8sRUFDUEMsVUFBVSxFQUNWQyxlQUFlLEVBQ2ZDLGtCQUFrQixFQUNsQkMsVUFBVSxFQUNWQyxhQUFhLEVBQ2JDLFlBQVksRUFDWkMsZUFBZSxFQUNmQyxjQUFjLEVBQ2RDLGlCQUFpQixFQUNqQkMsT0FBTyxFQUNQQyxVQUFVLEVBQ1ZDLFdBQVcsRUFDWEMsY0FBYyxFQUNkQyxZQUFZLEVBQ2IsR0FsQmdDOzs7SUFtQi9CLE1BQU0sQ0FBQ0MsV0FBV0MsYUFBYSxHQUFHNUIsK0NBQVFBLENBQUM7SUFDM0MsTUFBTSxDQUFDNkIsVUFBVUMsWUFBWSxHQUFHOUIsK0NBQVFBLENBQUMsV0FBVyxxQkFBcUI7SUFDekUsTUFBTSxDQUFDK0IsVUFBVUMsWUFBWSxHQUFHaEMsK0NBQVFBLENBQUM7SUFDekMsTUFBTSxDQUFDaUMsYUFBYUMsZUFBZSxHQUFHbEMsK0NBQVFBLENBQUM7SUFDL0MsTUFBTSxDQUFDbUMsc0JBQXNCQyx3QkFBd0IsR0FBR3BDLCtDQUFRQSxDQUFDO0lBQ2pFLE1BQU1xQyxlQUFlO0lBRXJCLDRCQUE0QjtJQUM1Qm5DLGdEQUFTQSxDQUFDO1FBQ1IsTUFBTW9DLGNBQWM7WUFDbEJOLFlBQVlPLE9BQU9DLFVBQVUsR0FBRztRQUNsQztRQUVBRjtRQUNBQyxPQUFPRSxnQkFBZ0IsQ0FBQyxVQUFVSDtRQUNsQyxPQUFPLElBQU1DLE9BQU9HLG1CQUFtQixDQUFDLFVBQVVKO0lBQ3BELEdBQUcsRUFBRTtJQUVMLDhCQUE4QjtJQUM5QnBDLGdEQUFTQSxDQUFDO1FBQ1IsSUFBSTZCLFVBQVU7WUFDWkQsWUFBWTtRQUNkO0lBQ0YsR0FBRztRQUFDQztLQUFTO0lBRWIsd0RBQXdEO0lBQ3hEN0IsZ0RBQVNBLENBQUM7UUFDUmdDLGVBQWU7SUFDakIsR0FBRztRQUFDUDtRQUFXRTtLQUFTO0lBRXhCLDRDQUE0QztJQUU1QyxNQUFNLEVBQUVjLHFCQUFxQixFQUFFQyxxQkFBcUIsRUFBRSxHQUFHcEMsc0VBQWtCQSxDQUN6RWtCLGdCQUFpQixNQUFPLElBRXhCO0lBR0YsTUFBTW1CLG1CQUFtQjVDLGtEQUFXQSxDQUNsQyxDQUFDNkM7UUFDQyxNQUFNQyxPQUFPRCxNQUFNRSxNQUFNLENBQUNDLEtBQUssQ0FBQyxFQUFFO1FBQ2xDLElBQUlGLE1BQU07WUFDUixNQUFNRyxNQUFNQyxJQUFJQyxlQUFlLENBQUNMO1lBQ2hDOUIsY0FBY2lDO1lBQ2R2QyxhQUFhO1lBQ2IsaUNBQWlDO1lBRWpDLElBQUllLGNBQWM7Z0JBQ2hCaUI7WUFDRjtRQUNGO0lBQ0YsR0FDQTtRQUFDMUI7UUFBZU47UUFBY2dDO1FBQXVCakI7S0FBYTtJQUdwRSxNQUFNMkIsVUFBVTtRQUNkO1lBQUVDLE9BQU87WUFBV0MsT0FBTztRQUFVO1FBQ3JDO1lBQUVELE9BQU87WUFBY0MsT0FBTztRQUFVO1FBQ3hDO1lBQUVELE9BQU87WUFBZUMsT0FBTztRQUFVO1FBQ3pDO1lBQUVELE9BQU87WUFBZUMsT0FBTztRQUFVO1FBQ3pDO1lBQUVELE9BQU87WUFBWUMsT0FBTztRQUFVO1FBQ3RDO1lBQUVELE9BQU87WUFBVUMsT0FBTztRQUFTO1FBQ25DO1lBQUVELE9BQU87WUFBUUMsT0FBTztRQUFPO1FBQy9CO1lBQUVELE9BQU87WUFBU0MsT0FBTztRQUFRO1FBQ2pDO1lBQUVELE9BQU87WUFBYUMsT0FBTztRQUFZO1FBQ3pDO1lBQUVELE9BQU87WUFBVUMsT0FBTztRQUFTO1FBQ25DO1lBQUVELE9BQU87WUFBYUMsT0FBTztRQUFZO1FBQ3pDO1lBQUVELE9BQU87WUFBVUMsT0FBTztRQUFTO1FBQ25DO1lBQUVELE9BQU87WUFBZ0JDLE9BQU87UUFBZTtRQUMvQztZQUFFRCxPQUFPO1lBQVFDLE9BQU87UUFBTztRQUMvQjtZQUFFRCxPQUFPO1lBQVFDLE9BQU87UUFBTztLQUNoQztJQUVELG1CQUFtQjtJQUNuQixNQUFNQyxhQUFhQyxLQUFLQyxJQUFJLENBQUNMLFFBQVFNLE1BQU0sR0FBR3RCO0lBQzlDLE1BQU11QixhQUFhM0IsY0FBY0k7SUFDakMsTUFBTXdCLFdBQVdELGFBQWF2QjtJQUM5QixNQUFNeUIsaUJBQWlCVCxRQUFRVSxLQUFLLENBQUNILFlBQVlDO0lBRWpELE1BQU1HLFdBQVcsQ0FBQ0M7UUFDaEIvQixlQUFldUIsS0FBS1MsR0FBRyxDQUFDLEdBQUdULEtBQUtVLEdBQUcsQ0FBQ0YsTUFBTVQsYUFBYTtJQUN6RDtJQUVBLE1BQU1ZLGVBQWU7UUFDbkIsSUFBSW5DLGNBQWN1QixhQUFhLEdBQUc7WUFDaEN0QixlQUFlRCxjQUFjO1FBQy9CO0lBQ0Y7SUFFQSxNQUFNb0MsZUFBZTtRQUNuQixJQUFJcEMsY0FBYyxHQUFHO1lBQ25CQyxlQUFlRCxjQUFjO1FBQy9CO0lBQ0Y7SUFFQSxNQUFNcUMscUJBQXFCO1FBQ3pCbEMsd0JBQXdCLENBQUNEO0lBQzNCO0lBRUEsTUFBTW9DLHFCQUFxQjtRQUN6Qm5DLHdCQUF3QjtJQUMxQjtJQUVBLE1BQU1vQyxzQkFBc0IsQ0FBQ0M7UUFDM0IsTUFBTUMsV0FBV0QsTUFBTUUsR0FBRztRQUMxQjlELFdBQVc2RDtJQUNiO0lBRUEsTUFBTUUsZUFBZSxTQUFDckIsT0FBT0QsT0FBT3VCLFVBQVVWLEtBQUtELEtBQUtZO1lBQU1DLHdFQUFPOzZCQUNuRSw4REFBQ0M7WUFBSUMsV0FBVTs7OEJBQ2IsOERBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQzFCOzRCQUFNMEIsV0FBVTtzQ0FDZDFCOzs7Ozs7c0NBRUgsOERBQUMyQjs0QkFBS0QsV0FBVTs7Z0NBQ2IzQixNQUFNNkIsT0FBTyxDQUFDO2dDQUNkSjs7Ozs7Ozs7Ozs7Ozs4QkFHTCw4REFBQ0s7b0JBQ0NDLE1BQUs7b0JBQ0xsQixLQUFLQTtvQkFDTEQsS0FBS0E7b0JBQ0xZLE1BQU1BO29CQUNOeEIsT0FBT0E7b0JBQ1B1QixVQUFVLENBQUNTO3dCQUNUVCxTQUFTUzt3QkFFVCxJQUFJNUQsY0FBYzs0QkFDaEJrQjt3QkFDRjtvQkFDRjtvQkFDQXFDLFdBQVU7Ozs7Ozs7Ozs7Ozs7SUFLaEIscUJBQ0UsOERBQUNEO1FBQUlDLFdBQVU7a0JBQ2IsNEVBQUNEO1lBQUlDLFdBQVU7OzhCQUNiLDhEQUFDTTtvQkFBR04sV0FBVTs4QkFBNkM7Ozs7Ozs4QkFLM0QsOERBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ087NEJBQ0NDLFNBQVMsSUFBTTdELGFBQWE7NEJBQzVCcUQsV0FBVywrRkFJVixPQUhDdEQsY0FBYyxZQUNWLHdDQUNBO3NDQUVQOzs7Ozs7c0NBYUQsOERBQUM2RDs0QkFDQ0MsU0FBUyxJQUFNN0QsYUFBYTs0QkFDNUJxRCxXQUFXLCtGQUlWLE9BSEN0RCxjQUFjLGFBQ1Ysd0NBQ0E7c0NBRVA7Ozs7Ozs7Ozs7OztnQkFNRkEsY0FBYywyQkFDYiw4REFBQ3FEO29CQUFJQyxXQUFVOzt3QkFFWixDQUFDbEQsMEJBQ0EsOERBQUNpRDs0QkFBSUMsV0FBVTtzQ0FDYiw0RUFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDTzt3Q0FDQ0MsU0FBUyxJQUFNM0QsWUFBWTt3Q0FDM0JtRCxXQUFXLDBEQUlWLE9BSENwRCxhQUFhLFdBQ1QsMkJBQ0E7d0NBRU42RCxPQUFNOzswREFFTiw4REFBQ25GLHdGQUFJQTtnREFBQzBFLFdBQVU7Ozs7OzswREFDaEIsOERBQUNEO2dEQUFJQyxXQUFVOzBEQUF5Tjs7Ozs7Ozs7Ozs7O2tEQUkxTyw4REFBQ087d0NBQ0NDLFNBQVMsSUFBTTNELFlBQVk7d0NBQzNCbUQsV0FBVywwREFJVixPQUhDcEQsYUFBYSxTQUNULDJCQUNBO3dDQUVONkQsT0FBTTs7MERBRU4sOERBQUNwRix3RkFBT0E7Z0RBQUMyRSxXQUFVOzs7Ozs7MERBQ25CLDhEQUFDRDtnREFBSUMsV0FBVTswREFBeU47Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3dCQVMvT3BELGFBQWEsMEJBQ1osOERBQUNtRDs0QkFBSUMsV0FBVTs7OENBRWIsOERBQUNEO29DQUFJQyxXQUFVOzhDQUNiLDRFQUFDNUUsNkVBQXdCQTt3Q0FDdkJzRixRQUFRakY7d0NBQ1JNLFlBQVlBO3dDQUNaNEUsV0FBVzFFOzs7Ozs7Ozs7Ozs4Q0FLZiw4REFBQzhEO29DQUFJQyxXQUFVOzhDQUNabkIsZUFBZStCLEdBQUcsQ0FBQyxDQUFDRix1QkFDbkIsOERBQUNIOzRDQUVDQyxTQUFTO2dEQUNQOUUsYUFBYWdGLE9BQU9yQyxLQUFLO2dEQUV6QixJQUFJNUIsY0FBYztvREFDaEJpQjtnREFDRjs0Q0FDRjs0Q0FDQXNDLFdBQVcsdUZBSVYsT0FIQ3ZFLGNBQWNpRixPQUFPckMsS0FBSyxHQUN0Qix3Q0FDQTs7OERBR04sOERBQUM0QjtvREFBS0QsV0FBVTs4REFDYlUsT0FBT3BDLEtBQUs7Ozs7OztnREFFZDdDLGNBQWNpRixPQUFPckMsS0FBSyxrQkFDekIsOERBQUMwQjtvREFBSUMsV0FBVTs7Ozs7OzsyQ0FsQlpVLE9BQU9yQyxLQUFLOzs7Ozs7Ozs7O2dDQXlCdEJFLGFBQWEsbUJBQ1osOERBQUN3QjtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNPOzRDQUNDQyxTQUFTcEI7NENBQ1R5QixVQUFVN0QsZ0JBQWdCOzRDQUMxQmdELFdBQVcsZ0dBSVYsT0FIQ2hELGdCQUFnQixJQUNaLHFDQUNBOzs4REFHTiw4REFBQzhEO29EQUNDZCxXQUFVO29EQUNWZSxNQUFLO29EQUNMQyxRQUFPO29EQUNQQyxTQUFROzhEQUVSLDRFQUFDQzt3REFDQ0MsZUFBYzt3REFDZEMsZ0JBQWU7d0RBQ2ZDLGFBQWE7d0RBQ2JDLEdBQUU7Ozs7Ozs7Ozs7O2dEQUVBOzs7Ozs7O3NEQUlSLDhEQUFDdkI7NENBQUlDLFdBQVU7c0RBQ1p1QixNQUFNQyxJQUFJLENBQUM7Z0RBQUU5QyxRQUFRSDs0Q0FBVyxHQUFHLENBQUNrRCxHQUFHQyxrQkFDdEMsOERBQUNuQjtvREFFQ0MsU0FBUyxJQUFNekIsU0FBUzJDO29EQUN4QjFCLFdBQVcsb0RBSVYsT0FIQ2hELGdCQUFnQjBFLElBQ1osZ0JBQ0E7bURBTERBOzs7Ozs7Ozs7O3NEQVdYLDhEQUFDbkI7NENBQ0NDLFNBQVNyQjs0Q0FDVDBCLFVBQVU3RCxnQkFBZ0J1QixhQUFhOzRDQUN2Q3lCLFdBQVcsZ0dBSVYsT0FIQ2hELGdCQUFnQnVCLGFBQWEsSUFDekIscUNBQ0E7O2dEQUVQOzhEQUVDLDhEQUFDdUM7b0RBQ0NkLFdBQVU7b0RBQ1ZlLE1BQUs7b0RBQ0xDLFFBQU87b0RBQ1BDLFNBQVE7OERBRVIsNEVBQUNDO3dEQUNDQyxlQUFjO3dEQUNkQyxnQkFBZTt3REFDZkMsYUFBYTt3REFDYkMsR0FBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7d0JBVWYxRSxhQUFhLFVBQVUsQ0FBQ0UsMEJBQ3ZCLDhEQUFDaUQ7NEJBQUlDLFdBQVU7c0NBQ1o1QixRQUFRd0MsR0FBRyxDQUFDLENBQUNGLHVCQUNaLDhEQUFDSDtvQ0FFQ0MsU0FBUzt3Q0FDUDlFLGFBQWFnRixPQUFPckMsS0FBSzt3Q0FFekIsSUFBSTVCLGNBQWM7NENBQ2hCaUI7d0NBQ0Y7b0NBQ0Y7b0NBQ0FzQyxXQUFXLHdGQUlWLE9BSEN2RSxjQUFjaUYsT0FBT3JDLEtBQUssR0FDdEIsd0NBQ0E7O3dDQUdMcUMsT0FBT3JDLEtBQUssS0FBSyx1QkFDaEIsOERBQUNsRCwyRUFBc0JBOzRDQUNyQnVGLFFBQVFBLE9BQU9yQyxLQUFLOzRDQUNwQnNDLFdBQVcxRTs7Ozs7aUVBR2IsOERBQUM4RDs0Q0FBSUMsV0FBVTtzREFBZ0Y7Ozs7OztzREFJakcsOERBQUNDOzRDQUFLRCxXQUFVO3NEQUNiVSxPQUFPcEMsS0FBSzs7Ozs7O3dDQUVkN0MsY0FBY2lGLE9BQU9yQyxLQUFLLGtCQUN6Qiw4REFBQzBCOzRDQUFJQyxXQUFVOzs7Ozs7O21DQTVCWlUsT0FBT3JDLEtBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7Z0JBaUc1QjNCLGNBQWMsNEJBQ2IsOERBQUNxRDtvQkFBSUMsV0FBVTs7c0NBRWIsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQzJCO29DQUFHM0IsV0FBVTs4Q0FBb0Q7Ozs7Ozs4Q0FHbEUsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQzFCOzRDQUFNMEIsV0FBVTs7OERBQ2YsOERBQUNHO29EQUNDQyxNQUFLO29EQUNMd0IsU0FBUy9GO29EQUNUK0QsVUFBVSxDQUFDUyxJQUFNdkUsbUJBQW1CdUUsRUFBRXRDLE1BQU0sQ0FBQzZELE9BQU87b0RBQ3BENUIsV0FBVTs7Ozs7OzhEQUVaLDhEQUFDQztvREFBS0QsV0FBVTs4REFBbUM7Ozs7Ozs7Ozs7OztzREFLckQsOERBQUNEOzs4REFDQyw4REFBQ3pCO29EQUFNMEIsV0FBVTs4REFBMEQ7Ozs7Ozs4REFHM0UsOERBQUNEO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ0Q7NERBQUlDLFdBQVU7OzhFQUNiLDhEQUFDRDtvRUFDQ0MsV0FBVTtvRUFDVjZCLE9BQU87d0VBQUVDLGlCQUFpQm5HO29FQUFRO29FQUNsQzZFLFNBQVNuQjs7Ozs7O2dFQUVWbkMsc0NBQ0MsOERBQUM2QztvRUFBSThCLE9BQU87d0VBQUVFLFVBQVU7d0VBQVlDLFFBQVE7b0VBQUk7O3NGQUM5Qyw4REFBQ2pDOzRFQUNDOEIsT0FBTztnRkFDTEUsVUFBVTtnRkFDVkUsS0FBSztnRkFDTEMsT0FBTztnRkFDUEMsUUFBUTtnRkFDUkMsTUFBTTs0RUFDUjs0RUFDQTVCLFNBQVNsQjs7Ozs7O3NGQUVYLDhEQUFDcEUscURBQVlBOzRFQUNYc0UsT0FBTzdEOzRFQUNQaUUsVUFBVUw7NEVBQ1Y4QyxZQUFZOzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0VBS3BCLDhEQUFDbEM7NERBQ0NDLE1BQUs7NERBQ0wvQixPQUFPMUM7NERBQ1BpRSxVQUFVLENBQUNTO2dFQUNULE1BQU1oQyxRQUFRZ0MsRUFBRXRDLE1BQU0sQ0FBQ00sS0FBSztnRUFDNUIsdUNBQXVDO2dFQUN2QyxJQUNFQSxNQUFNaUUsS0FBSyxDQUFDLDBCQUNaakUsVUFBVSxJQUNWO29FQUNBekMsV0FBV3lDO29FQUNYLElBQUk1QixjQUFjO3dFQUNoQmlCO29FQUNGO2dFQUNGOzREQUNGOzREQUNBNkUsYUFBWTs0REFDWnZDLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FRcEIsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQzJCO29DQUFHM0IsV0FBVTs4Q0FBb0Q7Ozs7Ozs4Q0FHbEUsOERBQUNEO29DQUFJQyxXQUFVOzt3Q0FDWkwsYUFDQyx5QkFDQTFELGNBQ0EsQ0FBQ29FLElBQU1uRSxnQkFBZ0JzRyxXQUFXbkMsRUFBRXRDLE1BQU0sQ0FBQ00sS0FBSyxJQUNoRCxHQUNBLElBQ0E7d0NBR0RzQixhQUNDLHdCQUNBcEQsYUFDQSxDQUFDOEQ7NENBQ0MsTUFBTW9DLFdBQVdDLE9BQU9yQyxFQUFFdEMsTUFBTSxDQUFDTSxLQUFLOzRDQUN0QzdCLGVBQ0VpRyxZQUFZLElBQUlBLFdBQVcsTUFBTSxNQUFPQSxXQUFXO3dDQUV2RCxHQUNBLEdBQ0FqRSxLQUFLbUUsRUFBRSxHQUFHLEdBQ1YsTUFDQTt3Q0FHRGhELGFBQ0MsbUJBQ0F0RCxTQUNBLENBQUNnRSxJQUFNL0QsV0FBV2tHLFdBQVduQyxFQUFFdEMsTUFBTSxDQUFDTSxLQUFLLElBQzNDLEdBQ0EsR0FDQTs7Ozs7Ozs7Ozs7OztzQ0FNTiw4REFBQzBCOzRCQUFJQyxXQUFVO3NDQUNiLDRFQUFDMUI7Z0NBQU0wQixXQUFVOztrREFDZiw4REFBQ0c7d0NBQ0NDLE1BQUs7d0NBQ0x3QixTQUFTekY7d0NBQ1R5RCxVQUFVLENBQUNTOzRDQUNUakUsa0JBQWtCaUUsRUFBRXRDLE1BQU0sQ0FBQzZELE9BQU87NENBRWxDLElBQUluRixjQUFjO2dEQUNoQmlCOzRDQUNGO3dDQUNGO3dDQUNBc0MsV0FBVTs7Ozs7O2tEQUVaLDhEQUFDQzt3Q0FBS0QsV0FBVTtrREFBbUM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFVbkU7R0E5a0JnQnhFOztRQW1EMkNELGtFQUFrQkE7OztLQW5EN0RDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9jb21wb25lbnRzL0FydGlzdFRvb2wvRW52aXJvbm1lbnRQYW5lbC5qc3g/NzY1NiJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcclxuXHJcbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VDYWxsYmFjaywgdXNlRWZmZWN0IH0gZnJvbSBcInJlYWN0XCI7XHJcbmltcG9ydCB7IENocm9tZVBpY2tlciB9IGZyb20gXCJyZWFjdC1jb2xvclwiO1xyXG5pbXBvcnQge1xyXG4gIEVudmlyb25tZW50UHJldmlld0JhbGwsXHJcbiAgU2luZ2xlRW52aXJvbm1lbnRQcmV2aWV3LFxyXG59IGZyb20gXCIuL0Vudmlyb25tZW50UHJldmlld0JhbGxcIjtcclxuaW1wb3J0IHsgR3JpZDNYMywgTGlzdCB9IGZyb20gXCJsdWNpZGUtcmVhY3RcIjtcclxuaW1wb3J0IHsgdXNlSGlzdG9yeURlYm91bmNlIH0gZnJvbSBcIkAvaG9va3MvdXNlRGVib3VuY2VcIjtcclxuXHJcbmV4cG9ydCBmdW5jdGlvbiBFbnZpcm9ubWVudFBhbmVsKHtcclxuICBlbnZQcmVzZXQsXHJcbiAgc2V0RW52UHJlc2V0LFxyXG4gIGJnQ29sb3IsXHJcbiAgc2V0QmdDb2xvcixcclxuICBzaG93RW52aXJvbm1lbnQsXHJcbiAgc2V0U2hvd0Vudmlyb25tZW50LFxyXG4gIGN1c3RvbUhkcmksXHJcbiAgc2V0Q3VzdG9tSGRyaSxcclxuICBlbnZJbnRlbnNpdHksXHJcbiAgc2V0RW52SW50ZW5zaXR5LFxyXG4gIHNob3dNb2RlbFN0YXRzLFxyXG4gIHNldFNob3dNb2RlbFN0YXRzLFxyXG4gIGVudkJsdXIsXHJcbiAgc2V0RW52Qmx1cixcclxuICBlbnZSb3RhdGlvbixcclxuICBzZXRFbnZSb3RhdGlvbixcclxuICBhZGRUb0hpc3RvcnksXHJcbn0pIHtcclxuICBjb25zdCBbYWN0aXZlVGFiLCBzZXRBY3RpdmVUYWJdID0gdXNlU3RhdGUoXCJwcmVzZXRzXCIpO1xyXG4gIGNvbnN0IFt2aWV3TW9kZSwgc2V0Vmlld01vZGVdID0gdXNlU3RhdGUoXCJzaW5nbGVcIik7IC8vIFwic2luZ2xlXCIgb3IgXCJncmlkXCJcclxuICBjb25zdCBbaXNNb2JpbGUsIHNldElzTW9iaWxlXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBbY3VycmVudFBhZ2UsIHNldEN1cnJlbnRQYWdlXSA9IHVzZVN0YXRlKDApO1xyXG4gIGNvbnN0IFtkaXNwbGF5QmdDb2xvclBpY2tlciwgc2V0RGlzcGxheUJnQ29sb3JQaWNrZXJdID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IGl0ZW1zUGVyUGFnZSA9IDY7XHJcblxyXG4gIC8vIENoZWNrIGlmIGRldmljZSBpcyBtb2JpbGVcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgY29uc3QgY2hlY2tNb2JpbGUgPSAoKSA9PiB7XHJcbiAgICAgIHNldElzTW9iaWxlKHdpbmRvdy5pbm5lcldpZHRoIDwgNzY4KTtcclxuICAgIH07XHJcblxyXG4gICAgY2hlY2tNb2JpbGUoKTtcclxuICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKFwicmVzaXplXCIsIGNoZWNrTW9iaWxlKTtcclxuICAgIHJldHVybiAoKSA9PiB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcihcInJlc2l6ZVwiLCBjaGVja01vYmlsZSk7XHJcbiAgfSwgW10pO1xyXG5cclxuICAvLyBGb3JjZSBzaW5nbGUgdmlldyBvbiBtb2JpbGVcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgaWYgKGlzTW9iaWxlKSB7XHJcbiAgICAgIHNldFZpZXdNb2RlKFwic2luZ2xlXCIpO1xyXG4gICAgfVxyXG4gIH0sIFtpc01vYmlsZV0pO1xyXG5cclxuICAvLyBSZXNldCB0byBmaXJzdCBwYWdlIHdoZW4gc3dpdGNoaW5nIHRhYnMgb3IgdmlldyBtb2Rlc1xyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBzZXRDdXJyZW50UGFnZSgwKTtcclxuICB9LCBbYWN0aXZlVGFiLCB2aWV3TW9kZV0pO1xyXG5cclxuICAvLyBTZXR1cCBkZWJvdW5jZWQgaGlzdG9yeSBmb3Igc2xpZGVyIGlucHV0c1xyXG5cclxuICBjb25zdCB7IGFkZFRvSGlzdG9yeUltbWVkaWF0ZSwgYWRkVG9IaXN0b3J5RGVib3VuY2VkIH0gPSB1c2VIaXN0b3J5RGVib3VuY2UoXHJcbiAgICBhZGRUb0hpc3RvcnkgfHwgKCgpID0+IHt9KSxcclxuXHJcbiAgICA5MDBcclxuICApO1xyXG5cclxuICBjb25zdCBoYW5kbGVIZHJpVXBsb2FkID0gdXNlQ2FsbGJhY2soXHJcbiAgICAoZXZlbnQpID0+IHtcclxuICAgICAgY29uc3QgZmlsZSA9IGV2ZW50LnRhcmdldC5maWxlc1swXTtcclxuICAgICAgaWYgKGZpbGUpIHtcclxuICAgICAgICBjb25zdCB1cmwgPSBVUkwuY3JlYXRlT2JqZWN0VVJMKGZpbGUpO1xyXG4gICAgICAgIHNldEN1c3RvbUhkcmkodXJsKTtcclxuICAgICAgICBzZXRFbnZQcmVzZXQoXCJjdXN0b21cIik7XHJcbiAgICAgICAgLy8gQWRkIHRvIGhpc3RvcnkgZm9yIEhEUkkgdXBsb2FkXHJcblxyXG4gICAgICAgIGlmIChhZGRUb0hpc3RvcnkpIHtcclxuICAgICAgICAgIGFkZFRvSGlzdG9yeUltbWVkaWF0ZSgpO1xyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG4gICAgfSxcclxuICAgIFtzZXRDdXN0b21IZHJpLCBzZXRFbnZQcmVzZXQsIGFkZFRvSGlzdG9yeUltbWVkaWF0ZSwgYWRkVG9IaXN0b3J5XVxyXG4gICk7XHJcblxyXG4gIGNvbnN0IHByZXNldHMgPSBbXHJcbiAgICB7IHZhbHVlOiBcImhkcmlfMTVcIiwgbGFiZWw6IFwiTWV0YWwgMVwiIH0sXHJcbiAgICB7IHZhbHVlOiBcImhkcmlfbWV0YWxcIiwgbGFiZWw6IFwiTWV0YWwgMlwiIH0sXHJcbiAgICB7IHZhbHVlOiBcImhkcmlfbWV0YWwyXCIsIGxhYmVsOiBcIk1ldGFsIDNcIiB9LFxyXG4gICAgeyB2YWx1ZTogXCJoZHJpX21ldGFsM1wiLCBsYWJlbDogXCJNZXRhbCA0XCIgfSxcclxuICAgIHsgdmFsdWU6IFwiaGRyaV9nZW1cIiwgbGFiZWw6IFwiR2VtIEVudlwiIH0sXHJcbiAgICB7IHZhbHVlOiBcInN1bnNldFwiLCBsYWJlbDogXCJTdW5zZXRcIiB9LFxyXG4gICAgeyB2YWx1ZTogXCJkYXduXCIsIGxhYmVsOiBcIkRhd25cIiB9LFxyXG4gICAgeyB2YWx1ZTogXCJuaWdodFwiLCBsYWJlbDogXCJOaWdodFwiIH0sXHJcbiAgICB7IHZhbHVlOiBcIndhcmVob3VzZVwiLCBsYWJlbDogXCJXYXJlaG91c2VcIiB9LFxyXG4gICAgeyB2YWx1ZTogXCJmb3Jlc3RcIiwgbGFiZWw6IFwiRm9yZXN0XCIgfSxcclxuICAgIHsgdmFsdWU6IFwiYXBhcnRtZW50XCIsIGxhYmVsOiBcIkFwYXJ0bWVudFwiIH0sXHJcbiAgICB7IHZhbHVlOiBcInN0dWRpb1wiLCBsYWJlbDogXCJTdHVkaW9cIiB9LFxyXG4gICAgeyB2YWx1ZTogXCJzdHVkaW9fc21hbGxcIiwgbGFiZWw6IFwiU3R1ZGlvIFNtYWxsXCIgfSxcclxuICAgIHsgdmFsdWU6IFwiY2l0eVwiLCBsYWJlbDogXCJDaXR5XCIgfSxcclxuICAgIHsgdmFsdWU6IFwibm9uZVwiLCBsYWJlbDogXCJOb25lXCIgfSxcclxuICBdO1xyXG5cclxuICAvLyBQYWdpbmF0aW9uIGxvZ2ljXHJcbiAgY29uc3QgdG90YWxQYWdlcyA9IE1hdGguY2VpbChwcmVzZXRzLmxlbmd0aCAvIGl0ZW1zUGVyUGFnZSk7XHJcbiAgY29uc3Qgc3RhcnRJbmRleCA9IGN1cnJlbnRQYWdlICogaXRlbXNQZXJQYWdlO1xyXG4gIGNvbnN0IGVuZEluZGV4ID0gc3RhcnRJbmRleCArIGl0ZW1zUGVyUGFnZTtcclxuICBjb25zdCBjdXJyZW50UHJlc2V0cyA9IHByZXNldHMuc2xpY2Uoc3RhcnRJbmRleCwgZW5kSW5kZXgpO1xyXG5cclxuICBjb25zdCBnb1RvUGFnZSA9IChwYWdlKSA9PiB7XHJcbiAgICBzZXRDdXJyZW50UGFnZShNYXRoLm1heCgwLCBNYXRoLm1pbihwYWdlLCB0b3RhbFBhZ2VzIC0gMSkpKTtcclxuICB9O1xyXG5cclxuICBjb25zdCBnb1RvTmV4dFBhZ2UgPSAoKSA9PiB7XHJcbiAgICBpZiAoY3VycmVudFBhZ2UgPCB0b3RhbFBhZ2VzIC0gMSkge1xyXG4gICAgICBzZXRDdXJyZW50UGFnZShjdXJyZW50UGFnZSArIDEpO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIGNvbnN0IGdvVG9QcmV2UGFnZSA9ICgpID0+IHtcclxuICAgIGlmIChjdXJyZW50UGFnZSA+IDApIHtcclxuICAgICAgc2V0Q3VycmVudFBhZ2UoY3VycmVudFBhZ2UgLSAxKTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICBjb25zdCBoYW5kbGVCZ0NvbG9yQ2xpY2sgPSAoKSA9PiB7XHJcbiAgICBzZXREaXNwbGF5QmdDb2xvclBpY2tlcighZGlzcGxheUJnQ29sb3JQaWNrZXIpO1xyXG4gIH07XHJcblxyXG4gIGNvbnN0IGhhbmRsZUJnQ29sb3JDbG9zZSA9ICgpID0+IHtcclxuICAgIHNldERpc3BsYXlCZ0NvbG9yUGlja2VyKGZhbHNlKTtcclxuICB9O1xyXG5cclxuICBjb25zdCBoYW5kbGVCZ0NvbG9yQ2hhbmdlID0gKGNvbG9yKSA9PiB7XHJcbiAgICBjb25zdCBoZXhDb2xvciA9IGNvbG9yLmhleDtcclxuICAgIHNldEJnQ29sb3IoaGV4Q29sb3IpO1xyXG4gIH07XHJcblxyXG4gIGNvbnN0IHJlbmRlclNsaWRlciA9IChsYWJlbCwgdmFsdWUsIG9uQ2hhbmdlLCBtaW4sIG1heCwgc3RlcCwgdW5pdCA9IFwiXCIpID0+IChcclxuICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNFwiPlxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlciBtYi0xXCI+XHJcbiAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cInRleHQteHMgbWQ6dGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktMjAwXCI+XHJcbiAgICAgICAgICB7bGFiZWx9XHJcbiAgICAgICAgPC9sYWJlbD5cclxuICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LVsxMHB4XSBtZDp0ZXh0LXhzIHRleHQtZ3JheS00MDBcIj5cclxuICAgICAgICAgIHt2YWx1ZS50b0ZpeGVkKDIpfVxyXG4gICAgICAgICAge3VuaXR9XHJcbiAgICAgICAgPC9zcGFuPlxyXG4gICAgICA8L2Rpdj5cclxuICAgICAgPGlucHV0XHJcbiAgICAgICAgdHlwZT1cInJhbmdlXCJcclxuICAgICAgICBtaW49e21pbn1cclxuICAgICAgICBtYXg9e21heH1cclxuICAgICAgICBzdGVwPXtzdGVwfVxyXG4gICAgICAgIHZhbHVlPXt2YWx1ZX1cclxuICAgICAgICBvbkNoYW5nZT17KGUpID0+IHtcclxuICAgICAgICAgIG9uQ2hhbmdlKGUpO1xyXG5cclxuICAgICAgICAgIGlmIChhZGRUb0hpc3RvcnkpIHtcclxuICAgICAgICAgICAgYWRkVG9IaXN0b3J5RGVib3VuY2VkKCk7XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfX1cclxuICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgaC0yIGJnLWdyYXktNzAwIHJvdW5kZWQtbGcgYXBwZWFyYW5jZS1ub25lIGN1cnNvci1wb2ludGVyIGFjY2VudC1ibHVlLTUwMFwiXHJcbiAgICAgIC8+XHJcbiAgICA8L2Rpdj5cclxuICApO1xyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIHctZnVsbCBvdmVyZmxvdy15LWF1dG8gdGV4dC1bMTBweF0gbWQ6dGV4dC14cyBzY3JvbGxiYXItbm9uZVwiPlxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIlwiPlxyXG4gICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LWJhc2UgZm9udC1zZW1pYm9sZCBtYi0yIHRleHQtZ3JheS0xMDBcIj5cclxuICAgICAgICAgIEVudmlyb25tZW50XHJcbiAgICAgICAgPC9oMj5cclxuXHJcbiAgICAgICAgey8qIFRhYiBOYXZpZ2F0aW9uICovfVxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBnYXAtMSBtYi00IGJnLWdyYXktODAwLzMwIHAtMC41IHJvdW5kZWQtbWRcIj5cclxuICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0QWN0aXZlVGFiKFwicHJlc2V0c1wiKX1cclxuICAgICAgICAgICAgY2xhc3NOYW1lPXtgZmxleC0xIHB4LTIuNSBweS0xLjUgcm91bmRlZCB0ZXh0LVsxMHB4XSBtZDp0ZXh0LXhzIGZvbnQtbWVkaXVtIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCAke1xyXG4gICAgICAgICAgICAgIGFjdGl2ZVRhYiA9PT0gXCJwcmVzZXRzXCJcclxuICAgICAgICAgICAgICAgID8gXCJiZy1ibHVlLTUwMC85MCB0ZXh0LXdoaXRlIHNoYWRvdy1zbVwiXHJcbiAgICAgICAgICAgICAgICA6IFwidGV4dC1ncmF5LTMwMCBob3ZlcjpiZy1ncmF5LTcwMC81MFwiXHJcbiAgICAgICAgICAgIH1gfVxyXG4gICAgICAgICAgPlxyXG4gICAgICAgICAgICBQcmVzZXRzXHJcbiAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgIHsvKiA8YnV0dG9uXHJcbiAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldEFjdGl2ZVRhYihcImN1c3RvbVwiKX1cclxuICAgICAgICAgICAgY2xhc3NOYW1lPXtgZmxleC0xIHB4LTIuNSBweS0xLjUgcm91bmRlZCB0ZXh0LVsxMHB4XSBtZDp0ZXh0LXhzIGZvbnQtbWVkaXVtIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCAke1xyXG4gICAgICAgICAgICAgIGFjdGl2ZVRhYiA9PT0gXCJjdXN0b21cIlxyXG4gICAgICAgICAgICAgICAgPyBcImJnLWJsdWUtNTAwLzkwIHRleHQtd2hpdGUgc2hhZG93LXNtXCJcclxuICAgICAgICAgICAgICAgIDogXCJ0ZXh0LWdyYXktMzAwIGhvdmVyOmJnLWdyYXktNzAwLzUwXCJcclxuICAgICAgICAgICAgfWB9XHJcbiAgICAgICAgICA+XHJcbiAgICAgICAgICAgIEN1c3RvbVxyXG4gICAgICAgICAgPC9idXR0b24+ICovfVxyXG4gICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRBY3RpdmVUYWIoXCJzZXR0aW5nc1wiKX1cclxuICAgICAgICAgICAgY2xhc3NOYW1lPXtgZmxleC0xIHB4LTIuNSBweS0xLjUgcm91bmRlZCB0ZXh0LVsxMHB4XSBtZDp0ZXh0LXhzIGZvbnQtbWVkaXVtIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCAke1xyXG4gICAgICAgICAgICAgIGFjdGl2ZVRhYiA9PT0gXCJzZXR0aW5nc1wiXHJcbiAgICAgICAgICAgICAgICA/IFwiYmctYmx1ZS01MDAvOTAgdGV4dC13aGl0ZSBzaGFkb3ctc21cIlxyXG4gICAgICAgICAgICAgICAgOiBcInRleHQtZ3JheS0zMDAgaG92ZXI6YmctZ3JheS03MDAvNTBcIlxyXG4gICAgICAgICAgICB9YH1cclxuICAgICAgICAgID5cclxuICAgICAgICAgICAgU2V0dGluZ3NcclxuICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICB7LyogUHJlc2V0cyBUYWIgKi99XHJcbiAgICAgICAge2FjdGl2ZVRhYiA9PT0gXCJwcmVzZXRzXCIgJiYgKFxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cclxuICAgICAgICAgICAgey8qIFZpZXcgTW9kZSBUb2dnbGUgLSBPbmx5IHNob3cgb24gbGFyZ2VyIHNjcmVlbnMgKi99XHJcbiAgICAgICAgICAgIHshaXNNb2JpbGUgJiYgKFxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWVuZCBpdGVtcy1jZW50ZXIgbWItNFwiPlxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGJnLWdyYXktODAwLzUwIHJvdW5kZWQtbGcgcC0xXCI+XHJcbiAgICAgICAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRWaWV3TW9kZShcInNpbmdsZVwiKX1cclxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BwLTIgcm91bmRlZCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgcmVsYXRpdmUgZ3JvdXAgJHtcclxuICAgICAgICAgICAgICAgICAgICAgIHZpZXdNb2RlID09PSBcInNpbmdsZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgID8gXCJiZy1ibHVlLTUwMCB0ZXh0LXdoaXRlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgOiBcInRleHQtZ3JheS0zMDAgaG92ZXI6dGV4dC13aGl0ZSBob3ZlcjpiZy1ncmF5LTcwMC81MFwiXHJcbiAgICAgICAgICAgICAgICAgICAgfWB9XHJcbiAgICAgICAgICAgICAgICAgICAgdGl0bGU9XCJTaW5nbGUgUHJldmlldyAtIFNob3dzIG9uZSBsYXJnZSBwcmV2aWV3IHdpdGggbGlzdCBvZiBwcmVzZXQgbmFtZXNcIlxyXG4gICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgPExpc3QgY2xhc3NOYW1lPVwidy00IGgtNFwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBib3R0b20tZnVsbCBsZWZ0LTEvMiB0cmFuc2Zvcm0gLXRyYW5zbGF0ZS14LTEvMiBtYi0yIHB4LTIgcHktMSBiZy1ncmF5LTkwMCB0ZXh0LXdoaXRlIHRleHQtWzEwcHhdIG1kOnRleHQteHMgcm91bmRlZCBvcGFjaXR5LTAgZ3JvdXAtaG92ZXI6b3BhY2l0eS0xMDAgdHJhbnNpdGlvbi1vcGFjaXR5IGR1cmF0aW9uLTIwMCB3aGl0ZXNwYWNlLW5vd3JhcCB6LTEwXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICBTaW5nbGUgUHJldmlld1xyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFZpZXdNb2RlKFwiZ3JpZFwiKX1cclxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BwLTIgcm91bmRlZCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgcmVsYXRpdmUgZ3JvdXAgJHtcclxuICAgICAgICAgICAgICAgICAgICAgIHZpZXdNb2RlID09PSBcImdyaWRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA/IFwiYmctYmx1ZS01MDAgdGV4dC13aGl0ZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDogXCJ0ZXh0LWdyYXktMzAwIGhvdmVyOnRleHQtd2hpdGUgaG92ZXI6YmctZ3JheS03MDAvNTBcIlxyXG4gICAgICAgICAgICAgICAgICAgIH1gfVxyXG4gICAgICAgICAgICAgICAgICAgIHRpdGxlPVwiR3JpZCBQcmV2aWV3IC0gU2hvd3MgYWxsIHByZXNldHMgYXMgc21hbGwgcHJldmlldyBiYWxsc1wiXHJcbiAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICA8R3JpZDNYMyBjbGFzc05hbWU9XCJ3LTQgaC00XCIgLz5cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGJvdHRvbS1mdWxsIGxlZnQtMS8yIHRyYW5zZm9ybSAtdHJhbnNsYXRlLXgtMS8yIG1iLTIgcHgtMiBweS0xIGJnLWdyYXktOTAwIHRleHQtd2hpdGUgdGV4dC1bMTBweF0gbWQ6dGV4dC14cyByb3VuZGVkIG9wYWNpdHktMCBncm91cC1ob3ZlcjpvcGFjaXR5LTEwMCB0cmFuc2l0aW9uLW9wYWNpdHkgZHVyYXRpb24tMjAwIHdoaXRlc3BhY2Utbm93cmFwIHotMTBcIj5cclxuICAgICAgICAgICAgICAgICAgICAgIEdyaWQgUHJldmlld1xyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICApfVxyXG5cclxuICAgICAgICAgICAgey8qIFNpbmdsZSBQcmV2aWV3IE1vZGUgKi99XHJcbiAgICAgICAgICAgIHt2aWV3TW9kZSA9PT0gXCJzaW5nbGVcIiAmJiAoXHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTNcIj5cclxuICAgICAgICAgICAgICAgIHsvKiBTaW5nbGUgUHJldmlldyBEaXNwbGF5ICovfVxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi0zXCI+XHJcbiAgICAgICAgICAgICAgICAgIDxTaW5nbGVFbnZpcm9ubWVudFByZXZpZXdcclxuICAgICAgICAgICAgICAgICAgICBwcmVzZXQ9e2VudlByZXNldH1cclxuICAgICAgICAgICAgICAgICAgICBjdXN0b21IZHJpPXtjdXN0b21IZHJpfVxyXG4gICAgICAgICAgICAgICAgICAgIGludGVuc2l0eT17ZW52SW50ZW5zaXR5fVxyXG4gICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICAgICAgey8qIFByZXNldCBTZWxlY3Rpb24gTGlzdCAqL31cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0xLjVcIj5cclxuICAgICAgICAgICAgICAgICAge2N1cnJlbnRQcmVzZXRzLm1hcCgocHJlc2V0KSA9PiAoXHJcbiAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgICAga2V5PXtwcmVzZXQudmFsdWV9XHJcbiAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHNldEVudlByZXNldChwcmVzZXQudmFsdWUpO1xyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGFkZFRvSGlzdG9yeSkge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGFkZFRvSGlzdG9yeUltbWVkaWF0ZSgpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgdy1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBwLTIgcm91bmRlZC1tZCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgJHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgZW52UHJlc2V0ID09PSBwcmVzZXQudmFsdWVcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA/IFwiYmctYmx1ZS01MDAvMjAgcmluZy0xIHJpbmctYmx1ZS01MDBcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDogXCJiZy1ncmF5LTgwMC81MCBob3ZlcjpiZy1ncmF5LTcwMC81MFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICB9YH1cclxuICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LVsxMHB4XSBtZDp0ZXh0LXhzIGZvbnQtbWVkaXVtIHRleHQtZ3JheS0yMDBcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAge3ByZXNldC5sYWJlbH1cclxuICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgIHtlbnZQcmVzZXQgPT09IHByZXNldC52YWx1ZSAmJiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xLjUgaC0xLjUgYmctYmx1ZS01MDAgcm91bmRlZC1mdWxsXCIgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICAgICAgICAgICkpfVxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICAgICAgey8qIFBhZ2luYXRpb24gQ29udHJvbHMgKi99XHJcbiAgICAgICAgICAgICAgICB7dG90YWxQYWdlcyA+IDEgJiYgKFxyXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBtdC00IHB0LTMgYm9yZGVyLXQgYm9yZGVyLWdyYXktNzAwLzUwXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17Z29Ub1ByZXZQYWdlfVxyXG4gICAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2N1cnJlbnRQYWdlID09PSAwfVxyXG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTEgcHgtMiBweS0xIHJvdW5kZWQgdGV4dC1bMTBweF0gbWQ6dGV4dC14cyB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgJHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgY3VycmVudFBhZ2UgPT09IDBcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA/IFwidGV4dC1ncmF5LTUwMCBjdXJzb3Itbm90LWFsbG93ZWRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDogXCJ0ZXh0LWdyYXktMzAwIGhvdmVyOnRleHQtd2hpdGUgaG92ZXI6YmctZ3JheS03MDAvNTBcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgfWB9XHJcbiAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPHN2Z1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LTMgaC0zXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgZmlsbD1cIm5vbmVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBzdHJva2U9XCJjdXJyZW50Q29sb3JcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICB2aWV3Qm94PVwiMCAwIDI0IDI0XCJcclxuICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPHBhdGhcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBzdHJva2VMaW5lY2FwPVwicm91bmRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZVdpZHRoPXsyfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGQ9XCJNMTUgMTlsLTctNyA3LTdcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9zdmc+XHJcbiAgICAgICAgICAgICAgICAgICAgICBQcmV2XHJcbiAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XHJcblxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTFcIj5cclxuICAgICAgICAgICAgICAgICAgICAgIHtBcnJheS5mcm9tKHsgbGVuZ3RoOiB0b3RhbFBhZ2VzIH0sIChfLCBpKSA9PiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBrZXk9e2l9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gZ29Ub1BhZ2UoaSl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgdy0yIGgtMiByb3VuZGVkLWZ1bGwgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwICR7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjdXJyZW50UGFnZSA9PT0gaVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IFwiYmctYmx1ZS01MDBcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IFwiYmctZ3JheS02MDAgaG92ZXI6YmctZ3JheS01MDBcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIH1gfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgKSl9XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2dvVG9OZXh0UGFnZX1cclxuICAgICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtjdXJyZW50UGFnZSA9PT0gdG90YWxQYWdlcyAtIDF9XHJcbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMSBweC0yIHB5LTEgcm91bmRlZCB0ZXh0LVsxMHB4XSBtZDp0ZXh0LXhzIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCAke1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBjdXJyZW50UGFnZSA9PT0gdG90YWxQYWdlcyAtIDFcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA/IFwidGV4dC1ncmF5LTUwMCBjdXJzb3Itbm90LWFsbG93ZWRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDogXCJ0ZXh0LWdyYXktMzAwIGhvdmVyOnRleHQtd2hpdGUgaG92ZXI6YmctZ3JheS03MDAvNTBcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgfWB9XHJcbiAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgTmV4dFxyXG4gICAgICAgICAgICAgICAgICAgICAgPHN2Z1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LTMgaC0zXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgZmlsbD1cIm5vbmVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBzdHJva2U9XCJjdXJyZW50Q29sb3JcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICB2aWV3Qm94PVwiMCAwIDI0IDI0XCJcclxuICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPHBhdGhcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBzdHJva2VMaW5lY2FwPVwicm91bmRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZVdpZHRoPXsyfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGQ9XCJNOSA1bDcgNy03IDdcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9zdmc+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgKX1cclxuXHJcbiAgICAgICAgICAgIHsvKiBHcmlkIFByZXZpZXcgTW9kZSAqL31cclxuICAgICAgICAgICAge3ZpZXdNb2RlID09PSBcImdyaWRcIiAmJiAhaXNNb2JpbGUgJiYgKFxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMyBnYXAtM1wiPlxyXG4gICAgICAgICAgICAgICAge3ByZXNldHMubWFwKChwcmVzZXQpID0+IChcclxuICAgICAgICAgICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgIGtleT17cHJlc2V0LnZhbHVlfVxyXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgIHNldEVudlByZXNldChwcmVzZXQudmFsdWUpO1xyXG5cclxuICAgICAgICAgICAgICAgICAgICAgIGlmIChhZGRUb0hpc3RvcnkpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgYWRkVG9IaXN0b3J5SW1tZWRpYXRlKCk7XHJcbiAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2Bncm91cCByZWxhdGl2ZSBmbGV4IGZsZXgtY29sIGl0ZW1zLWNlbnRlciBwLTIgcm91bmRlZC1sZyB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgJHtcclxuICAgICAgICAgICAgICAgICAgICAgIGVudlByZXNldCA9PT0gcHJlc2V0LnZhbHVlXHJcbiAgICAgICAgICAgICAgICAgICAgICAgID8gXCJiZy1ibHVlLTUwMC8yMCByaW5nLTIgcmluZy1ibHVlLTUwMFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDogXCJiZy1ncmF5LTgwMC81MCBob3ZlcjpiZy1ncmF5LTcwMC81MFwiXHJcbiAgICAgICAgICAgICAgICAgICAgfWB9XHJcbiAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICB7cHJlc2V0LnZhbHVlICE9PSBcIm5vbmVcIiA/IChcclxuICAgICAgICAgICAgICAgICAgICAgIDxFbnZpcm9ubWVudFByZXZpZXdCYWxsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHByZXNldD17cHJlc2V0LnZhbHVlfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBpbnRlbnNpdHk9e2VudkludGVuc2l0eX1cclxuICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xNiBoLTE2IHJvdW5kZWQtbGcgYmctZ3JheS04MDAgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgdGV4dC1bMTBweF1cIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgTm9uZVxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJtdC0yIHRleHQtWzEwcHhdIG1kOnRleHQteHMgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTIwMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAge3ByZXNldC5sYWJlbH1cclxuICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAge2VudlByZXNldCA9PT0gcHJlc2V0LnZhbHVlICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgdG9wLTEgcmlnaHQtMSB3LTIgaC0yIGJnLWJsdWUtNTAwIHJvdW5kZWQtZnVsbFwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICAgICAgICApKX1cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgKX1cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICl9XHJcblxyXG4gICAgICAgIHsvKiBDdXN0b20gSERSSSBUYWIgKi99XHJcbiAgICAgICAgey8qIHthY3RpdmVUYWIgPT09IFwiY3VzdG9tXCIgJiYgKFxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmF5LTgwMC81MCByb3VuZGVkLWxnIHAtNFwiPlxyXG4gICAgICAgICAgICAgIDxpbnB1dFxyXG4gICAgICAgICAgICAgICAgdHlwZT1cImZpbGVcIlxyXG4gICAgICAgICAgICAgICAgYWNjZXB0PVwiLmhkciwuZXhyLC5wbmcsLmpwZywuanBlZ1wiXHJcbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlSGRyaVVwbG9hZH1cclxuICAgICAgICAgICAgICAgIHN0eWxlPXt7IGRpc3BsYXk6IFwibm9uZVwiIH19XHJcbiAgICAgICAgICAgICAgICBpZD1cImhkcmktdXBsb2FkXCJcclxuICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgIDxsYWJlbFxyXG4gICAgICAgICAgICAgICAgaHRtbEZvcj1cImhkcmktdXBsb2FkXCJcclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGdhcC0yIHB4LTQgcHktMyBiZy1ibHVlLTUwMCBob3ZlcjpiZy1ibHVlLTYwMCB0ZXh0LXdoaXRlIHJvdW5kZWQtbGcgY3Vyc29yLXBvaW50ZXIgdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMjAwXCJcclxuICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICA8c3ZnXHJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctNCBoLTRcIlxyXG4gICAgICAgICAgICAgICAgICBmaWxsPVwibm9uZVwiXHJcbiAgICAgICAgICAgICAgICAgIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiXHJcbiAgICAgICAgICAgICAgICAgIHZpZXdCb3g9XCIwIDAgMjQgMjRcIlxyXG4gICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICA8cGF0aFxyXG4gICAgICAgICAgICAgICAgICAgIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiXHJcbiAgICAgICAgICAgICAgICAgICAgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiXHJcbiAgICAgICAgICAgICAgICAgICAgc3Ryb2tlV2lkdGg9ezJ9XHJcbiAgICAgICAgICAgICAgICAgICAgZD1cIk0xMiA0djE2bTgtOEg0XCJcclxuICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgIDwvc3ZnPlxyXG4gICAgICAgICAgICAgICAgVXBsb2FkIEhEUklcclxuICAgICAgICAgICAgICA8L2xhYmVsPlxyXG4gICAgICAgICAgICAgIHtjdXN0b21IZHJpICYmIChcclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtNCBmbGV4IGZsZXgtY29sIGl0ZW1zLWNlbnRlclwiPlxyXG4gICAgICAgICAgICAgICAgICA8U2luZ2xlRW52aXJvbm1lbnRQcmV2aWV3XHJcbiAgICAgICAgICAgICAgICAgICAgcHJlc2V0PVwiY3VzdG9tXCJcclxuICAgICAgICAgICAgICAgICAgICBjdXN0b21IZHJpPXtjdXN0b21IZHJpfVxyXG4gICAgICAgICAgICAgICAgICAgIGludGVuc2l0eT17ZW52SW50ZW5zaXR5fVxyXG4gICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTIgdGV4dC14cyBtZDp0ZXh0LXNtIHRleHQtZ3JlZW4tNDAwIGZsZXggaXRlbXMtY2VudGVyIGdhcC0xXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPHN2Z1xyXG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy00IGgtNFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICBmaWxsPVwibm9uZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICBzdHJva2U9XCJjdXJyZW50Q29sb3JcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgdmlld0JveD1cIjAgMCAyNCAyNFwiXHJcbiAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPHBhdGhcclxuICAgICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlTGluZWNhcD1cInJvdW5kXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZVdpZHRoPXsyfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBkPVwiTTUgMTNsNCA0TDE5IDdcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICA8L3N2Zz5cclxuICAgICAgICAgICAgICAgICAgICBDdXN0b20gSERSSSBsb2FkZWRcclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICl9ICovfVxyXG5cclxuICAgICAgICB7LyogU2V0dGluZ3MgVGFiICovfVxyXG4gICAgICAgIHthY3RpdmVUYWIgPT09IFwic2V0dGluZ3NcIiAmJiAoXHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxyXG4gICAgICAgICAgICB7LyogRW52aXJvbm1lbnQgRGlzcGxheSBTZXR0aW5ncyAqL31cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmF5LTgwMC81MCByb3VuZGVkLWxnIHAtMSBtZDpwLTRcIj5cclxuICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC14cyBtZDp0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS0yMDAgbWItNFwiPlxyXG4gICAgICAgICAgICAgICAgRGlzcGxheSBTZXR0aW5nc1xyXG4gICAgICAgICAgICAgIDwvaDM+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cclxuICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiBjdXJzb3ItcG9pbnRlclwiPlxyXG4gICAgICAgICAgICAgICAgICA8aW5wdXRcclxuICAgICAgICAgICAgICAgICAgICB0eXBlPVwiY2hlY2tib3hcIlxyXG4gICAgICAgICAgICAgICAgICAgIGNoZWNrZWQ9e3Nob3dFbnZpcm9ubWVudH1cclxuICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFNob3dFbnZpcm9ubWVudChlLnRhcmdldC5jaGVja2VkKX1cclxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmb3JtLWNoZWNrYm94IGgtNCB3LTQgdGV4dC1ibHVlLTUwMCByb3VuZGVkIGJvcmRlci1ncmF5LTYwMCBmb2N1czpyaW5nLWJsdWUtNTAwXCJcclxuICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14cyBtZDp0ZXh0LXNtIHRleHQtZ3JheS0yMDBcIj5cclxuICAgICAgICAgICAgICAgICAgICBTaG93IGFzIEJhY2tncm91bmRcclxuICAgICAgICAgICAgICAgICAgPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgPC9sYWJlbD5cclxuXHJcbiAgICAgICAgICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC14cyBtZDp0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS0yMDAgbWItMlwiPlxyXG4gICAgICAgICAgICAgICAgICAgIEJhY2tncm91bmQgQ29sb3JcclxuICAgICAgICAgICAgICAgICAgPC9sYWJlbD5cclxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtM1wiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmVcIj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxkaXZcclxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy0xMCBoLTEwIHJvdW5kZWQgY3Vyc29yLXBvaW50ZXIgYm9yZGVyIGJvcmRlci1ncmF5LTYwMFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHN0eWxlPXt7IGJhY2tncm91bmRDb2xvcjogYmdDb2xvciB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVCZ0NvbG9yQ2xpY2t9XHJcbiAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAge2Rpc3BsYXlCZ0NvbG9yUGlja2VyICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBzdHlsZT17eyBwb3NpdGlvbjogXCJhYnNvbHV0ZVwiLCB6SW5kZXg6IFwiMlwiIH19PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXZcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0eWxlPXt7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBvc2l0aW9uOiBcImZpeGVkXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRvcDogXCIwcHhcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmlnaHQ6IFwiMHB4XCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJvdHRvbTogXCIwcHhcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbGVmdDogXCIwcHhcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVCZ0NvbG9yQ2xvc2V9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2hyb21lUGlja2VyXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb2xvcj17YmdDb2xvcn1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVCZ0NvbG9yQ2hhbmdlfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZGlzYWJsZUFscGhhXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgIDxpbnB1dFxyXG4gICAgICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2JnQ29sb3J9XHJcbiAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgdmFsdWUgPSBlLnRhcmdldC52YWx1ZTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgLy8gQWxsb3cgdHlwaW5nIGFuZCB2YWxpZGF0ZSBoZXggZm9ybWF0XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZS5tYXRjaCgvXiNbMC05QS1GYS1mXXswLDZ9JC8pIHx8XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWUgPT09IFwiXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0QmdDb2xvcih2YWx1ZSk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGFkZFRvSGlzdG9yeSkge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgYWRkVG9IaXN0b3J5SW1tZWRpYXRlKCk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCIjZmZmZmZmXCJcclxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTIgcHktMSBiZy1ncmF5LTcwMCBib3JkZXIgYm9yZGVyLWdyYXktNjAwIHJvdW5kZWQgdGV4dC1bMTBweF0gbWQ6dGV4dC14cyB0ZXh0LWdyYXktMjAwIGZvY3VzOmJvcmRlci1ibHVlLTUwMCBmb2N1czpvdXRsaW5lLW5vbmUgdy0yMFwiXHJcbiAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICB7LyogRW52aXJvbm1lbnQgQWRqdXN0bWVudCBTZXR0aW5ncyAqL31cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmF5LTgwMC81MCByb3VuZGVkLWxnIHAtMSBtZDpwLTRcIj5cclxuICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC14cyBtZDp0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS0yMDAgbWItNFwiPlxyXG4gICAgICAgICAgICAgICAgRW52aXJvbm1lbnQgQWRqdXN0bWVudHNcclxuICAgICAgICAgICAgICA8L2gzPlxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XHJcbiAgICAgICAgICAgICAgICB7cmVuZGVyU2xpZGVyKFxyXG4gICAgICAgICAgICAgICAgICBcIkVudmlyb25tZW50IEludGVuc2l0eVwiLFxyXG4gICAgICAgICAgICAgICAgICBlbnZJbnRlbnNpdHksXHJcbiAgICAgICAgICAgICAgICAgIChlKSA9PiBzZXRFbnZJbnRlbnNpdHkocGFyc2VGbG9hdChlLnRhcmdldC52YWx1ZSkpLFxyXG4gICAgICAgICAgICAgICAgICAwLFxyXG4gICAgICAgICAgICAgICAgICAxMCxcclxuICAgICAgICAgICAgICAgICAgMC4wMVxyXG4gICAgICAgICAgICAgICAgKX1cclxuXHJcbiAgICAgICAgICAgICAgICB7cmVuZGVyU2xpZGVyKFxyXG4gICAgICAgICAgICAgICAgICBcIkVudmlyb25tZW50IFJvdGF0aW9uXCIsXHJcbiAgICAgICAgICAgICAgICAgIGVudlJvdGF0aW9uLFxyXG4gICAgICAgICAgICAgICAgICAoZSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IG5ld1ZhbHVlID0gTnVtYmVyKGUudGFyZ2V0LnZhbHVlKTtcclxuICAgICAgICAgICAgICAgICAgICBzZXRFbnZSb3RhdGlvbihcclxuICAgICAgICAgICAgICAgICAgICAgIG5ld1ZhbHVlID49IDAgPyBuZXdWYWx1ZSAlIDM2MCA6IDM2MCArIChuZXdWYWx1ZSAlIDM2MClcclxuICAgICAgICAgICAgICAgICAgICApO1xyXG4gICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgICAwLFxyXG4gICAgICAgICAgICAgICAgICBNYXRoLlBJICogMixcclxuICAgICAgICAgICAgICAgICAgMC4wMSxcclxuICAgICAgICAgICAgICAgICAgXCLCsFwiXHJcbiAgICAgICAgICAgICAgICApfVxyXG5cclxuICAgICAgICAgICAgICAgIHtyZW5kZXJTbGlkZXIoXHJcbiAgICAgICAgICAgICAgICAgIFwiQmFja2dyb3VuZCBCbHVyXCIsXHJcbiAgICAgICAgICAgICAgICAgIGVudkJsdXIsXHJcbiAgICAgICAgICAgICAgICAgIChlKSA9PiBzZXRFbnZCbHVyKHBhcnNlRmxvYXQoZS50YXJnZXQudmFsdWUpKSxcclxuICAgICAgICAgICAgICAgICAgMCxcclxuICAgICAgICAgICAgICAgICAgMSxcclxuICAgICAgICAgICAgICAgICAgMC4wMVxyXG4gICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICB7LyogTW9kZWwgU3RhdHMgVG9nZ2xlICovfVxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYXktODAwLzUwIHJvdW5kZWQtbGcgcC00XCI+XHJcbiAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yIGN1cnNvci1wb2ludGVyXCI+XHJcbiAgICAgICAgICAgICAgICA8aW5wdXRcclxuICAgICAgICAgICAgICAgICAgdHlwZT1cImNoZWNrYm94XCJcclxuICAgICAgICAgICAgICAgICAgY2hlY2tlZD17c2hvd01vZGVsU3RhdHN9XHJcbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgIHNldFNob3dNb2RlbFN0YXRzKGUudGFyZ2V0LmNoZWNrZWQpO1xyXG5cclxuICAgICAgICAgICAgICAgICAgICBpZiAoYWRkVG9IaXN0b3J5KSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICBhZGRUb0hpc3RvcnlJbW1lZGlhdGUoKTtcclxuICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZvcm0tY2hlY2tib3ggaC00IHctNCB0ZXh0LWJsdWUtNTAwIHJvdW5kZWQgYm9yZGVyLWdyYXktNjAwIGZvY3VzOnJpbmctYmx1ZS01MDBcIlxyXG4gICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteHMgbWQ6dGV4dC1zbSB0ZXh0LWdyYXktMjAwXCI+XHJcbiAgICAgICAgICAgICAgICAgIFNob3cgTW9kZWwgU3RhdHNcclxuICAgICAgICAgICAgICAgIDwvc3Bhbj5cclxuICAgICAgICAgICAgICA8L2xhYmVsPlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICl9XHJcbiAgICAgIDwvZGl2PlxyXG4gICAgPC9kaXY+XHJcbiAgKTtcclxufVxyXG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VDYWxsYmFjayIsInVzZUVmZmVjdCIsIkNocm9tZVBpY2tlciIsIkVudmlyb25tZW50UHJldmlld0JhbGwiLCJTaW5nbGVFbnZpcm9ubWVudFByZXZpZXciLCJHcmlkM1gzIiwiTGlzdCIsInVzZUhpc3RvcnlEZWJvdW5jZSIsIkVudmlyb25tZW50UGFuZWwiLCJlbnZQcmVzZXQiLCJzZXRFbnZQcmVzZXQiLCJiZ0NvbG9yIiwic2V0QmdDb2xvciIsInNob3dFbnZpcm9ubWVudCIsInNldFNob3dFbnZpcm9ubWVudCIsImN1c3RvbUhkcmkiLCJzZXRDdXN0b21IZHJpIiwiZW52SW50ZW5zaXR5Iiwic2V0RW52SW50ZW5zaXR5Iiwic2hvd01vZGVsU3RhdHMiLCJzZXRTaG93TW9kZWxTdGF0cyIsImVudkJsdXIiLCJzZXRFbnZCbHVyIiwiZW52Um90YXRpb24iLCJzZXRFbnZSb3RhdGlvbiIsImFkZFRvSGlzdG9yeSIsImFjdGl2ZVRhYiIsInNldEFjdGl2ZVRhYiIsInZpZXdNb2RlIiwic2V0Vmlld01vZGUiLCJpc01vYmlsZSIsInNldElzTW9iaWxlIiwiY3VycmVudFBhZ2UiLCJzZXRDdXJyZW50UGFnZSIsImRpc3BsYXlCZ0NvbG9yUGlja2VyIiwic2V0RGlzcGxheUJnQ29sb3JQaWNrZXIiLCJpdGVtc1BlclBhZ2UiLCJjaGVja01vYmlsZSIsIndpbmRvdyIsImlubmVyV2lkdGgiLCJhZGRFdmVudExpc3RlbmVyIiwicmVtb3ZlRXZlbnRMaXN0ZW5lciIsImFkZFRvSGlzdG9yeUltbWVkaWF0ZSIsImFkZFRvSGlzdG9yeURlYm91bmNlZCIsImhhbmRsZUhkcmlVcGxvYWQiLCJldmVudCIsImZpbGUiLCJ0YXJnZXQiLCJmaWxlcyIsInVybCIsIlVSTCIsImNyZWF0ZU9iamVjdFVSTCIsInByZXNldHMiLCJ2YWx1ZSIsImxhYmVsIiwidG90YWxQYWdlcyIsIk1hdGgiLCJjZWlsIiwibGVuZ3RoIiwic3RhcnRJbmRleCIsImVuZEluZGV4IiwiY3VycmVudFByZXNldHMiLCJzbGljZSIsImdvVG9QYWdlIiwicGFnZSIsIm1heCIsIm1pbiIsImdvVG9OZXh0UGFnZSIsImdvVG9QcmV2UGFnZSIsImhhbmRsZUJnQ29sb3JDbGljayIsImhhbmRsZUJnQ29sb3JDbG9zZSIsImhhbmRsZUJnQ29sb3JDaGFuZ2UiLCJjb2xvciIsImhleENvbG9yIiwiaGV4IiwicmVuZGVyU2xpZGVyIiwib25DaGFuZ2UiLCJzdGVwIiwidW5pdCIsImRpdiIsImNsYXNzTmFtZSIsInNwYW4iLCJ0b0ZpeGVkIiwiaW5wdXQiLCJ0eXBlIiwiZSIsImgyIiwiYnV0dG9uIiwib25DbGljayIsInRpdGxlIiwicHJlc2V0IiwiaW50ZW5zaXR5IiwibWFwIiwiZGlzYWJsZWQiLCJzdmciLCJmaWxsIiwic3Ryb2tlIiwidmlld0JveCIsInBhdGgiLCJzdHJva2VMaW5lY2FwIiwic3Ryb2tlTGluZWpvaW4iLCJzdHJva2VXaWR0aCIsImQiLCJBcnJheSIsImZyb20iLCJfIiwiaSIsImgzIiwiY2hlY2tlZCIsInN0eWxlIiwiYmFja2dyb3VuZENvbG9yIiwicG9zaXRpb24iLCJ6SW5kZXgiLCJ0b3AiLCJyaWdodCIsImJvdHRvbSIsImxlZnQiLCJkaXNhYmxlQWxwaGEiLCJtYXRjaCIsInBsYWNlaG9sZGVyIiwicGFyc2VGbG9hdCIsIm5ld1ZhbHVlIiwiTnVtYmVyIiwiUEkiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ArtistTool/EnvironmentPanel.jsx\n"));

/***/ })

});