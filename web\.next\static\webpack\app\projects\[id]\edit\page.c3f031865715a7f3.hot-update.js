"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/projects/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/ArtistTool/EnvironmentPanel.jsx":
/*!********************************************************!*\
  !*** ./src/components/ArtistTool/EnvironmentPanel.jsx ***!
  \********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EnvironmentPanel: function() { return /* binding */ EnvironmentPanel; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_color__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-color */ \"(app-pages-browser)/./node_modules/.pnpm/react-color@2.19.3_react@18.3.1/node_modules/react-color/es/index.js\");\n/* harmony import */ var _EnvironmentPreviewBall__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./EnvironmentPreviewBall */ \"(app-pages-browser)/./src/components/ArtistTool/EnvironmentPreviewBall.jsx\");\n/* harmony import */ var _barrel_optimize_names_Grid3X3_List_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Grid3X3,List!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/list.js\");\n/* harmony import */ var _barrel_optimize_names_Grid3X3_List_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Grid3X3,List!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.396.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/grid-3x3.js\");\n/* harmony import */ var _hooks_useDebounce__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useDebounce */ \"(app-pages-browser)/./src/hooks/useDebounce.ts\");\n/* __next_internal_client_entry_do_not_use__ EnvironmentPanel auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction EnvironmentPanel(param) {\n    let { envPreset, setEnvPreset, bgColor, setBgColor, showEnvironment, setShowEnvironment, customHdri, setCustomHdri, envIntensity, setEnvIntensity, showModelStats, setShowModelStats, envBlur, setEnvBlur, envRotation, setEnvRotation, addToHistory } = param;\n    var _this = this;\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"presets\");\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"single\"); // \"single\" or \"grid\"\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [displayBgColorPicker, setDisplayBgColorPicker] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [localEnvPreset, setLocalEnvPreset] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(envPreset);\n    const itemsPerPage = 6;\n    // Sync localEnvPreset with prop\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setLocalEnvPreset(envPreset);\n    }, [\n        envPreset\n    ]);\n    // Debounced update to parent\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const timeout = setTimeout(()=>{\n            if (localEnvPreset !== envPreset) {\n                setEnvPreset(localEnvPreset);\n                if (addToHistory) addToHistoryImmediate();\n            }\n        }, 250);\n        return ()=>clearTimeout(timeout);\n    }, [\n        localEnvPreset\n    ]);\n    // Check if device is mobile\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const checkMobile = ()=>{\n            setIsMobile(window.innerWidth < 768);\n        };\n        checkMobile();\n        window.addEventListener(\"resize\", checkMobile);\n        return ()=>window.removeEventListener(\"resize\", checkMobile);\n    }, []);\n    // Force single view on mobile\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isMobile) {\n            setViewMode(\"single\");\n        }\n    }, [\n        isMobile\n    ]);\n    // Reset to first page when switching tabs or view modes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setCurrentPage(0);\n    }, [\n        activeTab,\n        viewMode\n    ]);\n    // Setup debounced history for slider inputs\n    const { addToHistoryImmediate, addToHistoryDebounced } = (0,_hooks_useDebounce__WEBPACK_IMPORTED_MODULE_4__.useHistoryDebounce)(addToHistory || (()=>{}), 900);\n    const handleHdriUpload = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((event)=>{\n        const file = event.target.files[0];\n        if (file) {\n            const url = URL.createObjectURL(file);\n            setCustomHdri(url);\n            setEnvPreset(\"custom\");\n            // Add to history for HDRI upload\n            if (addToHistory) {\n                addToHistoryImmediate();\n            }\n        }\n    }, [\n        setCustomHdri,\n        setEnvPreset,\n        addToHistoryImmediate,\n        addToHistory\n    ]);\n    const presets = [\n        {\n            value: \"hdri_15\",\n            label: \"Metal 1\"\n        },\n        {\n            value: \"hdri_metal\",\n            label: \"Metal 2\"\n        },\n        {\n            value: \"hdri_metal2\",\n            label: \"Metal 3\"\n        },\n        {\n            value: \"hdri_metal3\",\n            label: \"Metal 4\"\n        },\n        {\n            value: \"hdri_gem\",\n            label: \"Gem Env\"\n        },\n        {\n            value: \"sunset\",\n            label: \"Sunset\"\n        },\n        {\n            value: \"dawn\",\n            label: \"Dawn\"\n        },\n        {\n            value: \"night\",\n            label: \"Night\"\n        },\n        {\n            value: \"warehouse\",\n            label: \"Warehouse\"\n        },\n        {\n            value: \"forest\",\n            label: \"Forest\"\n        },\n        {\n            value: \"apartment\",\n            label: \"Apartment\"\n        },\n        {\n            value: \"studio\",\n            label: \"Studio\"\n        },\n        {\n            value: \"studio_small\",\n            label: \"Studio Small\"\n        },\n        {\n            value: \"city\",\n            label: \"City\"\n        },\n        {\n            value: \"none\",\n            label: \"None\"\n        }\n    ];\n    // Pagination logic\n    const totalPages = Math.ceil(presets.length / itemsPerPage);\n    const startIndex = currentPage * itemsPerPage;\n    const endIndex = startIndex + itemsPerPage;\n    const currentPresets = presets.slice(startIndex, endIndex);\n    const goToPage = (page)=>{\n        setCurrentPage(Math.max(0, Math.min(page, totalPages - 1)));\n    };\n    const goToNextPage = ()=>{\n        if (currentPage < totalPages - 1) {\n            setCurrentPage(currentPage + 1);\n        }\n    };\n    const goToPrevPage = ()=>{\n        if (currentPage > 0) {\n            setCurrentPage(currentPage - 1);\n        }\n    };\n    const handleBgColorClick = ()=>{\n        setDisplayBgColorPicker(!displayBgColorPicker);\n    };\n    const handleBgColorClose = ()=>{\n        setDisplayBgColorPicker(false);\n    };\n    const handleBgColorChange = (color)=>{\n        const hexColor = color.hex;\n        setBgColor(hexColor);\n    };\n    const renderSlider = function(label, value, onChange, min, max, step) {\n        let unit = arguments.length > 6 && arguments[6] !== void 0 ? arguments[6] : \"\";\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mb-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center mb-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"text-xs md:text-sm font-medium text-gray-200\",\n                            children: label\n                        }, void 0, false, {\n                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                            lineNumber: 159,\n                            columnNumber: 9\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-[10px] md:text-xs text-gray-400\",\n                            children: [\n                                value.toFixed(2),\n                                unit\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                            lineNumber: 162,\n                            columnNumber: 9\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                    lineNumber: 158,\n                    columnNumber: 7\n                }, _this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                    type: \"range\",\n                    min: min,\n                    max: max,\n                    step: step,\n                    value: value,\n                    onChange: (e)=>{\n                        onChange(e);\n                        if (addToHistory) {\n                            addToHistoryDebounced();\n                        }\n                    },\n                    className: \"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer accent-blue-500\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                    lineNumber: 167,\n                    columnNumber: 7\n                }, _this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n            lineNumber: 157,\n            columnNumber: 5\n        }, _this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"text-white w-full overflow-y-auto text-[10px] md:text-xs scrollbar-none\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-base font-semibold mb-2 text-gray-100\",\n                    children: \"Environment\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                    lineNumber: 188,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-1 mb-4 bg-gray-800/30 p-0.5 rounded-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setActiveTab(\"presets\"),\n                            className: \"flex-1 px-2.5 py-1.5 rounded text-[10px] md:text-xs font-medium transition-all duration-200 \".concat(activeTab === \"presets\" ? \"bg-blue-500/90 text-white shadow-sm\" : \"text-gray-300 hover:bg-gray-700/50\"),\n                            children: \"Presets\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                            lineNumber: 194,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setActiveTab(\"settings\"),\n                            className: \"flex-1 px-2.5 py-1.5 rounded text-[10px] md:text-xs font-medium transition-all duration-200 \".concat(activeTab === \"settings\" ? \"bg-blue-500/90 text-white shadow-sm\" : \"text-gray-300 hover:bg-gray-700/50\"),\n                            children: \"Settings\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                            lineNumber: 214,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                    lineNumber: 193,\n                    columnNumber: 9\n                }, this),\n                activeTab === \"presets\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        !isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end items-center mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex bg-gray-800/50 rounded-lg p-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setViewMode(\"single\"),\n                                        className: \"p-2 rounded transition-all duration-200 relative group \".concat(viewMode === \"single\" ? \"bg-blue-500 text-white\" : \"text-gray-300 hover:text-white hover:bg-gray-700/50\"),\n                                        title: \"Single Preview - Shows one large preview with list of preset names\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Grid3X3_List_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-900 text-white text-[10px] md:text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap z-10\",\n                                                children: \"Single Preview\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setViewMode(\"grid\"),\n                                        className: \"p-2 rounded transition-all duration-200 relative group \".concat(viewMode === \"grid\" ? \"bg-blue-500 text-white\" : \"text-gray-300 hover:text-white hover:bg-gray-700/50\"),\n                                        title: \"Grid Preview - Shows all presets as small preview balls\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Grid3X3_List_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-900 text-white text-[10px] md:text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap z-10\",\n                                                children: \"Grid Preview\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                lineNumber: 232,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                            lineNumber: 231,\n                            columnNumber: 15\n                        }, this),\n                        viewMode === \"single\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EnvironmentPreviewBall__WEBPACK_IMPORTED_MODULE_3__.SingleEnvironmentPreview, {\n                                        preset: envPreset,\n                                        customHdri: customHdri,\n                                        intensity: envIntensity\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                    lineNumber: 269,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-1.5\",\n                                    children: currentPresets.map((preset)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                setLocalEnvPreset(preset.value);\n                                            },\n                                            className: \"w-full flex items-center justify-between p-2 rounded-md transition-all duration-200 \".concat(envPreset === preset.value ? \"bg-blue-500/20 ring-1 ring-blue-500\" : \"bg-gray-800/50 hover:bg-gray-700/50\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-[10px] md:text-xs font-medium text-gray-200\",\n                                                    children: preset.label\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                                    lineNumber: 291,\n                                                    columnNumber: 23\n                                                }, this),\n                                                envPreset === preset.value && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-1.5 h-1.5 bg-blue-500 rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                                    lineNumber: 295,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, preset.value, true, {\n                                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 21\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 17\n                                }, this),\n                                totalPages > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mt-4 pt-3 border-t border-gray-700/50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: goToPrevPage,\n                                            disabled: currentPage === 0,\n                                            className: \"flex items-center gap-1 px-2 py-1 rounded text-[10px] md:text-xs transition-all duration-200 \".concat(currentPage === 0 ? \"text-gray-500 cursor-not-allowed\" : \"text-gray-300 hover:text-white hover:bg-gray-700/50\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-3 h-3\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M15 19l-7-7 7-7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                                        lineNumber: 319,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                                    lineNumber: 313,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \"Prev\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                            lineNumber: 304,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1\",\n                                            children: Array.from({\n                                                length: totalPages\n                                            }, (_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>goToPage(i),\n                                                    className: \"w-2 h-2 rounded-full transition-all duration-200 \".concat(currentPage === i ? \"bg-blue-500\" : \"bg-gray-600 hover:bg-gray-500\")\n                                                }, i, false, {\n                                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                                    lineNumber: 331,\n                                                    columnNumber: 25\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                            lineNumber: 329,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: goToNextPage,\n                                            disabled: currentPage === totalPages - 1,\n                                            className: \"flex items-center gap-1 px-2 py-1 rounded text-[10px] md:text-xs transition-all duration-200 \".concat(currentPage === totalPages - 1 ? \"text-gray-500 cursor-not-allowed\" : \"text-gray-300 hover:text-white hover:bg-gray-700/50\"),\n                                            children: [\n                                                \"Next\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-3 h-3\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9 5l7 7-7 7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                                        lineNumber: 359,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                                    lineNumber: 353,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                    lineNumber: 303,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                            lineNumber: 267,\n                            columnNumber: 15\n                        }, this),\n                        viewMode === \"grid\" && !isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-3 gap-3\",\n                            children: presets.map((preset)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        setLocalEnvPreset(preset.value);\n                                    },\n                                    className: \"group relative flex flex-col items-center p-2 rounded-lg transition-all duration-200 \".concat(envPreset === preset.value ? \"bg-blue-500/20 ring-2 ring-blue-500\" : \"bg-gray-800/50 hover:bg-gray-700/50\"),\n                                    children: [\n                                        preset.value !== \"none\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EnvironmentPreviewBall__WEBPACK_IMPORTED_MODULE_3__.EnvironmentPreviewBall, {\n                                            preset: preset.value,\n                                            intensity: envIntensity\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                            lineNumber: 388,\n                                            columnNumber: 23\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 rounded-lg bg-gray-800 flex items-center justify-center text-[10px]\",\n                                            children: \"None\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                            lineNumber: 393,\n                                            columnNumber: 23\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"mt-2 text-[10px] md:text-xs font-medium text-gray-200\",\n                                            children: preset.label\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                            lineNumber: 397,\n                                            columnNumber: 21\n                                        }, this),\n                                        envPreset === preset.value && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-1 right-1 w-2 h-2 bg-blue-500 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                            lineNumber: 401,\n                                            columnNumber: 23\n                                        }, this)\n                                    ]\n                                }, preset.value, true, {\n                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                    lineNumber: 376,\n                                    columnNumber: 19\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                            lineNumber: 374,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                    lineNumber: 228,\n                    columnNumber: 11\n                }, this),\n                activeTab === \"settings\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-800/50 rounded-lg p-1 md:p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xs md:text-sm font-medium text-gray-200 mb-4\",\n                                    children: \"Display Settings\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                    lineNumber: 474,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"flex items-center gap-2 cursor-pointer\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"checkbox\",\n                                                    checked: showEnvironment,\n                                                    onChange: (e)=>setShowEnvironment(e.target.checked),\n                                                    className: \"form-checkbox h-4 w-4 text-blue-500 rounded border-gray-600 focus:ring-blue-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                                    lineNumber: 479,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs md:text-sm text-gray-200\",\n                                                    children: \"Show as Background\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                                    lineNumber: 485,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                            lineNumber: 478,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-xs md:text-sm font-medium text-gray-200 mb-2\",\n                                                    children: \"Background Color\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                                    lineNumber: 491,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-10 h-10 rounded cursor-pointer border border-gray-600\",\n                                                                    style: {\n                                                                        backgroundColor: bgColor\n                                                                    },\n                                                                    onClick: handleBgColorClick\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                                                    lineNumber: 496,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                displayBgColorPicker && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        position: \"absolute\",\n                                                                        zIndex: \"2\"\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            style: {\n                                                                                position: \"fixed\",\n                                                                                top: \"0px\",\n                                                                                right: \"0px\",\n                                                                                bottom: \"0px\",\n                                                                                left: \"0px\"\n                                                                            },\n                                                                            onClick: handleBgColorClose\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                                                            lineNumber: 503,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_color__WEBPACK_IMPORTED_MODULE_2__.ChromePicker, {\n                                                                            color: bgColor,\n                                                                            onChange: handleBgColorChange,\n                                                                            disableAlpha: true\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                                                            lineNumber: 513,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                                                    lineNumber: 502,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                                            lineNumber: 495,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: bgColor,\n                                                            onChange: (e)=>{\n                                                                const value = e.target.value;\n                                                                // Allow typing and validate hex format\n                                                                if (value.match(/^#[0-9A-Fa-f]{0,6}$/) || value === \"\") {\n                                                                    setBgColor(value);\n                                                                    if (addToHistory) {\n                                                                        addToHistoryImmediate();\n                                                                    }\n                                                                }\n                                                            },\n                                                            placeholder: \"#ffffff\",\n                                                            className: \"px-2 py-1 bg-gray-700 border border-gray-600 rounded text-[10px] md:text-xs text-gray-200 focus:border-blue-500 focus:outline-none w-20\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                                            lineNumber: 521,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                                    lineNumber: 494,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                            lineNumber: 490,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                    lineNumber: 477,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                            lineNumber: 473,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-800/50 rounded-lg p-1 md:p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xs md:text-sm font-medium text-gray-200 mb-4\",\n                                    children: \"Environment Adjustments\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                    lineNumber: 547,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        renderSlider(\"Environment Intensity\", envIntensity, (e)=>setEnvIntensity(parseFloat(e.target.value)), 0, 10, 0.01),\n                                        renderSlider(\"Environment Rotation\", envRotation, (e)=>{\n                                            const newValue = Number(e.target.value);\n                                            setEnvRotation(newValue >= 0 ? newValue % 360 : 360 + newValue % 360);\n                                        }, 0, Math.PI * 2, 0.01, \"\\xb0\"),\n                                        renderSlider(\"Background Blur\", envBlur, (e)=>setEnvBlur(parseFloat(e.target.value)), 0, 1, 0.01)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                    lineNumber: 550,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                            lineNumber: 546,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-800/50 rounded-lg p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"flex items-center gap-2 cursor-pointer\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        checked: showModelStats,\n                                        onChange: (e)=>{\n                                            setShowModelStats(e.target.checked);\n                                            if (addToHistory) {\n                                                addToHistoryImmediate();\n                                            }\n                                        },\n                                        className: \"form-checkbox h-4 w-4 text-blue-500 rounded border-gray-600 focus:ring-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                        lineNumber: 589,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs md:text-sm text-gray-200\",\n                                        children: \"Show Model Stats\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                        lineNumber: 601,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                                lineNumber: 588,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                            lineNumber: 587,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n                    lineNumber: 471,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n            lineNumber: 187,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EnvironmentPanel.jsx\",\n        lineNumber: 186,\n        columnNumber: 5\n    }, this);\n}\n_s(EnvironmentPanel, \"kA2RdvUOALmIPzEfA0mDoqL7Nco=\", false, function() {\n    return [\n        _hooks_useDebounce__WEBPACK_IMPORTED_MODULE_4__.useHistoryDebounce\n    ];\n});\n_c = EnvironmentPanel;\nvar _c;\n$RefreshReg$(_c, \"EnvironmentPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ArtistTool/EnvironmentPanel.jsx\n"));

/***/ })

});