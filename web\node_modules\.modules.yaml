hoistPattern:
  - '*'
hoistedDependencies:
  '@alloc/quick-lru@5.2.0':
    '@alloc/quick-lru': private
  '@auth/core@0.34.2(nodemailer@6.9.16)':
    '@auth/core': private
  '@aws-crypto/crc32@3.0.0':
    '@aws-crypto/crc32': private
  '@aws-crypto/crc32c@3.0.0':
    '@aws-crypto/crc32c': private
  '@aws-crypto/ie11-detection@3.0.0':
    '@aws-crypto/ie11-detection': private
  '@aws-crypto/sha1-browser@3.0.0':
    '@aws-crypto/sha1-browser': private
  '@aws-crypto/sha256-browser@3.0.0':
    '@aws-crypto/sha256-browser': private
  '@aws-crypto/sha256-js@3.0.0':
    '@aws-crypto/sha256-js': private
  '@aws-crypto/supports-web-crypto@3.0.0':
    '@aws-crypto/supports-web-crypto': private
  '@aws-crypto/util@3.0.0':
    '@aws-crypto/util': private
  '@aws-sdk/abort-controller@3.296.0':
    '@aws-sdk/abort-controller': private
  '@aws-sdk/chunked-blob-reader@3.295.0':
    '@aws-sdk/chunked-blob-reader': private
  '@aws-sdk/client-sso-oidc@3.300.0':
    '@aws-sdk/client-sso-oidc': private
  '@aws-sdk/client-sso@3.300.0':
    '@aws-sdk/client-sso': private
  '@aws-sdk/client-sts@3.300.0':
    '@aws-sdk/client-sts': private
  '@aws-sdk/config-resolver@3.300.0':
    '@aws-sdk/config-resolver': private
  '@aws-sdk/credential-provider-env@3.296.0':
    '@aws-sdk/credential-provider-env': private
  '@aws-sdk/credential-provider-imds@3.300.0':
    '@aws-sdk/credential-provider-imds': private
  '@aws-sdk/credential-provider-ini@3.300.0':
    '@aws-sdk/credential-provider-ini': private
  '@aws-sdk/credential-provider-node@3.300.0':
    '@aws-sdk/credential-provider-node': private
  '@aws-sdk/credential-provider-process@3.300.0':
    '@aws-sdk/credential-provider-process': private
  '@aws-sdk/credential-provider-sso@3.300.0':
    '@aws-sdk/credential-provider-sso': private
  '@aws-sdk/credential-provider-web-identity@3.296.0':
    '@aws-sdk/credential-provider-web-identity': private
  '@aws-sdk/eventstream-codec@3.296.0':
    '@aws-sdk/eventstream-codec': private
  '@aws-sdk/eventstream-serde-browser@3.296.0':
    '@aws-sdk/eventstream-serde-browser': private
  '@aws-sdk/eventstream-serde-config-resolver@3.296.0':
    '@aws-sdk/eventstream-serde-config-resolver': private
  '@aws-sdk/eventstream-serde-node@3.299.0':
    '@aws-sdk/eventstream-serde-node': private
  '@aws-sdk/eventstream-serde-universal@3.296.0':
    '@aws-sdk/eventstream-serde-universal': private
  '@aws-sdk/fetch-http-handler@3.296.0':
    '@aws-sdk/fetch-http-handler': private
  '@aws-sdk/hash-blob-browser@3.299.0':
    '@aws-sdk/hash-blob-browser': private
  '@aws-sdk/hash-node@3.296.0':
    '@aws-sdk/hash-node': private
  '@aws-sdk/hash-stream-node@3.296.0':
    '@aws-sdk/hash-stream-node': private
  '@aws-sdk/invalid-dependency@3.296.0':
    '@aws-sdk/invalid-dependency': private
  '@aws-sdk/is-array-buffer@3.295.0':
    '@aws-sdk/is-array-buffer': private
  '@aws-sdk/md5-js@3.296.0':
    '@aws-sdk/md5-js': private
  '@aws-sdk/middleware-bucket-endpoint@3.300.0':
    '@aws-sdk/middleware-bucket-endpoint': private
  '@aws-sdk/middleware-content-length@3.296.0':
    '@aws-sdk/middleware-content-length': private
  '@aws-sdk/middleware-endpoint@3.299.0':
    '@aws-sdk/middleware-endpoint': private
  '@aws-sdk/middleware-expect-continue@3.296.0':
    '@aws-sdk/middleware-expect-continue': private
  '@aws-sdk/middleware-flexible-checksums@3.296.0':
    '@aws-sdk/middleware-flexible-checksums': private
  '@aws-sdk/middleware-host-header@3.296.0':
    '@aws-sdk/middleware-host-header': private
  '@aws-sdk/middleware-location-constraint@3.296.0':
    '@aws-sdk/middleware-location-constraint': private
  '@aws-sdk/middleware-logger@3.296.0':
    '@aws-sdk/middleware-logger': private
  '@aws-sdk/middleware-recursion-detection@3.296.0':
    '@aws-sdk/middleware-recursion-detection': private
  '@aws-sdk/middleware-retry@3.300.0':
    '@aws-sdk/middleware-retry': private
  '@aws-sdk/middleware-sdk-s3@3.296.0':
    '@aws-sdk/middleware-sdk-s3': private
  '@aws-sdk/middleware-sdk-sts@3.299.0':
    '@aws-sdk/middleware-sdk-sts': private
  '@aws-sdk/middleware-serde@3.296.0':
    '@aws-sdk/middleware-serde': private
  '@aws-sdk/middleware-signing@3.299.0':
    '@aws-sdk/middleware-signing': private
  '@aws-sdk/middleware-ssec@3.296.0':
    '@aws-sdk/middleware-ssec': private
  '@aws-sdk/middleware-stack@3.296.0':
    '@aws-sdk/middleware-stack': private
  '@aws-sdk/middleware-user-agent@3.299.0':
    '@aws-sdk/middleware-user-agent': private
  '@aws-sdk/node-config-provider@3.300.0':
    '@aws-sdk/node-config-provider': private
  '@aws-sdk/node-http-handler@3.296.0':
    '@aws-sdk/node-http-handler': private
  '@aws-sdk/property-provider@3.296.0':
    '@aws-sdk/property-provider': private
  '@aws-sdk/protocol-http@3.296.0':
    '@aws-sdk/protocol-http': private
  '@aws-sdk/querystring-builder@3.296.0':
    '@aws-sdk/querystring-builder': private
  '@aws-sdk/querystring-parser@3.296.0':
    '@aws-sdk/querystring-parser': private
  '@aws-sdk/service-error-classification@3.296.0':
    '@aws-sdk/service-error-classification': private
  '@aws-sdk/shared-ini-file-loader@3.300.0':
    '@aws-sdk/shared-ini-file-loader': private
  '@aws-sdk/signature-v4-multi-region@3.299.0':
    '@aws-sdk/signature-v4-multi-region': private
  '@aws-sdk/signature-v4@3.299.0':
    '@aws-sdk/signature-v4': private
  '@aws-sdk/smithy-client@3.296.0':
    '@aws-sdk/smithy-client': private
  '@aws-sdk/token-providers@3.300.0':
    '@aws-sdk/token-providers': private
  '@aws-sdk/types@3.296.0':
    '@aws-sdk/types': private
  '@aws-sdk/url-parser@3.296.0':
    '@aws-sdk/url-parser': private
  '@aws-sdk/util-arn-parser@3.295.0':
    '@aws-sdk/util-arn-parser': private
  '@aws-sdk/util-base64@3.295.0':
    '@aws-sdk/util-base64': private
  '@aws-sdk/util-body-length-browser@3.295.0':
    '@aws-sdk/util-body-length-browser': private
  '@aws-sdk/util-body-length-node@3.295.0':
    '@aws-sdk/util-body-length-node': private
  '@aws-sdk/util-buffer-from@3.295.0':
    '@aws-sdk/util-buffer-from': private
  '@aws-sdk/util-config-provider@3.295.0':
    '@aws-sdk/util-config-provider': private
  '@aws-sdk/util-defaults-mode-browser@3.296.0':
    '@aws-sdk/util-defaults-mode-browser': private
  '@aws-sdk/util-defaults-mode-node@3.300.0':
    '@aws-sdk/util-defaults-mode-node': private
  '@aws-sdk/util-endpoints@3.296.0':
    '@aws-sdk/util-endpoints': private
  '@aws-sdk/util-format-url@3.296.0':
    '@aws-sdk/util-format-url': private
  '@aws-sdk/util-hex-encoding@3.295.0':
    '@aws-sdk/util-hex-encoding': private
  '@aws-sdk/util-locate-window@3.693.0':
    '@aws-sdk/util-locate-window': private
  '@aws-sdk/util-middleware@3.296.0':
    '@aws-sdk/util-middleware': private
  '@aws-sdk/util-retry@3.296.0':
    '@aws-sdk/util-retry': private
  '@aws-sdk/util-stream-browser@3.296.0':
    '@aws-sdk/util-stream-browser': private
  '@aws-sdk/util-stream-node@3.296.0':
    '@aws-sdk/util-stream-node': private
  '@aws-sdk/util-uri-escape@3.295.0':
    '@aws-sdk/util-uri-escape': private
  '@aws-sdk/util-user-agent-browser@3.299.0':
    '@aws-sdk/util-user-agent-browser': private
  '@aws-sdk/util-user-agent-node@3.300.0':
    '@aws-sdk/util-user-agent-node': private
  '@aws-sdk/util-utf8-browser@3.259.0':
    '@aws-sdk/util-utf8-browser': private
  '@aws-sdk/util-utf8@3.295.0':
    '@aws-sdk/util-utf8': private
  '@aws-sdk/util-waiter@3.296.0':
    '@aws-sdk/util-waiter': private
  '@aws-sdk/xml-builder@3.295.0':
    '@aws-sdk/xml-builder': private
  '@babel/runtime@7.26.9':
    '@babel/runtime': private
  '@emnapi/runtime@1.3.1':
    '@emnapi/runtime': private
  '@eslint-community/eslint-utils@4.4.0(eslint@8.57.0)':
    '@eslint-community/eslint-utils': public
  '@eslint-community/regexpp@4.11.0':
    '@eslint-community/regexpp': public
  '@eslint/eslintrc@2.1.4':
    '@eslint/eslintrc': public
  '@eslint/js@8.57.0':
    '@eslint/js': public
  '@floating-ui/core@1.6.7':
    '@floating-ui/core': private
  '@floating-ui/dom@1.6.10':
    '@floating-ui/dom': private
  '@floating-ui/react-dom@2.1.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@floating-ui/react-dom': private
  '@floating-ui/utils@0.2.7':
    '@floating-ui/utils': private
  '@gilbarbara/deep-equal@0.3.1':
    '@gilbarbara/deep-equal': private
  '@humanwhocodes/config-array@0.11.14':
    '@humanwhocodes/config-array': private
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': private
  '@humanwhocodes/object-schema@2.0.3':
    '@humanwhocodes/object-schema': private
  '@icons/material@0.2.4(react@18.3.1)':
    '@icons/material': private
  '@img/sharp-darwin-arm64@0.33.5':
    '@img/sharp-darwin-arm64': private
  '@img/sharp-darwin-x64@0.33.5':
    '@img/sharp-darwin-x64': private
  '@img/sharp-libvips-darwin-arm64@1.0.4':
    '@img/sharp-libvips-darwin-arm64': private
  '@img/sharp-libvips-darwin-x64@1.0.4':
    '@img/sharp-libvips-darwin-x64': private
  '@img/sharp-libvips-linux-arm64@1.0.4':
    '@img/sharp-libvips-linux-arm64': private
  '@img/sharp-libvips-linux-arm@1.0.5':
    '@img/sharp-libvips-linux-arm': private
  '@img/sharp-libvips-linux-s390x@1.0.4':
    '@img/sharp-libvips-linux-s390x': private
  '@img/sharp-libvips-linux-x64@1.0.4':
    '@img/sharp-libvips-linux-x64': private
  '@img/sharp-libvips-linuxmusl-arm64@1.0.4':
    '@img/sharp-libvips-linuxmusl-arm64': private
  '@img/sharp-libvips-linuxmusl-x64@1.0.4':
    '@img/sharp-libvips-linuxmusl-x64': private
  '@img/sharp-linux-arm64@0.33.5':
    '@img/sharp-linux-arm64': private
  '@img/sharp-linux-arm@0.33.5':
    '@img/sharp-linux-arm': private
  '@img/sharp-linux-s390x@0.33.5':
    '@img/sharp-linux-s390x': private
  '@img/sharp-linux-x64@0.33.5':
    '@img/sharp-linux-x64': private
  '@img/sharp-linuxmusl-arm64@0.33.5':
    '@img/sharp-linuxmusl-arm64': private
  '@img/sharp-linuxmusl-x64@0.33.5':
    '@img/sharp-linuxmusl-x64': private
  '@img/sharp-wasm32@0.33.5':
    '@img/sharp-wasm32': private
  '@img/sharp-win32-ia32@0.33.5':
    '@img/sharp-win32-ia32': private
  '@img/sharp-win32-x64@0.33.5':
    '@img/sharp-win32-x64': private
  '@ioredis/commands@1.2.0':
    '@ioredis/commands': private
  '@isaacs/cliui@8.0.2':
    '@isaacs/cliui': private
  '@jridgewell/gen-mapping@0.3.5':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/set-array@1.2.1':
    '@jridgewell/set-array': private
  '@jridgewell/sourcemap-codec@1.5.0':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.25':
    '@jridgewell/trace-mapping': private
  '@mediapipe/tasks-vision@0.10.17':
    '@mediapipe/tasks-vision': private
  '@mongodb-js/saslprep@1.1.9':
    '@mongodb-js/saslprep': private
  '@monogrid/gainmap-js@3.1.0(three@0.167.1)':
    '@monogrid/gainmap-js': private
  '@msgpackr-extract/msgpackr-extract-darwin-arm64@3.0.3':
    '@msgpackr-extract/msgpackr-extract-darwin-arm64': private
  '@msgpackr-extract/msgpackr-extract-darwin-x64@3.0.3':
    '@msgpackr-extract/msgpackr-extract-darwin-x64': private
  '@msgpackr-extract/msgpackr-extract-linux-arm64@3.0.3':
    '@msgpackr-extract/msgpackr-extract-linux-arm64': private
  '@msgpackr-extract/msgpackr-extract-linux-arm@3.0.3':
    '@msgpackr-extract/msgpackr-extract-linux-arm': private
  '@msgpackr-extract/msgpackr-extract-linux-x64@3.0.3':
    '@msgpackr-extract/msgpackr-extract-linux-x64': private
  '@msgpackr-extract/msgpackr-extract-win32-x64@3.0.3':
    '@msgpackr-extract/msgpackr-extract-win32-x64': private
  '@next/env@14.2.4':
    '@next/env': private
  '@next/eslint-plugin-next@14.2.4':
    '@next/eslint-plugin-next': public
  '@next/swc-darwin-arm64@14.2.4':
    '@next/swc-darwin-arm64': private
  '@next/swc-darwin-x64@14.2.4':
    '@next/swc-darwin-x64': private
  '@next/swc-linux-arm64-gnu@14.2.4':
    '@next/swc-linux-arm64-gnu': private
  '@next/swc-linux-arm64-musl@14.2.4':
    '@next/swc-linux-arm64-musl': private
  '@next/swc-linux-x64-gnu@14.2.4':
    '@next/swc-linux-x64-gnu': private
  '@next/swc-linux-x64-musl@14.2.4':
    '@next/swc-linux-x64-musl': private
  '@next/swc-win32-arm64-msvc@14.2.4':
    '@next/swc-win32-arm64-msvc': private
  '@next/swc-win32-ia32-msvc@14.2.4':
    '@next/swc-win32-ia32-msvc': private
  '@next/swc-win32-x64-msvc@14.2.4':
    '@next/swc-win32-x64-msvc': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@panva/hkdf@1.2.1':
    '@panva/hkdf': private
  '@pkgjs/parseargs@0.11.0':
    '@pkgjs/parseargs': private
  '@radix-ui/number@1.1.0':
    '@radix-ui/number': private
  '@radix-ui/primitive@1.1.0':
    '@radix-ui/primitive': private
  '@radix-ui/react-arrow@1.1.0(@types/react-dom@18.3.0)(@types/react@18.3.4)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-arrow': private
  '@radix-ui/react-collapsible@1.1.0(@types/react-dom@18.3.0)(@types/react@18.3.4)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-collapsible': private
  '@radix-ui/react-collection@1.1.0(@types/react-dom@18.3.0)(@types/react@18.3.4)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-collection': private
  '@radix-ui/react-compose-refs@1.1.0(@types/react@18.3.4)(react@18.3.1)':
    '@radix-ui/react-compose-refs': private
  '@radix-ui/react-context@1.1.0(@types/react@18.3.4)(react@18.3.1)':
    '@radix-ui/react-context': private
  '@radix-ui/react-direction@1.1.0(@types/react@18.3.4)(react@18.3.1)':
    '@radix-ui/react-direction': private
  '@radix-ui/react-dismissable-layer@1.1.0(@types/react-dom@18.3.0)(@types/react@18.3.4)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-dismissable-layer': private
  '@radix-ui/react-focus-guards@1.1.0(@types/react@18.3.4)(react@18.3.1)':
    '@radix-ui/react-focus-guards': private
  '@radix-ui/react-focus-scope@1.1.0(@types/react-dom@18.3.0)(@types/react@18.3.4)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-focus-scope': private
  '@radix-ui/react-id@1.1.0(@types/react@18.3.4)(react@18.3.1)':
    '@radix-ui/react-id': private
  '@radix-ui/react-menu@2.1.1(@types/react-dom@18.3.0)(@types/react@18.3.4)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-menu': private
  '@radix-ui/react-popper@1.2.0(@types/react-dom@18.3.0)(@types/react@18.3.4)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-popper': private
  '@radix-ui/react-portal@1.1.1(@types/react-dom@18.3.0)(@types/react@18.3.4)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-portal': private
  '@radix-ui/react-presence@1.1.0(@types/react-dom@18.3.0)(@types/react@18.3.4)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-presence': private
  '@radix-ui/react-primitive@2.0.0(@types/react-dom@18.3.0)(@types/react@18.3.4)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-primitive': private
  '@radix-ui/react-roving-focus@1.1.0(@types/react-dom@18.3.0)(@types/react@18.3.4)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-roving-focus': private
  '@radix-ui/react-use-callback-ref@1.1.0(@types/react@18.3.4)(react@18.3.1)':
    '@radix-ui/react-use-callback-ref': private
  '@radix-ui/react-use-controllable-state@1.1.0(@types/react@18.3.4)(react@18.3.1)':
    '@radix-ui/react-use-controllable-state': private
  '@radix-ui/react-use-escape-keydown@1.1.0(@types/react@18.3.4)(react@18.3.1)':
    '@radix-ui/react-use-escape-keydown': private
  '@radix-ui/react-use-layout-effect@1.1.0(@types/react@18.3.4)(react@18.3.1)':
    '@radix-ui/react-use-layout-effect': private
  '@radix-ui/react-use-previous@1.1.0(@types/react@18.3.4)(react@18.3.1)':
    '@radix-ui/react-use-previous': private
  '@radix-ui/react-use-rect@1.1.0(@types/react@18.3.4)(react@18.3.1)':
    '@radix-ui/react-use-rect': private
  '@radix-ui/react-use-size@1.1.0(@types/react@18.3.4)(react@18.3.1)':
    '@radix-ui/react-use-size': private
  '@radix-ui/react-visually-hidden@1.1.0(@types/react-dom@18.3.0)(@types/react@18.3.4)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-visually-hidden': private
  '@radix-ui/rect@1.1.0':
    '@radix-ui/rect': private
  '@react-spring/animated@9.7.5(react@18.3.1)':
    '@react-spring/animated': private
  '@react-spring/core@9.7.5(react@18.3.1)':
    '@react-spring/core': private
  '@react-spring/rafz@9.7.5':
    '@react-spring/rafz': private
  '@react-spring/shared@9.7.5(react@18.3.1)':
    '@react-spring/shared': private
  '@react-spring/three@9.7.5(@react-three/fiber@8.18.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(three@0.167.1))(react@18.3.1)(three@0.167.1)':
    '@react-spring/three': private
  '@react-spring/types@9.7.5':
    '@react-spring/types': private
  '@rushstack/eslint-patch@1.10.4':
    '@rushstack/eslint-patch': public
  '@smithy/types@4.3.1':
    '@smithy/types': private
  '@socket.io/component-emitter@3.1.2':
    '@socket.io/component-emitter': private
  '@swc/counter@0.1.3':
    '@swc/counter': private
  '@swc/helpers@0.5.5':
    '@swc/helpers': private
  '@tweenjs/tween.js@23.1.3':
    '@tweenjs/tween.js': private
  '@types/cookie@0.6.0':
    '@types/cookie': private
  '@types/cors@2.8.17':
    '@types/cors': private
  '@types/draco3d@1.4.10':
    '@types/draco3d': private
  '@types/json5@0.0.29':
    '@types/json5': private
  '@types/ndarray@1.0.14':
    '@types/ndarray': private
  '@types/offscreencanvas@2019.7.3':
    '@types/offscreencanvas': private
  '@types/prop-types@15.7.12':
    '@types/prop-types': private
  '@types/react-reconciler@0.26.7':
    '@types/react-reconciler': private
  '@types/reactcss@1.2.12':
    '@types/reactcss': private
  '@types/stats.js@0.17.3':
    '@types/stats.js': private
  '@types/webidl-conversions@7.0.3':
    '@types/webidl-conversions': private
  '@types/webxr@0.5.19':
    '@types/webxr': private
  '@types/whatwg-url@11.0.5':
    '@types/whatwg-url': private
  '@typescript-eslint/parser@7.2.0(eslint@8.57.0)(typescript@5.5.4)':
    '@typescript-eslint/parser': public
  '@typescript-eslint/scope-manager@7.2.0':
    '@typescript-eslint/scope-manager': public
  '@typescript-eslint/types@7.2.0':
    '@typescript-eslint/types': public
  '@typescript-eslint/typescript-estree@7.2.0(typescript@5.5.4)':
    '@typescript-eslint/typescript-estree': public
  '@typescript-eslint/visitor-keys@7.2.0':
    '@typescript-eslint/visitor-keys': public
  '@ungap/structured-clone@1.2.0':
    '@ungap/structured-clone': private
  '@use-gesture/core@10.3.1':
    '@use-gesture/core': private
  '@use-gesture/react@10.3.1(react@18.3.1)':
    '@use-gesture/react': private
  '@webgpu/types@0.1.44':
    '@webgpu/types': private
  accepts@1.3.8:
    accepts: private
  acorn-jsx@5.3.2(acorn@8.12.1):
    acorn-jsx: private
  acorn@8.12.1:
    acorn: private
  ajv@6.12.6:
    ajv: private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@4.3.0:
    ansi-styles: private
  any-promise@1.3.0:
    any-promise: private
  anymatch@3.1.3:
    anymatch: private
  arg@5.0.2:
    arg: private
  argparse@2.0.1:
    argparse: private
  aria-hidden@1.2.4:
    aria-hidden: private
  aria-query@5.1.3:
    aria-query: private
  array-buffer-byte-length@1.0.1:
    array-buffer-byte-length: private
  array-includes@3.1.8:
    array-includes: private
  array-union@2.1.0:
    array-union: private
  array.prototype.findlast@1.2.5:
    array.prototype.findlast: private
  array.prototype.findlastindex@1.2.5:
    array.prototype.findlastindex: private
  array.prototype.flat@1.3.2:
    array.prototype.flat: private
  array.prototype.flatmap@1.3.2:
    array.prototype.flatmap: private
  array.prototype.tosorted@1.1.4:
    array.prototype.tosorted: private
  arraybuffer.prototype.slice@1.0.3:
    arraybuffer.prototype.slice: private
  ast-types-flow@0.0.8:
    ast-types-flow: private
  attr-accept@2.2.2:
    attr-accept: private
  available-typed-arrays@1.0.7:
    available-typed-arrays: private
  axe-core@4.10.0:
    axe-core: private
  axobject-query@3.1.1:
    axobject-query: private
  balanced-match@1.0.2:
    balanced-match: private
  base64-js@1.5.1:
    base64-js: private
  base64id@2.0.0:
    base64id: private
  bidi-js@1.0.3:
    bidi-js: private
  binary-extensions@2.3.0:
    binary-extensions: private
  bowser@2.11.0:
    bowser: private
  brace-expansion@1.1.11:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  bson@6.8.0:
    bson: private
  buffer@6.0.3:
    buffer: private
  busboy@1.6.0:
    busboy: private
  call-bind@1.0.7:
    call-bind: private
  callsites@3.1.0:
    callsites: private
  camelcase-css@2.0.1:
    camelcase-css: private
  camera-controls@2.9.0(three@0.167.1):
    camera-controls: private
  caniuse-lite@1.0.30001651:
    caniuse-lite: private
  chalk@4.1.2:
    chalk: private
  chokidar@3.6.0:
    chokidar: private
  client-only@0.0.1:
    client-only: private
  cluster-key-slot@1.1.2:
    cluster-key-slot: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  color-string@1.9.1:
    color-string: private
  color@4.2.3:
    color: private
  commander@4.1.1:
    commander: private
  concat-map@0.0.1:
    concat-map: private
  cookie@0.5.0:
    cookie: private
  cors@2.8.5:
    cors: private
  cron-parser@4.9.0:
    cron-parser: private
  cross-env@7.0.3:
    cross-env: private
  cross-spawn@7.0.3:
    cross-spawn: private
  cssesc@3.0.0:
    cssesc: private
  csstype@3.1.3:
    csstype: private
  cwise-compiler@1.1.3:
    cwise-compiler: private
  damerau-levenshtein@1.0.8:
    damerau-levenshtein: private
  data-view-buffer@1.0.1:
    data-view-buffer: private
  data-view-byte-length@1.0.1:
    data-view-byte-length: private
  data-view-byte-offset@1.0.0:
    data-view-byte-offset: private
  debug@4.3.6:
    debug: private
  deep-diff@1.0.2:
    deep-diff: private
  deep-equal@2.2.3:
    deep-equal: private
  deep-is@0.1.4:
    deep-is: private
  deepmerge@4.3.1:
    deepmerge: private
  define-data-property@1.1.4:
    define-data-property: private
  define-properties@1.2.1:
    define-properties: private
  delegate-it@6.2.0:
    delegate-it: private
  denque@2.1.0:
    denque: private
  detect-gpu@5.0.70:
    detect-gpu: private
  detect-libc@2.0.3:
    detect-libc: private
  detect-node-es@1.1.0:
    detect-node-es: private
  didyoumean@1.2.2:
    didyoumean: private
  dir-glob@3.0.1:
    dir-glob: private
  dlv@1.1.3:
    dlv: private
  doctrine@3.0.0:
    doctrine: private
  draco3d@1.5.7:
    draco3d: private
  eastasianwidth@0.2.0:
    eastasianwidth: private
  emoji-regex@9.2.2:
    emoji-regex: private
  engine.io-client@6.6.1:
    engine.io-client: private
  engine.io-parser@5.2.3:
    engine.io-parser: private
  engine.io@6.6.1:
    engine.io: private
  enhanced-resolve@5.17.1:
    enhanced-resolve: private
  es-abstract@1.23.3:
    es-abstract: private
  es-define-property@1.0.0:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-get-iterator@1.1.3:
    es-get-iterator: private
  es-iterator-helpers@1.0.19:
    es-iterator-helpers: private
  es-object-atoms@1.0.0:
    es-object-atoms: private
  es-set-tostringtag@2.0.3:
    es-set-tostringtag: private
  es-shim-unscopables@1.0.2:
    es-shim-unscopables: private
  es-to-primitive@1.2.1:
    es-to-primitive: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  eslint-import-resolver-node@0.3.9:
    eslint-import-resolver-node: public
  eslint-import-resolver-typescript@3.6.1(@typescript-eslint/parser@7.2.0(eslint@8.57.0)(typescript@5.5.4))(eslint-import-resolver-node@0.3.9)(eslint-plugin-import@2.29.1(eslint@8.57.0))(eslint@8.57.0):
    eslint-import-resolver-typescript: public
  eslint-module-utils@2.8.1(@typescript-eslint/parser@7.2.0(eslint@8.57.0)(typescript@5.5.4))(eslint-import-resolver-node@0.3.9)(eslint-import-resolver-typescript@3.6.1(@typescript-eslint/parser@7.2.0(eslint@8.57.0)(typescript@5.5.4))(eslint-import-resolver-node@0.3.9)(eslint-plugin-import@2.29.1(eslint@8.57.0))(eslint@8.57.0))(eslint@8.57.0):
    eslint-module-utils: public
  eslint-plugin-import@2.29.1(@typescript-eslint/parser@7.2.0(eslint@8.57.0)(typescript@5.5.4))(eslint-import-resolver-typescript@3.6.1(@typescript-eslint/parser@7.2.0(eslint@8.57.0)(typescript@5.5.4))(eslint-import-resolver-node@0.3.9)(eslint-plugin-import@2.29.1(eslint@8.57.0))(eslint@8.57.0))(eslint@8.57.0):
    eslint-plugin-import: public
  eslint-plugin-jsx-a11y@6.9.0(eslint@8.57.0):
    eslint-plugin-jsx-a11y: public
  eslint-plugin-react-hooks@4.6.2(eslint@8.57.0):
    eslint-plugin-react-hooks: public
  eslint-plugin-react@7.35.0(eslint@8.57.0):
    eslint-plugin-react: public
  eslint-scope@7.2.2:
    eslint-scope: public
  eslint-visitor-keys@3.4.3:
    eslint-visitor-keys: public
  espree@9.6.1:
    espree: private
  esquery@1.6.0:
    esquery: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@5.3.0:
    estraverse: private
  esutils@2.0.3:
    esutils: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-glob@3.3.2:
    fast-glob: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fast-levenshtein@2.0.6:
    fast-levenshtein: private
  fast-xml-parser@4.1.2:
    fast-xml-parser: private
  fastq@1.17.1:
    fastq: private
  fflate@0.8.2:
    fflate: private
  file-entry-cache@6.0.1:
    file-entry-cache: private
  file-selector@0.6.0:
    file-selector: private
  fill-range@7.1.1:
    fill-range: private
  find-up@5.0.0:
    find-up: private
  flairup@1.0.0:
    flairup: private
  flat-cache@3.2.0:
    flat-cache: private
  flatted@3.3.1:
    flatted: private
  for-each@0.3.3:
    for-each: private
  foreground-child@3.3.0:
    foreground-child: private
  fs.realpath@1.0.0:
    fs.realpath: private
  fsevents@2.3.3:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  function.prototype.name@1.1.6:
    function.prototype.name: private
  functions-have-names@1.2.3:
    functions-have-names: private
  get-intrinsic@1.2.4:
    get-intrinsic: private
  get-nonce@1.0.1:
    get-nonce: private
  get-port@5.1.1:
    get-port: private
  get-symbol-description@1.0.2:
    get-symbol-description: private
  get-tsconfig@4.7.6:
    get-tsconfig: private
  glob-parent@6.0.2:
    glob-parent: private
  glob@10.3.10:
    glob: private
  globals@13.24.0:
    globals: private
  globalthis@1.0.4:
    globalthis: private
  globby@11.1.0:
    globby: private
  glsl-noise@0.0.0:
    glsl-noise: private
  gopd@1.0.1:
    gopd: private
  graceful-fs@4.2.11:
    graceful-fs: private
  graphemer@1.4.0:
    graphemer: private
  has-bigints@1.0.2:
    has-bigints: private
  has-flag@4.0.0:
    has-flag: private
  has-property-descriptors@1.0.2:
    has-property-descriptors: private
  has-proto@1.0.3:
    has-proto: private
  has-symbols@1.0.3:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  hasown@2.0.2:
    hasown: private
  hls.js@1.5.20:
    hls.js: private
  ieee754@1.2.1:
    ieee754: private
  ignore@5.3.2:
    ignore: private
  immediate@3.0.6:
    immediate: private
  import-fresh@3.3.0:
    import-fresh: private
  imurmurhash@0.1.4:
    imurmurhash: private
  inflight@1.0.6:
    inflight: private
  inherits@2.0.4:
    inherits: private
  internal-slot@1.0.7:
    internal-slot: private
  invariant@2.2.4:
    invariant: private
  iota-array@1.0.0:
    iota-array: private
  is-arguments@1.1.1:
    is-arguments: private
  is-array-buffer@3.0.4:
    is-array-buffer: private
  is-arrayish@0.3.2:
    is-arrayish: private
  is-async-function@2.0.0:
    is-async-function: private
  is-bigint@1.0.4:
    is-bigint: private
  is-binary-path@2.1.0:
    is-binary-path: private
  is-boolean-object@1.1.2:
    is-boolean-object: private
  is-buffer@1.1.6:
    is-buffer: private
  is-callable@1.2.7:
    is-callable: private
  is-core-module@2.15.1:
    is-core-module: private
  is-data-view@1.0.1:
    is-data-view: private
  is-date-object@1.0.5:
    is-date-object: private
  is-extglob@2.1.1:
    is-extglob: private
  is-finalizationregistry@1.0.2:
    is-finalizationregistry: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-generator-function@1.0.10:
    is-generator-function: private
  is-glob@4.0.3:
    is-glob: private
  is-lite@1.2.1:
    is-lite: private
  is-map@2.0.3:
    is-map: private
  is-negative-zero@2.0.3:
    is-negative-zero: private
  is-number-object@1.0.7:
    is-number-object: private
  is-number@7.0.0:
    is-number: private
  is-path-inside@3.0.3:
    is-path-inside: private
  is-promise@2.2.2:
    is-promise: private
  is-regex@1.1.4:
    is-regex: private
  is-set@2.0.3:
    is-set: private
  is-shared-array-buffer@1.0.3:
    is-shared-array-buffer: private
  is-string@1.0.7:
    is-string: private
  is-symbol@1.0.4:
    is-symbol: private
  is-typed-array@1.1.13:
    is-typed-array: private
  is-weakmap@2.0.2:
    is-weakmap: private
  is-weakref@1.0.2:
    is-weakref: private
  is-weakset@2.0.3:
    is-weakset: private
  isarray@2.0.5:
    isarray: private
  isexe@2.0.0:
    isexe: private
  iterator.prototype@1.1.2:
    iterator.prototype: private
  its-fine@1.2.5(react@18.3.1):
    its-fine: private
  jackspeak@2.3.6:
    jackspeak: private
  jiti@1.21.6:
    jiti: private
  jose@4.15.9:
    jose: private
  js-tokens@4.0.0:
    js-tokens: private
  js-yaml@4.1.0:
    js-yaml: private
  json-buffer@3.0.1:
    json-buffer: private
  json-schema-traverse@0.4.1:
    json-schema-traverse: private
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: private
  json5@1.0.2:
    json5: private
  jsx-ast-utils@3.3.5:
    jsx-ast-utils: private
  kareem@2.6.3:
    kareem: private
  keyv@4.5.4:
    keyv: private
  ktx-parse@1.0.0:
    ktx-parse: private
  language-subtag-registry@0.3.23:
    language-subtag-registry: private
  language-tags@1.0.9:
    language-tags: private
  levn@0.4.1:
    levn: private
  lie@3.3.0:
    lie: private
  lilconfig@2.1.0:
    lilconfig: private
  lines-and-columns@1.2.4:
    lines-and-columns: private
  locate-path@6.0.0:
    locate-path: private
  lodash-es@4.17.21:
    lodash-es: private
  lodash.defaults@4.2.0:
    lodash.defaults: private
  lodash.isarguments@3.1.0:
    lodash.isarguments: private
  lodash.merge@4.6.2:
    lodash.merge: private
  loose-envify@1.4.0:
    loose-envify: private
  lru-cache@6.0.0:
    lru-cache: private
  luxon@3.5.0:
    luxon: private
  maath@0.10.8(@types/three@0.168.0)(three@0.167.1):
    maath: private
  material-colors@1.2.6:
    material-colors: private
  memory-pager@1.5.0:
    memory-pager: private
  merge2@1.4.1:
    merge2: private
  meshline@3.3.1(three@0.167.1):
    meshline: private
  meshoptimizer@0.18.1:
    meshoptimizer: private
  micromatch@4.0.7:
    micromatch: private
  mime-db@1.52.0:
    mime-db: private
  mime-types@2.1.35:
    mime-types: private
  mini-svg-data-uri@1.4.4:
    mini-svg-data-uri: private
  minimatch@3.1.2:
    minimatch: private
  minimist@1.2.8:
    minimist: private
  minipass@7.1.2:
    minipass: private
  mongodb-connection-string-url@3.0.1:
    mongodb-connection-string-url: private
  mpath@0.9.0:
    mpath: private
  mquery@5.0.0:
    mquery: private
  ms@2.1.3:
    ms: private
  msgpackr-extract@3.0.3:
    msgpackr-extract: private
  msgpackr@1.11.2:
    msgpackr: private
  mz@2.7.0:
    mz: private
  n8ao@1.9.4(postprocessing@6.37.0(three@0.167.1))(three@0.167.1):
    n8ao: private
  nanoid@3.3.7:
    nanoid: private
  natural-compare@1.4.0:
    natural-compare: private
  ndarray-lanczos@0.3.0:
    ndarray-lanczos: private
  ndarray-ops@1.2.2:
    ndarray-ops: private
  ndarray-pixels@4.1.0:
    ndarray-pixels: private
  ndarray@1.0.19:
    ndarray: private
  negotiator@0.6.3:
    negotiator: private
  node-abort-controller@3.1.1:
    node-abort-controller: private
  node-gyp-build-optional-packages@5.2.2:
    node-gyp-build-optional-packages: private
  normalize-path@3.0.0:
    normalize-path: private
  oauth4webapi@2.12.1:
    oauth4webapi: private
  oauth@0.9.15:
    oauth: private
  object-assign@4.1.1:
    object-assign: private
  object-hash@3.0.0:
    object-hash: private
  object-inspect@1.13.2:
    object-inspect: private
  object-is@1.1.6:
    object-is: private
  object-keys@1.1.1:
    object-keys: private
  object.assign@4.1.5:
    object.assign: private
  object.entries@1.1.8:
    object.entries: private
  object.fromentries@2.0.8:
    object.fromentries: private
  object.groupby@1.0.3:
    object.groupby: private
  object.values@1.2.0:
    object.values: private
  oidc-token-hash@5.0.3:
    oidc-token-hash: private
  once@1.4.0:
    once: private
  openid-client@5.6.5:
    openid-client: private
  optionator@0.9.4:
    optionator: private
  p-limit@3.1.0:
    p-limit: private
  p-locate@5.0.0:
    p-locate: private
  package-json-from-dist@1.0.0:
    package-json-from-dist: private
  parent-module@1.0.1:
    parent-module: private
  path-exists@4.0.0:
    path-exists: private
  path-is-absolute@1.0.1:
    path-is-absolute: private
  path-key@3.1.1:
    path-key: private
  path-parse@1.0.7:
    path-parse: private
  path-scurry@1.11.1:
    path-scurry: private
  path-type@4.0.0:
    path-type: private
  picocolors@1.0.1:
    picocolors: private
  picomatch@2.3.1:
    picomatch: private
  pify@2.3.0:
    pify: private
  pirates@4.0.6:
    pirates: private
  popper.js@1.16.1:
    popper.js: private
  possible-typed-array-names@1.0.0:
    possible-typed-array-names: private
  postcss-import@15.1.0(postcss@8.4.41):
    postcss-import: private
  postcss-js@4.0.1(postcss@8.4.41):
    postcss-js: private
  postcss-load-config@4.0.2(postcss@8.4.41):
    postcss-load-config: private
  postcss-nested@6.2.0(postcss@8.4.41):
    postcss-nested: private
  postcss-selector-parser@6.1.2:
    postcss-selector-parser: private
  postcss-value-parser@4.2.0:
    postcss-value-parser: private
  postprocessing@6.37.0(three@0.167.1):
    postprocessing: private
  potpack@1.0.2:
    potpack: private
  preact-render-to-string@5.2.3(preact@10.11.3):
    preact-render-to-string: private
  preact@10.11.3:
    preact: private
  prelude-ls@1.2.1:
    prelude-ls: private
  pretty-format@3.8.0:
    pretty-format: private
  promise-worker-transferable@1.0.4:
    promise-worker-transferable: private
  prop-types@15.8.1:
    prop-types: private
  property-graph@3.0.0:
    property-graph: private
  punycode@2.3.1:
    punycode: private
  queue-microtask@1.2.3:
    queue-microtask: private
  react-composer@5.0.3(react@18.3.1):
    react-composer: private
  react-floater@0.7.9(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    react-floater: private
  react-innertext@1.1.5(@types/react@18.3.4)(react@18.3.1):
    react-innertext: private
  react-is@16.13.1:
    react-is: private
  react-reconciler@0.27.0(react@18.3.1):
    react-reconciler: private
  react-remove-scroll-bar@2.3.6(@types/react@18.3.4)(react@18.3.1):
    react-remove-scroll-bar: private
  react-remove-scroll@2.5.7(@types/react@18.3.4)(react@18.3.1):
    react-remove-scroll: private
  react-style-singleton@2.2.1(@types/react@18.3.4)(react@18.3.1):
    react-style-singleton: private
  react-use-measure@2.1.7(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    react-use-measure: private
  reactcss@1.2.3(react@18.3.1):
    reactcss: private
  read-cache@1.0.0:
    read-cache: private
  readdirp@3.6.0:
    readdirp: private
  redis-errors@1.2.0:
    redis-errors: private
  redis-parser@3.0.0:
    redis-parser: private
  reflect.getprototypeof@1.0.6:
    reflect.getprototypeof: private
  regenerator-runtime@0.14.1:
    regenerator-runtime: private
  regexp.prototype.flags@1.5.2:
    regexp.prototype.flags: private
  require-from-string@2.0.2:
    require-from-string: private
  resolve-from@4.0.0:
    resolve-from: private
  resolve-pkg-maps@1.0.0:
    resolve-pkg-maps: private
  resolve@1.22.8:
    resolve: private
  reusify@1.0.4:
    reusify: private
  rimraf@3.0.2:
    rimraf: private
  run-parallel@1.2.0:
    run-parallel: private
  safe-array-concat@1.1.2:
    safe-array-concat: private
  safe-regex-test@1.0.3:
    safe-regex-test: private
  scheduler@0.21.0:
    scheduler: private
  scroll@3.0.1:
    scroll: private
  scrollparent@2.1.0:
    scrollparent: private
  semver@7.6.3:
    semver: private
  set-function-length@1.2.2:
    set-function-length: private
  set-function-name@2.0.2:
    set-function-name: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  side-channel@1.0.6:
    side-channel: private
  sift@17.1.3:
    sift: private
  signal-exit@4.1.0:
    signal-exit: private
  simple-swizzle@0.2.2:
    simple-swizzle: private
  slash@3.0.0:
    slash: private
  socket.io-adapter@2.5.5:
    socket.io-adapter: private
  socket.io-parser@4.2.4:
    socket.io-parser: private
  source-map-js@1.2.0:
    source-map-js: private
  sparse-bitfield@3.0.3:
    sparse-bitfield: private
  standard-as-callback@2.1.0:
    standard-as-callback: private
  stats-gl@2.2.8:
    stats-gl: private
  stats.js@0.17.0:
    stats.js: private
  stop-iteration-iterator@1.0.0:
    stop-iteration-iterator: private
  streamsearch@1.1.0:
    streamsearch: private
  string-width@4.2.3:
    string-width-cjs: private
  string-width@5.1.2:
    string-width: private
  string.prototype.includes@2.0.0:
    string.prototype.includes: private
  string.prototype.matchall@4.0.11:
    string.prototype.matchall: private
  string.prototype.repeat@1.0.0:
    string.prototype.repeat: private
  string.prototype.trim@1.2.9:
    string.prototype.trim: private
  string.prototype.trimend@1.0.8:
    string.prototype.trimend: private
  string.prototype.trimstart@1.0.8:
    string.prototype.trimstart: private
  strip-ansi@6.0.1:
    strip-ansi: private
    strip-ansi-cjs: private
  strip-bom@3.0.0:
    strip-bom: private
  strip-json-comments@3.1.1:
    strip-json-comments: private
  strnum@1.0.5:
    strnum: private
  styled-jsx@5.1.1(react@18.3.1):
    styled-jsx: private
  sucrase@3.35.0:
    sucrase: private
  supports-color@7.2.0:
    supports-color: private
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: private
  suspend-react@0.1.3(react@18.3.1):
    suspend-react: private
  tapable@2.2.1:
    tapable: private
  text-table@0.2.0:
    text-table: private
  thenify-all@1.6.0:
    thenify-all: private
  thenify@3.3.1:
    thenify: private
  three-mesh-bvh@0.7.8(three@0.167.1):
    three-mesh-bvh: private
  tinycolor2@1.6.0:
    tinycolor2: private
  to-regex-range@5.0.1:
    to-regex-range: private
  tr46@4.1.1:
    tr46: private
  tree-changes@0.11.3:
    tree-changes: private
  troika-three-text@0.52.3(three@0.167.1):
    troika-three-text: private
  troika-three-utils@0.52.0(three@0.167.1):
    troika-three-utils: private
  troika-worker-utils@0.52.0:
    troika-worker-utils: private
  ts-api-utils@1.3.0(typescript@5.5.4):
    ts-api-utils: private
  ts-interface-checker@0.1.13:
    ts-interface-checker: private
  tsconfig-paths@3.15.0:
    tsconfig-paths: private
  tslib@2.6.3:
    tslib: private
  tunnel-rat@0.1.2(@types/react@18.3.4)(react@18.3.1):
    tunnel-rat: private
  type-check@0.4.0:
    type-check: private
  type-fest@4.41.0:
    type-fest: private
  typed-array-buffer@1.0.2:
    typed-array-buffer: private
  typed-array-byte-length@1.0.1:
    typed-array-byte-length: private
  typed-array-byte-offset@1.0.2:
    typed-array-byte-offset: private
  typed-array-length@1.0.6:
    typed-array-length: private
  typed-query-selector@2.12.0:
    typed-query-selector: private
  unbox-primitive@1.0.2:
    unbox-primitive: private
  undici-types@6.19.8:
    undici-types: private
  uniq@1.0.1:
    uniq: private
  uri-js@4.4.1:
    uri-js: private
  use-callback-ref@1.3.2(@types/react@18.3.4)(react@18.3.1):
    use-callback-ref: private
  use-sidecar@1.1.2(@types/react@18.3.4)(react@18.3.1):
    use-sidecar: private
  use-sync-external-store@1.2.2(react@18.3.1):
    use-sync-external-store: private
  util-deprecate@1.0.2:
    util-deprecate: private
  utility-types@3.11.0:
    utility-types: private
  vary@1.1.2:
    vary: private
  webgl-constants@1.1.1:
    webgl-constants: private
  webgl-sdf-generator@1.1.1:
    webgl-sdf-generator: private
  webidl-conversions@7.0.0:
    webidl-conversions: private
  whatwg-url@13.0.0:
    whatwg-url: private
  which-boxed-primitive@1.0.2:
    which-boxed-primitive: private
  which-builtin-type@1.1.4:
    which-builtin-type: private
  which-collection@1.0.2:
    which-collection: private
  which-typed-array@1.1.15:
    which-typed-array: private
  which@2.0.2:
    which: private
  word-wrap@1.2.5:
    word-wrap: private
  wrap-ansi@7.0.0:
    wrap-ansi-cjs: private
  wrap-ansi@8.1.0:
    wrap-ansi: private
  wrappy@1.0.2:
    wrappy: private
  ws@8.17.1:
    ws: private
  xmlhttprequest-ssl@2.1.1:
    xmlhttprequest-ssl: private
  yallist@4.0.0:
    yallist: private
  yaml@2.5.0:
    yaml: private
  yocto-queue@0.1.0:
    yocto-queue: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@9.5.0
pendingBuilds: []
prunedAt: Thu, 17 Jul 2025 04:47:52 GMT
publicHoistPattern:
  - '*eslint*'
  - '*prettier*'
registries:
  default: https://registry.npmjs.org/
skipped: []
storeDir: G:\.pnpm-store\v3
virtualStoreDir: G:\agape\agape-newFrontend\web\node_modules\.pnpm
virtualStoreDirMaxLength: 120
