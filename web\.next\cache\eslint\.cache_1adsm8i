[{"G:\\agape\\agape-newFrontend\\web\\src\\app\\(auth)\\login\\layout.tsx": "1", "G:\\agape\\agape-newFrontend\\web\\src\\app\\(auth)\\login\\page.tsx": "2", "G:\\agape\\agape-newFrontend\\web\\src\\app\\(auth)\\signup\\layout.tsx": "3", "G:\\agape\\agape-newFrontend\\web\\src\\app\\(auth)\\signup\\page.tsx": "4", "G:\\agape\\agape-newFrontend\\web\\src\\app\\animProviders.tsx": "5", "G:\\agape\\agape-newFrontend\\web\\src\\app\\api\\auth\\[...nextauth]\\route.ts": "6", "G:\\agape\\agape-newFrontend\\web\\src\\app\\api\\hello.ts": "7", "G:\\agape\\agape-newFrontend\\web\\src\\app\\layout.tsx": "8", "G:\\agape\\agape-newFrontend\\web\\src\\app\\library\\layout.tsx": "9", "G:\\agape\\agape-newFrontend\\web\\src\\app\\library\\page.tsx": "10", "G:\\agape\\agape-newFrontend\\web\\src\\app\\not-found.tsx": "11", "G:\\agape\\agape-newFrontend\\web\\src\\app\\page.tsx": "12", "G:\\agape\\agape-newFrontend\\web\\src\\app\\projects\\[id]\\page.tsx": "13", "G:\\agape\\agape-newFrontend\\web\\src\\app\\settings\\account\\layout.tsx": "14", "G:\\agape\\agape-newFrontend\\web\\src\\app\\settings\\account\\page.tsx": "15", "G:\\agape\\agape-newFrontend\\web\\src\\app\\settings\\profile\\layout.tsx": "16", "G:\\agape\\agape-newFrontend\\web\\src\\app\\settings\\profile\\page.tsx": "17", "G:\\agape\\agape-newFrontend\\web\\src\\app\\upload\\layout.tsx": "18", "G:\\agape\\agape-newFrontend\\web\\src\\app\\upload\\page.tsx": "19", "G:\\agape\\agape-newFrontend\\web\\src\\app\\workspace\\[workspaceId]\\comments\\layout.tsx": "20", "G:\\agape\\agape-newFrontend\\web\\src\\app\\workspace\\[workspaceId]\\comments\\page.tsx": "21", "G:\\agape\\agape-newFrontend\\web\\src\\app\\workspace\\[workspaceId]\\page.tsx": "22", "G:\\agape\\agape-newFrontend\\web\\src\\components\\AdvanceRendering.tsx": "23", "G:\\agape\\agape-newFrontend\\web\\src\\components\\AuthPages\\LoginPageContent.tsx": "24", "G:\\agape\\agape-newFrontend\\web\\src\\components\\AuthPages\\MagicLinkForm.tsx": "25", "G:\\agape\\agape-newFrontend\\web\\src\\components\\AuthPages\\SignupPageContent.tsx": "26", "G:\\agape\\agape-newFrontend\\web\\src\\components\\CameraSider\\CameraAdjustments.tsx": "27", "G:\\agape\\agape-newFrontend\\web\\src\\components\\CameraSider\\CameraSider.tsx": "28", "G:\\agape\\agape-newFrontend\\web\\src\\components\\ColorsWindow.tsx": "29", "G:\\agape\\agape-newFrontend\\web\\src\\components\\Combobox.tsx": "30", "G:\\agape\\agape-newFrontend\\web\\src\\components\\Comments\\CommentCard.tsx": "31", "G:\\agape\\agape-newFrontend\\web\\src\\components\\Comments\\CommentInput.tsx": "32", "G:\\agape\\agape-newFrontend\\web\\src\\components\\Comments\\CommentReplyInput.tsx": "33", "G:\\agape\\agape-newFrontend\\web\\src\\components\\Comments\\CommentsPageContent.tsx": "34", "G:\\agape\\agape-newFrontend\\web\\src\\components\\ComponentsSider.tsx": "35", "G:\\agape\\agape-newFrontend\\web\\src\\components\\CubeMenu.tsx": "36", "G:\\agape\\agape-newFrontend\\web\\src\\components\\FeaturesSider.tsx": "37", "G:\\agape\\agape-newFrontend\\web\\src\\components\\GemStone.tsx": "38", "G:\\agape\\agape-newFrontend\\web\\src\\components\\Gizmo\\GizmoMenu.tsx": "39", "G:\\agape\\agape-newFrontend\\web\\src\\components\\Gizmo.tsx": "40", "G:\\agape\\agape-newFrontend\\web\\src\\components\\Home\\ApprovalConfirmationModal.tsx": "41", "G:\\agape\\agape-newFrontend\\web\\src\\components\\Home\\ApprovalDialog.tsx": "42", "G:\\agape\\agape-newFrontend\\web\\src\\components\\Home\\BackgroundImages.tsx": "43", "G:\\agape\\agape-newFrontend\\web\\src\\components\\Home\\CommentNotification.tsx": "44", "G:\\agape\\agape-newFrontend\\web\\src\\components\\Home\\FilterOverlay.tsx": "45", "G:\\agape\\agape-newFrontend\\web\\src\\components\\Home\\HomePage.tsx": "46", "G:\\agape\\agape-newFrontend\\web\\src\\components\\Home\\HomePageContent.tsx": "47", "G:\\agape\\agape-newFrontend\\web\\src\\components\\Home\\LibraryContent.tsx": "48", "G:\\agape\\agape-newFrontend\\web\\src\\components\\Home\\NewProjectDialog.tsx": "49", "G:\\agape\\agape-newFrontend\\web\\src\\components\\Home\\NotificationsPopup.tsx": "50", "G:\\agape\\agape-newFrontend\\web\\src\\components\\Home\\ProjectCard.tsx": "51", "G:\\agape\\agape-newFrontend\\web\\src\\components\\Home\\RightSidebar.tsx": "52", "G:\\agape\\agape-newFrontend\\web\\src\\components\\Home\\SearchOverlay.tsx": "53", "G:\\agape\\agape-newFrontend\\web\\src\\components\\Home\\Sidebar.tsx": "54", "G:\\agape\\agape-newFrontend\\web\\src\\components\\Home\\TopNavigation.tsx": "55", "G:\\agape\\agape-newFrontend\\web\\src\\components\\Home\\UploadDialog.tsx": "56", "G:\\agape\\agape-newFrontend\\web\\src\\components\\InvitationsModal.tsx": "57", "G:\\agape\\agape-newFrontend\\web\\src\\components\\LeftSidebar.tsx": "58", "G:\\agape\\agape-newFrontend\\web\\src\\components\\Library\\ActionDropdown.tsx": "59", "G:\\agape\\agape-newFrontend\\web\\src\\components\\Library\\DeleteConfirmationDialog.tsx": "60", "G:\\agape\\agape-newFrontend\\web\\src\\components\\Library\\DynamicModel.tsx": "61", "G:\\agape\\agape-newFrontend\\web\\src\\components\\Library\\EditMaterial.tsx": "62", "G:\\agape\\agape-newFrontend\\web\\src\\components\\Library\\HDRIBall.tsx": "63", "G:\\agape\\agape-newFrontend\\web\\src\\components\\Library\\HDRIsCard.tsx": "64", "G:\\agape\\agape-newFrontend\\web\\src\\components\\Library\\ImageModal.tsx": "65", "G:\\agape\\agape-newFrontend\\web\\src\\components\\Library\\ImagesCard.tsx": "66", "G:\\agape\\agape-newFrontend\\web\\src\\components\\Library\\LibraryPageContent.tsx": "67", "G:\\agape\\agape-newFrontend\\web\\src\\components\\Library\\MaterialBall.tsx": "68", "G:\\agape\\agape-newFrontend\\web\\src\\components\\Library\\MaterialPBR.tsx": "69", "G:\\agape\\agape-newFrontend\\web\\src\\components\\Library\\MaterialsCard.tsx": "70", "G:\\agape\\agape-newFrontend\\web\\src\\components\\Library\\ModelsCard.tsx": "71", "G:\\agape\\agape-newFrontend\\web\\src\\components\\Library\\PreviewModal.tsx": "72", "G:\\agape\\agape-newFrontend\\web\\src\\components\\Library\\ProjectsCard.tsx": "73", "G:\\agape\\agape-newFrontend\\web\\src\\components\\Library\\ProjectSnapshotsModal.tsx": "74", "G:\\agape\\agape-newFrontend\\web\\src\\components\\Lighting.tsx": "75", "G:\\agape\\agape-newFrontend\\web\\src\\components\\LightLinkingSider.tsx": "76", "G:\\agape\\agape-newFrontend\\web\\src\\components\\Loader\\Loader.tsx": "77", "G:\\agape\\agape-newFrontend\\web\\src\\components\\MaterialSider\\MaterialAccordionItem.tsx": "78", "G:\\agape\\agape-newFrontend\\web\\src\\components\\MaterialSider\\MaterialSelector.tsx": "79", "G:\\agape\\agape-newFrontend\\web\\src\\components\\MaterialSider.tsx": "80", "G:\\agape\\agape-newFrontend\\web\\src\\components\\models\\Ball.js": "81", "G:\\agape\\agape-newFrontend\\web\\src\\components\\models\\CSSRing.jsx": "82", "G:\\agape\\agape-newFrontend\\web\\src\\components\\models\\Ring.js": "83", "G:\\agape\\agape-newFrontend\\web\\src\\components\\models\\RingSingle.js": "84", "G:\\agape\\agape-newFrontend\\web\\src\\components\\models\\RingSingle_comp.jsx": "85", "G:\\agape\\agape-newFrontend\\web\\src\\components\\models\\Spider.js": "86", "G:\\agape\\agape-newFrontend\\web\\src\\components\\ModelViewer.tsx": "87", "G:\\agape\\agape-newFrontend\\web\\src\\components\\Navbar.tsx": "88", "G:\\agape\\agape-newFrontend\\web\\src\\components\\Outliner.tsx": "89", "G:\\agape\\agape-newFrontend\\web\\src\\components\\PieMenu.tsx": "90", "G:\\agape\\agape-newFrontend\\web\\src\\components\\ProjectSettings.tsx": "91", "G:\\agape\\agape-newFrontend\\web\\src\\components\\Proportions.tsx": "92", "G:\\agape\\agape-newFrontend\\web\\src\\components\\QuickActions.tsx": "93", "G:\\agape\\agape-newFrontend\\web\\src\\components\\Rendering.tsx": "94", "G:\\agape\\agape-newFrontend\\web\\src\\components\\Settings\\AccountPageContent.tsx": "95", "G:\\agape\\agape-newFrontend\\web\\src\\components\\Settings\\Layout.tsx": "96", "G:\\agape\\agape-newFrontend\\web\\src\\components\\Settings\\ProfilePageContent.tsx": "97", "G:\\agape\\agape-newFrontend\\web\\src\\components\\Settings\\TeamMember.tsx": "98", "G:\\agape\\agape-newFrontend\\web\\src\\components\\ShareModal.tsx": "99", "G:\\agape\\agape-newFrontend\\web\\src\\components\\SideSheet.tsx": "100", "G:\\agape\\agape-newFrontend\\web\\src\\components\\SideSheetContent.tsx": "101", "G:\\agape\\agape-newFrontend\\web\\src\\components\\Theme\\theme-provider.tsx": "102", "G:\\agape\\agape-newFrontend\\web\\src\\components\\Toolbar\\CameraContent.tsx": "103", "G:\\agape\\agape-newFrontend\\web\\src\\components\\Toolbar\\HDRContent.tsx": "104", "G:\\agape\\agape-newFrontend\\web\\src\\components\\Toolbar\\HDRPreviewMenu.tsx": "105", "G:\\agape\\agape-newFrontend\\web\\src\\components\\Toolbar\\LightingContent.tsx": "106", "G:\\agape\\agape-newFrontend\\web\\src\\components\\Toolbar\\PostProcessing.tsx": "107", "G:\\agape\\agape-newFrontend\\web\\src\\components\\Toolbar\\ViewportContent.tsx": "108", "G:\\agape\\agape-newFrontend\\web\\src\\components\\Toolbar.tsx": "109", "G:\\agape\\agape-newFrontend\\web\\src\\components\\ui\\accordion.tsx": "110", "G:\\agape\\agape-newFrontend\\web\\src\\components\\ui\\alert-dialog.tsx": "111", "G:\\agape\\agape-newFrontend\\web\\src\\components\\ui\\avatar.tsx": "112", "G:\\agape\\agape-newFrontend\\web\\src\\components\\ui\\badge.tsx": "113", "G:\\agape\\agape-newFrontend\\web\\src\\components\\ui\\button-shimmer.tsx": "114", "G:\\agape\\agape-newFrontend\\web\\src\\components\\ui\\button.tsx": "115", "G:\\agape\\agape-newFrontend\\web\\src\\components\\ui\\card.tsx": "116", "G:\\agape\\agape-newFrontend\\web\\src\\components\\ui\\checkbox.tsx": "117", "G:\\agape\\agape-newFrontend\\web\\src\\components\\ui\\command.tsx": "118", "G:\\agape\\agape-newFrontend\\web\\src\\components\\ui\\dialog.tsx": "119", "G:\\agape\\agape-newFrontend\\web\\src\\components\\ui\\drawer.tsx": "120", "G:\\agape\\agape-newFrontend\\web\\src\\components\\ui\\dropdown-menu.tsx": "121", "G:\\agape\\agape-newFrontend\\web\\src\\components\\ui\\form.tsx": "122", "G:\\agape\\agape-newFrontend\\web\\src\\components\\ui\\input.tsx": "123", "G:\\agape\\agape-newFrontend\\web\\src\\components\\ui\\label.tsx": "124", "G:\\agape\\agape-newFrontend\\web\\src\\components\\ui\\popover.tsx": "125", "G:\\agape\\agape-newFrontend\\web\\src\\components\\ui\\scroll-area.tsx": "126", "G:\\agape\\agape-newFrontend\\web\\src\\components\\ui\\select.tsx": "127", "G:\\agape\\agape-newFrontend\\web\\src\\components\\ui\\sheet.tsx": "128", "G:\\agape\\agape-newFrontend\\web\\src\\components\\ui\\slider.tsx": "129", "G:\\agape\\agape-newFrontend\\web\\src\\components\\ui\\sonner.tsx": "130", "G:\\agape\\agape-newFrontend\\web\\src\\components\\ui\\switch.tsx": "131", "G:\\agape\\agape-newFrontend\\web\\src\\components\\ui\\tabs.tsx": "132", "G:\\agape\\agape-newFrontend\\web\\src\\components\\ui\\textarea.tsx": "133", "G:\\agape\\agape-newFrontend\\web\\src\\components\\ui\\tooltip.tsx": "134", "G:\\agape\\agape-newFrontend\\web\\src\\components\\Upload\\CategoryItemViewer.tsx": "135", "G:\\agape\\agape-newFrontend\\web\\src\\components\\Upload\\GroupingScreen.tsx": "136", "G:\\agape\\agape-newFrontend\\web\\src\\components\\Upload\\Model.tsx": "137", "G:\\agape\\agape-newFrontend\\web\\src\\components\\Upload\\ModelViewer.tsx": "138", "G:\\agape\\agape-newFrontend\\web\\src\\components\\Upload\\Sidebar.tsx": "139", "G:\\agape\\agape-newFrontend\\web\\src\\components\\Upload\\SingleGroup.tsx": "140", "G:\\agape\\agape-newFrontend\\web\\src\\components\\Upload\\UploadPageContent.tsx": "141", "G:\\agape\\agape-newFrontend\\web\\src\\components\\Upload\\UploadScreen.tsx": "142", "G:\\agape\\agape-newFrontend\\web\\src\\components\\VersionHistory.tsx": "143", "G:\\agape\\agape-newFrontend\\web\\src\\components\\Workspace\\DynamicModel.tsx": "144", "G:\\agape\\agape-newFrontend\\web\\src\\config\\seo-meta-data.ts": "145", "G:\\agape\\agape-newFrontend\\web\\src\\config\\site.ts": "146", "G:\\agape\\agape-newFrontend\\web\\src\\lib\\actions\\comment.actions.ts": "147", "G:\\agape\\agape-newFrontend\\web\\src\\lib\\actions\\invite.actions.ts": "148", "G:\\agape\\agape-newFrontend\\web\\src\\lib\\actions\\material.actions.ts": "149", "G:\\agape\\agape-newFrontend\\web\\src\\lib\\actions\\mesh.actions.ts": "150", "G:\\agape\\agape-newFrontend\\web\\src\\lib\\actions\\meshCategory.actions.ts": "151", "G:\\agape\\agape-newFrontend\\web\\src\\lib\\actions\\user.actions.ts": "152", "G:\\agape\\agape-newFrontend\\web\\src\\lib\\actions\\workspace.actions.ts": "153", "G:\\agape\\agape-newFrontend\\web\\src\\lib\\auth.d.ts": "154", "G:\\agape\\agape-newFrontend\\web\\src\\lib\\auth.ts": "155", "G:\\agape\\agape-newFrontend\\web\\src\\lib\\configurations.ts": "156", "G:\\agape\\agape-newFrontend\\web\\src\\lib\\db\\db.ts": "157", "G:\\agape\\agape-newFrontend\\web\\src\\lib\\db\\models\\comment.models.ts": "158", "G:\\agape\\agape-newFrontend\\web\\src\\lib\\db\\models\\invite.models.ts": "159", "G:\\agape\\agape-newFrontend\\web\\src\\lib\\db\\models\\material.models.ts": "160", "G:\\agape\\agape-newFrontend\\web\\src\\lib\\db\\models\\membership.models.ts": "161", "G:\\agape\\agape-newFrontend\\web\\src\\lib\\db\\models\\mesh.models.ts": "162", "G:\\agape\\agape-newFrontend\\web\\src\\lib\\db\\models\\meshCategory.models.ts": "163", "G:\\agape\\agape-newFrontend\\web\\src\\lib\\db\\models\\user.models.ts": "164", "G:\\agape\\agape-newFrontend\\web\\src\\lib\\db\\models\\workspace.models.ts": "165", "G:\\agape\\agape-newFrontend\\web\\src\\lib\\email\\sendEmailNotification.tsx": "166", "G:\\agape\\agape-newFrontend\\web\\src\\lib\\email\\sendLoginMail.tsx": "167", "G:\\agape\\agape-newFrontend\\web\\src\\lib\\mongoose.ts": "168", "G:\\agape\\agape-newFrontend\\web\\src\\lib\\queues\\emailQueue.ts": "169", "G:\\agape\\agape-newFrontend\\web\\src\\lib\\queues\\emailWorker.ts": "170", "G:\\agape\\agape-newFrontend\\web\\src\\lib\\s3\\s3.ts": "171", "G:\\agape\\agape-newFrontend\\web\\src\\lib\\session.ts": "172", "G:\\agape\\agape-newFrontend\\web\\src\\lib\\states.ts": "173", "G:\\agape\\agape-newFrontend\\web\\src\\lib\\utils.ts": "174", "G:\\agape\\agape-newFrontend\\web\\src\\types\\index.ts": "175", "G:\\agape\\agape-newFrontend\\web\\src\\utils\\buffers.ts": "176", "G:\\agape\\agape-newFrontend\\web\\src\\utils\\getPageData.ts": "177", "G:\\agape\\agape-newFrontend\\web\\src\\utils\\isExtension.ts": "178", "G:\\agape\\agape-newFrontend\\web\\src\\app\\projects\\[id]\\edit\\layout.tsx": "179", "G:\\agape\\agape-newFrontend\\web\\src\\app\\projects\\[id]\\edit\\page.tsx": "180", "G:\\agape\\agape-newFrontend\\web\\src\\app\\projects\\[id]\\layout.tsx": "181", "G:\\agape\\agape-newFrontend\\web\\src\\components\\ArtistTool\\animations\\CameraAnimations.js": "182", "G:\\agape\\agape-newFrontend\\web\\src\\components\\ArtistTool\\BackPlate.jsx": "183", "G:\\agape\\agape-newFrontend\\web\\src\\components\\ArtistTool\\EditorExperience.jsx": "184", "G:\\agape\\agape-newFrontend\\web\\src\\components\\ArtistTool\\EngravingPanel.jsx": "185", "G:\\agape\\agape-newFrontend\\web\\src\\components\\ArtistTool\\Environment.tsx": "186", "G:\\agape\\agape-newFrontend\\web\\src\\components\\ArtistTool\\EnvironmentPanel.jsx": "187", "G:\\agape\\agape-newFrontend\\web\\src\\components\\ArtistTool\\EnvironmentPreviewBall.jsx": "188", "G:\\agape\\agape-newFrontend\\web\\src\\components\\ArtistTool\\Ground.jsx": "189", "G:\\agape\\agape-newFrontend\\web\\src\\components\\ArtistTool\\Header.jsx": "190", "G:\\agape\\agape-newFrontend\\web\\src\\components\\ArtistTool\\InitialOverlayPanel.jsx": "191", "G:\\agape\\agape-newFrontend\\web\\src\\components\\ArtistTool\\LightPanel.jsx": "192", "G:\\agape\\agape-newFrontend\\web\\src\\components\\ArtistTool\\LoadSceneModal.jsx": "193", "G:\\agape\\agape-newFrontend\\web\\src\\components\\ArtistTool\\MaterialPanel.jsx": "194", "G:\\agape\\agape-newFrontend\\web\\src\\components\\ArtistTool\\ModelStage.jsx": "195", "G:\\agape\\agape-newFrontend\\web\\src\\components\\ArtistTool\\ModelStats.jsx": "196", "G:\\agape\\agape-newFrontend\\web\\src\\components\\ArtistTool\\Outliner.jsx": "197", "G:\\agape\\agape-newFrontend\\web\\src\\components\\ArtistTool\\PostProcessingEffects.jsx": "198", "G:\\agape\\agape-newFrontend\\web\\src\\components\\ArtistTool\\PostProcessingPanel.jsx": "199", "G:\\agape\\agape-newFrontend\\web\\src\\components\\ArtistTool\\SaveSceneModal.jsx": "200", "G:\\agape\\agape-newFrontend\\web\\src\\components\\ArtistTool\\SceneLights.jsx": "201", "G:\\agape\\agape-newFrontend\\web\\src\\components\\ArtistTool\\SelectionOutline.jsx": "202", "G:\\agape\\agape-newFrontend\\web\\src\\components\\ArtistTool\\Toolbar.jsx": "203", "G:\\agape\\agape-newFrontend\\web\\src\\components\\ArtistTool\\TransformPanel.jsx": "204", "G:\\agape\\agape-newFrontend\\web\\src\\components\\ArtistTool\\UploadedModel.jsx": "205", "G:\\agape\\agape-newFrontend\\web\\src\\components\\EditProjectPage.tsx": "206", "G:\\agape\\agape-newFrontend\\web\\src\\components\\Home\\InvitationsDropdown.tsx": "207", "G:\\agape\\agape-newFrontend\\web\\src\\components\\Home\\ModelPreview.tsx": "208", "G:\\agape\\agape-newFrontend\\web\\src\\components\\Home\\search-dropdown.tsx": "209", "G:\\agape\\agape-newFrontend\\web\\src\\components\\Home\\searchbar.tsx": "210", "G:\\agape\\agape-newFrontend\\web\\src\\components\\InviteModal.tsx": "211", "G:\\agape\\agape-newFrontend\\web\\src\\components\\models\\ChosenRing.jsx": "212", "G:\\agape\\agape-newFrontend\\web\\src\\components\\Onboarding\\index.ts": "213", "G:\\agape\\agape-newFrontend\\web\\src\\components\\Onboarding\\OnboardingProvider.tsx": "214", "G:\\agape\\agape-newFrontend\\web\\src\\components\\Onboarding\\OnboardingTrigger.tsx": "215", "G:\\agape\\agape-newFrontend\\web\\src\\components\\ProjectPage.tsx": "216", "G:\\agape\\agape-newFrontend\\web\\src\\components\\providers\\UserProvider.tsx": "217", "G:\\agape\\agape-newFrontend\\web\\src\\components\\ui\\alert.tsx": "218", "G:\\agape\\agape-newFrontend\\web\\src\\hooks\\use-click-outside.tsx": "219", "G:\\agape\\agape-newFrontend\\web\\src\\hooks\\useDebounce.ts": "220", "G:\\agape\\agape-newFrontend\\web\\src\\lib\\actions\\version.actions.ts": "221", "G:\\agape\\agape-newFrontend\\web\\src\\lib\\db\\models\\tag.models.ts": "222", "G:\\agape\\agape-newFrontend\\web\\src\\lib\\db\\models\\version.models.ts": "223", "G:\\agape\\agape-newFrontend\\web\\src\\utils\\ArtistTool\\cameraUtils.js": "224", "G:\\agape\\agape-newFrontend\\web\\src\\utils\\ArtistTool\\historyUtils.js": "225", "G:\\agape\\agape-newFrontend\\web\\src\\utils\\ArtistTool\\materialUtils.js": "226", "G:\\agape\\agape-newFrontend\\web\\src\\utils\\ArtistTool\\refraction_materials\\Diamond_Fragment.js": "227", "G:\\agape\\agape-newFrontend\\web\\src\\utils\\ArtistTool\\refraction_materials\\Diamond_Shader.js": "228", "G:\\agape\\agape-newFrontend\\web\\src\\utils\\ArtistTool\\refraction_materials\\MeshRefractionMaterial.js": "229", "G:\\agape\\agape-newFrontend\\web\\src\\utils\\ArtistTool\\refraction_materials\\NormalCube.js": "230", "G:\\agape\\agape-newFrontend\\web\\src\\utils\\ArtistTool\\sceneUtils.js": "231", "G:\\agape\\agape-newFrontend\\web\\src\\utils\\ArtistTool\\TextBumpMapGenerator.jsx": "232"}, {"size": 286, "mtime": 1751961237468, "results": "233", "hashOfConfig": "234"}, {"size": 1167, "mtime": 1749468331931, "results": "235", "hashOfConfig": "234"}, {"size": 276, "mtime": 1751961237468, "results": "236", "hashOfConfig": "234"}, {"size": 1143, "mtime": 1749468331939, "results": "237", "hashOfConfig": "234"}, {"size": 4168, "mtime": 1749468331941, "results": "238", "hashOfConfig": "234"}, {"size": 167, "mtime": 1749468331944, "results": "239", "hashOfConfig": "234"}, {"size": 325, "mtime": 1749468331946, "results": "240", "hashOfConfig": "234"}, {"size": 1515, "mtime": 1751961237470, "results": "241", "hashOfConfig": "234"}, {"size": 283, "mtime": 1751961237471, "results": "242", "hashOfConfig": "234"}, {"size": 753, "mtime": 1749468331966, "results": "243", "hashOfConfig": "234"}, {"size": 1084, "mtime": 1749468331969, "results": "244", "hashOfConfig": "234"}, {"size": 1246, "mtime": 1749468332048, "results": "245", "hashOfConfig": "234"}, {"size": 607, "mtime": 1749468332066, "results": "246", "hashOfConfig": "234"}, {"size": 320, "mtime": 1749468332070, "results": "247", "hashOfConfig": "234"}, {"size": 471, "mtime": 1749468332072, "results": "248", "hashOfConfig": "234"}, {"size": 297, "mtime": 1749468332078, "results": "249", "hashOfConfig": "234"}, {"size": 655, "mtime": 1749468332080, "results": "250", "hashOfConfig": "234"}, {"size": 279, "mtime": 1749468332086, "results": "251", "hashOfConfig": "234"}, {"size": 720, "mtime": 1749468332086, "results": "252", "hashOfConfig": "234"}, {"size": 163, "mtime": 1749468332112, "results": "253", "hashOfConfig": "234"}, {"size": 1587, "mtime": 1749468332113, "results": "254", "hashOfConfig": "234"}, {"size": 2142, "mtime": 1749468332116, "results": "255", "hashOfConfig": "234"}, {"size": 2401, "mtime": 1749468332119, "results": "256", "hashOfConfig": "234"}, {"size": 2610, "mtime": 1749468332120, "results": "257", "hashOfConfig": "234"}, {"size": 6254, "mtime": 1749468332121, "results": "258", "hashOfConfig": "234"}, {"size": 2683, "mtime": 1749468332122, "results": "259", "hashOfConfig": "234"}, {"size": 1789, "mtime": 1749468332125, "results": "260", "hashOfConfig": "234"}, {"size": 5729, "mtime": 1749468332126, "results": "261", "hashOfConfig": "234"}, {"size": 1694, "mtime": 1749468332127, "results": "262", "hashOfConfig": "234"}, {"size": 2717, "mtime": 1749468332128, "results": "263", "hashOfConfig": "234"}, {"size": 3329, "mtime": 1749468332134, "results": "264", "hashOfConfig": "234"}, {"size": 2514, "mtime": 1749468332135, "results": "265", "hashOfConfig": "234"}, {"size": 8367, "mtime": 1749468332137, "results": "266", "hashOfConfig": "234"}, {"size": 20006, "mtime": 1749468332139, "results": "267", "hashOfConfig": "234"}, {"size": 4330, "mtime": 1749468332141, "results": "268", "hashOfConfig": "234"}, {"size": 1922, "mtime": 1749468332142, "results": "269", "hashOfConfig": "234"}, {"size": 5235, "mtime": 1749468332145, "results": "270", "hashOfConfig": "234"}, {"size": 1472, "mtime": 1749468332146, "results": "271", "hashOfConfig": "234"}, {"size": 2492, "mtime": 1749468332150, "results": "272", "hashOfConfig": "234"}, {"size": 1317, "mtime": 1749468332148, "results": "273", "hashOfConfig": "234"}, {"size": 2076, "mtime": 1749468332151, "results": "274", "hashOfConfig": "234"}, {"size": 2475, "mtime": 1749468332152, "results": "275", "hashOfConfig": "234"}, {"size": 729, "mtime": 1749468332154, "results": "276", "hashOfConfig": "234"}, {"size": 1791, "mtime": 1749468332156, "results": "277", "hashOfConfig": "234"}, {"size": 27446, "mtime": 1751961237486, "results": "278", "hashOfConfig": "234"}, {"size": 10400, "mtime": 1751966341300, "results": "279", "hashOfConfig": "234"}, {"size": 19941, "mtime": 1751961237487, "results": "280", "hashOfConfig": "234"}, {"size": 828, "mtime": 1749468332167, "results": "281", "hashOfConfig": "234"}, {"size": 18449, "mtime": 1749468332169, "results": "282", "hashOfConfig": "234"}, {"size": 2120, "mtime": 1749468332171, "results": "283", "hashOfConfig": "234"}, {"size": 9904, "mtime": 1751967624528, "results": "284", "hashOfConfig": "234"}, {"size": 9836, "mtime": 1751961237489, "results": "285", "hashOfConfig": "234"}, {"size": 8317, "mtime": 1749468332176, "results": "286", "hashOfConfig": "234"}, {"size": 8210, "mtime": 1749468332179, "results": "287", "hashOfConfig": "234"}, {"size": 12917, "mtime": 1751961237490, "results": "288", "hashOfConfig": "234"}, {"size": 7080, "mtime": 1749468332183, "results": "289", "hashOfConfig": "234"}, {"size": 5675, "mtime": 1749468332190, "results": "290", "hashOfConfig": "234"}, {"size": 2112, "mtime": 1749468332192, "results": "291", "hashOfConfig": "234"}, {"size": 3128, "mtime": 1749468332203, "results": "292", "hashOfConfig": "234"}, {"size": 1292, "mtime": 1749468332205, "results": "293", "hashOfConfig": "234"}, {"size": 1160, "mtime": 1751961237491, "results": "294", "hashOfConfig": "234"}, {"size": 2826, "mtime": 1749468332209, "results": "295", "hashOfConfig": "234"}, {"size": 450, "mtime": 1749468332210, "results": "296", "hashOfConfig": "234"}, {"size": 7793, "mtime": 1749468332213, "results": "297", "hashOfConfig": "234"}, {"size": 1107, "mtime": 1749468332218, "results": "298", "hashOfConfig": "234"}, {"size": 7664, "mtime": 1749468332219, "results": "299", "hashOfConfig": "234"}, {"size": 4285, "mtime": 1749468332220, "results": "300", "hashOfConfig": "234"}, {"size": 1775, "mtime": 1749468332224, "results": "301", "hashOfConfig": "234"}, {"size": 8350, "mtime": 1749468332226, "results": "302", "hashOfConfig": "234"}, {"size": 8480, "mtime": 1749468332228, "results": "303", "hashOfConfig": "234"}, {"size": 7943, "mtime": 1749468332230, "results": "304", "hashOfConfig": "234"}, {"size": 1980, "mtime": 1749468332231, "results": "305", "hashOfConfig": "234"}, {"size": 12596, "mtime": 1749468332233, "results": "306", "hashOfConfig": "234"}, {"size": 3026, "mtime": 1749468332232, "results": "307", "hashOfConfig": "234"}, {"size": 1893, "mtime": 1749468332236, "results": "308", "hashOfConfig": "234"}, {"size": 5878, "mtime": 1749468332236, "results": "309", "hashOfConfig": "234"}, {"size": 3609, "mtime": 1751961237492, "results": "310", "hashOfConfig": "234"}, {"size": 3650, "mtime": 1749468332242, "results": "311", "hashOfConfig": "234"}, {"size": 3338, "mtime": 1749468332244, "results": "312", "hashOfConfig": "234"}, {"size": 10379, "mtime": 1749468332240, "results": "313", "hashOfConfig": "234"}, {"size": 781, "mtime": 1749468332307, "results": "314", "hashOfConfig": "234"}, {"size": 5085, "mtime": 1749468332309, "results": "315", "hashOfConfig": "234"}, {"size": 1122, "mtime": 1749468332315, "results": "316", "hashOfConfig": "234"}, {"size": 5827, "mtime": 1751961237504, "results": "317", "hashOfConfig": "234"}, {"size": 1192, "mtime": 1749468332319, "results": "318", "hashOfConfig": "234"}, {"size": 4900, "mtime": 1749468332321, "results": "319", "hashOfConfig": "234"}, {"size": 47499, "mtime": 1752122960848, "results": "320", "hashOfConfig": "234"}, {"size": 15221, "mtime": 1749468332248, "results": "321", "hashOfConfig": "234"}, {"size": 6271, "mtime": 1749468332250, "results": "322", "hashOfConfig": "234"}, {"size": 1942, "mtime": 1749468332252, "results": "323", "hashOfConfig": "234"}, {"size": 6871, "mtime": 1749468332259, "results": "324", "hashOfConfig": "234"}, {"size": 1199, "mtime": 1749468332261, "results": "325", "hashOfConfig": "234"}, {"size": 3000, "mtime": 1749468332262, "results": "326", "hashOfConfig": "234"}, {"size": 1102, "mtime": 1749468332263, "results": "327", "hashOfConfig": "234"}, {"size": 3279, "mtime": 1749468332266, "results": "328", "hashOfConfig": "234"}, {"size": 2202, "mtime": 1749468332268, "results": "329", "hashOfConfig": "234"}, {"size": 3806, "mtime": 1749468332270, "results": "330", "hashOfConfig": "234"}, {"size": 2927, "mtime": 1749468332272, "results": "331", "hashOfConfig": "234"}, {"size": 9392, "mtime": 1749468332273, "results": "332", "hashOfConfig": "234"}, {"size": 1143, "mtime": 1749468332275, "results": "333", "hashOfConfig": "234"}, {"size": 1801, "mtime": 1749468332276, "results": "334", "hashOfConfig": "234"}, {"size": 341, "mtime": 1749468332277, "results": "335", "hashOfConfig": "234"}, {"size": 5323, "mtime": 1749468332281, "results": "336", "hashOfConfig": "234"}, {"size": 6251, "mtime": 1749468332283, "results": "337", "hashOfConfig": "234"}, {"size": 1926, "mtime": 1749468332285, "results": "338", "hashOfConfig": "234"}, {"size": 685, "mtime": 1749468332285, "results": "339", "hashOfConfig": "234"}, {"size": 3138, "mtime": 1749468332286, "results": "340", "hashOfConfig": "234"}, {"size": 1701, "mtime": 1749468332288, "results": "341", "hashOfConfig": "234"}, {"size": 2955, "mtime": 1751961237495, "results": "342", "hashOfConfig": "234"}, {"size": 2090, "mtime": 1751961237505, "results": "343", "hashOfConfig": "234"}, {"size": 4559, "mtime": 1749468332326, "results": "344", "hashOfConfig": "234"}, {"size": 1453, "mtime": 1749468332329, "results": "345", "hashOfConfig": "234"}, {"size": 1164, "mtime": 1749468332330, "results": "346", "hashOfConfig": "234"}, {"size": 667, "mtime": 1749468332331, "results": "347", "hashOfConfig": "234"}, {"size": 1957, "mtime": 1749468332335, "results": "348", "hashOfConfig": "234"}, {"size": 1956, "mtime": 1749468332336, "results": "349", "hashOfConfig": "234"}, {"size": 1084, "mtime": 1749468332338, "results": "350", "hashOfConfig": "234"}, {"size": 5030, "mtime": 1749468332343, "results": "351", "hashOfConfig": "234"}, {"size": 4096, "mtime": 1749468332345, "results": "352", "hashOfConfig": "234"}, {"size": 3123, "mtime": 1749468332348, "results": "353", "hashOfConfig": "234"}, {"size": 7493, "mtime": 1749468332351, "results": "354", "hashOfConfig": "234"}, {"size": 4261, "mtime": 1749468332353, "results": "355", "hashOfConfig": "234"}, {"size": 849, "mtime": 1749468332355, "results": "356", "hashOfConfig": "234"}, {"size": 734, "mtime": 1749468332356, "results": "357", "hashOfConfig": "234"}, {"size": 1259, "mtime": 1749468332365, "results": "358", "hashOfConfig": "234"}, {"size": 1688, "mtime": 1749468332369, "results": "359", "hashOfConfig": "234"}, {"size": 5773, "mtime": 1749468332372, "results": "360", "hashOfConfig": "234"}, {"size": 4707, "mtime": 1749468332376, "results": "361", "hashOfConfig": "234"}, {"size": 1103, "mtime": 1749468332380, "results": "362", "hashOfConfig": "234"}, {"size": 931, "mtime": 1749468332384, "results": "363", "hashOfConfig": "234"}, {"size": 1166, "mtime": 1749468332386, "results": "364", "hashOfConfig": "234"}, {"size": 1936, "mtime": 1749468332388, "results": "365", "hashOfConfig": "234"}, {"size": 796, "mtime": 1749468332391, "results": "366", "hashOfConfig": "234"}, {"size": 1257, "mtime": 1751961237506, "results": "367", "hashOfConfig": "234"}, {"size": 1670, "mtime": 1749468332289, "results": "368", "hashOfConfig": "234"}, {"size": 23063, "mtime": 1749468332291, "results": "369", "hashOfConfig": "234"}, {"size": 1028, "mtime": 1749468332292, "results": "370", "hashOfConfig": "234"}, {"size": 1357, "mtime": 1749468332293, "results": "371", "hashOfConfig": "234"}, {"size": 8828, "mtime": 1749468332295, "results": "372", "hashOfConfig": "234"}, {"size": 6782, "mtime": 1749468332296, "results": "373", "hashOfConfig": "234"}, {"size": 1852, "mtime": 1749468332300, "results": "374", "hashOfConfig": "234"}, {"size": 9894, "mtime": 1749468332301, "results": "375", "hashOfConfig": "234"}, {"size": 11591, "mtime": 1752570973206, "results": "376", "hashOfConfig": "234"}, {"size": 2228, "mtime": 1751961237495, "results": "377", "hashOfConfig": "234"}, {"size": 754, "mtime": 1751961237506, "results": "378", "hashOfConfig": "234"}, {"size": 961, "mtime": 1751961237507, "results": "379", "hashOfConfig": "234"}, {"size": 13544, "mtime": 1749468332405, "results": "380", "hashOfConfig": "234"}, {"size": 5859, "mtime": 1749468332407, "results": "381", "hashOfConfig": "234"}, {"size": 11399, "mtime": 1749468332408, "results": "382", "hashOfConfig": "234"}, {"size": 11315, "mtime": 1749468332410, "results": "383", "hashOfConfig": "234"}, {"size": 9117, "mtime": 1749468332411, "results": "384", "hashOfConfig": "234"}, {"size": 3276, "mtime": 1749468332414, "results": "385", "hashOfConfig": "234"}, {"size": 19345, "mtime": 1749468332418, "results": "386", "hashOfConfig": "234"}, {"size": 449, "mtime": 1749468332419, "results": "387", "hashOfConfig": "234"}, {"size": 1842, "mtime": 1749468332422, "results": "388", "hashOfConfig": "234"}, {"size": 2693, "mtime": 1749468332424, "results": "389", "hashOfConfig": "234"}, {"size": 1345, "mtime": 1749468332431, "results": "390", "hashOfConfig": "234"}, {"size": 2178, "mtime": 1749468332433, "results": "391", "hashOfConfig": "234"}, {"size": 1135, "mtime": 1749468332436, "results": "392", "hashOfConfig": "234"}, {"size": 2520, "mtime": 1749468332437, "results": "393", "hashOfConfig": "234"}, {"size": 700, "mtime": 1749468332446, "results": "394", "hashOfConfig": "234"}, {"size": 1027, "mtime": 1749468332453, "results": "395", "hashOfConfig": "234"}, {"size": 1295, "mtime": 1751972007162, "results": "396", "hashOfConfig": "234"}, {"size": 814, "mtime": 1749468332463, "results": "397", "hashOfConfig": "234"}, {"size": 1163, "mtime": 1749468332468, "results": "398", "hashOfConfig": "234"}, {"size": 4669, "mtime": 1749468332473, "results": "399", "hashOfConfig": "234"}, {"size": 3219, "mtime": 1749468332475, "results": "400", "hashOfConfig": "234"}, {"size": 713, "mtime": 1749468332476, "results": "401", "hashOfConfig": "234"}, {"size": 618, "mtime": 1749468332481, "results": "402", "hashOfConfig": "234"}, {"size": 1376, "mtime": 1749468332483, "results": "403", "hashOfConfig": "234"}, {"size": 2523, "mtime": 1749468332488, "results": "404", "hashOfConfig": "234"}, {"size": 270, "mtime": 1749468332490, "results": "405", "hashOfConfig": "234"}, {"size": 10045, "mtime": 1752670329753, "results": "406", "hashOfConfig": "234"}, {"size": 1470, "mtime": 1749468332493, "results": "407", "hashOfConfig": "234"}, {"size": 2355, "mtime": 1749468332500, "results": "408", "hashOfConfig": "234"}, {"size": 511, "mtime": 1749468332504, "results": "409", "hashOfConfig": "234"}, {"size": 719, "mtime": 1749468332505, "results": "410", "hashOfConfig": "234"}, {"size": 158, "mtime": 1749468332506, "results": "411", "hashOfConfig": "234"}, {"size": 1233, "mtime": 1751961237472, "results": "412", "hashOfConfig": "234"}, {"size": 597, "mtime": 1751961237472, "results": "413", "hashOfConfig": "234"}, {"size": 1229, "mtime": 1751961237472, "results": "414", "hashOfConfig": "234"}, {"size": 6573, "mtime": 1751961237485, "results": "415", "hashOfConfig": "234"}, {"size": 526, "mtime": 1751961237473, "results": "416", "hashOfConfig": "234"}, {"size": 70321, "mtime": 1752646295048, "results": "417", "hashOfConfig": "234"}, {"size": 23991, "mtime": 1751961237475, "results": "418", "hashOfConfig": "234"}, {"size": 4035, "mtime": 1751961237476, "results": "419", "hashOfConfig": "234"}, {"size": 22693, "mtime": 1752575034632, "results": "420", "hashOfConfig": "234"}, {"size": 4194, "mtime": 1751961237477, "results": "421", "hashOfConfig": "234"}, {"size": 1250, "mtime": 1751961237477, "results": "422", "hashOfConfig": "234"}, {"size": 6887, "mtime": 1752645943786, "results": "423", "hashOfConfig": "234"}, {"size": 5058, "mtime": 1752730634809, "results": "424", "hashOfConfig": "234"}, {"size": 24648, "mtime": 1752578507585, "results": "425", "hashOfConfig": "234"}, {"size": 3927, "mtime": 1751961237479, "results": "426", "hashOfConfig": "234"}, {"size": 57243, "mtime": 1752643583006, "results": "427", "hashOfConfig": "234"}, {"size": 2432, "mtime": 1751961237480, "results": "428", "hashOfConfig": "234"}, {"size": 684, "mtime": 1751961237480, "results": "429", "hashOfConfig": "234"}, {"size": 19627, "mtime": 1752647330554, "results": "430", "hashOfConfig": "234"}, {"size": 1207, "mtime": 1751961237481, "results": "431", "hashOfConfig": "234"}, {"size": 14634, "mtime": 1752576317273, "results": "432", "hashOfConfig": "234"}, {"size": 6496, "mtime": 1752469290174, "results": "433", "hashOfConfig": "234"}, {"size": 10713, "mtime": 1751961237482, "results": "434", "hashOfConfig": "234"}, {"size": 2167, "mtime": 1751961237482, "results": "435", "hashOfConfig": "234"}, {"size": 4555, "mtime": 1751961237483, "results": "436", "hashOfConfig": "234"}, {"size": 14415, "mtime": 1752643966463, "results": "437", "hashOfConfig": "234"}, {"size": 488, "mtime": 1751961237484, "results": "438", "hashOfConfig": "234"}, {"size": 494, "mtime": 1752137317336, "results": "439", "hashOfConfig": "234"}, {"size": 7054, "mtime": 1751961237488, "results": "440", "hashOfConfig": "234"}, {"size": 1567, "mtime": 1751961237489, "results": "441", "hashOfConfig": "234"}, {"size": 1643, "mtime": 1749468332185, "results": "442", "hashOfConfig": "234"}, {"size": 4212, "mtime": 1751961237491, "results": "443", "hashOfConfig": "234"}, {"size": 19915, "mtime": 1749468332191, "results": "444", "hashOfConfig": "234"}, {"size": 5288, "mtime": 1749468332311, "results": "445", "hashOfConfig": "234"}, {"size": 133, "mtime": 1751961237493, "results": "446", "hashOfConfig": "234"}, {"size": 8047, "mtime": 1751961237493, "results": "447", "hashOfConfig": "234"}, {"size": 943, "mtime": 1751961237493, "results": "448", "hashOfConfig": "234"}, {"size": 42503, "mtime": 1752651860366, "results": "449", "hashOfConfig": "234"}, {"size": 601, "mtime": 1749468332323, "results": "450", "hashOfConfig": "234"}, {"size": 1643, "mtime": 1749468332328, "results": "451", "hashOfConfig": "234"}, {"size": 661, "mtime": 1749468332401, "results": "452", "hashOfConfig": "234"}, {"size": 1423, "mtime": 1752574627802, "results": "453", "hashOfConfig": "234"}, {"size": 10347, "mtime": 1752489549452, "results": "454", "hashOfConfig": "234"}, {"size": 440, "mtime": 1749468332460, "results": "455", "hashOfConfig": "234"}, {"size": 1130, "mtime": 1752147736943, "results": "456", "hashOfConfig": "234"}, {"size": 2955, "mtime": 1751961237508, "results": "457", "hashOfConfig": "234"}, {"size": 2100, "mtime": 1751961237508, "results": "458", "hashOfConfig": "234"}, {"size": 2887, "mtime": 1751961237508, "results": "459", "hashOfConfig": "234"}, {"size": 9570, "mtime": 1751961237508, "results": "460", "hashOfConfig": "234"}, {"size": 2375, "mtime": 1751961237508, "results": "461", "hashOfConfig": "234"}, {"size": 9828, "mtime": 1751961237510, "results": "462", "hashOfConfig": "234"}, {"size": 3139, "mtime": 1751961237510, "results": "463", "hashOfConfig": "234"}, {"size": 3094, "mtime": 1751961237511, "results": "464", "hashOfConfig": "234"}, {"size": 8229, "mtime": 1751961237507, "results": "465", "hashOfConfig": "234"}, {"filePath": "466", "messages": "467", "suppressedMessages": "468", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "19yel12", {"filePath": "469", "messages": "470", "suppressedMessages": "471", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "472", "messages": "473", "suppressedMessages": "474", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "475", "messages": "476", "suppressedMessages": "477", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "478", "messages": "479", "suppressedMessages": "480", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "481", "messages": "482", "suppressedMessages": "483", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "484", "messages": "485", "suppressedMessages": "486", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "487", "messages": "488", "suppressedMessages": "489", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "490", "messages": "491", "suppressedMessages": "492", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "493", "messages": "494", "suppressedMessages": "495", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "496", "messages": "497", "suppressedMessages": "498", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "499", "messages": "500", "suppressedMessages": "501", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "502", "messages": "503", "suppressedMessages": "504", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "505", "messages": "506", "suppressedMessages": "507", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "508", "messages": "509", "suppressedMessages": "510", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "511", "messages": "512", "suppressedMessages": "513", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "514", "messages": "515", "suppressedMessages": "516", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "517", "messages": "518", "suppressedMessages": "519", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "520", "messages": "521", "suppressedMessages": "522", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "523", "messages": "524", "suppressedMessages": "525", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "526", "messages": "527", "suppressedMessages": "528", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "529", "messages": "530", "suppressedMessages": "531", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "532", "messages": "533", "suppressedMessages": "534", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "535", "messages": "536", "suppressedMessages": "537", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "538", "messages": "539", "suppressedMessages": "540", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "541", "messages": "542", "suppressedMessages": "543", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "544", "messages": "545", "suppressedMessages": "546", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "547", "messages": "548", "suppressedMessages": "549", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "550", "messages": "551", "suppressedMessages": "552", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "553", "messages": "554", "suppressedMessages": "555", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "556", "messages": "557", "suppressedMessages": "558", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "559", "messages": "560", "suppressedMessages": "561", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "562", "messages": "563", "suppressedMessages": "564", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "565", "messages": "566", "suppressedMessages": "567", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "568", "messages": "569", "suppressedMessages": "570", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "571", "messages": "572", "suppressedMessages": "573", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "574", "messages": "575", "suppressedMessages": "576", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "577", "messages": "578", "suppressedMessages": "579", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "580", "messages": "581", "suppressedMessages": "582", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "583", "messages": "584", "suppressedMessages": "585", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "586", "messages": "587", "suppressedMessages": "588", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "589", "messages": "590", "suppressedMessages": "591", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "592", "messages": "593", "suppressedMessages": "594", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "595", "messages": "596", "suppressedMessages": "597", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "598", "messages": "599", "suppressedMessages": "600", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "601", "messages": "602", "suppressedMessages": "603", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "604", "messages": "605", "suppressedMessages": "606", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "607", "messages": "608", "suppressedMessages": "609", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "610", "messages": "611", "suppressedMessages": "612", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "613", "messages": "614", "suppressedMessages": "615", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "616", "messages": "617", "suppressedMessages": "618", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "619", "messages": "620", "suppressedMessages": "621", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "622", "messages": "623", "suppressedMessages": "624", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "625", "messages": "626", "suppressedMessages": "627", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "628", "messages": "629", "suppressedMessages": "630", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "631", "messages": "632", "suppressedMessages": "633", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "634", "messages": "635", "suppressedMessages": "636", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "637", "messages": "638", "suppressedMessages": "639", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "640", "messages": "641", "suppressedMessages": "642", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "643", "messages": "644", "suppressedMessages": "645", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "646", "messages": "647", "suppressedMessages": "648", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "649", "messages": "650", "suppressedMessages": "651", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "652", "messages": "653", "suppressedMessages": "654", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "655", "messages": "656", "suppressedMessages": "657", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "658", "messages": "659", "suppressedMessages": "660", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "661", "messages": "662", "suppressedMessages": "663", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "664", "messages": "665", "suppressedMessages": "666", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "667", "messages": "668", "suppressedMessages": "669", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "670", "messages": "671", "suppressedMessages": "672", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "673", "messages": "674", "suppressedMessages": "675", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "676", "messages": "677", "suppressedMessages": "678", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "679", "messages": "680", "suppressedMessages": "681", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "682", "messages": "683", "suppressedMessages": "684", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "685", "messages": "686", "suppressedMessages": "687", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "688", "messages": "689", "suppressedMessages": "690", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "691", "messages": "692", "suppressedMessages": "693", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "694", "messages": "695", "suppressedMessages": "696", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "697", "messages": "698", "suppressedMessages": "699", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "700", "messages": "701", "suppressedMessages": "702", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "703", "messages": "704", "suppressedMessages": "705", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "706", "messages": "707", "suppressedMessages": "708", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "709", "messages": "710", "suppressedMessages": "711", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "712", "messages": "713", "suppressedMessages": "714", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "715", "messages": "716", "suppressedMessages": "717", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "718", "messages": "719", "suppressedMessages": "720", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "721", "messages": "722", "suppressedMessages": "723", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "724", "messages": "725", "suppressedMessages": "726", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "727", "messages": "728", "suppressedMessages": "729", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "730", "messages": "731", "suppressedMessages": "732", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "733", "messages": "734", "suppressedMessages": "735", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "736", "messages": "737", "suppressedMessages": "738", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "739", "messages": "740", "suppressedMessages": "741", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "742", "messages": "743", "suppressedMessages": "744", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "745", "messages": "746", "suppressedMessages": "747", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "748", "messages": "749", "suppressedMessages": "750", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "751", "messages": "752", "suppressedMessages": "753", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "754", "messages": "755", "suppressedMessages": "756", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "757", "messages": "758", "suppressedMessages": "759", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "760", "messages": "761", "suppressedMessages": "762", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "763", "messages": "764", "suppressedMessages": "765", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "766", "messages": "767", "suppressedMessages": "768", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "769", "messages": "770", "suppressedMessages": "771", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "772", "messages": "773", "suppressedMessages": "774", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "775", "messages": "776", "suppressedMessages": "777", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "778", "messages": "779", "suppressedMessages": "780", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "781", "messages": "782", "suppressedMessages": "783", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "784", "messages": "785", "suppressedMessages": "786", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "787", "messages": "788", "suppressedMessages": "789", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "790", "messages": "791", "suppressedMessages": "792", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "793", "messages": "794", "suppressedMessages": "795", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "796", "messages": "797", "suppressedMessages": "798", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "799", "messages": "800", "suppressedMessages": "801", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "802", "messages": "803", "suppressedMessages": "804", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "805", "messages": "806", "suppressedMessages": "807", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "808", "messages": "809", "suppressedMessages": "810", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "811", "messages": "812", "suppressedMessages": "813", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "814", "messages": "815", "suppressedMessages": "816", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "817", "messages": "818", "suppressedMessages": "819", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "820", "messages": "821", "suppressedMessages": "822", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "823", "messages": "824", "suppressedMessages": "825", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "826", "messages": "827", "suppressedMessages": "828", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "829", "messages": "830", "suppressedMessages": "831", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "832", "messages": "833", "suppressedMessages": "834", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "835", "messages": "836", "suppressedMessages": "837", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "838", "messages": "839", "suppressedMessages": "840", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "841", "messages": "842", "suppressedMessages": "843", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "844", "messages": "845", "suppressedMessages": "846", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "847", "messages": "848", "suppressedMessages": "849", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "850", "messages": "851", "suppressedMessages": "852", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "853", "messages": "854", "suppressedMessages": "855", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "856", "messages": "857", "suppressedMessages": "858", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "859", "messages": "860", "suppressedMessages": "861", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "862", "messages": "863", "suppressedMessages": "864", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "865", "messages": "866", "suppressedMessages": "867", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "868", "messages": "869", "suppressedMessages": "870", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "871", "messages": "872", "suppressedMessages": "873", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "874", "messages": "875", "suppressedMessages": "876", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "877", "messages": "878", "suppressedMessages": "879", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "880", "messages": "881", "suppressedMessages": "882", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "883", "messages": "884", "suppressedMessages": "885", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "886", "messages": "887", "suppressedMessages": "888", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "889", "messages": "890", "suppressedMessages": "891", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "892", "messages": "893", "suppressedMessages": "894", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "895", "messages": "896", "suppressedMessages": "897", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "898", "messages": "899", "suppressedMessages": "900", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "901", "messages": "902", "suppressedMessages": "903", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "904", "messages": "905", "suppressedMessages": "906", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "907", "messages": "908", "suppressedMessages": "909", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "910", "messages": "911", "suppressedMessages": "912", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "913", "messages": "914", "suppressedMessages": "915", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "916", "messages": "917", "suppressedMessages": "918", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "919", "messages": "920", "suppressedMessages": "921", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "922", "messages": "923", "suppressedMessages": "924", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "925", "messages": "926", "suppressedMessages": "927", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "928", "messages": "929", "suppressedMessages": "930", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "931", "messages": "932", "suppressedMessages": "933", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "934", "messages": "935", "suppressedMessages": "936", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "937", "messages": "938", "suppressedMessages": "939", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "940", "messages": "941", "suppressedMessages": "942", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "943", "messages": "944", "suppressedMessages": "945", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "946", "messages": "947", "suppressedMessages": "948", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "949", "messages": "950", "suppressedMessages": "951", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "952", "messages": "953", "suppressedMessages": "954", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "955", "messages": "956", "suppressedMessages": "957", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "958", "messages": "959", "suppressedMessages": "960", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "961", "messages": "962", "suppressedMessages": "963", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "964", "messages": "965", "suppressedMessages": "966", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "967", "messages": "968", "suppressedMessages": "969", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "970", "messages": "971", "suppressedMessages": "972", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "973", "messages": "974", "suppressedMessages": "975", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "976", "messages": "977", "suppressedMessages": "978", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "979", "messages": "980", "suppressedMessages": "981", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "982", "messages": "983", "suppressedMessages": "984", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "985", "messages": "986", "suppressedMessages": "987", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "988", "messages": "989", "suppressedMessages": "990", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "991", "messages": "992", "suppressedMessages": "993", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "994", "messages": "995", "suppressedMessages": "996", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "997", "messages": "998", "suppressedMessages": "999", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1000", "messages": "1001", "suppressedMessages": "1002", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1003", "messages": "1004", "suppressedMessages": "1005", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1006", "messages": "1007", "suppressedMessages": "1008", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1009", "messages": "1010", "suppressedMessages": "1011", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1012", "messages": "1013", "suppressedMessages": "1014", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1015", "messages": "1016", "suppressedMessages": "1017", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1018", "messages": "1019", "suppressedMessages": "1020", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1021", "messages": "1022", "suppressedMessages": "1023", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1024", "messages": "1025", "suppressedMessages": "1026", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1027", "messages": "1028", "suppressedMessages": "1029", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1030", "messages": "1031", "suppressedMessages": "1032", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1033", "messages": "1034", "suppressedMessages": "1035", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1036", "messages": "1037", "suppressedMessages": "1038", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1039", "messages": "1040", "suppressedMessages": "1041", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1042", "messages": "1043", "suppressedMessages": "1044", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1045", "messages": "1046", "suppressedMessages": "1047", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1048", "messages": "1049", "suppressedMessages": "1050", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1051", "messages": "1052", "suppressedMessages": "1053", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1054", "messages": "1055", "suppressedMessages": "1056", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1057", "messages": "1058", "suppressedMessages": "1059", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1060", "messages": "1061", "suppressedMessages": "1062", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1063", "messages": "1064", "suppressedMessages": "1065", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1066", "messages": "1067", "suppressedMessages": "1068", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1069", "messages": "1070", "suppressedMessages": "1071", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1072", "messages": "1073", "suppressedMessages": "1074", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1075", "messages": "1076", "suppressedMessages": "1077", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1078", "messages": "1079", "suppressedMessages": "1080", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1081", "messages": "1082", "suppressedMessages": "1083", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1084", "messages": "1085", "suppressedMessages": "1086", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1087", "messages": "1088", "suppressedMessages": "1089", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1090", "messages": "1091", "suppressedMessages": "1092", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1093", "messages": "1094", "suppressedMessages": "1095", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1096", "messages": "1097", "suppressedMessages": "1098", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1099", "messages": "1100", "suppressedMessages": "1101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1102", "messages": "1103", "suppressedMessages": "1104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1105", "messages": "1106", "suppressedMessages": "1107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1108", "messages": "1109", "suppressedMessages": "1110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1111", "messages": "1112", "suppressedMessages": "1113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1114", "messages": "1115", "suppressedMessages": "1116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1117", "messages": "1118", "suppressedMessages": "1119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1120", "messages": "1121", "suppressedMessages": "1122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1123", "messages": "1124", "suppressedMessages": "1125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1126", "messages": "1127", "suppressedMessages": "1128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1129", "messages": "1130", "suppressedMessages": "1131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1132", "messages": "1133", "suppressedMessages": "1134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1135", "messages": "1136", "suppressedMessages": "1137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1138", "messages": "1139", "suppressedMessages": "1140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1141", "messages": "1142", "suppressedMessages": "1143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1144", "messages": "1145", "suppressedMessages": "1146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1147", "messages": "1148", "suppressedMessages": "1149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1150", "messages": "1151", "suppressedMessages": "1152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1153", "messages": "1154", "suppressedMessages": "1155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1156", "messages": "1157", "suppressedMessages": "1158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1159", "messages": "1160", "suppressedMessages": "1161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "G:\\agape\\agape-newFrontend\\web\\src\\app\\(auth)\\login\\layout.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\app\\(auth)\\login\\page.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\app\\(auth)\\signup\\layout.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\app\\(auth)\\signup\\page.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\app\\animProviders.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\app\\api\\auth\\[...nextauth]\\route.ts", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\app\\api\\hello.ts", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\app\\layout.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\app\\library\\layout.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\app\\library\\page.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\app\\not-found.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\app\\page.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\app\\projects\\[id]\\page.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\app\\settings\\account\\layout.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\app\\settings\\account\\page.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\app\\settings\\profile\\layout.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\app\\settings\\profile\\page.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\app\\upload\\layout.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\app\\upload\\page.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\app\\workspace\\[workspaceId]\\comments\\layout.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\app\\workspace\\[workspaceId]\\comments\\page.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\app\\workspace\\[workspaceId]\\page.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\AdvanceRendering.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\AuthPages\\LoginPageContent.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\AuthPages\\MagicLinkForm.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\AuthPages\\SignupPageContent.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\CameraSider\\CameraAdjustments.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\CameraSider\\CameraSider.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\ColorsWindow.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\Combobox.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\Comments\\CommentCard.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\Comments\\CommentInput.tsx", ["1162"], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\Comments\\CommentReplyInput.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\Comments\\CommentsPageContent.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\ComponentsSider.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\CubeMenu.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\FeaturesSider.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\GemStone.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\Gizmo\\GizmoMenu.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\Gizmo.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\Home\\ApprovalConfirmationModal.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\Home\\ApprovalDialog.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\Home\\BackgroundImages.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\Home\\CommentNotification.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\Home\\FilterOverlay.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\Home\\HomePage.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\Home\\HomePageContent.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\Home\\LibraryContent.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\Home\\NewProjectDialog.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\Home\\NotificationsPopup.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\Home\\ProjectCard.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\Home\\RightSidebar.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\Home\\SearchOverlay.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\Home\\Sidebar.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\Home\\TopNavigation.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\Home\\UploadDialog.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\InvitationsModal.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\LeftSidebar.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\Library\\ActionDropdown.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\Library\\DeleteConfirmationDialog.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\Library\\DynamicModel.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\Library\\EditMaterial.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\Library\\HDRIBall.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\Library\\HDRIsCard.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\Library\\ImageModal.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\Library\\ImagesCard.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\Library\\LibraryPageContent.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\Library\\MaterialBall.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\Library\\MaterialPBR.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\Library\\MaterialsCard.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\Library\\ModelsCard.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\Library\\PreviewModal.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\Library\\ProjectsCard.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\Library\\ProjectSnapshotsModal.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\Lighting.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\LightLinkingSider.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\Loader\\Loader.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\MaterialSider\\MaterialAccordionItem.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\MaterialSider\\MaterialSelector.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\MaterialSider.tsx", ["1163", "1164"], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\models\\Ball.js", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\models\\CSSRing.jsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\models\\Ring.js", ["1165"], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\models\\RingSingle.js", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\models\\RingSingle_comp.jsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\models\\Spider.js", ["1166"], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\ModelViewer.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\Navbar.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\Outliner.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\PieMenu.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\ProjectSettings.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\Proportions.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\QuickActions.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\Rendering.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\Settings\\AccountPageContent.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\Settings\\Layout.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\Settings\\ProfilePageContent.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\Settings\\TeamMember.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\ShareModal.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\SideSheet.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\SideSheetContent.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\Theme\\theme-provider.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\Toolbar\\CameraContent.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\Toolbar\\HDRContent.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\Toolbar\\HDRPreviewMenu.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\Toolbar\\LightingContent.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\Toolbar\\PostProcessing.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\Toolbar\\ViewportContent.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\Toolbar.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\ui\\accordion.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\ui\\alert-dialog.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\ui\\avatar.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\ui\\badge.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\ui\\button-shimmer.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\ui\\button.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\ui\\card.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\ui\\checkbox.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\ui\\command.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\ui\\dialog.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\ui\\drawer.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\ui\\dropdown-menu.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\ui\\form.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\ui\\input.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\ui\\label.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\ui\\popover.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\ui\\scroll-area.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\ui\\select.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\ui\\sheet.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\ui\\slider.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\ui\\sonner.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\ui\\switch.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\ui\\tabs.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\ui\\textarea.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\ui\\tooltip.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\Upload\\CategoryItemViewer.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\Upload\\GroupingScreen.tsx", ["1167"], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\Upload\\Model.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\Upload\\ModelViewer.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\Upload\\Sidebar.tsx", ["1168", "1169", "1170"], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\Upload\\SingleGroup.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\Upload\\UploadPageContent.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\Upload\\UploadScreen.tsx", ["1171"], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\VersionHistory.tsx", ["1172"], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\Workspace\\DynamicModel.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\config\\seo-meta-data.ts", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\config\\site.ts", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\lib\\actions\\comment.actions.ts", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\lib\\actions\\invite.actions.ts", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\lib\\actions\\material.actions.ts", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\lib\\actions\\mesh.actions.ts", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\lib\\actions\\meshCategory.actions.ts", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\lib\\actions\\user.actions.ts", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\lib\\actions\\workspace.actions.ts", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\lib\\auth.d.ts", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\lib\\auth.ts", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\lib\\configurations.ts", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\lib\\db\\db.ts", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\lib\\db\\models\\comment.models.ts", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\lib\\db\\models\\invite.models.ts", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\lib\\db\\models\\material.models.ts", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\lib\\db\\models\\membership.models.ts", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\lib\\db\\models\\mesh.models.ts", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\lib\\db\\models\\meshCategory.models.ts", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\lib\\db\\models\\user.models.ts", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\lib\\db\\models\\workspace.models.ts", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\lib\\email\\sendEmailNotification.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\lib\\email\\sendLoginMail.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\lib\\mongoose.ts", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\lib\\queues\\emailQueue.ts", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\lib\\queues\\emailWorker.ts", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\lib\\s3\\s3.ts", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\lib\\session.ts", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\lib\\states.ts", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\lib\\utils.ts", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\types\\index.ts", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\utils\\buffers.ts", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\utils\\getPageData.ts", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\utils\\isExtension.ts", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\app\\projects\\[id]\\edit\\layout.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\app\\projects\\[id]\\edit\\page.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\app\\projects\\[id]\\layout.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\ArtistTool\\animations\\CameraAnimations.js", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\ArtistTool\\BackPlate.jsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\ArtistTool\\EditorExperience.jsx", ["1173", "1174", "1175", "1176", "1177"], ["1178"], "G:\\agape\\agape-newFrontend\\web\\src\\components\\ArtistTool\\EngravingPanel.jsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\ArtistTool\\Environment.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\ArtistTool\\EnvironmentPanel.jsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\ArtistTool\\EnvironmentPreviewBall.jsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\ArtistTool\\Ground.jsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\ArtistTool\\Header.jsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\ArtistTool\\InitialOverlayPanel.jsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\ArtistTool\\LightPanel.jsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\ArtistTool\\LoadSceneModal.jsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\ArtistTool\\MaterialPanel.jsx", ["1179"], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\ArtistTool\\ModelStage.jsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\ArtistTool\\ModelStats.jsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\ArtistTool\\Outliner.jsx", ["1180"], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\ArtistTool\\PostProcessingEffects.jsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\ArtistTool\\PostProcessingPanel.jsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\ArtistTool\\SaveSceneModal.jsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\ArtistTool\\SceneLights.jsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\ArtistTool\\SelectionOutline.jsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\ArtistTool\\Toolbar.jsx", ["1181", "1182", "1183", "1184", "1185", "1186"], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\ArtistTool\\TransformPanel.jsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\ArtistTool\\UploadedModel.jsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\EditProjectPage.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\Home\\InvitationsDropdown.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\Home\\ModelPreview.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\Home\\search-dropdown.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\Home\\searchbar.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\InviteModal.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\models\\ChosenRing.jsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\Onboarding\\index.ts", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\Onboarding\\OnboardingProvider.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\Onboarding\\OnboardingTrigger.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\ProjectPage.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\providers\\UserProvider.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\components\\ui\\alert.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\hooks\\use-click-outside.tsx", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\hooks\\useDebounce.ts", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\lib\\actions\\version.actions.ts", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\lib\\db\\models\\tag.models.ts", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\lib\\db\\models\\version.models.ts", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\utils\\ArtistTool\\cameraUtils.js", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\utils\\ArtistTool\\historyUtils.js", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\utils\\ArtistTool\\materialUtils.js", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\utils\\ArtistTool\\refraction_materials\\Diamond_Fragment.js", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\utils\\ArtistTool\\refraction_materials\\Diamond_Shader.js", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\utils\\ArtistTool\\refraction_materials\\MeshRefractionMaterial.js", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\utils\\ArtistTool\\refraction_materials\\NormalCube.js", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\utils\\ArtistTool\\sceneUtils.js", [], [], "G:\\agape\\agape-newFrontend\\web\\src\\utils\\ArtistTool\\TextBumpMapGenerator.jsx", ["1187"], [], {"ruleId": "1188", "severity": 1, "message": "1189", "line": 79, "column": 7, "nodeType": "1190", "endLine": 83, "endColumn": 9}, {"ruleId": "1188", "severity": 1, "message": "1189", "line": 182, "column": 15, "nodeType": "1190", "endLine": 187, "endColumn": 17}, {"ruleId": "1191", "severity": 1, "message": "1192", "line": 182, "column": 15, "nodeType": "1190", "endLine": 187, "endColumn": 17}, {"ruleId": "1193", "severity": 1, "message": "1194", "line": 20, "column": 5, "nodeType": "1195", "endLine": 20, "endColumn": 30, "suggestions": "1196"}, {"ruleId": "1193", "severity": 1, "message": "1194", "line": 22, "column": 5, "nodeType": "1195", "endLine": 22, "endColumn": 30, "suggestions": "1197"}, {"ruleId": "1188", "severity": 1, "message": "1189", "line": 671, "column": 21, "nodeType": "1190", "endLine": 675, "endColumn": 23}, {"ruleId": "1188", "severity": 1, "message": "1189", "line": 79, "column": 19, "nodeType": "1190", "endLine": 83, "endColumn": 21}, {"ruleId": "1188", "severity": 1, "message": "1189", "line": 154, "column": 19, "nodeType": "1190", "endLine": 158, "endColumn": 21}, {"ruleId": "1188", "severity": 1, "message": "1189", "line": 169, "column": 23, "nodeType": "1190", "endLine": 173, "endColumn": 25}, {"ruleId": "1193", "severity": 1, "message": "1198", "line": 95, "column": 5, "nodeType": "1195", "endLine": 95, "endColumn": 23, "suggestions": "1199"}, {"ruleId": "1193", "severity": 1, "message": "1200", "line": 84, "column": 6, "nodeType": "1195", "endLine": 84, "endColumn": 35, "suggestions": "1201"}, {"ruleId": "1193", "severity": 1, "message": "1202", "line": 254, "column": 6, "nodeType": "1195", "endLine": 254, "endColumn": 8, "suggestions": "1203"}, {"ruleId": "1193", "severity": 1, "message": "1204", "line": 669, "column": 6, "nodeType": "1195", "endLine": 669, "endColumn": 41, "suggestions": "1205"}, {"ruleId": "1193", "severity": 1, "message": "1206", "line": 677, "column": 67, "nodeType": "1207", "endLine": 677, "endColumn": 74}, {"ruleId": "1193", "severity": 1, "message": "1208", "line": 1167, "column": 6, "nodeType": "1195", "endLine": 1167, "endColumn": 8, "suggestions": "1209"}, {"ruleId": "1193", "severity": 1, "message": "1210", "line": 1309, "column": 6, "nodeType": "1195", "endLine": 1309, "endColumn": 35, "suggestions": "1211"}, {"ruleId": "1193", "severity": 1, "message": "1212", "line": 1765, "column": 6, "nodeType": "1195", "endLine": 1765, "endColumn": 8, "suggestions": "1213", "suppressions": "1214"}, {"ruleId": "1193", "severity": 1, "message": "1215", "line": 295, "column": 6, "nodeType": "1195", "endLine": 295, "endColumn": 33, "suggestions": "1216"}, {"ruleId": "1188", "severity": 1, "message": "1189", "line": 278, "column": 11, "nodeType": "1190", "endLine": 282, "endColumn": 13}, {"ruleId": "1188", "severity": 1, "message": "1189", "line": 92, "column": 11, "nodeType": "1190", "endLine": 96, "endColumn": 13}, {"ruleId": "1188", "severity": 1, "message": "1189", "line": 105, "column": 11, "nodeType": "1190", "endLine": 105, "endColumn": 80}, {"ruleId": "1188", "severity": 1, "message": "1189", "line": 114, "column": 11, "nodeType": "1190", "endLine": 114, "endColumn": 78}, {"ruleId": "1188", "severity": 1, "message": "1189", "line": 123, "column": 11, "nodeType": "1190", "endLine": 127, "endColumn": 13}, {"ruleId": "1188", "severity": 1, "message": "1189", "line": 136, "column": 11, "nodeType": "1190", "endLine": 140, "endColumn": 13}, {"ruleId": "1188", "severity": 1, "message": "1189", "line": 149, "column": 11, "nodeType": "1190", "endLine": 153, "endColumn": 13}, {"ruleId": "1193", "severity": 1, "message": "1217", "line": 165, "column": 6, "nodeType": "1195", "endLine": 165, "endColumn": 63, "suggestions": "1218"}, "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", "jsx-a11y/alt-text", "img elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", "react-hooks/exhaustive-deps", "React Hook useCallback has a missing dependency: 'props'. Either include it or remove the dependency array. However, 'props' will change when *any* prop changes, so the preferred fix is to destructure the 'props' object outside of the useCallback call and refer to those specific props inside useCallback.", "ArrayExpression", ["1219"], ["1220"], "React Hook useCallback has a missing dependency: 'setSelectedFile'. Either include it or remove the dependency array. If 'setSelectedFile' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["1221"], "React Hook useEffect has missing dependencies: 'refreshVersions', 'startPeriodicRefresh', and 'versions'. Either include them or remove the dependency array.", ["1222"], "React Hook useEffect has missing dependencies: 'bgColor', 'envBlur', 'envIntensity', 'envPreset', 'groundType', 'lights', 'postProcessingSettings', 'selectedModel', 'showEnvironment', 'showGrid', 'showLightSpheres', and 'wireframe'. Either include them or remove the dependency array.", ["1223"], "React Hook useCallback has an unnecessary dependency: 'setIsPlacingDecal'. Either exclude it or remove the dependency array.", ["1224"], "The ref value 'selectionGroupRef.current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy 'selectionGroupRef.current' to a variable inside the effect, and use that variable in the cleanup function.", "Identifier", "React Hook useCallback has missing dependencies: 'customHdri' and 'uploadedModel'. Either include them or remove the dependency array.", ["1225"], "React Hook useEffect has missing dependencies: 'handleLoadScene' and 'lastSelectedModel'. Either include them or remove the dependency array.", ["1226"], "React Hook useEffect has missing dependencies: 'handleLoadScene', 'project._id', and 'user.id'. Either include them or remove the dependency array.", ["1227"], ["1228"], "React Hook useEffect has missing dependencies: 'createDiamondData', 'filterOutDecals', 'setUseDiamondShader', and 'useDiamondShader'. Either include them or remove the dependency array. If 'setUseDiamondShader' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["1229"], "React Hook useMemo has an unnecessary dependency: 'aspect'. Either exclude it or remove the dependency array.", ["1230"], {"desc": "1231", "fix": "1232"}, {"desc": "1231", "fix": "1233"}, {"desc": "1234", "fix": "1235"}, {"desc": "1236", "fix": "1237"}, {"desc": "1238", "fix": "1239"}, {"desc": "1240", "fix": "1241"}, {"desc": "1242", "fix": "1243"}, {"desc": "1244", "fix": "1245"}, {"desc": "1246", "fix": "1247"}, {"kind": "1248", "justification": "1249"}, {"desc": "1250", "fix": "1251"}, {"desc": "1252", "fix": "1253"}, "Update the dependencies array to be: [gl.domElement, props]", {"range": "1254", "text": "1255"}, {"range": "1256", "text": "1255"}, "Update the dependencies array to be: [setSelectedFile, setUploadedModel]", {"range": "1257", "text": "1258"}, "Update the dependencies array to be: [isOpen, refreshVersions, startPeriodicRefresh, userId, versions, workspaceId]", {"range": "1259", "text": "1260"}, "Update the dependencies array to be: [bgColor, envBlur, envIntensity, envPreset, groundType, lights, postProcessingSettings, selectedModel, showEnvironment, showGrid, showLightSpheres, wireframe]", {"range": "1261", "text": "1262"}, "Update the dependencies array to be: [isPlacingDecal]", {"range": "1263", "text": "1264"}, "Update the dependencies array to be: [customHdri, uploadedModel]", {"range": "1265", "text": "1266"}, "Update the dependencies array to be: [handleLoadScene, lastSelectedModel, sceneObjects, selectedModel]", {"range": "1267", "text": "1268"}, "Update the dependencies array to be: [handleLoadScene, project._id, user.id]", {"range": "1269", "text": "1270"}, "directive", "", "Update the dependencies array to be: [selectedObjects, renderer, filterOutDecals, useDiamondShader, setUseDiamondShader, createDiamondData]", {"range": "1271", "text": "1272"}, "Update the dependencies array to be: [text, font, fontSize, bumpStrength, textureSize]", {"range": "1273", "text": "1274"}, [628, 653], "[gl.dom<PERSON><PERSON>, props]", [659, 684], [2866, 2884], "[setSelectedFile, setUploadedModel]", [2364, 2393], "[isOpen, refreshVersions, startPeriodicRefresh, userId, versions, workspaceId]", [7351, 7353], "[bgColor, envBlur, envIntensity, envPreset, groundType, lights, postProcessingSettings, selectedModel, showEnvironment, showGrid, showLightSpheres, wireframe]", [21524, 21559], "[isPlacingDecal]", [35074, 35076], "[customHdri, uploadedModel]", [40388, 40417], "[handleLoadScene, lastSelectedModel, sceneObjects, selectedModel]", [55619, 55621], "[handleLoadScene, project._id, user.id]", [9398, 9425], "[selectedObjects, renderer, filterOutDecals, useDiamondShader, setUseDiamondShader, createDiamondData]", [5828, 5885], "[text, font, fontSize, bumpStrength, textureSize]"]