"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/[...nextauth]/route";
exports.ids = ["app/api/auth/[...nextauth]/route"];
exports.modules = {

/***/ "mongodb":
/*!**************************!*\
  !*** external "mongodb" ***!
  \**************************/
/***/ ((module) => {

module.exports = require("mongodb");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "dns":
/*!**********************!*\
  !*** external "dns" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("dns");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("net");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("querystring");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=G%3A%5Cagape%5Cagape-newFrontend%5Cweb%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5Cagape%5Cagape-newFrontend%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=G%3A%5Cagape%5Cagape-newFrontend%5Cweb%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5Cagape%5Cagape-newFrontend%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var G_agape_agape_newFrontend_web_src_app_api_auth_nextauth_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/auth/[...nextauth]/route.ts */ \"(rsc)/./src/app/api/auth/[...nextauth]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/[...nextauth]/route\",\n        pathname: \"/api/auth/[...nextauth]\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/[...nextauth]/route\"\n    },\n    resolvedPagePath: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\app\\\\api\\\\auth\\\\[...nextauth]\\\\route.ts\",\n    nextConfigOutput,\n    userland: G_agape_agape_newFrontend_web_src_app_api_auth_nextauth_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/auth/[...nextauth]/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=G%3A%5Cagape%5Cagape-newFrontend%5Cweb%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5Cagape%5Cagape-newFrontend%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/auth/[...nextauth]/route.ts":
/*!*************************************************!*\
  !*** ./src/app/api/auth/[...nextauth]/route.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ handler),\n/* harmony export */   POST: () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/.pnpm/next-auth@4.24.7_next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1__nodemailer@6.9.16_r_cn7rdjlltjmhlsi5xxj5pwkivm/node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst handler = next_auth__WEBPACK_IMPORTED_MODULE_1___default()(_lib_auth__WEBPACK_IMPORTED_MODULE_0__.authOptions);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9hdXRoL1suLi5uZXh0YXV0aF0vcm91dGUudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBeUM7QUFDUjtBQUVqQyxNQUFNRSxVQUFVRCxnREFBUUEsQ0FBQ0Qsa0RBQVdBO0FBRU8iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hZ2FwZS1uZXctZnJvbnRlbmQvLi9zcmMvYXBwL2FwaS9hdXRoL1suLi5uZXh0YXV0aF0vcm91dGUudHM/MDA5OCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBhdXRoT3B0aW9ucyB9IGZyb20gXCJAL2xpYi9hdXRoXCI7XHJcbmltcG9ydCBOZXh0QXV0aCBmcm9tIFwibmV4dC1hdXRoXCI7XHJcblxyXG5jb25zdCBoYW5kbGVyID0gTmV4dEF1dGgoYXV0aE9wdGlvbnMpO1xyXG5cclxuZXhwb3J0IHsgaGFuZGxlciBhcyBHRVQsIGhhbmRsZXIgYXMgUE9TVCB9O1xyXG4iXSwibmFtZXMiOlsiYXV0aE9wdGlvbnMiLCJOZXh0QXV0aCIsImhhbmRsZXIiLCJHRVQiLCJQT1NUIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/auth/[...nextauth]/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/config/site.ts":
/*!****************************!*\
  !*** ./src/config/site.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   siteConfig: () => (/* binding */ siteConfig)\n/* harmony export */ });\nconst siteConfig = {\n    name: \"ChowSangSang Content Library\",\n    url: \"http://ec2-13-202-135-145.ap-south-1.compute.amazonaws.com:3000\" || 0,\n    ogImage: `${\"http://ec2-13-202-135-145.ap-south-1.compute.amazonaws.com:3000\" || 0}/og-image.png`,\n    creator: \"Reunite Limited\",\n    description: \"ChowSangSang Content Library\",\n    mainNav: [\n        {\n            title: \"Home\",\n            href: \"/\"\n        },\n        {\n            title: \"Home2\",\n            href: \"/home\"\n        },\n        {\n            title: \"Comments\",\n            href: \"/comments\"\n        },\n        {\n            title: \"Library\",\n            href: \"/library\"\n        },\n        {\n            title: \"Upload\",\n            href: \"/upload\"\n        },\n        {\n            title: \"Account\",\n            href: \"/settings/account\"\n        },\n        {\n            title: \"Profile\",\n            href: \"/profile\"\n        },\n        {\n            title: \"Signup\",\n            href: \"/signup\"\n        },\n        {\n            title: \"Login\",\n            href: \"/login\"\n        }\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/config/site.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var _auth_mongodb_adapter__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @auth/mongodb-adapter */ \"(rsc)/./node_modules/.pnpm/@auth+mongodb-adapter@3.4.2_mongodb@6.8.1_nodemailer@6.9.16/node_modules/@auth/mongodb-adapter/index.js\");\n/* harmony import */ var next_auth_providers_email__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/email */ \"(rsc)/./node_modules/.pnpm/next-auth@4.24.7_next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1__nodemailer@6.9.16_r_cn7rdjlltjmhlsi5xxj5pwkivm/node_modules/next-auth/providers/email.js\");\n/* harmony import */ var _db_db__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./db/db */ \"(rsc)/./src/lib/db/db.ts\");\n/* harmony import */ var _email_sendLoginMail__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./email/sendLoginMail */ \"(rsc)/./src/lib/email/sendLoginMail.tsx\");\n/* harmony import */ var _config_site__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/config/site */ \"(rsc)/./src/config/site.ts\");\n\n\n\n\n\nconst authOptions = {\n    adapter: (0,_auth_mongodb_adapter__WEBPACK_IMPORTED_MODULE_0__.MongoDBAdapter)(_db_db__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        databaseName: \"AgapeEngine\"\n    }),\n    secret: process.env.AUTH_SECRET,\n    pages: {\n        error: \"/login\",\n        verifyRequest: \"/auth/verify-request\",\n        signIn: \"/login\"\n    },\n    theme: {\n        colorScheme: \"light\",\n        logo: \"/favicon.ico\",\n        buttonText: \"#ffffff\",\n        brandColor: \"#000000\"\n    },\n    providers: [\n        (0,next_auth_providers_email__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            sendVerificationRequest: _email_sendLoginMail__WEBPACK_IMPORTED_MODULE_3__.sendVerificationRequest,\n            server: {\n                host: process.env.EMAIL_SERVER_HOST,\n                port: Number(process.env.EMAIL_SERVER_PORT),\n                auth: {\n                    user: process.env.EMAIL_SERVER_USER,\n                    pass: process.env.EMAIL_SERVER_PASSWORD\n                }\n            },\n            from: `${_config_site__WEBPACK_IMPORTED_MODULE_4__.siteConfig.name} <${process.env.EMAIL_FROM}>`\n        })\n    ],\n    callbacks: {\n        session: async ({ session, user })=>{\n            if (session?.user) {\n                //@ts-ignore\n                session.user.id = user.id;\n            }\n            return session;\n        },\n        async signIn ({ user }) {\n            if (!user.joinedAt) {\n                user.joinedAt = new Date();\n            }\n            if (!user.username) {\n                user.username = \"\";\n            }\n            if (!user.bio) {\n                user.bio = \"\";\n            }\n            return true;\n        }\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db/db.ts":
/*!**************************!*\
  !*** ./src/lib/db/db.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongodb__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongodb */ \"mongodb\");\n/* harmony import */ var mongodb__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongodb__WEBPACK_IMPORTED_MODULE_0__);\n// This approach is taken from https://github.com/vercel/next.js/tree/canary/examples/with-mongodb\n\nif (!process.env.MONGODB_URI) {\n    throw new Error('Invalid/Missing environment variable: \"MONGODB_URI\"');\n}\nconst uri = process.env.MONGODB_URI;\nconst options = {\n    serverApi: {\n        version: mongodb__WEBPACK_IMPORTED_MODULE_0__.ServerApiVersion.v1,\n        strict: true,\n        deprecationErrors: true\n    }\n};\nlet client;\nlet clientPromise;\nif (true) {\n    // In development mode, use a global variable so that the value\n    // is preserved across module reloads caused by HMR (Hot Module Replacement).\n    let globalWithMongo = global;\n    if (!globalWithMongo._mongoClientPromise) {\n        client = new mongodb__WEBPACK_IMPORTED_MODULE_0__.MongoClient(uri, options);\n        globalWithMongo._mongoClientPromise = client.connect();\n    }\n    clientPromise = globalWithMongo._mongoClientPromise;\n} else {}\n// Export a module-scoped MongoClient promise. By doing this in a\n// separate module, the client can be shared across functions.\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (clientPromise);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db/db.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/email/sendLoginMail.tsx":
/*!*****************************************!*\
  !*** ./src/lib/email/sendLoginMail.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   sendVerificationRequest: () => (/* binding */ sendVerificationRequest)\n/* harmony export */ });\n/* harmony import */ var nodemailer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! nodemailer */ \"(rsc)/./node_modules/.pnpm/nodemailer@6.9.16/node_modules/nodemailer/lib/nodemailer.js\");\n\nasync function sendVerificationRequest({ ...params }) {\n    const { identifier, url, provider, theme } = params;\n    const { host } = new URL(url);\n    const transport = (0,nodemailer__WEBPACK_IMPORTED_MODULE_0__.createTransport)(provider.server);\n    const result = await transport.sendMail({\n        to: identifier,\n        from: provider.from,\n        subject: `Sign in to ${host}`,\n        text: text({\n            url,\n            host\n        }),\n        html: html({\n            url,\n            host,\n            theme\n        })\n    });\n    const failed = result.rejected.concat(result.pending).filter(Boolean);\n    if (failed.length) {\n        throw new Error(`Email(s) (${failed.join(\", \")}) could not be sent`);\n    }\n}\n/**\r\n * Email HTML body\r\n * Insert invisible space into domains from being turned into a hyperlink by email\r\n * clients like Outlook and Apple mail, as this is confusing because it seems\r\n * like they are supposed to click on it to sign in.\r\n *\r\n * @note We don't add the email address to avoid needing to escape it, if you do, remember to sanitize it!\r\n */ function html(params) {\n    const { url, host, theme } = params;\n    // const escapedHost = host.replace(/\\./g, '&#8203;.');\n    const brandColor = theme.brandColor || \"#346df1\";\n    const color = {\n        background: \"#f9f9f9\",\n        text: \"#444\",\n        mainBackground: \"#fff\",\n        buttonBackground: brandColor,\n        buttonBorder: brandColor,\n        buttonText: theme.buttonText || \"#fff\"\n    };\n    return `\r\n<body style=\"background: ${color.background};\">\r\n  <table width=\"100%\" border=\"0\" cellspacing=\"20\" cellpadding=\"0\"\r\n    style=\"background: ${color.mainBackground}; max-width: 600px; margin: auto; border-radius: 10px;\">\r\n    <tr>\r\n      <td align=\"center\"\r\n        style=\"padding: 10px 0px; font-size: 22px; font-family: Helvetica, Arial, sans-serif; color: ${color.text};\">\r\n        Sign in to <strong>Agape Engine</strong>\r\n      </td>\r\n    </tr>\r\n    <tr>\r\n      <td align=\"center\" style=\"padding: 20px 0;\">\r\n        <table border=\"0\" cellspacing=\"0\" cellpadding=\"0\">\r\n          <tr>\r\n            <td align=\"center\" style=\"border-radius: 5px;\" bgcolor=\"${color.buttonBackground}\"><a href=\"${url}\"\r\n                target=\"_blank\"\r\n                style=\"font-size: 18px; font-family: Helvetica, Arial, sans-serif; color: ${color.buttonText}; text-decoration: none; border-radius: 5px; padding: 10px 20px; border: 1px solid ${color.buttonBorder}; display: inline-block; font-weight: bold;\">Login now</a></td>\r\n          </tr>\r\n        </table>\r\n      </td>\r\n    </tr>\r\n    <tr>\r\n      <td align=\"center\"\r\n        style=\"padding: 0px 0px 10px 0px; font-size: 16px; line-height: 22px; font-family: Helvetica, Arial, sans-serif; color: ${color.text};\">\r\n        If you did not request this email you can safely ignore it.\r\n      </td>\r\n    </tr>\r\n  </table>\r\n</body>\r\n`;\n}\n/** Email Text body (fallback for email clients that don't render HTML, e.g. feature phones) */ function text({ url, host }) {\n    return `Sign in to ${host}\\n${url}\\n\\n`;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2VtYWlsL3NlbmRMb2dpbk1haWwudHN4IiwibWFwcGluZ3MiOiI7Ozs7O0FBQTZDO0FBT3RDLGVBQWVDLHdCQUF3QixFQUM1QyxHQUFHQyxRQUMyQjtJQUM5QixNQUFNLEVBQUVDLFVBQVUsRUFBRUMsR0FBRyxFQUFFQyxRQUFRLEVBQUVDLEtBQUssRUFBRSxHQUFHSjtJQUM3QyxNQUFNLEVBQUVLLElBQUksRUFBRSxHQUFHLElBQUlDLElBQUlKO0lBQ3pCLE1BQU1LLFlBQVlULDJEQUFlQSxDQUFDSyxTQUFTSyxNQUFNO0lBQ2pELE1BQU1DLFNBQVMsTUFBTUYsVUFBVUcsUUFBUSxDQUFDO1FBQ3RDQyxJQUFJVjtRQUNKVyxNQUFNVCxTQUFTUyxJQUFJO1FBQ25CQyxTQUFTLENBQUMsV0FBVyxFQUFFUixLQUFLLENBQUM7UUFDN0JTLE1BQU1BLEtBQUs7WUFBRVo7WUFBS0c7UUFBSztRQUN2QlUsTUFBTUEsS0FBSztZQUFFYjtZQUFLRztZQUFNRDtRQUFNO0lBQ2hDO0lBQ0EsTUFBTVksU0FBU1AsT0FBT1EsUUFBUSxDQUFDQyxNQUFNLENBQUNULE9BQU9VLE9BQU8sRUFBRUMsTUFBTSxDQUFDQztJQUM3RCxJQUFJTCxPQUFPTSxNQUFNLEVBQUU7UUFDakIsTUFBTSxJQUFJQyxNQUFNLENBQUMsVUFBVSxFQUFFUCxPQUFPUSxJQUFJLENBQUMsTUFBTSxtQkFBbUIsQ0FBQztJQUNyRTtBQUNGO0FBRUE7Ozs7Ozs7Q0FPQyxHQUNELFNBQVNULEtBQUtmLE1BQW1EO0lBQy9ELE1BQU0sRUFBRUUsR0FBRyxFQUFFRyxJQUFJLEVBQUVELEtBQUssRUFBRSxHQUFHSjtJQUU3Qix1REFBdUQ7SUFFdkQsTUFBTXlCLGFBQWFyQixNQUFNcUIsVUFBVSxJQUFJO0lBQ3ZDLE1BQU1DLFFBQVE7UUFDWkMsWUFBWTtRQUNaYixNQUFNO1FBQ05jLGdCQUFnQjtRQUNoQkMsa0JBQWtCSjtRQUNsQkssY0FBY0w7UUFDZE0sWUFBWTNCLE1BQU0yQixVQUFVLElBQUk7SUFDbEM7SUFFQSxPQUFPLENBQUM7eUJBQ2UsRUFBRUwsTUFBTUMsVUFBVSxDQUFDOzt1QkFFckIsRUFBRUQsTUFBTUUsY0FBYyxDQUFDOzs7cUdBR3VELEVBQUVGLE1BQU1aLElBQUksQ0FBQzs7Ozs7Ozs7b0VBUTlDLEVBQUVZLE1BQU1HLGdCQUFnQixDQUFDLFdBQVcsRUFBRTNCLElBQUk7OzBGQUVwQixFQUFFd0IsTUFBTUssVUFBVSxDQUFDLG1GQUFtRixFQUFFTCxNQUFNSSxZQUFZLENBQUM7Ozs7Ozs7Z0lBT3JGLEVBQUVKLE1BQU1aLElBQUksQ0FBQzs7Ozs7O0FBTTdJLENBQUM7QUFDRDtBQUVBLDZGQUE2RixHQUM3RixTQUFTQSxLQUFLLEVBQUVaLEdBQUcsRUFBRUcsSUFBSSxFQUFpQztJQUN4RCxPQUFPLENBQUMsV0FBVyxFQUFFQSxLQUFLLEVBQUUsRUFBRUgsSUFBSSxJQUFJLENBQUM7QUFDekMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hZ2FwZS1uZXctZnJvbnRlbmQvLi9zcmMvbGliL2VtYWlsL3NlbmRMb2dpbk1haWwudHN4P2Y3OTkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlVHJhbnNwb3J0IH0gZnJvbSBcIm5vZGVtYWlsZXJcIjtcclxuaW1wb3J0IHsgU2VuZFZlcmlmaWNhdGlvblJlcXVlc3RQYXJhbXMgfSBmcm9tIFwibmV4dC1hdXRoL3Byb3ZpZGVycy9lbWFpbFwiO1xyXG50eXBlIFRoZW1lID0ge1xyXG4gIGJyYW5kQ29sb3I/OiBzdHJpbmc7XHJcbiAgYnV0dG9uVGV4dD86IHN0cmluZztcclxufTtcclxuXHJcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBzZW5kVmVyaWZpY2F0aW9uUmVxdWVzdCh7XHJcbiAgLi4ucGFyYW1zXHJcbn06IFNlbmRWZXJpZmljYXRpb25SZXF1ZXN0UGFyYW1zKSB7XHJcbiAgY29uc3QgeyBpZGVudGlmaWVyLCB1cmwsIHByb3ZpZGVyLCB0aGVtZSB9ID0gcGFyYW1zO1xyXG4gIGNvbnN0IHsgaG9zdCB9ID0gbmV3IFVSTCh1cmwpO1xyXG4gIGNvbnN0IHRyYW5zcG9ydCA9IGNyZWF0ZVRyYW5zcG9ydChwcm92aWRlci5zZXJ2ZXIpO1xyXG4gIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHRyYW5zcG9ydC5zZW5kTWFpbCh7XHJcbiAgICB0bzogaWRlbnRpZmllcixcclxuICAgIGZyb206IHByb3ZpZGVyLmZyb20sXHJcbiAgICBzdWJqZWN0OiBgU2lnbiBpbiB0byAke2hvc3R9YCxcclxuICAgIHRleHQ6IHRleHQoeyB1cmwsIGhvc3QgfSksXHJcbiAgICBodG1sOiBodG1sKHsgdXJsLCBob3N0LCB0aGVtZSB9KSxcclxuICB9KTtcclxuICBjb25zdCBmYWlsZWQgPSByZXN1bHQucmVqZWN0ZWQuY29uY2F0KHJlc3VsdC5wZW5kaW5nKS5maWx0ZXIoQm9vbGVhbik7XHJcbiAgaWYgKGZhaWxlZC5sZW5ndGgpIHtcclxuICAgIHRocm93IG5ldyBFcnJvcihgRW1haWwocykgKCR7ZmFpbGVkLmpvaW4oXCIsIFwiKX0pIGNvdWxkIG5vdCBiZSBzZW50YCk7XHJcbiAgfVxyXG59XHJcblxyXG4vKipcclxuICogRW1haWwgSFRNTCBib2R5XHJcbiAqIEluc2VydCBpbnZpc2libGUgc3BhY2UgaW50byBkb21haW5zIGZyb20gYmVpbmcgdHVybmVkIGludG8gYSBoeXBlcmxpbmsgYnkgZW1haWxcclxuICogY2xpZW50cyBsaWtlIE91dGxvb2sgYW5kIEFwcGxlIG1haWwsIGFzIHRoaXMgaXMgY29uZnVzaW5nIGJlY2F1c2UgaXQgc2VlbXNcclxuICogbGlrZSB0aGV5IGFyZSBzdXBwb3NlZCB0byBjbGljayBvbiBpdCB0byBzaWduIGluLlxyXG4gKlxyXG4gKiBAbm90ZSBXZSBkb24ndCBhZGQgdGhlIGVtYWlsIGFkZHJlc3MgdG8gYXZvaWQgbmVlZGluZyB0byBlc2NhcGUgaXQsIGlmIHlvdSBkbywgcmVtZW1iZXIgdG8gc2FuaXRpemUgaXQhXHJcbiAqL1xyXG5mdW5jdGlvbiBodG1sKHBhcmFtczogeyB1cmw6IHN0cmluZzsgaG9zdDogc3RyaW5nOyB0aGVtZTogVGhlbWUgfSkge1xyXG4gIGNvbnN0IHsgdXJsLCBob3N0LCB0aGVtZSB9ID0gcGFyYW1zO1xyXG5cclxuICAvLyBjb25zdCBlc2NhcGVkSG9zdCA9IGhvc3QucmVwbGFjZSgvXFwuL2csICcmIzgyMDM7LicpO1xyXG5cclxuICBjb25zdCBicmFuZENvbG9yID0gdGhlbWUuYnJhbmRDb2xvciB8fCBcIiMzNDZkZjFcIjtcclxuICBjb25zdCBjb2xvciA9IHtcclxuICAgIGJhY2tncm91bmQ6IFwiI2Y5ZjlmOVwiLFxyXG4gICAgdGV4dDogXCIjNDQ0XCIsXHJcbiAgICBtYWluQmFja2dyb3VuZDogXCIjZmZmXCIsXHJcbiAgICBidXR0b25CYWNrZ3JvdW5kOiBicmFuZENvbG9yLFxyXG4gICAgYnV0dG9uQm9yZGVyOiBicmFuZENvbG9yLFxyXG4gICAgYnV0dG9uVGV4dDogdGhlbWUuYnV0dG9uVGV4dCB8fCBcIiNmZmZcIixcclxuICB9O1xyXG5cclxuICByZXR1cm4gYFxyXG48Ym9keSBzdHlsZT1cImJhY2tncm91bmQ6ICR7Y29sb3IuYmFja2dyb3VuZH07XCI+XHJcbiAgPHRhYmxlIHdpZHRoPVwiMTAwJVwiIGJvcmRlcj1cIjBcIiBjZWxsc3BhY2luZz1cIjIwXCIgY2VsbHBhZGRpbmc9XCIwXCJcclxuICAgIHN0eWxlPVwiYmFja2dyb3VuZDogJHtjb2xvci5tYWluQmFja2dyb3VuZH07IG1heC13aWR0aDogNjAwcHg7IG1hcmdpbjogYXV0bzsgYm9yZGVyLXJhZGl1czogMTBweDtcIj5cclxuICAgIDx0cj5cclxuICAgICAgPHRkIGFsaWduPVwiY2VudGVyXCJcclxuICAgICAgICBzdHlsZT1cInBhZGRpbmc6IDEwcHggMHB4OyBmb250LXNpemU6IDIycHg7IGZvbnQtZmFtaWx5OiBIZWx2ZXRpY2EsIEFyaWFsLCBzYW5zLXNlcmlmOyBjb2xvcjogJHtjb2xvci50ZXh0fTtcIj5cclxuICAgICAgICBTaWduIGluIHRvIDxzdHJvbmc+QWdhcGUgRW5naW5lPC9zdHJvbmc+XHJcbiAgICAgIDwvdGQ+XHJcbiAgICA8L3RyPlxyXG4gICAgPHRyPlxyXG4gICAgICA8dGQgYWxpZ249XCJjZW50ZXJcIiBzdHlsZT1cInBhZGRpbmc6IDIwcHggMDtcIj5cclxuICAgICAgICA8dGFibGUgYm9yZGVyPVwiMFwiIGNlbGxzcGFjaW5nPVwiMFwiIGNlbGxwYWRkaW5nPVwiMFwiPlxyXG4gICAgICAgICAgPHRyPlxyXG4gICAgICAgICAgICA8dGQgYWxpZ249XCJjZW50ZXJcIiBzdHlsZT1cImJvcmRlci1yYWRpdXM6IDVweDtcIiBiZ2NvbG9yPVwiJHtjb2xvci5idXR0b25CYWNrZ3JvdW5kfVwiPjxhIGhyZWY9XCIke3VybH1cIlxyXG4gICAgICAgICAgICAgICAgdGFyZ2V0PVwiX2JsYW5rXCJcclxuICAgICAgICAgICAgICAgIHN0eWxlPVwiZm9udC1zaXplOiAxOHB4OyBmb250LWZhbWlseTogSGVsdmV0aWNhLCBBcmlhbCwgc2Fucy1zZXJpZjsgY29sb3I6ICR7Y29sb3IuYnV0dG9uVGV4dH07IHRleHQtZGVjb3JhdGlvbjogbm9uZTsgYm9yZGVyLXJhZGl1czogNXB4OyBwYWRkaW5nOiAxMHB4IDIwcHg7IGJvcmRlcjogMXB4IHNvbGlkICR7Y29sb3IuYnV0dG9uQm9yZGVyfTsgZGlzcGxheTogaW5saW5lLWJsb2NrOyBmb250LXdlaWdodDogYm9sZDtcIj5Mb2dpbiBub3c8L2E+PC90ZD5cclxuICAgICAgICAgIDwvdHI+XHJcbiAgICAgICAgPC90YWJsZT5cclxuICAgICAgPC90ZD5cclxuICAgIDwvdHI+XHJcbiAgICA8dHI+XHJcbiAgICAgIDx0ZCBhbGlnbj1cImNlbnRlclwiXHJcbiAgICAgICAgc3R5bGU9XCJwYWRkaW5nOiAwcHggMHB4IDEwcHggMHB4OyBmb250LXNpemU6IDE2cHg7IGxpbmUtaGVpZ2h0OiAyMnB4OyBmb250LWZhbWlseTogSGVsdmV0aWNhLCBBcmlhbCwgc2Fucy1zZXJpZjsgY29sb3I6ICR7Y29sb3IudGV4dH07XCI+XHJcbiAgICAgICAgSWYgeW91IGRpZCBub3QgcmVxdWVzdCB0aGlzIGVtYWlsIHlvdSBjYW4gc2FmZWx5IGlnbm9yZSBpdC5cclxuICAgICAgPC90ZD5cclxuICAgIDwvdHI+XHJcbiAgPC90YWJsZT5cclxuPC9ib2R5PlxyXG5gO1xyXG59XHJcblxyXG4vKiogRW1haWwgVGV4dCBib2R5IChmYWxsYmFjayBmb3IgZW1haWwgY2xpZW50cyB0aGF0IGRvbid0IHJlbmRlciBIVE1MLCBlLmcuIGZlYXR1cmUgcGhvbmVzKSAqL1xyXG5mdW5jdGlvbiB0ZXh0KHsgdXJsLCBob3N0IH06IHsgdXJsOiBzdHJpbmc7IGhvc3Q6IHN0cmluZyB9KSB7XHJcbiAgcmV0dXJuIGBTaWduIGluIHRvICR7aG9zdH1cXG4ke3VybH1cXG5cXG5gO1xyXG59XHJcbiJdLCJuYW1lcyI6WyJjcmVhdGVUcmFuc3BvcnQiLCJzZW5kVmVyaWZpY2F0aW9uUmVxdWVzdCIsInBhcmFtcyIsImlkZW50aWZpZXIiLCJ1cmwiLCJwcm92aWRlciIsInRoZW1lIiwiaG9zdCIsIlVSTCIsInRyYW5zcG9ydCIsInNlcnZlciIsInJlc3VsdCIsInNlbmRNYWlsIiwidG8iLCJmcm9tIiwic3ViamVjdCIsInRleHQiLCJodG1sIiwiZmFpbGVkIiwicmVqZWN0ZWQiLCJjb25jYXQiLCJwZW5kaW5nIiwiZmlsdGVyIiwiQm9vbGVhbiIsImxlbmd0aCIsIkVycm9yIiwiam9pbiIsImJyYW5kQ29sb3IiLCJjb2xvciIsImJhY2tncm91bmQiLCJtYWluQmFja2dyb3VuZCIsImJ1dHRvbkJhY2tncm91bmQiLCJidXR0b25Cb3JkZXIiLCJidXR0b25UZXh0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/email/sendLoginMail.tsx\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1","vendor-chunks/nodemailer@6.9.16","vendor-chunks/next-auth@4.24.7_next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1__nodemailer@6.9.16_r_cn7rdjlltjmhlsi5xxj5pwkivm","vendor-chunks/jose@4.15.9","vendor-chunks/openid-client@5.6.5","vendor-chunks/@babel+runtime@7.25.4","vendor-chunks/oauth@0.9.15","vendor-chunks/@auth+mongodb-adapter@3.4.2_mongodb@6.8.1_nodemailer@6.9.16","vendor-chunks/object-hash@2.2.0","vendor-chunks/preact@10.11.3","vendor-chunks/uuid@8.3.2","vendor-chunks/yallist@4.0.0","vendor-chunks/lru-cache@6.0.0","vendor-chunks/preact-render-to-string@5.2.3_preact@10.11.3","vendor-chunks/cookie@0.5.0","vendor-chunks/oidc-token-hash@5.0.3","vendor-chunks/@panva+hkdf@1.2.1"], () => (__webpack_exec__("(rsc)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=G%3A%5Cagape%5Cagape-newFrontend%5Cweb%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5Cagape%5Cagape-newFrontend%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();