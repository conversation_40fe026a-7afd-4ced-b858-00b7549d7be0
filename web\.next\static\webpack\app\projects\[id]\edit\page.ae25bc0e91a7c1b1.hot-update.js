"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/projects/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/ArtistTool/Environment.tsx":
/*!***************************************************!*\
  !*** ./src/components/ArtistTool/Environment.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Environment: function() { return /* binding */ Environment; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/Environment.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! three */ \"(app-pages-browser)/./node_modules/.pnpm/three@0.167.1/node_modules/three/build/three.module.js\");\n\nvar _s = $RefreshSig$();\n\n\n\nfunction Environment(param) {\n    let { preset = \"sunset\", background = false, bgColor = \"#000000\", customHdri = null, intensity = 1, envRotation = 0, blur = 0.1 } = param;\n    _s();\n    const [backgroundColor, setBackgroundColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (bgColor) {\n            setBackgroundColor(new three__WEBPACK_IMPORTED_MODULE_2__.Color(bgColor));\n        }\n    }, [\n        bgColor\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            backgroundColor && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"color\", {\n                attach: \"background\",\n                args: [\n                    backgroundColor\n                ]\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Environment.tsx\",\n                lineNumber: 57,\n                columnNumber: 9\n            }, this),\n            preset !== \"none\" && (customHdri ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_3__.Environment, {\n                files: customHdri,\n                background: background,\n                environmentIntensity: intensity,\n                environmentRotation: [\n                    0,\n                    envRotation,\n                    0\n                ],\n                blur: background ? blur : 0\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Environment.tsx\",\n                lineNumber: 62,\n                columnNumber: 11\n            }, this) : preset === \"hdri_15\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_3__.Environment, {\n                files: \"/hdris/hdri_15.hdr\",\n                background: background,\n                environmentIntensity: intensity,\n                environmentRotation: [\n                    0,\n                    envRotation,\n                    0\n                ],\n                blur: background ? blur : 0\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Environment.tsx\",\n                lineNumber: 70,\n                columnNumber: 11\n            }, this) : preset === \"hdri_metal\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_3__.Environment, {\n                files: \"/hdris/hdri_metal.exr\",\n                background: background,\n                environmentIntensity: intensity,\n                environmentRotation: [\n                    0,\n                    envRotation,\n                    0\n                ],\n                blur: background ? blur : 0\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Environment.tsx\",\n                lineNumber: 78,\n                columnNumber: 11\n            }, this) : preset === \"hdri_metal2\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_3__.Environment, {\n                files: \"/hdris/metal_hdri2.hdr\",\n                background: background,\n                environmentIntensity: intensity,\n                environmentRotation: [\n                    0,\n                    envRotation,\n                    0\n                ],\n                blur: background ? blur : 0\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Environment.tsx\",\n                lineNumber: 86,\n                columnNumber: 11\n            }, this) : preset === \"hdri_metal3\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_3__.Environment, {\n                files: \"/hdris/metal_hdri3.hdr\",\n                background: background,\n                environmentIntensity: intensity,\n                environmentRotation: [\n                    0,\n                    envRotation,\n                    0\n                ],\n                blur: background ? blur : 0\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Environment.tsx\",\n                lineNumber: 94,\n                columnNumber: 11\n            }, this) : preset === \"hdri_gem\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_3__.Environment, {\n                files: \"/hdris/env_gem_002_30251392af.exr\",\n                background: background,\n                environmentIntensity: intensity,\n                environmentRotation: [\n                    0,\n                    envRotation,\n                    0\n                ],\n                blur: background ? blur : 0\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Environment.tsx\",\n                lineNumber: 102,\n                columnNumber: 11\n            }, this) : preset === \"studio_small\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_3__.Environment, {\n                files: \"/hdris/studio_small_02_2k.hdr\",\n                background: background,\n                environmentIntensity: intensity,\n                environmentRotation: [\n                    0,\n                    envRotation,\n                    0\n                ],\n                blur: background ? blur : 0\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Environment.tsx\",\n                lineNumber: 110,\n                columnNumber: 11\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_3__.Environment, {\n                preset: preset,\n                background: background,\n                environmentIntensity: intensity,\n                environmentRotation: [\n                    0,\n                    envRotation,\n                    0\n                ],\n                blur: background ? blur : 0\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Environment.tsx\",\n                lineNumber: 118,\n                columnNumber: 11\n            }, this))\n        ]\n    }, void 0, true);\n}\n_s(Environment, \"8dfVRfhsIeo/uEzTOufHhYu5Oto=\");\n_c = Environment;\nvar _c;\n$RefreshReg$(_c, \"Environment\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ArtistTool/Environment.tsx\n"));

/***/ })

});