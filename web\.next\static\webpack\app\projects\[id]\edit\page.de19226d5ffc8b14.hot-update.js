"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/projects/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/ArtistTool/EditorExperience.jsx":
/*!********************************************************!*\
  !*** ./src/components/ArtistTool/EditorExperience.jsx ***!
  \********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ EditorExperience; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/web/Html.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/web/Loader.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/OrbitControls.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/TransformControls.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/MeshReflectorMaterial.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/GizmoHelper.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/GizmoViewport.js\");\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @react-three/fiber */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_three@0.167.1/node_modules/@react-three/fiber/dist/events-776716bd.esm.js\");\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @react-three/fiber */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_three@0.167.1/node_modules/@react-three/fiber/dist/react-three-fiber.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/.pnpm/react-toastify@11.0.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/react-toastify/dist/index.mjs\");\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! three */ \"(app-pages-browser)/./node_modules/.pnpm/three@0.167.1/node_modules/three/build/three.module.js\");\n/* harmony import */ var _Environment__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Environment */ \"(app-pages-browser)/./src/components/ArtistTool/Environment.tsx\");\n/* harmony import */ var _EnvironmentPanel__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./EnvironmentPanel */ \"(app-pages-browser)/./src/components/ArtistTool/EnvironmentPanel.jsx\");\n/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Header */ \"(app-pages-browser)/./src/components/ArtistTool/Header.jsx\");\n/* harmony import */ var _LightPanel__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./LightPanel */ \"(app-pages-browser)/./src/components/ArtistTool/LightPanel.jsx\");\n/* harmony import */ var _MaterialPanel__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./MaterialPanel */ \"(app-pages-browser)/./src/components/ArtistTool/MaterialPanel.jsx\");\n/* harmony import */ var _ModelStats__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./ModelStats */ \"(app-pages-browser)/./src/components/ArtistTool/ModelStats.jsx\");\n/* harmony import */ var _Outliner__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./Outliner */ \"(app-pages-browser)/./src/components/ArtistTool/Outliner.jsx\");\n/* harmony import */ var _PostProcessingPanel__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./PostProcessingPanel */ \"(app-pages-browser)/./src/components/ArtistTool/PostProcessingPanel.jsx\");\n/* harmony import */ var _SceneLights__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./SceneLights */ \"(app-pages-browser)/./src/components/ArtistTool/SceneLights.jsx\");\n/* harmony import */ var _Toolbar__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./Toolbar */ \"(app-pages-browser)/./src/components/ArtistTool/Toolbar.jsx\");\n/* harmony import */ var _TransformPanel__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./TransformPanel */ \"(app-pages-browser)/./src/components/ArtistTool/TransformPanel.jsx\");\n/* harmony import */ var _utils_ArtistTool_cameraUtils__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../../utils/ArtistTool/cameraUtils */ \"(app-pages-browser)/./src/utils/ArtistTool/cameraUtils.js\");\n/* harmony import */ var _utils_ArtistTool_historyUtils__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../../utils/ArtistTool/historyUtils */ \"(app-pages-browser)/./src/utils/ArtistTool/historyUtils.js\");\n/* harmony import */ var _utils_ArtistTool_materialUtils__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../../utils/ArtistTool/materialUtils */ \"(app-pages-browser)/./src/utils/ArtistTool/materialUtils.js\");\n/* harmony import */ var _utils_ArtistTool_sceneUtils__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ../../utils/ArtistTool/sceneUtils */ \"(app-pages-browser)/./src/utils/ArtistTool/sceneUtils.js\");\n/* harmony import */ var _animations_CameraAnimations__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./animations/CameraAnimations */ \"(app-pages-browser)/./src/components/ArtistTool/animations/CameraAnimations.js\");\n/* harmony import */ var _EngravingPanel__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./EngravingPanel */ \"(app-pages-browser)/./src/components/ArtistTool/EngravingPanel.jsx\");\n/* harmony import */ var _InitialOverlayPanel__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./InitialOverlayPanel */ \"(app-pages-browser)/./src/components/ArtistTool/InitialOverlayPanel.jsx\");\n/* harmony import */ var _ModelStage__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./ModelStage */ \"(app-pages-browser)/./src/components/ArtistTool/ModelStage.jsx\");\n/* harmony import */ var _PostProcessingEffects__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ./PostProcessingEffects */ \"(app-pages-browser)/./src/components/ArtistTool/PostProcessingEffects.jsx\");\n/* harmony import */ var _SelectionOutline__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! ./SelectionOutline */ \"(app-pages-browser)/./src/components/ArtistTool/SelectionOutline.jsx\");\n/* harmony import */ var _lib_actions_version_actions__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/lib/actions/version.actions */ \"(app-pages-browser)/./src/lib/actions/version.actions.ts\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/.pnpm/sonner@1.5.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _lib_states__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @/lib/states */ \"(app-pages-browser)/./src/lib/states.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst ScreenshotTaker = (param)=>{\n    let { onScreenshotRef } = param;\n    _s();\n    const { gl, scene, camera } = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_27__.D)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (typeof onScreenshotRef === \"function\") {\n            onScreenshotRef(()=>{\n                try {\n                    // Render the scene\n                    gl.render(scene, camera);\n                    // Get the canvas data URL\n                    const dataURL = gl.domElement.toDataURL(\"image/png\");\n                    // Create and trigger a download\n                    const link = document.createElement(\"a\");\n                    const timestamp = new Date().toISOString().replace(/:/g, \"-\").substring(0, 19);\n                    link.download = \"agape-scene-\".concat(timestamp, \".png\");\n                    link.href = dataURL;\n                    document.body.appendChild(link);\n                    link.click();\n                    // Clean up\n                    setTimeout(()=>{\n                        document.body.removeChild(link);\n                    }, 100);\n                    console.log(\"Screenshot captured successfully\");\n                    return true;\n                } catch (error) {\n                    console.error(\"Error capturing screenshot:\", error);\n                    return false;\n                }\n            });\n        }\n    }, [\n        gl,\n        scene,\n        camera,\n        onScreenshotRef\n    ]);\n    return null;\n};\n_s(ScreenshotTaker, \"RVTFHuczdlyvGFUsZJPSpjurI/Y=\", false, function() {\n    return [\n        _react_three_fiber__WEBPACK_IMPORTED_MODULE_27__.D\n    ];\n});\n_c = ScreenshotTaker;\n// Component to provide renderer to MaterialPanel\nconst MaterialPanelWrapper = (param)=>{\n    let { renderer, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MaterialPanel__WEBPACK_IMPORTED_MODULE_7__.MaterialPanel, {\n        renderer: renderer,\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n        lineNumber: 96,\n        columnNumber: 10\n    }, undefined);\n};\n_c1 = MaterialPanelWrapper;\n// Component to get renderer from Canvas context\nconst RendererProvider = (param)=>{\n    let { children, onRendererReady } = param;\n    _s1();\n    const { gl } = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_27__.D)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (onRendererReady) {\n            onRendererReady(gl);\n        }\n    }, [\n        gl,\n        onRendererReady\n    ]);\n    return null;\n};\n_s1(RendererProvider, \"iTWbOa64ZxlA5nuyNExgWK+vjCI=\", false, function() {\n    return [\n        _react_three_fiber__WEBPACK_IMPORTED_MODULE_27__.D\n    ];\n});\n_c2 = RendererProvider;\nfunction EditorExperience(param) {\n    let { modelUrl, project, user } = param;\n    _s2();\n    const { addVersion } = (0,_lib_states__WEBPACK_IMPORTED_MODULE_26__.useVersionState)();\n    const configRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const workingConfigRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [selectedModel, setSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"uploaded\");\n    const [lastSelectedModel, setLastSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [uploadedModel, setUploadedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(modelUrl || null);\n    const [modelStats, setModelStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        vertices: 0,\n        triangles: 0,\n        materials: 0,\n        animations: 0\n    });\n    const [lights, setLights] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: \"defaultDirectional\",\n            type: \"directional\",\n            position: [\n                2.5,\n                5.5,\n                5\n            ],\n            intensity: 1.0,\n            color: \"#ffffff\",\n            name: \"Main Directional Light\",\n            castShadow: true,\n            helperVisible: false\n        },\n        {\n            id: \"keyLight\",\n            type: \"point\",\n            position: [\n                5,\n                5,\n                5\n            ],\n            intensity: 8.0,\n            color: \"#ffffff\",\n            name: \"Key Light\"\n        },\n        {\n            id: \"fillLight\",\n            type: \"point\",\n            position: [\n                -5,\n                5,\n                -5\n            ],\n            intensity: 4.0,\n            color: \"#ffffff\",\n            name: \"Fill Light\"\n        },\n        {\n            id: \"backLight\",\n            type: \"point\",\n            position: [\n                0,\n                5,\n                -5\n            ],\n            intensity: 2.0,\n            color: \"#ffffff\",\n            name: \"Back Light\"\n        }\n    ]);\n    const lightRefs = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({});\n    const [selectedObjects, setSelectedObjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedLight, setSelectedLight] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [lightPositionHistory, setLightPositionHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [sceneObjects, setSceneObjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [transformMode, setTransformMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"translate\");\n    const selectionGroupRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(new three__WEBPACK_IMPORTED_MODULE_28__.Group());\n    console.log(\"renderingggg editor experience\");\n    const [rightPanelTab, setRightPanelTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Decal placement state\n    const [isPlacingDecal, setIsPlacingDecal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showDecalDebug, setShowDecalDebug] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Scene controls state\n    const [showGrid, setShowGrid] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [wireframe, setWireframe] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [groundType, setGroundType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"none\");\n    const [envPreset, setEnvPreset] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"hdri_15\");\n    const [bgColor, setBgColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"#272631\");\n    const [showEnvironment, setShowEnvironment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showLightSpheres, setShowLightSpheres] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [customHdri, setCustomHdri] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [envIntensity, setEnvIntensity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1.85);\n    const [envBlur, setEnvBlur] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0.35);\n    const [envRotation, setEnvRotation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [postProcessingEnabled, setPostProcessingEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [postProcessingSettings, setPostProcessingSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        autofocus: {\n            enabled: false,\n            bokehScale: 18\n        },\n        bloom: {\n            enabled: true,\n            intensity: 0.22,\n            threshold: 1,\n            radius: 0.5\n        },\n        dof: {\n            enabled: false,\n            focusDistance: 0,\n            aperture: 0.01,\n            bokehScale: 3\n        },\n        vignette: {\n            enabled: true,\n            darkness: 0.5,\n            offset: 0.1\n        }\n    });\n    // Store initial state\n    const initialStateRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        selectedModel: null,\n        lights: [],\n        envPreset: null,\n        bgColor: null,\n        postProcessingSettings: null,\n        showGrid: null,\n        wireframe: null,\n        groundType: null,\n        showEnvironment: null,\n        showLightSpheres: null,\n        envIntensity: null,\n        envBlur: null,\n        cameraPosition: null,\n        cameraTarget: null\n    });\n    // Add a ref to store the initial state of model objects\n    const modelInitialStateRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        objects: []\n    });\n    // Capture initial state when component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        initialStateRef.current = {\n            selectedModel,\n            lights: [\n                ...lights\n            ],\n            envPreset,\n            bgColor,\n            postProcessingSettings: {\n                ...postProcessingSettings\n            },\n            showGrid,\n            wireframe,\n            groundType,\n            showEnvironment,\n            showLightSpheres,\n            envIntensity,\n            envBlur,\n            cameraPosition: new three__WEBPACK_IMPORTED_MODULE_28__.Vector3(5, 2, 5),\n            cameraTarget: new three__WEBPACK_IMPORTED_MODULE_28__.Vector3(0, 1, 0)\n        };\n    }, []);\n    const orbitControlsRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const sceneRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const [renderer, setRenderer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // History state for undo/redo\n    const [history, setHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [historyIndex, setHistoryIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(-1);\n    const isUndoRedoActionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const skipHistoryTrackingRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const isUndoRedoInProgressRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // Define forceUpdate and version setters early so they can be used in restoreConfig\n    const [_, setForceUpdate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const forceUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>setForceUpdate((f)=>f + 1), []);\n    const [transformVersion, setTransformVersion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [materialVersion, setMaterialVersion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const pendingSelectedObjectsUUIDs = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    function getFullConfig() {\n        return JSON.parse(JSON.stringify({\n            selectedModel,\n            uploadedModel,\n            lights,\n            envPreset,\n            bgColor,\n            showEnvironment,\n            showLightSpheres,\n            customHdri,\n            envIntensity,\n            envBlur,\n            envRotation,\n            postProcessingEnabled,\n            postProcessingSettings,\n            showGrid,\n            wireframe,\n            groundType,\n            selectedObjects: selectedObjects.map((obj)=>obj.uuid),\n            // Store object states for transforms and materials\n            objectStates: sceneObjects.map((obj)=>({\n                    uuid: obj.uuid,\n                    name: obj.name,\n                    position: obj.position.toArray(),\n                    rotation: obj.rotation.toArray(),\n                    scale: obj.scale.toArray(),\n                    material: obj.material ? {\n                        type: obj.material.type,\n                        color: obj.material.color ? obj.material.color.getHexString() : \"ffffff\",\n                        roughness: obj.material.roughness,\n                        metalness: obj.material.metalness,\n                        clearcoat: obj.material.clearcoat,\n                        clearcoatRoughness: obj.material.clearcoatRoughness,\n                        wireframe: obj.material.wireframe,\n                        transparent: obj.material.transparent,\n                        opacity: obj.material.opacity,\n                        envMapIntensity: obj.material.envMapIntensity\n                    } : null\n                }))\n        }));\n    }\n    const restoreConfig = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((config)=>{\n        setSelectedModel(config.selectedModel);\n        setUploadedModel(config.uploadedModel);\n        setLights(config.lights);\n        setEnvPreset(config.envPreset);\n        setBgColor(config.bgColor);\n        setShowEnvironment(config.showEnvironment);\n        setShowLightSpheres(config.showLightSpheres);\n        setCustomHdri(config.customHdri);\n        setEnvIntensity(config.envIntensity);\n        setEnvBlur(config.envBlur);\n        setEnvRotation(config.envRotation);\n        setPostProcessingEnabled(config.postProcessingEnabled);\n        setPostProcessingSettings(config.postProcessingSettings);\n        setShowGrid(config.showGrid);\n        setWireframe(config.wireframe);\n        setGroundType(config.groundType);\n        // Restore object states (transforms and materials)\n        if (config.objectStates && sceneObjects.length > 0) {\n            config.objectStates.forEach((savedState)=>{\n                const obj = sceneObjects.find((o)=>o.uuid === savedState.uuid);\n                if (obj) {\n                    // Restore transform\n                    obj.position.fromArray(savedState.position);\n                    obj.rotation.fromArray(savedState.rotation);\n                    obj.scale.fromArray(savedState.scale);\n                    // Restore material properties\n                    if (savedState.material && obj.material) {\n                        const material = obj.material;\n                        if (material.color) {\n                            material.color.set(savedState.material.color);\n                        }\n                        if (savedState.material.roughness !== undefined) {\n                            material.roughness = savedState.material.roughness;\n                        }\n                        if (savedState.material.metalness !== undefined) {\n                            material.metalness = savedState.material.metalness;\n                        }\n                        if (savedState.material.clearcoat !== undefined) {\n                            material.clearcoat = savedState.material.clearcoat;\n                        }\n                        if (savedState.material.clearcoatRoughness !== undefined) {\n                            material.clearcoatRoughness = savedState.material.clearcoatRoughness;\n                        }\n                        if (savedState.material.wireframe !== undefined) {\n                            material.wireframe = savedState.material.wireframe;\n                        }\n                        if (savedState.material.transparent !== undefined) {\n                            material.transparent = savedState.material.transparent;\n                        }\n                        if (savedState.material.opacity !== undefined) {\n                            material.opacity = savedState.material.opacity;\n                        }\n                        if (savedState.material.envMapIntensity !== undefined) {\n                            material.envMapIntensity = savedState.material.envMapIntensity;\n                        }\n                        material.needsUpdate = true;\n                    }\n                }\n            });\n            // Force update to reflect changes\n            forceUpdate();\n            // Don't increment versions during restore to avoid triggering history loop\n            if (!isUndoRedoActionRef.current) {\n                setMaterialVersion((v)=>v + 1);\n                setTransformVersion((v)=>v + 1);\n            }\n        }\n        // Store selectedObjects UUIDs to be restored after sceneObjects update\n        pendingSelectedObjectsUUIDs.current = config.selectedObjects || [];\n    // console.log(\"[restoreConfig] Restoring config\", config);\n    }, [\n        sceneObjects,\n        forceUpdate,\n        setMaterialVersion,\n        setTransformVersion\n    ]);\n    // After sceneObjects update, restore selectedObjects from UUIDs\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (pendingSelectedObjectsUUIDs.current && sceneObjects.length > 0) {\n            const uuids = pendingSelectedObjectsUUIDs.current;\n            const newSelected = sceneObjects.filter((obj)=>uuids.includes(obj.uuid));\n            setSelectedObjects(newSelected);\n            pendingSelectedObjectsUUIDs.current = null;\n        // console.log(\"[restoreConfig] Restored selectedObjects\", newSelected);\n        }\n    }, [\n        sceneObjects\n    ]);\n    const undoAction = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        // Prevent rapid undo operations\n        if (isUndoRedoInProgressRef.current) {\n            // console.log(\"[undoAction] Skipping - undo/redo already in progress\");\n            return;\n        }\n        if (historyIndex > 0) {\n            // Set all flags to prevent interference\n            isUndoRedoInProgressRef.current = true;\n            isUndoRedoActionRef.current = true;\n            skipHistoryTrackingRef.current = true;\n            const prevConfig = history[historyIndex - 1];\n            restoreConfig(prevConfig);\n            setHistoryIndex(historyIndex - 1);\n            // Reset flags after operation completes\n            setTimeout(()=>{\n                isUndoRedoActionRef.current = false;\n                skipHistoryTrackingRef.current = false;\n                isUndoRedoInProgressRef.current = false;\n            }, 1000);\n        }\n    }, [\n        historyIndex,\n        history,\n        restoreConfig\n    ]);\n    const redoAction = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        // Prevent rapid redo operations\n        if (isUndoRedoInProgressRef.current) {\n            // console.log(\"[redoAction] Skipping - undo/redo already in progress\");\n            return;\n        }\n        if (historyIndex < history.length - 1) {\n            // Set all flags to prevent interference\n            isUndoRedoInProgressRef.current = true;\n            isUndoRedoActionRef.current = true;\n            skipHistoryTrackingRef.current = true;\n            const nextConfig = history[historyIndex + 1];\n            restoreConfig(nextConfig);\n            setHistoryIndex(historyIndex + 1);\n            // Reset flags after operation completes\n            setTimeout(()=>{\n                isUndoRedoActionRef.current = false;\n                skipHistoryTrackingRef.current = false;\n                isUndoRedoInProgressRef.current = false;\n            }, 1000);\n        }\n    }, [\n        historyIndex,\n        history,\n        restoreConfig\n    ]);\n    const addToHistory = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (isUndoRedoActionRef.current || skipHistoryTrackingRef.current) {\n            // console.log(\"[addToHistory] Skipping - undo/redo action in progress\");\n            return;\n        }\n        const newState = getFullConfig();\n        // Check if the new state is different from the current state\n        if (history.length > 0 && historyIndex >= 0) {\n            const currentState = history[historyIndex];\n            if (JSON.stringify(currentState) === JSON.stringify(newState)) {\n                // console.log(\"[addToHistory] Skipping - state unchanged\");\n                return;\n            }\n        }\n        const newHistory = history.slice(0, historyIndex + 1);\n        setHistory([\n            ...newHistory,\n            newState\n        ]);\n        const newIndex = historyIndex + 1;\n        setHistoryIndex(newIndex);\n    }, [\n        history,\n        historyIndex,\n        getFullConfig\n    ]);\n    // Track changes to objects and lights for history\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const trackChanges = ()=>{\n            if (selectedObjects.length > 0 || lights.length > 0) {\n                addToHistory();\n            }\n        };\n        // Debounce to avoid too many history entries\n        const timeoutId = setTimeout(trackChanges, 500);\n        return ()=>clearTimeout(timeoutId);\n    }, [\n        selectedObjects,\n        lights,\n        addToHistory\n    ]);\n    // Use a ref to track the last state to prevent unnecessary history entries\n    const lastStateRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handler = setTimeout(()=>{\n            if (!isUndoRedoActionRef.current && !skipHistoryTrackingRef.current) {\n                // Only add to history if state actually changed\n                const currentState = JSON.stringify({\n                    selectedModel,\n                    envPreset,\n                    bgColor,\n                    showEnvironment,\n                    showLightSpheres,\n                    envIntensity,\n                    envBlur,\n                    envRotation,\n                    postProcessingEnabled,\n                    showGrid,\n                    wireframe,\n                    groundType,\n                    transformVersion,\n                    materialVersion,\n                    lightsLength: lights.length\n                });\n                if (lastStateRef.current !== currentState) {\n                    lastStateRef.current = currentState;\n                    addToHistory();\n                }\n            }\n        }, 300);\n        return ()=>clearTimeout(handler);\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        selectedModel,\n        envPreset,\n        bgColor,\n        showEnvironment,\n        showLightSpheres,\n        envIntensity,\n        envBlur,\n        envRotation,\n        postProcessingEnabled,\n        showGrid,\n        wireframe,\n        groundType,\n        transformVersion,\n        materialVersion,\n        lights.length\n    ]);\n    const handleObjectClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e)=>{\n        e.stopPropagation();\n        // If placing decal, let the decal placement handler do its job (triggered by model's onClick)\n        if (isPlacingDecal) {\n            return;\n        }\n        console.log(\"Object clicked:\", e.object.name, e.object);\n        if (e.shiftKey) {\n            setSelectedObjects((prev)=>{\n                // Ensure prev is always an array\n                const prevArray = Array.isArray(prev) ? prev : [\n                    prev\n                ].filter(Boolean);\n                if (prevArray.includes(e.object)) {\n                    return prevArray.filter((obj)=>obj !== e.object);\n                }\n                return [\n                    ...prevArray,\n                    e.object\n                ];\n            });\n        } else {\n            setSelectedObjects([\n                e.object\n            ]);\n        }\n    }, [\n        isPlacingDecal,\n        setSelectedObjects\n    ]);\n    // Add debugging for selectedObjects changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"ExperienceNew - selectedObjects changed:\", selectedObjects);\n        console.log(\"ExperienceNew - selectedObjects length:\", selectedObjects === null || selectedObjects === void 0 ? void 0 : selectedObjects.length);\n    }, [\n        selectedObjects\n    ]);\n    // Add debugging for sceneObjects changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"ExperienceNew - sceneObjects changed:\", sceneObjects);\n        console.log(\"ExperienceNew - sceneObjects length:\", sceneObjects === null || sceneObjects === void 0 ? void 0 : sceneObjects.length);\n        console.log(\"ExperienceNew - sceneObjects details:\", sceneObjects === null || sceneObjects === void 0 ? void 0 : sceneObjects.map((obj)=>({\n                name: obj === null || obj === void 0 ? void 0 : obj.name,\n                type: obj === null || obj === void 0 ? void 0 : obj.type,\n                userData: obj === null || obj === void 0 ? void 0 : obj.userData\n            })));\n    }, [\n        sceneObjects\n    ]);\n    const handlePointerMissed = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        // If in decal placement mode, a \"missed\" click (on the canvas background)\n        // should probably cancel placement mode, or do nothing to allow model click-through.\n        // For now, let's prevent it from deselecting objects if placing decal.\n        if (isPlacingDecal) {\n            // setIsPlacingDecal(false); // Optionally, cancel placement on missed click\n            return;\n        }\n        setSelectedObjects([]);\n        setSelectedLight(null);\n    // Do not close the rightPanelTab here, as that's usually handled by clicking outside the panel itself.\n    }, [\n        isPlacingDecal,\n        setIsPlacingDecal\n    ]);\n    // Add the selection group to the scene\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // This effect runs once to add the selection group to the scene\n        return ()=>{\n            // Clean up on unmount\n            if (selectionGroupRef.current && selectionGroupRef.current.parent) {\n                selectionGroupRef.current.parent.remove(selectionGroupRef.current);\n            }\n        };\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Ensure selectedObjects is always an array\n        const objectsArray = Array.isArray(selectedObjects) ? selectedObjects : [\n            selectedObjects\n        ].filter(Boolean);\n        while(selectionGroupRef.current.children.length > 0){\n            selectionGroupRef.current.remove(selectionGroupRef.current.children[0]);\n        }\n        if (objectsArray.length > 0) {\n            const center = new three__WEBPACK_IMPORTED_MODULE_28__.Vector3();\n            objectsArray.forEach((obj)=>{\n                center.add(obj.position);\n            });\n            center.divideScalar(objectsArray.length);\n            selectionGroupRef.current.position.copy(center);\n            objectsArray.forEach((obj)=>{\n                const clone = obj.clone();\n                // Make the clone invisible while keeping its geometry for transforms\n                clone.traverse((child)=>{\n                    if (child.isMesh) {\n                        child.visible = false;\n                    }\n                });\n                clone.position.sub(center);\n                selectionGroupRef.current.add(clone);\n            });\n        }\n    }, [\n        selectedObjects\n    ]);\n    const updateObjectsFromGroup = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        // Ensure selectedObjects is always an array\n        const objectsArray = Array.isArray(selectedObjects) ? selectedObjects : [\n            selectedObjects\n        ].filter(Boolean);\n        if (objectsArray.length > 0 && selectionGroupRef.current) {\n            objectsArray.forEach((obj, index)=>{\n                if (selectionGroupRef.current.children[index]) {\n                    const worldPos = new three__WEBPACK_IMPORTED_MODULE_28__.Vector3();\n                    selectionGroupRef.current.children[index].getWorldPosition(worldPos);\n                    obj.position.copy(worldPos);\n                    obj.rotation.copy(selectionGroupRef.current.rotation);\n                    obj.scale.copy(selectionGroupRef.current.scale);\n                }\n            });\n        }\n    }, [\n        selectedObjects\n    ]);\n    const handleSceneUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((scene)=>{\n        var _scene_children;\n        console.log(\"handleSceneUpdate called with scene:\", scene);\n        console.log(\"Scene type:\", scene.type);\n        console.log(\"Scene children count:\", (_scene_children = scene.children) === null || _scene_children === void 0 ? void 0 : _scene_children.length);\n        setSceneObjects([]);\n        setSelectedObjects([]);\n        const stats = (0,_utils_ArtistTool_sceneUtils__WEBPACK_IMPORTED_MODULE_17__.calculateModelStats)(scene);\n        setModelStats(stats);\n        (0,_utils_ArtistTool_sceneUtils__WEBPACK_IMPORTED_MODULE_17__.setShadowsOnModel)(scene);\n        const objects = [];\n        const initialObjects = [];\n        scene.traverse((object)=>{\n            console.log(\"Traversing object:\", object.name, object.type, \"isMesh:\", object.isMesh, \"userData:\", object.userData);\n            if (object.isMesh) {\n                if (!object.name) {\n                    object.name = \"Mesh_\".concat(object.id);\n                }\n                console.log(\"Adding mesh to objects:\", object.name, \"userData:\", object.userData);\n                objects.push(object);\n                initialObjects.push({\n                    id: object.uuid,\n                    name: object.name,\n                    position: object.position.clone(),\n                    rotation: object.rotation.clone(),\n                    scale: object.scale.clone(),\n                    material: object.material ? {\n                        color: object.material.color ? object.material.color.getHex() : 0xffffff,\n                        roughness: object.material.roughness,\n                        metalness: object.material.metalness,\n                        wireframe: object.material.wireframe,\n                        transparent: object.material.transparent,\n                        opacity: object.material.opacity\n                    } : null\n                });\n            }\n        });\n        console.log(\"Found\", objects.length, \"mesh objects:\", objects.map((obj)=>({\n                name: obj.name,\n                userData: obj.userData\n            })));\n        modelInitialStateRef.current.objects = initialObjects;\n        setSceneObjects(objects);\n    }, []);\n    // Add this effect to detect model changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedModel !== lastSelectedModel) {\n            // Clear the scene objects when model changes\n            setSceneObjects([]);\n            setSelectedObjects([]);\n            setLastSelectedModel(selectedModel);\n        }\n    }, [\n        selectedModel,\n        lastSelectedModel\n    ]);\n    const handleLightMoved = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((lightId, newPosition)=>{\n        setLightPositionHistory((prev)=>({\n                ...prev,\n                [lightId]: [\n                    ...prev[lightId] || [],\n                    newPosition\n                ]\n            }));\n    }, []);\n    const undoLightMove = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((lightId)=>{\n        setLightPositionHistory((prev)=>{\n            const history = prev[lightId] || [];\n            if (history.length <= 1) return prev;\n            const newHistory = history.slice(0, -1);\n            const lastPosition = newHistory[newHistory.length - 1];\n            if (lightRefs.current[lightId]) {\n                lightRefs.current[lightId].position.copy(lastPosition);\n            }\n            return {\n                ...prev,\n                [lightId]: newHistory\n            };\n        });\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleKeyDown = (event)=>{\n            // Handle undo/redo keyboard shortcuts\n            if (event.ctrlKey) {\n                if (event.key === \"z\") {\n                    event.preventDefault();\n                    undoAction();\n                    return;\n                }\n                if (event.key === \"y\") {\n                    event.preventDefault();\n                    redoAction();\n                    return;\n                }\n            }\n            // Handle Escape key for decal placement\n            if (event.key === \"Escape\" && isPlacingDecal) {\n                setIsPlacingDecal(false);\n                return;\n            }\n            // Handle light undo\n            if (event.ctrlKey && event.key === \"z\" && selectedLight) {\n                undoLightMove(selectedLight);\n            }\n            // Handle transform mode shortcuts\n            if (selectedObjects.length > 0) {\n                switch(event.key){\n                    case \"g\":\n                        setTransformMode(\"translate\");\n                        break;\n                    case \"r\":\n                        setTransformMode(\"rotate\");\n                        break;\n                    case \"s\":\n                        setTransformMode(\"scale\");\n                        break;\n                    case \"Escape\":\n                        setSelectedObjects([]);\n                        break;\n                }\n            }\n            // Handle select all\n            if (event.ctrlKey && event.key === \"a\") {\n                event.preventDefault();\n                if (sceneObjects.length > 0) {\n                    setSelectedObjects([\n                        ...sceneObjects\n                    ]);\n                }\n            }\n        };\n        window.addEventListener(\"keydown\", handleKeyDown);\n        return ()=>window.removeEventListener(\"keydown\", handleKeyDown);\n    }, [\n        selectedLight,\n        undoLightMove,\n        sceneObjects,\n        selectedObjects.length,\n        undoAction,\n        redoAction,\n        isPlacingDecal,\n        setIsPlacingDecal\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        return ()=>{\n            if (uploadedModel) {\n                URL.revokeObjectURL(uploadedModel);\n            }\n            if (customHdri) {\n                URL.revokeObjectURL(customHdri);\n            }\n        };\n    }, [\n        uploadedModel,\n        customHdri\n    ]);\n    const handleCameraView = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((view)=>{\n        (0,_utils_ArtistTool_cameraUtils__WEBPACK_IMPORTED_MODULE_14__.setCameraView)(orbitControlsRef.current, view);\n    }, []);\n    // Add the panelRef to ExperienceNew instead of in Toolbar\n    const panelRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [activeAnimation, setActiveAnimation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [animationStartTime, setAnimationStartTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const requestRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    // Animation loop handler\n    const animateCamera = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((timestamp)=>{\n        if (!activeAnimation || !orbitControlsRef.current) {\n            return;\n        }\n        // Calculate animation progress (0 to 1)\n        const animation = _animations_CameraAnimations__WEBPACK_IMPORTED_MODULE_18__.predefinedAnimations.find((a)=>a.id === activeAnimation);\n        if (!animation) return;\n        if (animationStartTime === 0) {\n            setAnimationStartTime(timestamp);\n        }\n        const elapsed = timestamp - animationStartTime;\n        const progress = elapsed % animation.duration / animation.duration;\n        // Get the target position (center of selected objects or scene center)\n        const target = new three__WEBPACK_IMPORTED_MODULE_28__.Vector3(0, 0, 0);\n        if (selectedObjects.length > 0) {\n            // Calculate center of selected objects\n            const objectsArray = Array.isArray(selectedObjects) ? selectedObjects : [\n                selectedObjects\n            ].filter(Boolean);\n            if (objectsArray.length > 0) {\n                const center = new three__WEBPACK_IMPORTED_MODULE_28__.Vector3();\n                objectsArray.forEach((obj)=>{\n                    center.add(obj.position);\n                });\n                center.divideScalar(objectsArray.length);\n                target.copy(center);\n            }\n        }\n        // Animate camera using the animation function\n        animation.animate(orbitControlsRef.current.object, orbitControlsRef.current, progress, target);\n        requestRef.current = requestAnimationFrame(animateCamera);\n    }, [\n        activeAnimation,\n        orbitControlsRef,\n        animationStartTime,\n        selectedObjects\n    ]);\n    // Setup animation loop\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (activeAnimation) {\n            setAnimationStartTime(0);\n            requestRef.current = requestAnimationFrame(animateCamera);\n            // Disable orbit controls during animation\n            if (orbitControlsRef.current) {\n                orbitControlsRef.current.enabled = false;\n            }\n        } else {\n            // Re-enable orbit controls when animation stops\n            if (orbitControlsRef.current && selectedObjects.length === 0) {\n                orbitControlsRef.current.enabled = true;\n            }\n        }\n        return ()=>{\n            if (requestRef.current) {\n                cancelAnimationFrame(requestRef.current);\n            }\n        };\n    }, [\n        activeAnimation,\n        animateCamera,\n        selectedObjects\n    ]);\n    // Handle animation playback from Outliner\n    const handlePlayAnimation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((animationId)=>{\n        setActiveAnimation(animationId);\n    }, []);\n    const getEnvironmentPreset = ()=>{\n        return envPreset;\n    };\n    // Reset function\n    const handleReset = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        // Check if there's a saved scene in localStorage\n        const savedScene = localStorage.getItem(\"agape_saved_scene\");\n        if (savedScene) {\n            const confirmReset = window.confirm(\"There is a saved scene in your browser storage. Resetting will clear this saved scene. Do you want to continue?\");\n            if (!confirmReset) {\n                return;\n            }\n            // Clear the saved scene from localStorage\n            localStorage.removeItem(\"agape_saved_scene\");\n        }\n        console.log(\"Resetting scene to initial state\");\n        setSceneObjects([]);\n        setSelectedObjects([]);\n        // Reset lights to default configuration\n        setLights([\n            {\n                id: \"defaultDirectional\",\n                type: \"directional\",\n                position: [\n                    2.5,\n                    5.5,\n                    5\n                ],\n                intensity: 1.0,\n                color: \"#ffffff\",\n                name: \"Main Directional Light\",\n                castShadow: true,\n                helperVisible: false\n            },\n            {\n                id: \"keyLight\",\n                type: \"point\",\n                position: [\n                    5,\n                    5,\n                    5\n                ],\n                intensity: 8.0,\n                color: \"#ffffff\",\n                name: \"Key Light\"\n            },\n            {\n                id: \"fillLight\",\n                type: \"point\",\n                position: [\n                    -5,\n                    5,\n                    -5\n                ],\n                intensity: 4.0,\n                color: \"#ffffff\",\n                name: \"Fill Light\"\n            },\n            {\n                id: \"backLight\",\n                type: \"point\",\n                position: [\n                    0,\n                    5,\n                    -5\n                ],\n                intensity: 2.0,\n                color: \"#ffffff\",\n                name: \"Back Light\"\n            }\n        ]);\n        // Reset environment settings\n        setEnvPreset(\"hdri_15\");\n        setBgColor(\"#202020\");\n        setShowEnvironment(false);\n        setShowLightSpheres(true);\n        setCustomHdri(null);\n        setEnvIntensity(1.5);\n        setEnvBlur(0.35);\n        setEnvRotation(0);\n        // Reset post-processing settings\n        setPostProcessingEnabled(false);\n        setPostProcessingSettings({\n            autofocus: {\n                enabled: false,\n                bokehScale: 18\n            },\n            bloom: {\n                enabled: true,\n                intensity: 0.12,\n                threshold: 0.8,\n                radius: 0.4\n            },\n            dof: {\n                enabled: false,\n                focusDistance: 0,\n                aperture: 0.01,\n                bokehScale: 3\n            },\n            vignette: {\n                enabled: true,\n                darkness: 0.5,\n                offset: 0.1\n            }\n        });\n        // Reset scene controls\n        setShowGrid(true);\n        setWireframe(false);\n        setGroundType(\"none\");\n        // Reset camera position\n        if (orbitControlsRef.current) {\n            orbitControlsRef.current.object.position.set(5, 2, 5);\n            orbitControlsRef.current.target.set(0, 1, 0);\n            orbitControlsRef.current.update();\n        }\n        // Reset transform state\n        setTransformMode(\"translate\");\n        setSelectedLight(null);\n        // Reset material shaders\n        setUseDiamondShader(false);\n        setUsePremiumWhiteGold(false);\n        setUsePremiumRoseGold(false);\n        // Clear history\n        setHistory([]);\n        setHistoryIndex(-1);\n        setLightPositionHistory({});\n        // Reset model stats\n        setModelStats({\n            vertices: 0,\n            triangles: 0,\n            materials: 0,\n            animations: 0\n        });\n        // Reset right panel tab\n        setRightPanelTab(null);\n        // Reset active animation\n        setActiveAnimation(null);\n        setAnimationStartTime(0);\n        if (requestRef.current) {\n            cancelAnimationFrame(requestRef.current);\n        }\n        // Clear any custom HDRI or uploaded model URLs\n        if (uploadedModel) {\n            URL.revokeObjectURL(uploadedModel);\n        }\n        if (customHdri) {\n            URL.revokeObjectURL(customHdri);\n        }\n        // Force a model reload by setting selectedModel to null first\n        setSelectedModel(null);\n        // Then set it back to uploaded after a small delay\n        setTimeout(()=>{\n            setSelectedModel(\"uploaded\");\n        }, 100);\n    }, []);\n    const screenshotRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const captureScreenshot = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (screenshotRef.current) {\n            console.log(\"Taking screenshot\");\n            screenshotRef.current();\n        } else {\n            console.error(\"Screenshot function not available yet\");\n        }\n    }, []);\n    // Load saved scene from localStorage on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadSavedScene = ()=>{\n            const savedScene = localStorage.getItem(\"agape_saved_scene\");\n            if (savedScene) {\n                try {\n                    const sceneConfig = JSON.parse(savedScene);\n                    console.log(\"Loading saved scene:\", sceneConfig);\n                    // Only load the saved configuration on initial load\n                    if (!lastSelectedModel) {\n                        console.log(\"Initial load - setting saved model:\", sceneConfig.model);\n                        setSelectedModel(\"uploaded\"); // Always use uploaded for S3 models\n                        // Load the rest of the saved scene configuration\n                        setLights(sceneConfig.lights);\n                        setEnvPreset(sceneConfig.environment.preset);\n                        setBgColor(sceneConfig.environment.bgColor);\n                        setShowEnvironment(sceneConfig.environment.showEnvironment);\n                        setShowLightSpheres(sceneConfig.environment.showLightSpheres);\n                        setEnvIntensity(sceneConfig.environment.intensity);\n                        setEnvBlur(sceneConfig.environment.blur);\n                        setEnvRotation(sceneConfig.environment.rotation);\n                        setPostProcessingEnabled(sceneConfig.postProcessing.enabled);\n                        setPostProcessingSettings(sceneConfig.postProcessing.settings);\n                        setShowGrid(sceneConfig.sceneControls.showGrid);\n                        setWireframe(sceneConfig.sceneControls.wireframe);\n                        setGroundType(sceneConfig.sceneControls.groundType);\n                        // Restore camera position after a short delay to ensure scene is loaded\n                        setTimeout(()=>{\n                            if (orbitControlsRef.current && sceneConfig.camera) {\n                                orbitControlsRef.current.object.position.fromArray(sceneConfig.camera.position);\n                                orbitControlsRef.current.target.fromArray(sceneConfig.camera.target);\n                                orbitControlsRef.current.update();\n                            }\n                        }, 1000);\n                    } else if (configRef.current) {\n                        // For explicit loads with configRef, do a complete load\n                        console.log(\"Explicit load with configRef\");\n                        handleLoadScene(sceneConfig);\n                    }\n                    // Create a function to restore materials\n                    const restoreMaterials = ()=>{\n                        if (!sceneConfig.materials || !sceneObjects.length) {\n                            console.log(\"No materials to restore or scene objects not ready\");\n                            return;\n                        }\n                        console.log(\"Attempting to restore materials for objects:\", sceneObjects);\n                        console.log(\"Saved materials:\", sceneConfig.materials);\n                        const savedMaterialsMap = new Map(sceneConfig.materials.map((m)=>[\n                                m.name,\n                                m.material\n                            ]));\n                        sceneObjects.forEach((obj)=>{\n                            if (obj.material && savedMaterialsMap.has(obj.name)) {\n                                const savedMaterial = savedMaterialsMap.get(obj.name);\n                                console.log(\"Restoring material for object:\", obj.name, savedMaterial);\n                                (0,_utils_ArtistTool_materialUtils__WEBPACK_IMPORTED_MODULE_16__.applyMaterialToObject)(obj, savedMaterial);\n                            } else {\n                                console.log(\"Could not find saved material for object:\", obj.name);\n                            }\n                        });\n                    };\n                    // Set up an interval to check for scene objects\n                    const checkInterval = setInterval(()=>{\n                        if (sceneObjects.length > 0) {\n                            clearInterval(checkInterval);\n                            // Add a small delay to ensure all materials are properly initialized\n                            setTimeout(restoreMaterials, 500);\n                        }\n                    }, 100);\n                    // Clean up interval after 5 seconds if scene objects never become available\n                    setTimeout(()=>{\n                        clearInterval(checkInterval);\n                    }, 5000);\n                    // Restore camera position after a short delay to ensure scene is loaded\n                    setTimeout(()=>{\n                        if (orbitControlsRef.current && sceneConfig.camera) {\n                            orbitControlsRef.current.object.position.fromArray(sceneConfig.camera.position);\n                            orbitControlsRef.current.target.fromArray(sceneConfig.camera.target);\n                            orbitControlsRef.current.update();\n                        }\n                    }, 1000);\n                } catch (error) {\n                    console.error(\"Error loading saved scene:\", error);\n                }\n            }\n        };\n        // Load saved scene immediately on mount\n        loadSavedScene();\n        // Also set up an interval to check for scene readiness\n        const checkSceneReady = setInterval(()=>{\n            if (sceneRef.current && orbitControlsRef.current) {\n                clearInterval(checkSceneReady);\n                loadSavedScene();\n            }\n        }, 100);\n        return ()=>clearInterval(checkSceneReady);\n    }, [\n        sceneObjects,\n        selectedModel\n    ]);\n    const [configState, setConfigState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (sceneObjects.length > 0 && configState && configState.materials) {\n            const applyMaterials = ()=>{\n                console.log(\"Applying materials to scene objects:\", sceneObjects);\n                console.log(\"Saved materials configuration:\", configState.materials);\n                sceneObjects.forEach((obj)=>{\n                    const materialConfig = configState.materials.find((m)=>m.name === obj.name || m.id === obj.uuid);\n                    if (materialConfig && materialConfig.material) {\n                        console.log(\"Found material config for object:\", obj.name, materialConfig.material);\n                        (0,_utils_ArtistTool_materialUtils__WEBPACK_IMPORTED_MODULE_16__.applyMaterialToObject)(obj, materialConfig.material);\n                    } else {\n                        console.log(\"No material config found for object:\", obj.name);\n                    }\n                });\n            };\n            // Add a small delay to ensure all materials are properly initialized\n            const timeoutId = setTimeout(applyMaterials, 500);\n            return ()=>clearTimeout(timeoutId);\n        }\n    }, [\n        sceneObjects,\n        configState\n    ]);\n    const handleLoadScene = (config)=>{\n        try {\n            console.log(\"Loading scene configuration:\", config);\n            // Store the config in the ref\n            configRef.current = config;\n            workingConfigRef.current = config; // Always keep this in sync with last applied config\n            setConfigState(config); // <-- keep in sync for effect\n            // Clear scene objects and selected objects first\n            setSceneObjects([]);\n            setSelectedObjects([]);\n            setLastSelectedModel(null);\n            // Restore scene settings\n            setSelectedModel(\"uploaded\");\n            setLights(config.lights);\n            setEnvPreset(config.environment.preset);\n            setBgColor(config.environment.bgColor);\n            setShowEnvironment(config.environment.showEnvironment);\n            setShowLightSpheres(config.environment.showLightSpheres);\n            setEnvIntensity(config.environment.intensity);\n            setEnvBlur(config.environment.blur);\n            setEnvRotation(config.environment.rotation);\n            setPostProcessingEnabled(config.postProcessing.enabled);\n            setPostProcessingSettings(config.postProcessing.settings);\n            setShowGrid(config.sceneControls.showGrid);\n            setWireframe(config.sceneControls.wireframe);\n            setGroundType(config.sceneControls.groundType);\n            // Wait for the scene to update before applying camera position\n            setTimeout(()=>{\n                if (orbitControlsRef.current && config.camera) {\n                    orbitControlsRef.current.object.position.fromArray(config.camera.position);\n                    orbitControlsRef.current.target.fromArray(config.camera.target);\n                    orbitControlsRef.current.update();\n                }\n            }, 1000);\n            // Restore materials after sceneObjects are ready\n            const restoreMaterials = ()=>{\n                if (!config.materials || !sceneObjects.length) {\n                    console.log(\"No materials to restore or scene objects not ready\");\n                    return;\n                }\n                console.log(\"Restoring materials for objects:\", sceneObjects);\n                const savedMaterialsMap = new Map(config.materials.map((m)=>[\n                        m.name,\n                        m.material\n                    ]));\n                sceneObjects.forEach((obj)=>{\n                    if (obj.material && savedMaterialsMap.has(obj.name)) {\n                        const savedMaterial = savedMaterialsMap.get(obj.name);\n                        console.log(\"Restoring material for object:\", obj.name, savedMaterial);\n                        (0,_utils_ArtistTool_materialUtils__WEBPACK_IMPORTED_MODULE_16__.applyMaterialToObject)(obj, savedMaterial);\n                    } else {\n                        console.log(\"Could not find saved material for object:\", obj.name);\n                    }\n                });\n            };\n            if (sceneObjects.length > 0) {\n                setTimeout(restoreMaterials, 500);\n            } else {\n                const checkInterval = setInterval(()=>{\n                    if (sceneObjects.length > 0) {\n                        clearInterval(checkInterval);\n                        setTimeout(restoreMaterials, 500);\n                    }\n                }, 100);\n                setTimeout(()=>{\n                    clearInterval(checkInterval);\n                }, 5000);\n            }\n        } catch (error) {\n            console.error(\"Error loading scene configuration:\", error);\n        }\n    };\n    const handleSaveScene = async (sceneName, description, saveType)=>{\n        // Use workingConfigRef if it exists and matches the current state, otherwise build from state\n        let sceneConfig;\n        if (workingConfigRef.current) {\n            // Use a shallow compare for top-level keys (or always use workingConfigRef for now)\n            sceneConfig = {\n                ...workingConfigRef.current,\n                name: sceneName,\n                timestamp: new Date().toISOString()\n            };\n        } else {\n            var _orbitControlsRef_current, _orbitControlsRef_current1;\n            // fallback to building from state\n            sceneConfig = {\n                name: sceneName,\n                timestamp: new Date().toISOString(),\n                model: selectedModel,\n                lights: lights.map((light)=>{\n                    var _lightRefs_current_light_id;\n                    return {\n                        ...light,\n                        position: ((_lightRefs_current_light_id = lightRefs.current[light.id]) === null || _lightRefs_current_light_id === void 0 ? void 0 : _lightRefs_current_light_id.position.toArray()) || light.position\n                    };\n                }),\n                environment: {\n                    preset: envPreset,\n                    intensity: envIntensity,\n                    blur: envBlur,\n                    rotation: envRotation,\n                    showEnvironment,\n                    bgColor,\n                    customHdri\n                },\n                postProcessing: {\n                    enabled: postProcessingEnabled,\n                    settings: postProcessingSettings\n                },\n                sceneControls: {\n                    showGrid,\n                    wireframe,\n                    groundType,\n                    showLightSpheres\n                },\n                camera: {\n                    position: (_orbitControlsRef_current = orbitControlsRef.current) === null || _orbitControlsRef_current === void 0 ? void 0 : _orbitControlsRef_current.object.position.toArray(),\n                    target: (_orbitControlsRef_current1 = orbitControlsRef.current) === null || _orbitControlsRef_current1 === void 0 ? void 0 : _orbitControlsRef_current1.target.toArray()\n                },\n                materials: sceneObjects.map((obj)=>{\n                    const material = obj.material;\n                    if (!material) return {\n                        id: obj.uuid,\n                        name: obj.name,\n                        material: null\n                    };\n                    return {\n                        id: obj.uuid,\n                        name: obj.name,\n                        material: {\n                            type: material.type,\n                            color: material.color ? material.color.getHexString() : \"ffffff\",\n                            roughness: material.roughness,\n                            metalness: material.metalness,\n                            wireframe: material.wireframe,\n                            transparent: material.transparent,\n                            opacity: material.opacity,\n                            envMapIntensity: material.envMapIntensity,\n                            ...material.type === \"MeshRefractionMaterial\" && {\n                                aberrationStrength: material.aberrationStrength,\n                                toneMapped: material.toneMapped,\n                                ior: material.ior,\n                                colorEnvMapRotY: material.colorEnvMapRotY\n                            }\n                        }\n                    };\n                })\n            };\n        }\n        if (saveType === \"local\") {\n            // Save to localStorage\n            localStorage.setItem(\"agape_saved_scene\", JSON.stringify(sceneConfig));\n        } else if (saveType === \"db\") {\n            // Save to DB (cloud)\n            try {\n                const version = await (0,_lib_actions_version_actions__WEBPACK_IMPORTED_MODULE_24__.createVersion)({\n                    userId: user.id,\n                    workspaceId: project._id,\n                    name: sceneName,\n                    config: sceneConfig,\n                    description: description\n                });\n                console.log(\"version\", version);\n                if (version) {\n                    addVersion(project._id, version);\n                }\n                sonner__WEBPACK_IMPORTED_MODULE_25__.toast.success(\"Scene saved to cloud!\");\n            } catch (err) {\n                sonner__WEBPACK_IMPORTED_MODULE_25__.toast.error(\"Failed to save scene to cloud\");\n            }\n        } else {\n            // Export as file\n            const blob = new Blob([\n                JSON.stringify(sceneConfig, null, 2)\n            ], {\n                type: \"application/json\"\n            });\n            const url = URL.createObjectURL(blob);\n            const a = document.createElement(\"a\");\n            a.href = url;\n            a.download = \"\".concat(sceneName.replace(/\\s+/g, \"_\"), \".json\");\n            document.body.appendChild(a);\n            a.click();\n            document.body.removeChild(a);\n            URL.revokeObjectURL(url);\n        }\n    };\n    // Engraving state\n    const [engravingText, setEngravingText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [decalPosition, setDecalPosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        0,\n        1.68,\n        0\n    ]);\n    const [decalScale, setDecalScale] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        2.8,\n        0.9,\n        1.5\n    ]);\n    const [decalRotation, setDecalRotation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        -Math.PI / 2,\n        0,\n        0\n    ]);\n    const [decalColor, setDecalColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"#cbcbcb\");\n    const [selectedFont, setSelectedFont] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"/fonts/ttf/RobotoSerif-Regular.ttf\");\n    // Diamond shader state\n    const [useDiamondShader, setUseDiamondShader] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [usePremiumWhiteGold, setUsePremiumWhiteGold] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [usePremiumRoseGold, setUsePremiumRoseGold] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleEngraveText = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((text)=>{\n        setEngravingText(text);\n        // Store in localStorage to make it persist across panel changes and page reloads\n        localStorage.setItem(\"engravingText\", text);\n    }, []);\n    const handleFontChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((font)=>{\n        setSelectedFont(font);\n        localStorage.setItem(\"engravingFont\", font);\n    }, []);\n    // Load saved engraving text on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const savedEngravingText = localStorage.getItem(\"engravingText\");\n        if (savedEngravingText) {\n            setEngravingText(savedEngravingText);\n        }\n    }, []);\n    const handleDecalPlacement = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((event)=>{\n        handleDecalPlacement(event, setIsPlacingDecal, setDecalPosition, setDecalRotation);\n    }, [\n        setDecalPosition,\n        setDecalRotation,\n        setIsPlacingDecal\n    ]);\n    // Get predefined decal positions based on selected model\n    const getDefaultDecalPosition = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((modelId)=>{\n        const modelConfigs = {\n            chosenring: [\n                0,\n                1.68,\n                0\n            ],\n            testring: [\n                0,\n                1.68,\n                0\n            ],\n            agapering1: [\n                0,\n                1.2,\n                0\n            ],\n            agapering2: [\n                0,\n                1.2,\n                0\n            ],\n            ringa: [\n                0,\n                0.26,\n                0\n            ],\n            ringb: [\n                0,\n                0.5,\n                0\n            ],\n            ringc: [\n                0,\n                0.26,\n                0\n            ]\n        };\n        return modelConfigs[modelId] || [\n            0,\n            1.68,\n            0\n        ];\n    }, []);\n    const getDefaultDecalRotation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((modelId)=>{\n        const modelConfigs = {\n            chosenring: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            testring: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            agapering1: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            agapering2: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            ringa: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            ringb: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            ringc: [\n                -Math.PI / 2,\n                0,\n                0\n            ]\n        };\n        return modelConfigs[modelId] || [\n            -Math.PI / 2,\n            0,\n            0\n        ];\n    }, []);\n    const getDefaultDecalScale = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((modelId)=>{\n        const modelConfigs = {\n            chosenring: [\n                6.0,\n                2.0,\n                1.5\n            ],\n            testring: [\n                6.0,\n                2.0,\n                1.5\n            ],\n            agapering1: [\n                3.2,\n                1.8,\n                1.0\n            ],\n            agapering2: [\n                3.2,\n                1.8,\n                1.0\n            ],\n            ringa: [\n                1.8,\n                2.0,\n                0.8\n            ],\n            ringb: [\n                1.8,\n                2.0,\n                0.8\n            ],\n            ringc: [\n                1.8,\n                2.0,\n                0.8\n            ]\n        };\n        return modelConfigs[modelId] || [\n            4.2,\n            2.0,\n            1.5\n        ];\n    }, []);\n    // Calculate dynamic scale based on text length and model type\n    const calculateDynamicScale = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((baseScale, text, modelId)=>{\n        if (!text || text.length === 0) return baseScale;\n        const textLength = text.length;\n        let [scaleX, scaleY, scaleZ] = baseScale;\n        // Calculate scale multiplier based on text length\n        let scaleMultiplier = 1.0;\n        if (textLength > 10) {\n            // For very long text, scale up more aggressively\n            scaleMultiplier = 1.3 + (textLength - 10) * 0.08;\n        } else if (textLength > 6) {\n            // For medium length text, scale up moderately\n            scaleMultiplier = 1.0 + (textLength - 6) * 0.08;\n        } else if (textLength < 4) {\n            // For short text, scale down slightly to prevent oversized appearance\n            scaleMultiplier = 0.8 + textLength * 0.05;\n        }\n        // Apply text length scaling to X-axis (width) primarily\n        scaleX = scaleX * scaleMultiplier;\n        // For very long text, also adjust the height slightly\n        if (textLength > 8) {\n            scaleY = scaleY * Math.min(1.2, 1.0 + (textLength - 8) * 0.02);\n        }\n        // Cap maximum scale to prevent excessive sizing (updated for larger Y values)\n        const maxScale = {\n            chosenring: [\n                12.0,\n                2.5,\n                2.0\n            ],\n            testring: [\n                12.0,\n                2.5,\n                2.0\n            ],\n            agapering1: [\n                6.5,\n                2.2,\n                1.5\n            ],\n            agapering2: [\n                6.5,\n                2.2,\n                1.5\n            ],\n            ringa: [\n                4.0,\n                2.5,\n                1.2\n            ],\n            ringb: [\n                4.0,\n                2.5,\n                1.2\n            ],\n            ringc: [\n                4.0,\n                2.5,\n                1.2\n            ]\n        };\n        const limits = maxScale[modelId] || [\n            8.5,\n            2.5,\n            2.0\n        ];\n        scaleX = Math.min(scaleX, limits[0]);\n        scaleY = Math.min(scaleY, limits[1]);\n        scaleZ = Math.min(scaleZ, limits[2]);\n        return [\n            scaleX,\n            scaleY,\n            scaleZ\n        ];\n    }, []);\n    // Update scale when text changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedModel && engravingText) {\n            const baseScale = getDefaultDecalScale(selectedModel);\n            const dynamicScale = calculateDynamicScale(baseScale, engravingText, selectedModel);\n            setDecalScale(dynamicScale);\n        }\n    }, [\n        engravingText,\n        selectedModel,\n        getDefaultDecalScale,\n        calculateDynamicScale\n    ]);\n    // Initialize decal position based on selected model\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedModel) {\n            setDecalPosition(getDefaultDecalPosition(selectedModel));\n            setDecalRotation(getDefaultDecalRotation(selectedModel));\n            // Only set initial scale if there's no engraving text yet\n            if (!engravingText) {\n                setDecalScale(getDefaultDecalScale(selectedModel));\n            }\n        }\n    }, [\n        selectedModel,\n        getDefaultDecalPosition,\n        getDefaultDecalRotation,\n        getDefaultDecalScale,\n        engravingText\n    ]);\n    const [isFullscreen, setIsFullscreen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Get camera position based on device type\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const checkMobile = ()=>{\n            setIsMobile(window.innerWidth < 768);\n        };\n        checkMobile();\n        window.addEventListener(\"resize\", checkMobile);\n        return ()=>window.removeEventListener(\"resize\", checkMobile);\n    }, []);\n    // Add state to control outliner collapsed state - initialize based on device type\n    const [outlinerCollapsed, setOutlinerCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        // Initialize based on device type - closed on mobile, open on desktop\n        if (true) {\n            return window.innerWidth < 768;\n        }\n        return false; // Default to open on server-side rendering\n    });\n    // Effect to close outliner when toolbar panels are opened on mobile\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isMobile && rightPanelTab) {\n            setOutlinerCollapsed(true);\n        }\n    }, [\n        isMobile,\n        rightPanelTab\n    ]);\n    // Effect to update outliner collapsed state when mobile state changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Update outliner state based on device type - closed on mobile, open on desktop\n        setOutlinerCollapsed(isMobile);\n    }, [\n        isMobile\n    ]);\n    const getCameraPosition = ()=>{\n        if (isMobile) {\n            return [\n                -1.85,\n                3.25,\n                4.5\n            ];\n        }\n        return [\n            -1.85,\n            1.55,\n            3.5\n        ];\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // On initial mount, fetch and apply the current version from DB\n        const fetchAndApplyCurrentVersion = async ()=>{\n            try {\n                const currentVersion = await (0,_lib_actions_version_actions__WEBPACK_IMPORTED_MODULE_24__.getCurrentVersion)({\n                    userId: user.id,\n                    workspaceId: project._id\n                });\n                if (currentVersion && currentVersion.config) {\n                    handleLoadScene(currentVersion.config);\n                }\n            } catch (err) {\n                console.error(\"Failed to load current version from DB\", err);\n            }\n        };\n        fetchAndApplyCurrentVersion();\n    // Only run on mount\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_toastify__WEBPACK_IMPORTED_MODULE_2__.ToastContainer, {}, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1720,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Header__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                onReset: handleReset,\n                onScreenshot: captureScreenshot,\n                onSaveScene: handleSaveScene,\n                onLoadScene: handleLoadScene,\n                isFullscreen: isFullscreen,\n                onToggleFullscreen: ()=>setIsFullscreen(!isFullscreen),\n                project: project,\n                user: user\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1721,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_InitialOverlayPanel__WEBPACK_IMPORTED_MODULE_20__.InitialOverlayPanel, {}, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1732,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed left-5 top-1/2 transform -translate-y-1/2 z-[1000] transition-all duration-300 \".concat(isFullscreen ? \"opacity-0 pointer-events-none\" : \"opacity-100\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Toolbar__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    transformMode: transformMode,\n                    setTransformMode: setTransformMode,\n                    undoAction: undoAction,\n                    redoAction: redoAction,\n                    rightPanelTab: rightPanelTab,\n                    setRightPanelTab: setRightPanelTab,\n                    orientation: \"vertical\",\n                    sceneObjects: sceneObjects,\n                    setSelectedObjects: setSelectedObjects,\n                    selectedObjects: selectedObjects,\n                    panelRef: panelRef,\n                    isMobile: isMobile,\n                    setOutlinerCollapsed: setOutlinerCollapsed\n                }, void 0, false, {\n                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                    lineNumber: 1740,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1735,\n                columnNumber: 7\n            }, this),\n            rightPanelTab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: panelRef,\n                className: \"fixed left-20 top-1/2 transform -translate-y-1/2 bg-gradient-to-tl from-[#32343D] to-[#14192D] p-4 rounded-lg shadow-lg z-[1000] max-h-[80vh] overflow-y-auto w-[250px] md:w-[300px] border border-[#A3A3A3]/30 scrollbar-none\",\n                children: [\n                    rightPanelTab === \"transform\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TransformPanel__WEBPACK_IMPORTED_MODULE_13__.TransformPanel, {\n                        selectedObjects: selectedObjects,\n                        transformMode: transformMode,\n                        setTransformMode: setTransformMode,\n                        undoAction: undoAction,\n                        redoAction: redoAction\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1764,\n                        columnNumber: 13\n                    }, this),\n                    rightPanelTab === \"material\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MaterialPanel__WEBPACK_IMPORTED_MODULE_7__.MaterialPanel, {\n                        selectedObjects: selectedObjects,\n                        useDiamondShader: useDiamondShader,\n                        setUseDiamondShader: setUseDiamondShader,\n                        usePremiumWhiteGold: usePremiumWhiteGold,\n                        setUsePremiumWhiteGold: setUsePremiumWhiteGold,\n                        usePremiumRoseGold: usePremiumRoseGold,\n                        setUsePremiumRoseGold: setUsePremiumRoseGold,\n                        renderer: renderer\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1774,\n                        columnNumber: 13\n                    }, this),\n                    rightPanelTab === \"postprocessing\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PostProcessingPanel__WEBPACK_IMPORTED_MODULE_10__.PostProcessingPanel, {\n                        postProcessingEnabled: postProcessingEnabled,\n                        setPostProcessingEnabled: setPostProcessingEnabled,\n                        postProcessingSettings: postProcessingSettings,\n                        setPostProcessingSettings: setPostProcessingSettings\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1787,\n                        columnNumber: 13\n                    }, this),\n                    rightPanelTab === \"environment\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EnvironmentPanel__WEBPACK_IMPORTED_MODULE_4__.EnvironmentPanel, {\n                        envPreset: envPreset,\n                        setEnvPreset: setEnvPreset,\n                        bgColor: bgColor,\n                        setBgColor: setBgColor,\n                        showEnvironment: showEnvironment,\n                        setShowEnvironment: setShowEnvironment,\n                        customHdri: customHdri,\n                        setCustomHdri: setCustomHdri,\n                        envIntensity: envIntensity,\n                        setEnvIntensity: setEnvIntensity,\n                        showModelStats: showModelStats,\n                        setShowModelStats: setShowModelStats,\n                        envBlur: envBlur,\n                        setEnvBlur: setEnvBlur,\n                        envRotation: envRotation,\n                        setEnvRotation: setEnvRotation\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1796,\n                        columnNumber: 13\n                    }, this),\n                    rightPanelTab === \"lights\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LightPanel__WEBPACK_IMPORTED_MODULE_6__.LightPanel, {\n                        lights: lights,\n                        setLights: setLights,\n                        lightRefs: lightRefs,\n                        showGrid: showGrid,\n                        setShowGrid: setShowGrid,\n                        wireframe: wireframe,\n                        setWireframe: setWireframe,\n                        showLightSpheres: showLightSpheres,\n                        setShowLightSpheres: setShowLightSpheres,\n                        onResetLights: ()=>{\n                            // Reset lights to default configuration\n                            const defaultLights = [\n                                {\n                                    id: \"defaultDirectional\",\n                                    type: \"directional\",\n                                    position: [\n                                        2.5,\n                                        5.5,\n                                        5\n                                    ],\n                                    intensity: 1.0,\n                                    color: \"#ffffff\",\n                                    name: \"Main Directional Light\",\n                                    castShadow: true,\n                                    helperVisible: false\n                                },\n                                {\n                                    id: \"keyLight\",\n                                    type: \"point\",\n                                    position: [\n                                        5,\n                                        5,\n                                        5\n                                    ],\n                                    intensity: 8.0,\n                                    color: \"#ffffff\",\n                                    name: \"Key Light\"\n                                },\n                                {\n                                    id: \"fillLight\",\n                                    type: \"point\",\n                                    position: [\n                                        -5,\n                                        5,\n                                        -5\n                                    ],\n                                    intensity: 4.0,\n                                    color: \"#ffffff\",\n                                    name: \"Fill Light\"\n                                },\n                                {\n                                    id: \"backLight\",\n                                    type: \"point\",\n                                    position: [\n                                        0,\n                                        5,\n                                        -5\n                                    ],\n                                    intensity: 2.0,\n                                    color: \"#ffffff\",\n                                    name: \"Back Light\"\n                                }\n                            ];\n                            setLights(defaultLights);\n                            // Clear selected light\n                            setSelectedLight(null);\n                            // Clear light position history\n                            setLightPositionHistory({});\n                        }\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1817,\n                        columnNumber: 13\n                    }, this),\n                    rightPanelTab === \"engraving\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EngravingPanel__WEBPACK_IMPORTED_MODULE_19__.EngravingPanel, {\n                        onEngraveText: handleEngraveText,\n                        onDecalPositionChange: setDecalPosition,\n                        onDecalScaleChange: setDecalScale,\n                        onDecalRotationChange: setDecalRotation,\n                        decalPosition: decalPosition,\n                        decalScale: decalScale,\n                        decalRotation: decalRotation,\n                        decalColor: decalColor,\n                        onDecalColorChange: setDecalColor,\n                        onFontChange: handleFontChange,\n                        selectedFont: selectedFont,\n                        isPlacingDecal: isPlacingDecal,\n                        setIsPlacingDecal: setIsPlacingDecal,\n                        showDebug: showDecalDebug,\n                        onShowDebugChange: setShowDecalDebug\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1875,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1759,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed right-5 top-24 z-[999] transition-all duration-300 \".concat(isFullscreen ? \"opacity-0 pointer-events-none\" : \"opacity-100\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-tl from-[#32343D] to-[#14192D] shadow-lg rounded-lg overflow-visible scrollbar-none\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Outliner__WEBPACK_IMPORTED_MODULE_9__.Outliner, {\n                        sceneObjects: sceneObjects,\n                        selectedObjects: selectedObjects,\n                        onSelectObject: setSelectedObjects,\n                        onCameraView: handleCameraView,\n                        onPlayAnimation: handlePlayAnimation,\n                        onSaveScene: handleSaveScene,\n                        groundType: groundType,\n                        setGroundType: setGroundType,\n                        collapsed: outlinerCollapsed,\n                        setCollapsed: setOutlinerCollapsed\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1903,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                    lineNumber: 1902,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1897,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-full\",\n                style: {\n                    background: \"linear-gradient(to bottom right, #363643, #5C5C6E)\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_fiber__WEBPACK_IMPORTED_MODULE_29__.Canvas, {\n                    className: \"w-full h-full\",\n                    camera: {\n                        position: getCameraPosition(),\n                        fov: 45,\n                        near: 0.1,\n                        far: 50\n                    },\n                    shadows: true,\n                    ref: sceneRef,\n                    gl: {\n                        antialias: true,\n                        preserveDrawingBuffer: true\n                    },\n                    onPointerMissed: handlePointerMissed,\n                    raycaster: {\n                        computeOffsets: (e)=>({\n                                offsetX: e.clientX,\n                                offsetY: e.clientY\n                            })\n                    },\n                    frameloop: \"demand\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n                        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_30__.Html, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_31__.Loader, {}, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 1948,\n                                columnNumber: 17\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                            lineNumber: 1947,\n                            columnNumber: 15\n                        }, void 0),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ScreenshotTaker, {\n                                onScreenshotRef: (fn)=>{\n                                    screenshotRef.current = fn;\n                                }\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 1954,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RendererProvider, {\n                                onRendererReady: (gl)=>{\n                                    setRenderer(gl);\n                                }\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 1960,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SceneLights__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                lights: lights,\n                                lightRefs: lightRefs,\n                                selectedLight: selectedLight,\n                                onLightSelect: setSelectedLight,\n                                onLightMoved: handleLightMoved,\n                                showLightSpheres: showLightSpheres,\n                                threePointLighting: true\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 1966,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Environment__WEBPACK_IMPORTED_MODULE_3__.Environment, {\n                                preset: getEnvironmentPreset(),\n                                background: showEnvironment,\n                                bgColor: bgColor,\n                                customHdri: customHdri,\n                                intensity: envIntensity,\n                                blur: envBlur,\n                                envRotation: envRotation\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 1975,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SelectionOutline__WEBPACK_IMPORTED_MODULE_23__.SelectionOutline, {\n                                selectedObjects: selectedObjects,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ModelStage__WEBPACK_IMPORTED_MODULE_21__.ModelStage, {\n                                    selectedModel: selectedModel,\n                                    wireframe: wireframe,\n                                    handleSceneUpdate: handleSceneUpdate,\n                                    handleObjectClick: handleObjectClick,\n                                    uploadedModel: uploadedModel,\n                                    groundType: groundType,\n                                    engravingText: engravingText,\n                                    activeAnimation: activeAnimation,\n                                    decalPosition: decalPosition,\n                                    decalScale: decalScale,\n                                    decalRotation: decalRotation,\n                                    decalColor: decalColor,\n                                    font: selectedFont,\n                                    isPlacingDecal: isPlacingDecal,\n                                    onDecalPlacement: handleDecalPlacement,\n                                    showDecalDebug: showDecalDebug,\n                                    sceneObjects: sceneObjects,\n                                    useDiamondShader: useDiamondShader,\n                                    usePremiumWhiteGold: usePremiumWhiteGold,\n                                    usePremiumRoseGold: usePremiumRoseGold\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                    lineNumber: 1986,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 1985,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_32__.OrbitControls, {\n                                ref: orbitControlsRef,\n                                target: [\n                                    0,\n                                    0.5,\n                                    0\n                                ],\n                                makeDefault: true,\n                                enableDamping: true,\n                                // minPolarAngle={0}\n                                // maxPolarAngle={Math.PI / 2.25}\n                                // minDistance={2.5}\n                                maxDistance: 20\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2010,\n                                columnNumber: 13\n                            }, this),\n                            selectedObjects.length > 0 && rightPanelTab === \"transform\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_33__.TransformControls, {\n                                object: selectedObjects.length === 1 ? selectedObjects[0] : selectionGroupRef.current,\n                                mode: transformMode,\n                                onMouseDown: ()=>{\n                                    if (orbitControlsRef.current) orbitControlsRef.current.enabled = false;\n                                },\n                                onMouseUp: ()=>{\n                                    if (orbitControlsRef.current) orbitControlsRef.current.enabled = true;\n                                    if (selectedObjects.length > 1) updateObjectsFromGroup();\n                                },\n                                onObjectChange: ()=>{\n                                    if (selectedObjects.length > 1) updateObjectsFromGroup();\n                                }\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2022,\n                                columnNumber: 15\n                            }, this),\n                            groundType === \"grid\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"gridHelper\", {\n                                args: [\n                                    50,\n                                    50,\n                                    \"#666666\",\n                                    \"#333333\"\n                                ],\n                                receiveShadow: true\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2044,\n                                columnNumber: 15\n                            }, this),\n                            groundType === \"solid\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n                                \"rotation-x\": -Math.PI / 2,\n                                receiveShadow: true,\n                                position: [\n                                    0,\n                                    -0.01,\n                                    0\n                                ],\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"planeGeometry\", {\n                                        args: [\n                                            200,\n                                            200\n                                        ]\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2052,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meshStandardMaterial\", {\n                                        color: \"#5d5d5d\",\n                                        wireframe: wireframe,\n                                        transparent: true,\n                                        opacity: 0.7\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2053,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2047,\n                                columnNumber: 15\n                            }, this),\n                            groundType === \"reflective\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n                                receiveShadow: true,\n                                rotation: [\n                                    -Math.PI / 2,\n                                    0,\n                                    0\n                                ],\n                                position: [\n                                    0,\n                                    -0.01,\n                                    0\n                                ],\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"planeGeometry\", {\n                                        args: [\n                                            80,\n                                            80\n                                        ]\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2067,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_34__.MeshReflectorMaterial, {\n                                        blur: [\n                                            800,\n                                            800\n                                        ],\n                                        resolution: 2048,\n                                        mixBlur: 0.5,\n                                        mixStrength: 10,\n                                        roughness: 1,\n                                        depthScale: 1,\n                                        minDepthThreshold: 0.4,\n                                        maxDepthThreshold: 1.2,\n                                        color: \"#363643\",\n                                        metalness: 0.01,\n                                        mirror: 0.3,\n                                        transparent: true,\n                                        opacity: 0.5\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2068,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2062,\n                                columnNumber: 15\n                            }, this),\n                            groundType === \"none\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n                                receiveShadow: true,\n                                rotation: [\n                                    -Math.PI / 2,\n                                    0,\n                                    0\n                                ],\n                                position: [\n                                    0,\n                                    -0.01,\n                                    0\n                                ],\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"planeGeometry\", {\n                                        args: [\n                                            200,\n                                            200\n                                        ]\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2091,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"primitive\", {\n                                        object: new three__WEBPACK_IMPORTED_MODULE_28__.ShadowMaterial({\n                                            opacity: 0.1,\n                                            color: new three__WEBPACK_IMPORTED_MODULE_28__.Color(0x000000),\n                                            transparent: true\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2092,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2086,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_35__.GizmoHelper, {\n                                alignment: \"bottom-right\",\n                                margin: [\n                                    80,\n                                    80\n                                ],\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_36__.GizmoViewport, {\n                                    labelColor: \"white\",\n                                    axisColors: [\n                                        \"red\",\n                                        \"green\",\n                                        \"blue\"\n                                    ]\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                    lineNumber: 2104,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2103,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"primitive\", {\n                                object: selectionGroupRef.current\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2110,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PostProcessingEffects__WEBPACK_IMPORTED_MODULE_22__.PostProcessingEffects, {\n                                enabled: postProcessingEnabled,\n                                settings: postProcessingSettings\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2112,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1945,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                    lineNumber: 1925,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1919,\n                columnNumber: 7\n            }, this),\n            showModelStats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ModelStats__WEBPACK_IMPORTED_MODULE_8__.ModelStats, {\n                stats: modelStats\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 2120,\n                columnNumber: 26\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s2(EditorExperience, \"rcyB5sbpNTHDHMaYA6L2Vgooz6c=\", false, function() {\n    return [\n        _lib_states__WEBPACK_IMPORTED_MODULE_26__.useVersionState\n    ];\n});\n_c3 = EditorExperience;\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"ScreenshotTaker\");\n$RefreshReg$(_c1, \"MaterialPanelWrapper\");\n$RefreshReg$(_c2, \"RendererProvider\");\n$RefreshReg$(_c3, \"EditorExperience\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ArtistTool/EditorExperience.jsx\n"));

/***/ })

});