globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/(auth)/login/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./src/components/Home/HomePage.tsx":{"*":{"id":"(ssr)/./src/components/Home/HomePage.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/providers/UserProvider.tsx":{"*":{"id":"(ssr)/./src/components/providers/UserProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Onboarding/OnboardingProvider.tsx":{"*":{"id":"(ssr)/./src/components/Onboarding/OnboardingProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Onboarding/OnboardingTrigger.tsx":{"*":{"id":"(ssr)/./src/components/Onboarding/OnboardingTrigger.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Theme/theme-provider.tsx":{"*":{"id":"(ssr)/./src/components/Theme/theme-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ui/sonner.tsx":{"*":{"id":"(ssr)/./src/components/ui/sonner.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/not-found.tsx":{"*":{"id":"(ssr)/./src/app/not-found.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/image-component.js":{"*":{"id":"(ssr)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/image-component.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/link.js":{"*":{"id":"(ssr)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/AuthPages/LoginPageContent.tsx":{"*":{"id":"(ssr)/./src/components/AuthPages/LoginPageContent.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ProjectPage.tsx":{"*":{"id":"(ssr)/./src/components/ProjectPage.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ArtistTool/EditorExperience.jsx":{"*":{"id":"(ssr)/./src/components/ArtistTool/EditorExperience.jsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"G:\\agape\\agape-newFrontend\\web\\src\\components\\Home\\HomePage.tsx":{"id":"(app-pages-browser)/./src/components/Home/HomePage.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"G:\\agape\\agape-newFrontend\\web\\src\\components\\providers\\UserProvider.tsx":{"id":"(app-pages-browser)/./src/components/providers/UserProvider.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"G:\\agape\\agape-newFrontend\\web\\node_modules\\.pnpm\\next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1\\node_modules\\next\\font\\local\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"\",\"arguments\":[{\"src\":[{\"path\":\"../../public/fonts/LarkenDEMO-MediumItalic.otf\",\"weight\":\"500\",\"style\":\"italic\"}],\"variable\":\"--font-larken\"}],\"variableName\":\"larken\"}":{"id":"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/font/local/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"\",\"arguments\":[{\"src\":[{\"path\":\"../../public/fonts/LarkenDEMO-MediumItalic.otf\",\"weight\":\"500\",\"style\":\"italic\"}],\"variable\":\"--font-larken\"}],\"variableName\":\"larken\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"G:\\agape\\agape-newFrontend\\web\\node_modules\\.pnpm\\next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"G:\\agape\\agape-newFrontend\\web\\node_modules\\.pnpm\\next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Sansita_Swashed\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-sansita-swashed\"}],\"variableName\":\"sansitaSwashed\"}":{"id":"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Sansita_Swashed\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-sansita-swashed\"}],\"variableName\":\"sansitaSwashed\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"G:\\agape\\agape-newFrontend\\web\\src\\components\\Onboarding\\OnboardingProvider.tsx":{"id":"(app-pages-browser)/./src/components/Onboarding/OnboardingProvider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"G:\\agape\\agape-newFrontend\\web\\src\\components\\Onboarding\\OnboardingTrigger.tsx":{"id":"(app-pages-browser)/./src/components/Onboarding/OnboardingTrigger.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"G:\\agape\\agape-newFrontend\\web\\src\\components\\Theme\\theme-provider.tsx":{"id":"(app-pages-browser)/./src/components/Theme/theme-provider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"G:\\agape\\agape-newFrontend\\web\\src\\components\\ui\\sonner.tsx":{"id":"(app-pages-browser)/./src/components/ui/sonner.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"G:\\agape\\agape-newFrontend\\web\\src\\styles\\globals.css":{"id":"(app-pages-browser)/./src/styles/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"G:\\agape\\agape-newFrontend\\web\\src\\app\\not-found.tsx":{"id":"(app-pages-browser)/./src/app/not-found.tsx","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"G:\\agape\\agape-newFrontend\\web\\node_modules\\.pnpm\\next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1\\node_modules\\next\\dist\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"G:\\agape\\agape-newFrontend\\web\\node_modules\\.pnpm\\next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1\\node_modules\\next\\dist\\esm\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"G:\\agape\\agape-newFrontend\\web\\node_modules\\.pnpm\\next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"G:\\agape\\agape-newFrontend\\web\\node_modules\\.pnpm\\next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"G:\\agape\\agape-newFrontend\\web\\node_modules\\.pnpm\\next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"G:\\agape\\agape-newFrontend\\web\\node_modules\\.pnpm\\next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"G:\\agape\\agape-newFrontend\\web\\node_modules\\.pnpm\\next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"G:\\agape\\agape-newFrontend\\web\\node_modules\\.pnpm\\next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"G:\\agape\\agape-newFrontend\\web\\node_modules\\.pnpm\\next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1\\node_modules\\next\\dist\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"G:\\agape\\agape-newFrontend\\web\\node_modules\\.pnpm\\next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1\\node_modules\\next\\dist\\esm\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"G:\\agape\\agape-newFrontend\\web\\node_modules\\.pnpm\\next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"G:\\agape\\agape-newFrontend\\web\\node_modules\\.pnpm\\next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"G:\\agape\\agape-newFrontend\\web\\node_modules\\.pnpm\\next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1\\node_modules\\next\\dist\\client\\image-component.js":{"id":"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/image-component.js","name":"*","chunks":["app/(auth)/login/page","static/chunks/app/(auth)/login/page.js"],"async":false},"G:\\agape\\agape-newFrontend\\web\\node_modules\\.pnpm\\next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1\\node_modules\\next\\dist\\esm\\client\\image-component.js":{"id":"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/image-component.js","name":"*","chunks":["app/(auth)/login/page","static/chunks/app/(auth)/login/page.js"],"async":false},"G:\\agape\\agape-newFrontend\\web\\node_modules\\.pnpm\\next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1\\node_modules\\next\\dist\\client\\link.js":{"id":"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/link.js","name":"*","chunks":["app/(auth)/login/page","static/chunks/app/(auth)/login/page.js"],"async":false},"G:\\agape\\agape-newFrontend\\web\\node_modules\\.pnpm\\next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1\\node_modules\\next\\dist\\esm\\client\\link.js":{"id":"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/link.js","name":"*","chunks":["app/(auth)/login/page","static/chunks/app/(auth)/login/page.js"],"async":false},"G:\\agape\\agape-newFrontend\\web\\src\\components\\AuthPages\\LoginPageContent.tsx":{"id":"(app-pages-browser)/./src/components/AuthPages/LoginPageContent.tsx","name":"*","chunks":["app/(auth)/login/page","static/chunks/app/(auth)/login/page.js"],"async":false},"G:\\agape\\agape-newFrontend\\web\\src\\components\\ProjectPage.tsx":{"id":"(app-pages-browser)/./src/components/ProjectPage.tsx","name":"*","chunks":[],"async":false},"G:\\agape\\agape-newFrontend\\web\\src\\components\\ArtistTool\\EditorExperience.jsx":{"id":"(app-pages-browser)/./src/components/ArtistTool/EditorExperience.jsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"G:\\agape\\agape-newFrontend\\web\\src\\":[],"G:\\agape\\agape-newFrontend\\web\\src\\app\\page":[],"G:\\agape\\agape-newFrontend\\web\\src\\app\\layout":["static/css/app/layout.css"],"G:\\agape\\agape-newFrontend\\web\\src\\app\\not-found":[],"G:\\agape\\agape-newFrontend\\web\\src\\app\\(auth)\\login\\page":[],"G:\\agape\\agape-newFrontend\\web\\src\\app\\(auth)\\login\\layout":[]}}