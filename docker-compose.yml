services:
  backend:
    build:
      context: ./backend # Path to the backend Dockerfile
    ports:
      - "3001:3001"
    env_file:
      - ./backend/.env
    environment:
      - NODE_ENV=production
      - PORT=3001
    # volumes:
    #   - ./backend:/app # Mount the backend directory to the container

  web:
    build:
      context: ./web # Path to the web Dockerfile
    ports:
      - "3000:3000"
    depends_on:
      - backend # Wait for backend service to be ready before starting web
    env_file:
      - ./web/.env
    environment:
      - NODE_ENV=production
    # volumes:
    #   - ./web:/app # Mount the web directory to the container
