@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=G:\agape\agape-newFrontend\web\node_modules\.pnpm\fast-xml-parser@4.1.2\node_modules\fast-xml-parser\src\cli\node_modules;G:\agape\agape-newFrontend\web\node_modules\.pnpm\fast-xml-parser@4.1.2\node_modules\fast-xml-parser\src\node_modules;G:\agape\agape-newFrontend\web\node_modules\.pnpm\fast-xml-parser@4.1.2\node_modules\fast-xml-parser\node_modules;G:\agape\agape-newFrontend\web\node_modules\.pnpm\fast-xml-parser@4.1.2\node_modules;G:\agape\agape-newFrontend\web\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=G:\agape\agape-newFrontend\web\node_modules\.pnpm\fast-xml-parser@4.1.2\node_modules\fast-xml-parser\src\cli\node_modules;G:\agape\agape-newFrontend\web\node_modules\.pnpm\fast-xml-parser@4.1.2\node_modules\fast-xml-parser\src\node_modules;G:\agape\agape-newFrontend\web\node_modules\.pnpm\fast-xml-parser@4.1.2\node_modules\fast-xml-parser\node_modules;G:\agape\agape-newFrontend\web\node_modules\.pnpm\fast-xml-parser@4.1.2\node_modules;G:\agape\agape-newFrontend\web\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\..\..\..\..\..\fast-xml-parser@4.1.2\node_modules\fast-xml-parser\src\cli\cli.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\..\..\..\..\..\fast-xml-parser@4.1.2\node_modules\fast-xml-parser\src\cli\cli.js" %*
)
