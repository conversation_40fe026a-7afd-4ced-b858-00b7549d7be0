"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/projects/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/ArtistTool/EditorExperience.jsx":
/*!********************************************************!*\
  !*** ./src/components/ArtistTool/EditorExperience.jsx ***!
  \********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ EditorExperience; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/web/Html.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/web/Loader.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/OrbitControls.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/TransformControls.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/MeshReflectorMaterial.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/GizmoHelper.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/GizmoViewport.js\");\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @react-three/fiber */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_three@0.167.1/node_modules/@react-three/fiber/dist/events-776716bd.esm.js\");\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @react-three/fiber */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_three@0.167.1/node_modules/@react-three/fiber/dist/react-three-fiber.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/.pnpm/react-toastify@11.0.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/react-toastify/dist/index.mjs\");\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! three */ \"(app-pages-browser)/./node_modules/.pnpm/three@0.167.1/node_modules/three/build/three.module.js\");\n/* harmony import */ var _Environment__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Environment */ \"(app-pages-browser)/./src/components/ArtistTool/Environment.tsx\");\n/* harmony import */ var _EnvironmentPanel__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./EnvironmentPanel */ \"(app-pages-browser)/./src/components/ArtistTool/EnvironmentPanel.jsx\");\n/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Header */ \"(app-pages-browser)/./src/components/ArtistTool/Header.jsx\");\n/* harmony import */ var _LightPanel__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./LightPanel */ \"(app-pages-browser)/./src/components/ArtistTool/LightPanel.jsx\");\n/* harmony import */ var _MaterialPanel__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./MaterialPanel */ \"(app-pages-browser)/./src/components/ArtistTool/MaterialPanel.jsx\");\n/* harmony import */ var _ModelStats__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./ModelStats */ \"(app-pages-browser)/./src/components/ArtistTool/ModelStats.jsx\");\n/* harmony import */ var _Outliner__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./Outliner */ \"(app-pages-browser)/./src/components/ArtistTool/Outliner.jsx\");\n/* harmony import */ var _PostProcessingPanel__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./PostProcessingPanel */ \"(app-pages-browser)/./src/components/ArtistTool/PostProcessingPanel.jsx\");\n/* harmony import */ var _SceneLights__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./SceneLights */ \"(app-pages-browser)/./src/components/ArtistTool/SceneLights.jsx\");\n/* harmony import */ var _Toolbar__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./Toolbar */ \"(app-pages-browser)/./src/components/ArtistTool/Toolbar.jsx\");\n/* harmony import */ var _TransformPanel__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./TransformPanel */ \"(app-pages-browser)/./src/components/ArtistTool/TransformPanel.jsx\");\n/* harmony import */ var _utils_ArtistTool_cameraUtils__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../../utils/ArtistTool/cameraUtils */ \"(app-pages-browser)/./src/utils/ArtistTool/cameraUtils.js\");\n/* harmony import */ var _utils_ArtistTool_historyUtils__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../../utils/ArtistTool/historyUtils */ \"(app-pages-browser)/./src/utils/ArtistTool/historyUtils.js\");\n/* harmony import */ var _utils_ArtistTool_materialUtils__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../../utils/ArtistTool/materialUtils */ \"(app-pages-browser)/./src/utils/ArtistTool/materialUtils.js\");\n/* harmony import */ var _utils_ArtistTool_sceneUtils__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ../../utils/ArtistTool/sceneUtils */ \"(app-pages-browser)/./src/utils/ArtistTool/sceneUtils.js\");\n/* harmony import */ var _animations_CameraAnimations__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./animations/CameraAnimations */ \"(app-pages-browser)/./src/components/ArtistTool/animations/CameraAnimations.js\");\n/* harmony import */ var _EngravingPanel__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./EngravingPanel */ \"(app-pages-browser)/./src/components/ArtistTool/EngravingPanel.jsx\");\n/* harmony import */ var _InitialOverlayPanel__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./InitialOverlayPanel */ \"(app-pages-browser)/./src/components/ArtistTool/InitialOverlayPanel.jsx\");\n/* harmony import */ var _ModelStage__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./ModelStage */ \"(app-pages-browser)/./src/components/ArtistTool/ModelStage.jsx\");\n/* harmony import */ var _PostProcessingEffects__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ./PostProcessingEffects */ \"(app-pages-browser)/./src/components/ArtistTool/PostProcessingEffects.jsx\");\n/* harmony import */ var _SelectionOutline__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! ./SelectionOutline */ \"(app-pages-browser)/./src/components/ArtistTool/SelectionOutline.jsx\");\n/* harmony import */ var _lib_actions_version_actions__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/lib/actions/version.actions */ \"(app-pages-browser)/./src/lib/actions/version.actions.ts\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/.pnpm/sonner@1.5.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _lib_states__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @/lib/states */ \"(app-pages-browser)/./src/lib/states.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst ScreenshotTaker = (param)=>{\n    let { onScreenshotRef } = param;\n    _s();\n    const { gl, scene, camera } = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_27__.D)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (typeof onScreenshotRef === \"function\") {\n            onScreenshotRef(()=>{\n                try {\n                    // Render the scene\n                    gl.render(scene, camera);\n                    // Get the canvas data URL\n                    const dataURL = gl.domElement.toDataURL(\"image/png\");\n                    // Create and trigger a download\n                    const link = document.createElement(\"a\");\n                    const timestamp = new Date().toISOString().replace(/:/g, \"-\").substring(0, 19);\n                    link.download = \"agape-scene-\".concat(timestamp, \".png\");\n                    link.href = dataURL;\n                    document.body.appendChild(link);\n                    link.click();\n                    // Clean up\n                    setTimeout(()=>{\n                        document.body.removeChild(link);\n                    }, 100);\n                    console.log(\"Screenshot captured successfully\");\n                    return true;\n                } catch (error) {\n                    console.error(\"Error capturing screenshot:\", error);\n                    return false;\n                }\n            });\n        }\n    }, [\n        gl,\n        scene,\n        camera,\n        onScreenshotRef\n    ]);\n    return null;\n};\n_s(ScreenshotTaker, \"RVTFHuczdlyvGFUsZJPSpjurI/Y=\", false, function() {\n    return [\n        _react_three_fiber__WEBPACK_IMPORTED_MODULE_27__.D\n    ];\n});\n_c = ScreenshotTaker;\n// Component to provide renderer to MaterialPanel\nconst MaterialPanelWrapper = (param)=>{\n    let { renderer, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MaterialPanel__WEBPACK_IMPORTED_MODULE_7__.MaterialPanel, {\n        renderer: renderer,\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n        lineNumber: 96,\n        columnNumber: 10\n    }, undefined);\n};\n_c1 = MaterialPanelWrapper;\n// Component to get renderer from Canvas context\nconst RendererProvider = (param)=>{\n    let { children, onRendererReady } = param;\n    _s1();\n    const { gl } = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_27__.D)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (onRendererReady) {\n            onRendererReady(gl);\n        }\n    }, [\n        gl,\n        onRendererReady\n    ]);\n    return null;\n};\n_s1(RendererProvider, \"iTWbOa64ZxlA5nuyNExgWK+vjCI=\", false, function() {\n    return [\n        _react_three_fiber__WEBPACK_IMPORTED_MODULE_27__.D\n    ];\n});\n_c2 = RendererProvider;\nfunction EditorExperience(param) {\n    let { modelUrl, project, user } = param;\n    _s2();\n    const { addVersion } = (0,_lib_states__WEBPACK_IMPORTED_MODULE_26__.useVersionState)();\n    const configRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const workingConfigRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [selectedModel, setSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"uploaded\");\n    const [lastSelectedModel, setLastSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [uploadedModel, setUploadedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(modelUrl || null);\n    const [modelStats, setModelStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        vertices: 0,\n        triangles: 0,\n        materials: 0,\n        animations: 0\n    });\n    const [lights, setLights] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: \"defaultDirectional\",\n            type: \"directional\",\n            position: [\n                2.5,\n                5.5,\n                5\n            ],\n            intensity: 1.0,\n            color: \"#ffffff\",\n            name: \"Main Directional Light\",\n            castShadow: true,\n            helperVisible: false\n        },\n        {\n            id: \"keyLight\",\n            type: \"point\",\n            position: [\n                5,\n                5,\n                5\n            ],\n            intensity: 8.0,\n            color: \"#ffffff\",\n            name: \"Key Light\"\n        },\n        {\n            id: \"fillLight\",\n            type: \"point\",\n            position: [\n                -5,\n                5,\n                -5\n            ],\n            intensity: 4.0,\n            color: \"#ffffff\",\n            name: \"Fill Light\"\n        },\n        {\n            id: \"backLight\",\n            type: \"point\",\n            position: [\n                0,\n                5,\n                -5\n            ],\n            intensity: 2.0,\n            color: \"#ffffff\",\n            name: \"Back Light\"\n        }\n    ]);\n    const lightRefs = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({});\n    const [selectedObjects, setSelectedObjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedLight, setSelectedLight] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [lightPositionHistory, setLightPositionHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [sceneObjects, setSceneObjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [transformMode, setTransformMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"translate\");\n    const selectionGroupRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(new three__WEBPACK_IMPORTED_MODULE_28__.Group());\n    console.log(\"renderingggg editor experience\");\n    const [rightPanelTab, setRightPanelTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Decal placement state\n    const [isPlacingDecal, setIsPlacingDecal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showDecalDebug, setShowDecalDebug] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Scene controls state\n    const [showGrid, setShowGrid] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [wireframe, setWireframe] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [groundType, setGroundType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"none\");\n    const [envPreset, setEnvPreset] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"hdri_15\");\n    const [bgColor, setBgColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"#272631\");\n    const [showEnvironment, setShowEnvironment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showLightSpheres, setShowLightSpheres] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [customHdri, setCustomHdri] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [envIntensity, setEnvIntensity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1.85);\n    const [envBlur, setEnvBlur] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0.35);\n    const [envRotation, setEnvRotation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [postProcessingEnabled, setPostProcessingEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [postProcessingSettings, setPostProcessingSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        autofocus: {\n            enabled: false,\n            bokehScale: 18\n        },\n        bloom: {\n            enabled: true,\n            intensity: 0.22,\n            threshold: 1,\n            radius: 0.5\n        },\n        dof: {\n            enabled: false,\n            focusDistance: 0,\n            aperture: 0.01,\n            bokehScale: 3\n        },\n        vignette: {\n            enabled: true,\n            darkness: 0.5,\n            offset: 0.1\n        }\n    });\n    // Store initial state\n    const initialStateRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        selectedModel: null,\n        lights: [],\n        envPreset: null,\n        bgColor: null,\n        postProcessingSettings: null,\n        showGrid: null,\n        wireframe: null,\n        groundType: null,\n        showEnvironment: null,\n        showLightSpheres: null,\n        envIntensity: null,\n        envBlur: null,\n        cameraPosition: null,\n        cameraTarget: null\n    });\n    // Add a ref to store the initial state of model objects\n    const modelInitialStateRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        objects: []\n    });\n    // Capture initial state when component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        initialStateRef.current = {\n            selectedModel,\n            lights: [\n                ...lights\n            ],\n            envPreset,\n            bgColor,\n            postProcessingSettings: {\n                ...postProcessingSettings\n            },\n            showGrid,\n            wireframe,\n            groundType,\n            showEnvironment,\n            showLightSpheres,\n            envIntensity,\n            envBlur,\n            cameraPosition: new three__WEBPACK_IMPORTED_MODULE_28__.Vector3(5, 2, 5),\n            cameraTarget: new three__WEBPACK_IMPORTED_MODULE_28__.Vector3(0, 1, 0)\n        };\n    }, []);\n    const orbitControlsRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const sceneRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const [renderer, setRenderer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // History state for undo/redo\n    const [history, setHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [historyIndex, setHistoryIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(-1);\n    const [isUndoRedoAction, setIsUndoRedoAction] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showModelStats, setShowModelStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Add a new state to the history\n    const addToHistory = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((state)=>{\n        if (isUndoRedoAction) {\n            setIsUndoRedoAction(false);\n            return;\n        }\n        const newState = (0,_utils_ArtistTool_historyUtils__WEBPACK_IMPORTED_MODULE_15__.createHistoryState)(selectedObjects, lights, lightRefs.current);\n        const newHistory = history.slice(0, historyIndex + 1);\n        setHistory([\n            ...newHistory,\n            newState\n        ]);\n        setHistoryIndex(historyIndex + 1);\n    }, [\n        history,\n        historyIndex,\n        isUndoRedoAction,\n        selectedObjects,\n        lights\n    ]);\n    // Undo action\n    const undoAction = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (historyIndex > 0) {\n            setIsUndoRedoAction(true);\n            const prevState = history[historyIndex - 1];\n            (0,_utils_ArtistTool_historyUtils__WEBPACK_IMPORTED_MODULE_15__.applyHistoryState)(prevState, sceneObjects, lightRefs.current);\n            setHistoryIndex(historyIndex - 1);\n        }\n    }, [\n        historyIndex,\n        history,\n        sceneObjects\n    ]);\n    // Redo action\n    const redoAction = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (historyIndex < history.length - 1) {\n            setIsUndoRedoAction(true);\n            const nextState = history[historyIndex + 1];\n            (0,_utils_ArtistTool_historyUtils__WEBPACK_IMPORTED_MODULE_15__.applyHistoryState)(nextState, sceneObjects, lightRefs.current);\n            setHistoryIndex(historyIndex + 1);\n        }\n    }, [\n        historyIndex,\n        history,\n        sceneObjects\n    ]);\n    // Track changes to objects and lights for history\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const trackChanges = ()=>{\n            if (selectedObjects.length > 0 || lights.length > 0) {\n                addToHistory({\n                    selectedObjects,\n                    lights\n                });\n            }\n        };\n        // Debounce to avoid too many history entries\n        const timeoutId = setTimeout(trackChanges, 500);\n        return ()=>clearTimeout(timeoutId);\n    }, [\n        selectedObjects,\n        lights,\n        addToHistory\n    ]);\n    const handleObjectClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e)=>{\n        e.stopPropagation();\n        // If placing decal, let the decal placement handler do its job (triggered by model's onClick)\n        if (isPlacingDecal) {\n            return;\n        }\n        console.log(\"Object clicked:\", e.object.name, e.object);\n        if (e.shiftKey) {\n            setSelectedObjects((prev)=>{\n                // Ensure prev is always an array\n                const prevArray = Array.isArray(prev) ? prev : [\n                    prev\n                ].filter(Boolean);\n                if (prevArray.includes(e.object)) {\n                    return prevArray.filter((obj)=>obj !== e.object);\n                }\n                return [\n                    ...prevArray,\n                    e.object\n                ];\n            });\n        } else {\n            setSelectedObjects([\n                e.object\n            ]);\n        }\n    }, [\n        isPlacingDecal,\n        setSelectedObjects\n    ]);\n    // Add debugging for selectedObjects changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"ExperienceNew - selectedObjects changed:\", selectedObjects);\n        console.log(\"ExperienceNew - selectedObjects length:\", selectedObjects === null || selectedObjects === void 0 ? void 0 : selectedObjects.length);\n    }, [\n        selectedObjects\n    ]);\n    // Add debugging for sceneObjects changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"ExperienceNew - sceneObjects changed:\", sceneObjects);\n        console.log(\"ExperienceNew - sceneObjects length:\", sceneObjects === null || sceneObjects === void 0 ? void 0 : sceneObjects.length);\n        console.log(\"ExperienceNew - sceneObjects details:\", sceneObjects === null || sceneObjects === void 0 ? void 0 : sceneObjects.map((obj)=>({\n                name: obj === null || obj === void 0 ? void 0 : obj.name,\n                type: obj === null || obj === void 0 ? void 0 : obj.type,\n                userData: obj === null || obj === void 0 ? void 0 : obj.userData\n            })));\n    }, [\n        sceneObjects\n    ]);\n    const handlePointerMissed = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        // If in decal placement mode, a \"missed\" click (on the canvas background)\n        // should probably cancel placement mode, or do nothing to allow model click-through.\n        // For now, let's prevent it from deselecting objects if placing decal.\n        if (isPlacingDecal) {\n            // setIsPlacingDecal(false); // Optionally, cancel placement on missed click\n            return;\n        }\n        setSelectedObjects([]);\n        setSelectedLight(null);\n    // Do not close the rightPanelTab here, as that's usually handled by clicking outside the panel itself.\n    }, [\n        isPlacingDecal,\n        setIsPlacingDecal\n    ]);\n    // Add the selection group to the scene\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // This effect runs once to add the selection group to the scene\n        return ()=>{\n            // Clean up on unmount\n            if (selectionGroupRef.current && selectionGroupRef.current.parent) {\n                selectionGroupRef.current.parent.remove(selectionGroupRef.current);\n            }\n        };\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Ensure selectedObjects is always an array\n        const objectsArray = Array.isArray(selectedObjects) ? selectedObjects : [\n            selectedObjects\n        ].filter(Boolean);\n        while(selectionGroupRef.current.children.length > 0){\n            selectionGroupRef.current.remove(selectionGroupRef.current.children[0]);\n        }\n        if (objectsArray.length > 0) {\n            const center = new three__WEBPACK_IMPORTED_MODULE_28__.Vector3();\n            objectsArray.forEach((obj)=>{\n                center.add(obj.position);\n            });\n            center.divideScalar(objectsArray.length);\n            selectionGroupRef.current.position.copy(center);\n            objectsArray.forEach((obj)=>{\n                const clone = obj.clone();\n                // Make the clone invisible while keeping its geometry for transforms\n                clone.traverse((child)=>{\n                    if (child.isMesh) {\n                        child.visible = false;\n                    }\n                });\n                clone.position.sub(center);\n                selectionGroupRef.current.add(clone);\n            });\n        }\n    }, [\n        selectedObjects\n    ]);\n    const updateObjectsFromGroup = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        // Ensure selectedObjects is always an array\n        const objectsArray = Array.isArray(selectedObjects) ? selectedObjects : [\n            selectedObjects\n        ].filter(Boolean);\n        if (objectsArray.length > 0 && selectionGroupRef.current) {\n            objectsArray.forEach((obj, index)=>{\n                if (selectionGroupRef.current.children[index]) {\n                    const worldPos = new three__WEBPACK_IMPORTED_MODULE_28__.Vector3();\n                    selectionGroupRef.current.children[index].getWorldPosition(worldPos);\n                    obj.position.copy(worldPos);\n                    obj.rotation.copy(selectionGroupRef.current.rotation);\n                    obj.scale.copy(selectionGroupRef.current.scale);\n                }\n            });\n        }\n    }, [\n        selectedObjects\n    ]);\n    const handleSceneUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((scene)=>{\n        var _scene_children;\n        console.log(\"handleSceneUpdate called with scene:\", scene);\n        console.log(\"Scene type:\", scene.type);\n        console.log(\"Scene children count:\", (_scene_children = scene.children) === null || _scene_children === void 0 ? void 0 : _scene_children.length);\n        setSceneObjects([]);\n        setSelectedObjects([]);\n        const stats = (0,_utils_ArtistTool_sceneUtils__WEBPACK_IMPORTED_MODULE_17__.calculateModelStats)(scene);\n        setModelStats(stats);\n        (0,_utils_ArtistTool_sceneUtils__WEBPACK_IMPORTED_MODULE_17__.setShadowsOnModel)(scene);\n        const objects = [];\n        const initialObjects = [];\n        scene.traverse((object)=>{\n            console.log(\"Traversing object:\", object.name, object.type, \"isMesh:\", object.isMesh, \"userData:\", object.userData);\n            if (object.isMesh) {\n                if (!object.name) {\n                    object.name = \"Mesh_\".concat(object.id);\n                }\n                console.log(\"Adding mesh to objects:\", object.name, \"userData:\", object.userData);\n                objects.push(object);\n                initialObjects.push({\n                    id: object.uuid,\n                    name: object.name,\n                    position: object.position.clone(),\n                    rotation: object.rotation.clone(),\n                    scale: object.scale.clone(),\n                    material: object.material ? {\n                        color: object.material.color ? object.material.color.getHex() : 0xffffff,\n                        roughness: object.material.roughness,\n                        metalness: object.material.metalness,\n                        wireframe: object.material.wireframe,\n                        transparent: object.material.transparent,\n                        opacity: object.material.opacity\n                    } : null\n                });\n            }\n        });\n        console.log(\"Found\", objects.length, \"mesh objects:\", objects.map((obj)=>({\n                name: obj.name,\n                userData: obj.userData\n            })));\n        modelInitialStateRef.current.objects = initialObjects;\n        setSceneObjects(objects);\n    }, []);\n    // Add this effect to detect model changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedModel !== lastSelectedModel) {\n            // Clear the scene objects when model changes\n            setSceneObjects([]);\n            setSelectedObjects([]);\n            setLastSelectedModel(selectedModel);\n        }\n    }, [\n        selectedModel,\n        lastSelectedModel\n    ]);\n    const handleLightMoved = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((lightId, newPosition)=>{\n        setLightPositionHistory((prev)=>({\n                ...prev,\n                [lightId]: [\n                    ...prev[lightId] || [],\n                    newPosition\n                ]\n            }));\n    }, []);\n    const undoLightMove = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((lightId)=>{\n        setLightPositionHistory((prev)=>{\n            const history = prev[lightId] || [];\n            if (history.length <= 1) return prev;\n            const newHistory = history.slice(0, -1);\n            const lastPosition = newHistory[newHistory.length - 1];\n            if (lightRefs.current[lightId]) {\n                lightRefs.current[lightId].position.copy(lastPosition);\n            }\n            return {\n                ...prev,\n                [lightId]: newHistory\n            };\n        });\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleKeyDown = (event)=>{\n            // Handle undo/redo keyboard shortcuts\n            if (event.ctrlKey) {\n                if (event.key === \"z\") {\n                    event.preventDefault();\n                    undoAction();\n                    return;\n                }\n                if (event.key === \"y\") {\n                    event.preventDefault();\n                    redoAction();\n                    return;\n                }\n            }\n            // Handle Escape key for decal placement\n            if (event.key === \"Escape\" && isPlacingDecal) {\n                setIsPlacingDecal(false);\n                return;\n            }\n            // Handle light undo\n            if (event.ctrlKey && event.key === \"z\" && selectedLight) {\n                undoLightMove(selectedLight);\n            }\n            // Handle transform mode shortcuts\n            if (selectedObjects.length > 0) {\n                switch(event.key){\n                    case \"g\":\n                        setTransformMode(\"translate\");\n                        break;\n                    case \"r\":\n                        setTransformMode(\"rotate\");\n                        break;\n                    case \"s\":\n                        setTransformMode(\"scale\");\n                        break;\n                    case \"Escape\":\n                        setSelectedObjects([]);\n                        break;\n                }\n            }\n            // Handle select all\n            if (event.ctrlKey && event.key === \"a\") {\n                event.preventDefault();\n                if (sceneObjects.length > 0) {\n                    setSelectedObjects([\n                        ...sceneObjects\n                    ]);\n                }\n            }\n        };\n        window.addEventListener(\"keydown\", handleKeyDown);\n        return ()=>window.removeEventListener(\"keydown\", handleKeyDown);\n    }, [\n        selectedLight,\n        undoLightMove,\n        sceneObjects,\n        selectedObjects.length,\n        undoAction,\n        redoAction,\n        isPlacingDecal,\n        setIsPlacingDecal\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        return ()=>{\n            if (uploadedModel) {\n                URL.revokeObjectURL(uploadedModel);\n            }\n            if (customHdri) {\n                URL.revokeObjectURL(customHdri);\n            }\n        };\n    }, [\n        uploadedModel,\n        customHdri\n    ]);\n    const handleCameraView = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((view)=>{\n        (0,_utils_ArtistTool_cameraUtils__WEBPACK_IMPORTED_MODULE_14__.setCameraView)(orbitControlsRef.current, view);\n    }, []);\n    // Add the panelRef to ExperienceNew instead of in Toolbar\n    const panelRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [activeAnimation, setActiveAnimation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [animationStartTime, setAnimationStartTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const requestRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    // Animation loop handler\n    const animateCamera = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((timestamp)=>{\n        if (!activeAnimation || !orbitControlsRef.current) {\n            return;\n        }\n        // Calculate animation progress (0 to 1)\n        const animation = _animations_CameraAnimations__WEBPACK_IMPORTED_MODULE_18__.predefinedAnimations.find((a)=>a.id === activeAnimation);\n        if (!animation) return;\n        if (animationStartTime === 0) {\n            setAnimationStartTime(timestamp);\n        }\n        const elapsed = timestamp - animationStartTime;\n        const progress = elapsed % animation.duration / animation.duration;\n        // Get the target position (center of selected objects or scene center)\n        const target = new three__WEBPACK_IMPORTED_MODULE_28__.Vector3(0, 0, 0);\n        if (selectedObjects.length > 0) {\n            // Calculate center of selected objects\n            const objectsArray = Array.isArray(selectedObjects) ? selectedObjects : [\n                selectedObjects\n            ].filter(Boolean);\n            if (objectsArray.length > 0) {\n                const center = new three__WEBPACK_IMPORTED_MODULE_28__.Vector3();\n                objectsArray.forEach((obj)=>{\n                    center.add(obj.position);\n                });\n                center.divideScalar(objectsArray.length);\n                target.copy(center);\n            }\n        }\n        // Animate camera using the animation function\n        animation.animate(orbitControlsRef.current.object, orbitControlsRef.current, progress, target);\n        requestRef.current = requestAnimationFrame(animateCamera);\n    }, [\n        activeAnimation,\n        orbitControlsRef,\n        animationStartTime,\n        selectedObjects\n    ]);\n    // Setup animation loop\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (activeAnimation) {\n            setAnimationStartTime(0);\n            requestRef.current = requestAnimationFrame(animateCamera);\n            // Disable orbit controls during animation\n            if (orbitControlsRef.current) {\n                orbitControlsRef.current.enabled = false;\n            }\n        } else {\n            // Re-enable orbit controls when animation stops\n            if (orbitControlsRef.current && selectedObjects.length === 0) {\n                orbitControlsRef.current.enabled = true;\n            }\n        }\n        return ()=>{\n            if (requestRef.current) {\n                cancelAnimationFrame(requestRef.current);\n            }\n        };\n    }, [\n        activeAnimation,\n        animateCamera,\n        selectedObjects\n    ]);\n    // Handle animation playback from Outliner\n    const handlePlayAnimation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((animationId)=>{\n        setActiveAnimation(animationId);\n    }, []);\n    const getEnvironmentPreset = ()=>{\n        return envPreset;\n    };\n    // Reset function\n    const handleReset = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        // Check if there's a saved scene in localStorage\n        const savedScene = localStorage.getItem(\"agape_saved_scene\");\n        if (savedScene) {\n            const confirmReset = window.confirm(\"There is a saved scene in your browser storage. Resetting will clear this saved scene. Do you want to continue?\");\n            if (!confirmReset) {\n                return;\n            }\n            // Clear the saved scene from localStorage\n            localStorage.removeItem(\"agape_saved_scene\");\n        }\n        console.log(\"Resetting scene to initial state\");\n        setSceneObjects([]);\n        setSelectedObjects([]);\n        // Reset lights to default configuration\n        setLights([\n            {\n                id: \"defaultDirectional\",\n                type: \"directional\",\n                position: [\n                    2.5,\n                    5.5,\n                    5\n                ],\n                intensity: 1.0,\n                color: \"#ffffff\",\n                name: \"Main Directional Light\",\n                castShadow: true,\n                helperVisible: false\n            },\n            {\n                id: \"keyLight\",\n                type: \"point\",\n                position: [\n                    5,\n                    5,\n                    5\n                ],\n                intensity: 8.0,\n                color: \"#ffffff\",\n                name: \"Key Light\"\n            },\n            {\n                id: \"fillLight\",\n                type: \"point\",\n                position: [\n                    -5,\n                    5,\n                    -5\n                ],\n                intensity: 4.0,\n                color: \"#ffffff\",\n                name: \"Fill Light\"\n            },\n            {\n                id: \"backLight\",\n                type: \"point\",\n                position: [\n                    0,\n                    5,\n                    -5\n                ],\n                intensity: 2.0,\n                color: \"#ffffff\",\n                name: \"Back Light\"\n            }\n        ]);\n        // Reset environment settings\n        setEnvPreset(\"hdri_15\");\n        setBgColor(\"#202020\");\n        setShowEnvironment(false);\n        setShowLightSpheres(true);\n        setCustomHdri(null);\n        setEnvIntensity(1.5);\n        setEnvBlur(0.35);\n        setEnvRotation(0);\n        // Reset post-processing settings\n        setPostProcessingEnabled(false);\n        setPostProcessingSettings({\n            autofocus: {\n                enabled: false,\n                bokehScale: 18\n            },\n            bloom: {\n                enabled: true,\n                intensity: 0.12,\n                threshold: 0.8,\n                radius: 0.4\n            },\n            dof: {\n                enabled: false,\n                focusDistance: 0,\n                aperture: 0.01,\n                bokehScale: 3\n            },\n            vignette: {\n                enabled: true,\n                darkness: 0.5,\n                offset: 0.1\n            }\n        });\n        // Reset scene controls\n        setShowGrid(true);\n        setWireframe(false);\n        setGroundType(\"none\");\n        // Reset camera position\n        if (orbitControlsRef.current) {\n            orbitControlsRef.current.object.position.set(5, 2, 5);\n            orbitControlsRef.current.target.set(0, 1, 0);\n            orbitControlsRef.current.update();\n        }\n        // Reset transform state\n        setTransformMode(\"translate\");\n        setSelectedLight(null);\n        // Reset material shaders\n        setUseDiamondShader(false);\n        setUsePremiumWhiteGold(false);\n        setUsePremiumRoseGold(false);\n        // Clear history\n        setHistory([]);\n        setHistoryIndex(-1);\n        setLightPositionHistory({});\n        // Reset model stats\n        setModelStats({\n            vertices: 0,\n            triangles: 0,\n            materials: 0,\n            animations: 0\n        });\n        // Reset right panel tab\n        setRightPanelTab(null);\n        // Reset active animation\n        setActiveAnimation(null);\n        setAnimationStartTime(0);\n        if (requestRef.current) {\n            cancelAnimationFrame(requestRef.current);\n        }\n        // Clear any custom HDRI or uploaded model URLs\n        if (uploadedModel) {\n            URL.revokeObjectURL(uploadedModel);\n        }\n        if (customHdri) {\n            URL.revokeObjectURL(customHdri);\n        }\n        // Force a model reload by setting selectedModel to null first\n        setSelectedModel(null);\n        // Then set it back to uploaded after a small delay\n        setTimeout(()=>{\n            setSelectedModel(\"uploaded\");\n        }, 100);\n    }, []);\n    const screenshotRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const captureScreenshot = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (screenshotRef.current) {\n            console.log(\"Taking screenshot\");\n            screenshotRef.current();\n        } else {\n            console.error(\"Screenshot function not available yet\");\n        }\n    }, []);\n    // Load saved scene from localStorage on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadSavedScene = ()=>{\n            const savedScene = localStorage.getItem(\"agape_saved_scene\");\n            if (savedScene) {\n                try {\n                    const sceneConfig = JSON.parse(savedScene);\n                    console.log(\"Loading saved scene:\", sceneConfig);\n                    // Only load the saved configuration on initial load\n                    if (!lastSelectedModel) {\n                        console.log(\"Initial load - setting saved model:\", sceneConfig.model);\n                        setSelectedModel(\"uploaded\"); // Always use uploaded for S3 models\n                        // Load the rest of the saved scene configuration\n                        setLights(sceneConfig.lights);\n                        setEnvPreset(sceneConfig.environment.preset);\n                        setBgColor(sceneConfig.environment.bgColor);\n                        setShowEnvironment(sceneConfig.environment.showEnvironment);\n                        setShowLightSpheres(sceneConfig.environment.showLightSpheres);\n                        setEnvIntensity(sceneConfig.environment.intensity);\n                        setEnvBlur(sceneConfig.environment.blur);\n                        setEnvRotation(sceneConfig.environment.rotation);\n                        setPostProcessingEnabled(sceneConfig.postProcessing.enabled);\n                        setPostProcessingSettings(sceneConfig.postProcessing.settings);\n                        setShowGrid(sceneConfig.sceneControls.showGrid);\n                        setWireframe(sceneConfig.sceneControls.wireframe);\n                        setGroundType(sceneConfig.sceneControls.groundType);\n                        // Restore camera position after a short delay to ensure scene is loaded\n                        setTimeout(()=>{\n                            if (orbitControlsRef.current && sceneConfig.camera) {\n                                orbitControlsRef.current.object.position.fromArray(sceneConfig.camera.position);\n                                orbitControlsRef.current.target.fromArray(sceneConfig.camera.target);\n                                orbitControlsRef.current.update();\n                            }\n                        }, 1000);\n                    } else if (configRef.current) {\n                        // For explicit loads with configRef, do a complete load\n                        console.log(\"Explicit load with configRef\");\n                        handleLoadScene(sceneConfig);\n                    }\n                    // Create a function to restore materials\n                    const restoreMaterials = ()=>{\n                        if (!sceneConfig.materials || !sceneObjects.length) {\n                            console.log(\"No materials to restore or scene objects not ready\");\n                            return;\n                        }\n                        console.log(\"Attempting to restore materials for objects:\", sceneObjects);\n                        console.log(\"Saved materials:\", sceneConfig.materials);\n                        const savedMaterialsMap = new Map(sceneConfig.materials.map((m)=>[\n                                m.name,\n                                m.material\n                            ]));\n                        sceneObjects.forEach((obj)=>{\n                            if (obj.material && savedMaterialsMap.has(obj.name)) {\n                                const savedMaterial = savedMaterialsMap.get(obj.name);\n                                console.log(\"Restoring material for object:\", obj.name, savedMaterial);\n                                (0,_utils_ArtistTool_materialUtils__WEBPACK_IMPORTED_MODULE_16__.applyMaterialToObject)(obj, savedMaterial);\n                            } else {\n                                console.log(\"Could not find saved material for object:\", obj.name);\n                            }\n                        });\n                    };\n                    // Set up an interval to check for scene objects\n                    const checkInterval = setInterval(()=>{\n                        if (sceneObjects.length > 0) {\n                            clearInterval(checkInterval);\n                            // Add a small delay to ensure all materials are properly initialized\n                            setTimeout(restoreMaterials, 500);\n                        }\n                    }, 100);\n                    // Clean up interval after 5 seconds if scene objects never become available\n                    setTimeout(()=>{\n                        clearInterval(checkInterval);\n                    }, 5000);\n                    // Restore camera position after a short delay to ensure scene is loaded\n                    setTimeout(()=>{\n                        if (orbitControlsRef.current && sceneConfig.camera) {\n                            orbitControlsRef.current.object.position.fromArray(sceneConfig.camera.position);\n                            orbitControlsRef.current.target.fromArray(sceneConfig.camera.target);\n                            orbitControlsRef.current.update();\n                        }\n                    }, 1000);\n                } catch (error) {\n                    console.error(\"Error loading saved scene:\", error);\n                }\n            }\n        };\n        // Load saved scene immediately on mount\n        loadSavedScene();\n        // Also set up an interval to check for scene readiness\n        const checkSceneReady = setInterval(()=>{\n            if (sceneRef.current && orbitControlsRef.current) {\n                clearInterval(checkSceneReady);\n                loadSavedScene();\n            }\n        }, 100);\n        return ()=>clearInterval(checkSceneReady);\n    }, [\n        sceneObjects,\n        selectedModel\n    ]);\n    const [configState, setConfigState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (sceneObjects.length > 0 && configState && configState.materials) {\n            const applyMaterials = ()=>{\n                console.log(\"Applying materials to scene objects:\", sceneObjects);\n                console.log(\"Saved materials configuration:\", configState.materials);\n                sceneObjects.forEach((obj)=>{\n                    const materialConfig = configState.materials.find((m)=>m.name === obj.name || m.id === obj.uuid);\n                    if (materialConfig && materialConfig.material) {\n                        console.log(\"Found material config for object:\", obj.name, materialConfig.material);\n                        (0,_utils_ArtistTool_materialUtils__WEBPACK_IMPORTED_MODULE_16__.applyMaterialToObject)(obj, materialConfig.material);\n                    } else {\n                        console.log(\"No material config found for object:\", obj.name);\n                    }\n                });\n            };\n            // Add a small delay to ensure all materials are properly initialized\n            const timeoutId = setTimeout(applyMaterials, 500);\n            return ()=>clearTimeout(timeoutId);\n        }\n    }, [\n        sceneObjects,\n        configState\n    ]);\n    const handleLoadScene = (config)=>{\n        try {\n            console.log(\"Loading scene configuration:\", config);\n            // Store the config in the ref\n            configRef.current = config;\n            workingConfigRef.current = config; // Always keep this in sync with last applied config\n            setConfigState(config); // <-- keep in sync for effect\n            // Clear scene objects and selected objects first\n            setSceneObjects([]);\n            setSelectedObjects([]);\n            setLastSelectedModel(null);\n            // Restore scene settings\n            setSelectedModel(\"uploaded\");\n            setLights(config.lights);\n            setEnvPreset(config.environment.preset);\n            setBgColor(config.environment.bgColor);\n            setShowEnvironment(config.environment.showEnvironment);\n            setShowLightSpheres(config.environment.showLightSpheres);\n            setEnvIntensity(config.environment.intensity);\n            setEnvBlur(config.environment.blur);\n            setEnvRotation(config.environment.rotation);\n            setPostProcessingEnabled(config.postProcessing.enabled);\n            setPostProcessingSettings(config.postProcessing.settings);\n            setShowGrid(config.sceneControls.showGrid);\n            setWireframe(config.sceneControls.wireframe);\n            setGroundType(config.sceneControls.groundType);\n            // Wait for the scene to update before applying camera position\n            setTimeout(()=>{\n                if (orbitControlsRef.current && config.camera) {\n                    orbitControlsRef.current.object.position.fromArray(config.camera.position);\n                    orbitControlsRef.current.target.fromArray(config.camera.target);\n                    orbitControlsRef.current.update();\n                }\n            }, 1000);\n            // Restore materials after sceneObjects are ready\n            const restoreMaterials = ()=>{\n                if (!config.materials || !sceneObjects.length) {\n                    console.log(\"No materials to restore or scene objects not ready\");\n                    return;\n                }\n                console.log(\"Restoring materials for objects:\", sceneObjects);\n                const savedMaterialsMap = new Map(config.materials.map((m)=>[\n                        m.name,\n                        m.material\n                    ]));\n                sceneObjects.forEach((obj)=>{\n                    if (obj.material && savedMaterialsMap.has(obj.name)) {\n                        const savedMaterial = savedMaterialsMap.get(obj.name);\n                        console.log(\"Restoring material for object:\", obj.name, savedMaterial);\n                        (0,_utils_ArtistTool_materialUtils__WEBPACK_IMPORTED_MODULE_16__.applyMaterialToObject)(obj, savedMaterial);\n                    } else {\n                        console.log(\"Could not find saved material for object:\", obj.name);\n                    }\n                });\n            };\n            if (sceneObjects.length > 0) {\n                setTimeout(restoreMaterials, 500);\n            } else {\n                const checkInterval = setInterval(()=>{\n                    if (sceneObjects.length > 0) {\n                        clearInterval(checkInterval);\n                        setTimeout(restoreMaterials, 500);\n                    }\n                }, 100);\n                setTimeout(()=>{\n                    clearInterval(checkInterval);\n                }, 5000);\n            }\n        } catch (error) {\n            console.error(\"Error loading scene configuration:\", error);\n        }\n    };\n    const handleSaveScene = async (sceneName, description, saveType)=>{\n        // Use workingConfigRef if it exists and matches the current state, otherwise build from state\n        let sceneConfig;\n        if (workingConfigRef.current) {\n            // Use a shallow compare for top-level keys (or always use workingConfigRef for now)\n            sceneConfig = {\n                ...workingConfigRef.current,\n                name: sceneName,\n                timestamp: new Date().toISOString()\n            };\n        } else {\n            var _orbitControlsRef_current, _orbitControlsRef_current1;\n            // fallback to building from state\n            sceneConfig = {\n                name: sceneName,\n                timestamp: new Date().toISOString(),\n                model: selectedModel,\n                lights: lights.map((light)=>{\n                    var _lightRefs_current_light_id;\n                    return {\n                        ...light,\n                        position: ((_lightRefs_current_light_id = lightRefs.current[light.id]) === null || _lightRefs_current_light_id === void 0 ? void 0 : _lightRefs_current_light_id.position.toArray()) || light.position\n                    };\n                }),\n                environment: {\n                    preset: envPreset,\n                    intensity: envIntensity,\n                    blur: envBlur,\n                    rotation: envRotation,\n                    showEnvironment,\n                    bgColor,\n                    customHdri\n                },\n                postProcessing: {\n                    enabled: postProcessingEnabled,\n                    settings: postProcessingSettings\n                },\n                sceneControls: {\n                    showGrid,\n                    wireframe,\n                    groundType,\n                    showLightSpheres\n                },\n                camera: {\n                    position: (_orbitControlsRef_current = orbitControlsRef.current) === null || _orbitControlsRef_current === void 0 ? void 0 : _orbitControlsRef_current.object.position.toArray(),\n                    target: (_orbitControlsRef_current1 = orbitControlsRef.current) === null || _orbitControlsRef_current1 === void 0 ? void 0 : _orbitControlsRef_current1.target.toArray()\n                },\n                materials: sceneObjects.map((obj)=>{\n                    const material = obj.material;\n                    if (!material) return {\n                        id: obj.uuid,\n                        name: obj.name,\n                        material: null\n                    };\n                    return {\n                        id: obj.uuid,\n                        name: obj.name,\n                        material: {\n                            type: material.type,\n                            color: material.color ? material.color.getHexString() : \"ffffff\",\n                            roughness: material.roughness,\n                            metalness: material.metalness,\n                            wireframe: material.wireframe,\n                            transparent: material.transparent,\n                            opacity: material.opacity,\n                            envMapIntensity: material.envMapIntensity,\n                            ...material.type === \"MeshRefractionMaterial\" && {\n                                aberrationStrength: material.aberrationStrength,\n                                toneMapped: material.toneMapped,\n                                ior: material.ior,\n                                colorEnvMapRotY: material.colorEnvMapRotY\n                            }\n                        }\n                    };\n                })\n            };\n        }\n        if (saveType === \"local\") {\n            // Save to localStorage\n            localStorage.setItem(\"agape_saved_scene\", JSON.stringify(sceneConfig));\n        } else if (saveType === \"db\") {\n            // Save to DB (cloud)\n            try {\n                const version = await (0,_lib_actions_version_actions__WEBPACK_IMPORTED_MODULE_24__.createVersion)({\n                    userId: user.id,\n                    workspaceId: project._id,\n                    name: sceneName,\n                    config: sceneConfig,\n                    description: description\n                });\n                console.log(\"version\", version);\n                if (version) {\n                    addVersion(project._id, version);\n                }\n                sonner__WEBPACK_IMPORTED_MODULE_25__.toast.success(\"Scene saved to cloud!\");\n            } catch (err) {\n                sonner__WEBPACK_IMPORTED_MODULE_25__.toast.error(\"Failed to save scene to cloud\");\n            }\n        } else {\n            // Export as file\n            const blob = new Blob([\n                JSON.stringify(sceneConfig, null, 2)\n            ], {\n                type: \"application/json\"\n            });\n            const url = URL.createObjectURL(blob);\n            const a = document.createElement(\"a\");\n            a.href = url;\n            a.download = \"\".concat(sceneName.replace(/\\s+/g, \"_\"), \".json\");\n            document.body.appendChild(a);\n            a.click();\n            document.body.removeChild(a);\n            URL.revokeObjectURL(url);\n        }\n    };\n    // Engraving state\n    const [engravingText, setEngravingText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [decalPosition, setDecalPosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        0,\n        1.68,\n        0\n    ]);\n    const [decalScale, setDecalScale] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        2.8,\n        0.9,\n        1.5\n    ]);\n    const [decalRotation, setDecalRotation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        -Math.PI / 2,\n        0,\n        0\n    ]);\n    const [decalColor, setDecalColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"#cbcbcb\");\n    const [selectedFont, setSelectedFont] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"/fonts/ttf/RobotoSerif-Regular.ttf\");\n    // Diamond shader state\n    const [useDiamondShader, setUseDiamondShader] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [usePremiumWhiteGold, setUsePremiumWhiteGold] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [usePremiumRoseGold, setUsePremiumRoseGold] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleEngraveText = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((text)=>{\n        setEngravingText(text);\n        // Store in localStorage to make it persist across panel changes and page reloads\n        localStorage.setItem(\"engravingText\", text);\n    }, []);\n    const handleFontChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((font)=>{\n        setSelectedFont(font);\n        localStorage.setItem(\"engravingFont\", font);\n    }, []);\n    // Load saved engraving text on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const savedEngravingText = localStorage.getItem(\"engravingText\");\n        if (savedEngravingText) {\n            setEngravingText(savedEngravingText);\n        }\n    }, []);\n    const handleDecalPlacement = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((event)=>{\n        handleDecalPlacement(event, setIsPlacingDecal, setDecalPosition, setDecalRotation);\n    }, [\n        setDecalPosition,\n        setDecalRotation,\n        setIsPlacingDecal\n    ]);\n    // Get predefined decal positions based on selected model\n    const getDefaultDecalPosition = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((modelId)=>{\n        const modelConfigs = {\n            chosenring: [\n                0,\n                1.68,\n                0\n            ],\n            testring: [\n                0,\n                1.68,\n                0\n            ],\n            agapering1: [\n                0,\n                1.2,\n                0\n            ],\n            agapering2: [\n                0,\n                1.2,\n                0\n            ],\n            ringa: [\n                0,\n                0.26,\n                0\n            ],\n            ringb: [\n                0,\n                0.5,\n                0\n            ],\n            ringc: [\n                0,\n                0.26,\n                0\n            ]\n        };\n        return modelConfigs[modelId] || [\n            0,\n            1.68,\n            0\n        ];\n    }, []);\n    const getDefaultDecalRotation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((modelId)=>{\n        const modelConfigs = {\n            chosenring: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            testring: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            agapering1: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            agapering2: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            ringa: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            ringb: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            ringc: [\n                -Math.PI / 2,\n                0,\n                0\n            ]\n        };\n        return modelConfigs[modelId] || [\n            -Math.PI / 2,\n            0,\n            0\n        ];\n    }, []);\n    const getDefaultDecalScale = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((modelId)=>{\n        const modelConfigs = {\n            chosenring: [\n                6.0,\n                2.0,\n                1.5\n            ],\n            testring: [\n                6.0,\n                2.0,\n                1.5\n            ],\n            agapering1: [\n                3.2,\n                1.8,\n                1.0\n            ],\n            agapering2: [\n                3.2,\n                1.8,\n                1.0\n            ],\n            ringa: [\n                1.8,\n                2.0,\n                0.8\n            ],\n            ringb: [\n                1.8,\n                2.0,\n                0.8\n            ],\n            ringc: [\n                1.8,\n                2.0,\n                0.8\n            ]\n        };\n        return modelConfigs[modelId] || [\n            4.2,\n            2.0,\n            1.5\n        ];\n    }, []);\n    // Calculate dynamic scale based on text length and model type\n    const calculateDynamicScale = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((baseScale, text, modelId)=>{\n        if (!text || text.length === 0) return baseScale;\n        const textLength = text.length;\n        let [scaleX, scaleY, scaleZ] = baseScale;\n        // Calculate scale multiplier based on text length\n        let scaleMultiplier = 1.0;\n        if (textLength > 10) {\n            // For very long text, scale up more aggressively\n            scaleMultiplier = 1.3 + (textLength - 10) * 0.08;\n        } else if (textLength > 6) {\n            // For medium length text, scale up moderately\n            scaleMultiplier = 1.0 + (textLength - 6) * 0.08;\n        } else if (textLength < 4) {\n            // For short text, scale down slightly to prevent oversized appearance\n            scaleMultiplier = 0.8 + textLength * 0.05;\n        }\n        // Apply text length scaling to X-axis (width) primarily\n        scaleX = scaleX * scaleMultiplier;\n        // For very long text, also adjust the height slightly\n        if (textLength > 8) {\n            scaleY = scaleY * Math.min(1.2, 1.0 + (textLength - 8) * 0.02);\n        }\n        // Cap maximum scale to prevent excessive sizing (updated for larger Y values)\n        const maxScale = {\n            chosenring: [\n                12.0,\n                2.5,\n                2.0\n            ],\n            testring: [\n                12.0,\n                2.5,\n                2.0\n            ],\n            agapering1: [\n                6.5,\n                2.2,\n                1.5\n            ],\n            agapering2: [\n                6.5,\n                2.2,\n                1.5\n            ],\n            ringa: [\n                4.0,\n                2.5,\n                1.2\n            ],\n            ringb: [\n                4.0,\n                2.5,\n                1.2\n            ],\n            ringc: [\n                4.0,\n                2.5,\n                1.2\n            ]\n        };\n        const limits = maxScale[modelId] || [\n            8.5,\n            2.5,\n            2.0\n        ];\n        scaleX = Math.min(scaleX, limits[0]);\n        scaleY = Math.min(scaleY, limits[1]);\n        scaleZ = Math.min(scaleZ, limits[2]);\n        return [\n            scaleX,\n            scaleY,\n            scaleZ\n        ];\n    }, []);\n    // Update scale when text changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedModel && engravingText) {\n            const baseScale = getDefaultDecalScale(selectedModel);\n            const dynamicScale = calculateDynamicScale(baseScale, engravingText, selectedModel);\n            setDecalScale(dynamicScale);\n        }\n    }, [\n        engravingText,\n        selectedModel,\n        getDefaultDecalScale,\n        calculateDynamicScale\n    ]);\n    // Initialize decal position based on selected model\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedModel) {\n            setDecalPosition(getDefaultDecalPosition(selectedModel));\n            setDecalRotation(getDefaultDecalRotation(selectedModel));\n            // Only set initial scale if there's no engraving text yet\n            if (!engravingText) {\n                setDecalScale(getDefaultDecalScale(selectedModel));\n            }\n        }\n    }, [\n        selectedModel,\n        getDefaultDecalPosition,\n        getDefaultDecalRotation,\n        getDefaultDecalScale,\n        engravingText\n    ]);\n    const [isFullscreen, setIsFullscreen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Get camera position based on device type\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const checkMobile = ()=>{\n            setIsMobile(window.innerWidth < 768);\n        };\n        checkMobile();\n        window.addEventListener(\"resize\", checkMobile);\n        return ()=>window.removeEventListener(\"resize\", checkMobile);\n    }, []);\n    // Add state to control outliner collapsed state - initialize based on device type\n    const [outlinerCollapsed, setOutlinerCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        // Initialize based on device type - closed on mobile, open on desktop\n        if (true) {\n            return window.innerWidth < 768;\n        }\n        return false; // Default to open on server-side rendering\n    });\n    // Effect to close outliner when toolbar panels are opened on mobile\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isMobile && rightPanelTab) {\n            setOutlinerCollapsed(true);\n        }\n    }, [\n        isMobile,\n        rightPanelTab\n    ]);\n    // Effect to update outliner collapsed state when mobile state changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Update outliner state based on device type - closed on mobile, open on desktop\n        setOutlinerCollapsed(isMobile);\n    }, [\n        isMobile\n    ]);\n    const getCameraPosition = ()=>{\n        if (isMobile) {\n            return [\n                -1.85,\n                3.25,\n                4.5\n            ];\n        }\n        return [\n            -1.85,\n            1.55,\n            3.5\n        ];\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // On initial mount, fetch and apply the current version from DB\n        const fetchAndApplyCurrentVersion = async ()=>{\n            try {\n                const currentVersion = await (0,_lib_actions_version_actions__WEBPACK_IMPORTED_MODULE_24__.getCurrentVersion)({\n                    userId: user.id,\n                    workspaceId: project._id\n                });\n                if (currentVersion && currentVersion.config) {\n                    handleLoadScene(currentVersion.config);\n                }\n            } catch (err) {\n                console.error(\"Failed to load current version from DB\", err);\n            }\n        };\n        fetchAndApplyCurrentVersion();\n    // Only run on mount\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_toastify__WEBPACK_IMPORTED_MODULE_2__.ToastContainer, {}, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1488,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Header__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                onReset: handleReset,\n                onScreenshot: captureScreenshot,\n                onSaveScene: handleSaveScene,\n                onLoadScene: handleLoadScene,\n                isFullscreen: isFullscreen,\n                onToggleFullscreen: ()=>setIsFullscreen(!isFullscreen),\n                project: project,\n                user: user\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1489,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_InitialOverlayPanel__WEBPACK_IMPORTED_MODULE_20__.InitialOverlayPanel, {}, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1500,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed left-5 top-1/2 transform -translate-y-1/2 z-[1000] transition-all duration-300 \".concat(isFullscreen ? \"opacity-0 pointer-events-none\" : \"opacity-100\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Toolbar__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    transformMode: transformMode,\n                    setTransformMode: setTransformMode,\n                    undoAction: undoAction,\n                    redoAction: redoAction,\n                    rightPanelTab: rightPanelTab,\n                    setRightPanelTab: setRightPanelTab,\n                    orientation: \"vertical\",\n                    sceneObjects: sceneObjects,\n                    setSelectedObjects: setSelectedObjects,\n                    selectedObjects: selectedObjects,\n                    panelRef: panelRef,\n                    isMobile: isMobile,\n                    setOutlinerCollapsed: setOutlinerCollapsed\n                }, void 0, false, {\n                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                    lineNumber: 1508,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1503,\n                columnNumber: 7\n            }, this),\n            rightPanelTab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: panelRef,\n                className: \"fixed left-20 top-1/2 transform -translate-y-1/2 bg-gradient-to-tl from-[#32343D] to-[#14192D] p-4 rounded-lg shadow-lg z-[1000] max-h-[80vh] overflow-y-auto w-[250px] md:w-[300px] border border-[#A3A3A3]/30 scrollbar-none\",\n                children: [\n                    rightPanelTab === \"transform\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TransformPanel__WEBPACK_IMPORTED_MODULE_13__.TransformPanel, {\n                        selectedObjects: selectedObjects,\n                        transformMode: transformMode,\n                        setTransformMode: setTransformMode,\n                        undoAction: undoAction,\n                        redoAction: redoAction\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1532,\n                        columnNumber: 13\n                    }, this),\n                    rightPanelTab === \"material\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MaterialPanel__WEBPACK_IMPORTED_MODULE_7__.MaterialPanel, {\n                        selectedObjects: selectedObjects,\n                        useDiamondShader: useDiamondShader,\n                        setUseDiamondShader: setUseDiamondShader,\n                        usePremiumWhiteGold: usePremiumWhiteGold,\n                        setUsePremiumWhiteGold: setUsePremiumWhiteGold,\n                        usePremiumRoseGold: usePremiumRoseGold,\n                        setUsePremiumRoseGold: setUsePremiumRoseGold,\n                        renderer: renderer\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1542,\n                        columnNumber: 13\n                    }, this),\n                    rightPanelTab === \"postprocessing\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PostProcessingPanel__WEBPACK_IMPORTED_MODULE_10__.PostProcessingPanel, {\n                        postProcessingEnabled: postProcessingEnabled,\n                        setPostProcessingEnabled: setPostProcessingEnabled,\n                        postProcessingSettings: postProcessingSettings,\n                        setPostProcessingSettings: setPostProcessingSettings\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1555,\n                        columnNumber: 13\n                    }, this),\n                    rightPanelTab === \"environment\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EnvironmentPanel__WEBPACK_IMPORTED_MODULE_4__.EnvironmentPanel, {\n                        envPreset: envPreset,\n                        setEnvPreset: setEnvPreset,\n                        bgColor: bgColor,\n                        setBgColor: setBgColor,\n                        showEnvironment: showEnvironment,\n                        setShowEnvironment: setShowEnvironment,\n                        customHdri: customHdri,\n                        setCustomHdri: setCustomHdri,\n                        envIntensity: envIntensity,\n                        setEnvIntensity: setEnvIntensity,\n                        showModelStats: showModelStats,\n                        setShowModelStats: setShowModelStats,\n                        envBlur: envBlur,\n                        setEnvBlur: setEnvBlur,\n                        envRotation: envRotation,\n                        setEnvRotation: setEnvRotation\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1564,\n                        columnNumber: 13\n                    }, this),\n                    rightPanelTab === \"lights\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LightPanel__WEBPACK_IMPORTED_MODULE_6__.LightPanel, {\n                        lights: lights,\n                        setLights: setLights,\n                        lightRefs: lightRefs,\n                        showGrid: showGrid,\n                        setShowGrid: setShowGrid,\n                        wireframe: wireframe,\n                        setWireframe: setWireframe,\n                        showLightSpheres: showLightSpheres,\n                        setShowLightSpheres: setShowLightSpheres,\n                        onResetLights: ()=>{\n                            // Reset lights to default configuration\n                            const defaultLights = [\n                                {\n                                    id: \"defaultDirectional\",\n                                    type: \"directional\",\n                                    position: [\n                                        2.5,\n                                        5.5,\n                                        5\n                                    ],\n                                    intensity: 1.0,\n                                    color: \"#ffffff\",\n                                    name: \"Main Directional Light\",\n                                    castShadow: true,\n                                    helperVisible: false\n                                },\n                                {\n                                    id: \"keyLight\",\n                                    type: \"point\",\n                                    position: [\n                                        5,\n                                        5,\n                                        5\n                                    ],\n                                    intensity: 8.0,\n                                    color: \"#ffffff\",\n                                    name: \"Key Light\"\n                                },\n                                {\n                                    id: \"fillLight\",\n                                    type: \"point\",\n                                    position: [\n                                        -5,\n                                        5,\n                                        -5\n                                    ],\n                                    intensity: 4.0,\n                                    color: \"#ffffff\",\n                                    name: \"Fill Light\"\n                                },\n                                {\n                                    id: \"backLight\",\n                                    type: \"point\",\n                                    position: [\n                                        0,\n                                        5,\n                                        -5\n                                    ],\n                                    intensity: 2.0,\n                                    color: \"#ffffff\",\n                                    name: \"Back Light\"\n                                }\n                            ];\n                            setLights(defaultLights);\n                            // Clear selected light\n                            setSelectedLight(null);\n                            // Clear light position history\n                            setLightPositionHistory({});\n                        }\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1585,\n                        columnNumber: 13\n                    }, this),\n                    rightPanelTab === \"engraving\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EngravingPanel__WEBPACK_IMPORTED_MODULE_19__.EngravingPanel, {\n                        onEngraveText: handleEngraveText,\n                        onDecalPositionChange: setDecalPosition,\n                        onDecalScaleChange: setDecalScale,\n                        onDecalRotationChange: setDecalRotation,\n                        decalPosition: decalPosition,\n                        decalScale: decalScale,\n                        decalRotation: decalRotation,\n                        decalColor: decalColor,\n                        onDecalColorChange: setDecalColor,\n                        onFontChange: handleFontChange,\n                        selectedFont: selectedFont,\n                        isPlacingDecal: isPlacingDecal,\n                        setIsPlacingDecal: setIsPlacingDecal,\n                        showDebug: showDecalDebug,\n                        onShowDebugChange: setShowDecalDebug\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1643,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1527,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed right-5 top-24 z-[999] transition-all duration-300 \".concat(isFullscreen ? \"opacity-0 pointer-events-none\" : \"opacity-100\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-tl from-[#32343D] to-[#14192D] shadow-lg rounded-lg overflow-visible scrollbar-none\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Outliner__WEBPACK_IMPORTED_MODULE_9__.Outliner, {\n                        sceneObjects: sceneObjects,\n                        selectedObjects: selectedObjects,\n                        onSelectObject: setSelectedObjects,\n                        onCameraView: handleCameraView,\n                        onPlayAnimation: handlePlayAnimation,\n                        onSaveScene: handleSaveScene,\n                        groundType: groundType,\n                        setGroundType: setGroundType,\n                        collapsed: outlinerCollapsed,\n                        setCollapsed: setOutlinerCollapsed\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1671,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                    lineNumber: 1670,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1665,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-full\",\n                style: {\n                    background: \"linear-gradient(to bottom right, #363643, #5C5C6E)\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_fiber__WEBPACK_IMPORTED_MODULE_29__.Canvas, {\n                    className: \"w-full h-full\",\n                    camera: {\n                        position: getCameraPosition(),\n                        fov: 45,\n                        near: 0.1,\n                        far: 50\n                    },\n                    shadows: true,\n                    ref: sceneRef,\n                    gl: {\n                        antialias: true,\n                        preserveDrawingBuffer: true\n                    },\n                    onPointerMissed: handlePointerMissed,\n                    raycaster: {\n                        computeOffsets: (e)=>({\n                                offsetX: e.clientX,\n                                offsetY: e.clientY\n                            })\n                    },\n                    frameloop: \"demand\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n                        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_30__.Html, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_31__.Loader, {}, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 1716,\n                                columnNumber: 17\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                            lineNumber: 1715,\n                            columnNumber: 15\n                        }, void 0),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ScreenshotTaker, {\n                                onScreenshotRef: (fn)=>{\n                                    screenshotRef.current = fn;\n                                }\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 1722,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RendererProvider, {\n                                onRendererReady: (gl)=>{\n                                    setRenderer(gl);\n                                }\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 1728,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SceneLights__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                lights: lights,\n                                lightRefs: lightRefs,\n                                selectedLight: selectedLight,\n                                onLightSelect: setSelectedLight,\n                                onLightMoved: handleLightMoved,\n                                showLightSpheres: showLightSpheres,\n                                threePointLighting: true\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 1734,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Environment__WEBPACK_IMPORTED_MODULE_3__.Environment, {\n                                preset: getEnvironmentPreset(),\n                                background: showEnvironment,\n                                bgColor: bgColor,\n                                customHdri: customHdri,\n                                intensity: envIntensity,\n                                blur: envBlur,\n                                envRotation: envRotation\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 1743,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SelectionOutline__WEBPACK_IMPORTED_MODULE_23__.SelectionOutline, {\n                                selectedObjects: selectedObjects,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ModelStage__WEBPACK_IMPORTED_MODULE_21__.ModelStage, {\n                                    selectedModel: selectedModel,\n                                    wireframe: wireframe,\n                                    handleSceneUpdate: handleSceneUpdate,\n                                    handleObjectClick: handleObjectClick,\n                                    uploadedModel: uploadedModel,\n                                    groundType: groundType,\n                                    engravingText: engravingText,\n                                    activeAnimation: activeAnimation,\n                                    decalPosition: decalPosition,\n                                    decalScale: decalScale,\n                                    decalRotation: decalRotation,\n                                    decalColor: decalColor,\n                                    font: selectedFont,\n                                    isPlacingDecal: isPlacingDecal,\n                                    onDecalPlacement: handleDecalPlacement,\n                                    showDecalDebug: showDecalDebug,\n                                    sceneObjects: sceneObjects,\n                                    useDiamondShader: useDiamondShader,\n                                    usePremiumWhiteGold: usePremiumWhiteGold,\n                                    usePremiumRoseGold: usePremiumRoseGold\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                    lineNumber: 1754,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 1753,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_32__.OrbitControls, {\n                                ref: orbitControlsRef,\n                                target: [\n                                    0,\n                                    0.5,\n                                    0\n                                ],\n                                makeDefault: true,\n                                enableDamping: true,\n                                // minPolarAngle={0}\n                                // maxPolarAngle={Math.PI / 2.25}\n                                // minDistance={2.5}\n                                maxDistance: 20\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 1778,\n                                columnNumber: 13\n                            }, this),\n                            selectedObjects.length > 0 && rightPanelTab === \"transform\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_33__.TransformControls, {\n                                object: selectedObjects.length === 1 ? selectedObjects[0] : selectionGroupRef.current,\n                                mode: transformMode,\n                                onMouseDown: ()=>{\n                                    if (orbitControlsRef.current) orbitControlsRef.current.enabled = false;\n                                },\n                                onMouseUp: ()=>{\n                                    if (orbitControlsRef.current) orbitControlsRef.current.enabled = true;\n                                    if (selectedObjects.length > 1) updateObjectsFromGroup();\n                                },\n                                onObjectChange: ()=>{\n                                    if (selectedObjects.length > 1) updateObjectsFromGroup();\n                                }\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 1790,\n                                columnNumber: 15\n                            }, this),\n                            groundType === \"grid\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"gridHelper\", {\n                                args: [\n                                    50,\n                                    50,\n                                    \"#666666\",\n                                    \"#333333\"\n                                ],\n                                receiveShadow: true\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 1812,\n                                columnNumber: 15\n                            }, this),\n                            groundType === \"solid\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n                                \"rotation-x\": -Math.PI / 2,\n                                receiveShadow: true,\n                                position: [\n                                    0,\n                                    -0.01,\n                                    0\n                                ],\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"planeGeometry\", {\n                                        args: [\n                                            200,\n                                            200\n                                        ]\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 1820,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meshStandardMaterial\", {\n                                        color: \"#5d5d5d\",\n                                        wireframe: wireframe,\n                                        transparent: true,\n                                        opacity: 0.7\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 1821,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 1815,\n                                columnNumber: 15\n                            }, this),\n                            groundType === \"reflective\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n                                receiveShadow: true,\n                                rotation: [\n                                    -Math.PI / 2,\n                                    0,\n                                    0\n                                ],\n                                position: [\n                                    0,\n                                    -0.01,\n                                    0\n                                ],\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"planeGeometry\", {\n                                        args: [\n                                            80,\n                                            80\n                                        ]\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 1835,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_34__.MeshReflectorMaterial, {\n                                        blur: [\n                                            800,\n                                            800\n                                        ],\n                                        resolution: 2048,\n                                        mixBlur: 0.5,\n                                        mixStrength: 10,\n                                        roughness: 1,\n                                        depthScale: 1,\n                                        minDepthThreshold: 0.4,\n                                        maxDepthThreshold: 1.2,\n                                        color: \"#363643\",\n                                        metalness: 0.01,\n                                        mirror: 0.3,\n                                        transparent: true,\n                                        opacity: 0.5\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 1836,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 1830,\n                                columnNumber: 15\n                            }, this),\n                            groundType === \"none\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n                                receiveShadow: true,\n                                rotation: [\n                                    -Math.PI / 2,\n                                    0,\n                                    0\n                                ],\n                                position: [\n                                    0,\n                                    -0.01,\n                                    0\n                                ],\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"planeGeometry\", {\n                                        args: [\n                                            200,\n                                            200\n                                        ]\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 1859,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"primitive\", {\n                                        object: new three__WEBPACK_IMPORTED_MODULE_28__.ShadowMaterial({\n                                            opacity: 0.1,\n                                            color: new three__WEBPACK_IMPORTED_MODULE_28__.Color(0x000000),\n                                            transparent: true\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 1860,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 1854,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_35__.GizmoHelper, {\n                                alignment: \"bottom-right\",\n                                margin: [\n                                    80,\n                                    80\n                                ],\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_36__.GizmoViewport, {\n                                    labelColor: \"white\",\n                                    axisColors: [\n                                        \"red\",\n                                        \"green\",\n                                        \"blue\"\n                                    ]\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                    lineNumber: 1872,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 1871,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"primitive\", {\n                                object: selectionGroupRef.current\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 1878,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PostProcessingEffects__WEBPACK_IMPORTED_MODULE_22__.PostProcessingEffects, {\n                                enabled: postProcessingEnabled,\n                                settings: postProcessingSettings\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 1880,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1713,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                    lineNumber: 1693,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1687,\n                columnNumber: 7\n            }, this),\n            showModelStats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ModelStats__WEBPACK_IMPORTED_MODULE_8__.ModelStats, {\n                stats: modelStats\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1888,\n                columnNumber: 26\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s2(EditorExperience, \"wrcaOker5RNRINZmW/VnKj5Y1XA=\", false, function() {\n    return [\n        _lib_states__WEBPACK_IMPORTED_MODULE_26__.useVersionState\n    ];\n});\n_c3 = EditorExperience;\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"ScreenshotTaker\");\n$RefreshReg$(_c1, \"MaterialPanelWrapper\");\n$RefreshReg$(_c2, \"RendererProvider\");\n$RefreshReg$(_c3, \"EditorExperience\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL0FydGlzdFRvb2wvRWRpdG9yRXhwZXJpZW5jZS5qc3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFVMkI7QUFDMkI7QUFDcUI7QUFDM0I7QUFFakI7QUFDYTtBQUNVO0FBQ3hCO0FBQ1k7QUFDTTtBQUNOO0FBQ0o7QUFDc0I7QUFDcEI7QUFDUjtBQUNrQjtBQUVpQjtBQUl0QjtBQUNnQztBQUlsQztBQUMwQjtBQUNuQjtBQUNVO0FBQ2xCO0FBQ3NCO0FBQ1Y7QUFJZjtBQUNSO0FBQ2dCO0FBRS9DLE1BQU0yQyxrQkFBa0I7UUFBQyxFQUFFQyxlQUFlLEVBQUU7O0lBQzFDLE1BQU0sRUFBRUMsRUFBRSxFQUFFQyxLQUFLLEVBQUVDLE1BQU0sRUFBRSxHQUFHdkMsc0RBQVFBO0lBRXRDRyxnREFBU0EsQ0FBQztRQUNSLElBQUksT0FBT2lDLG9CQUFvQixZQUFZO1lBQ3pDQSxnQkFBZ0I7Z0JBQ2QsSUFBSTtvQkFDRixtQkFBbUI7b0JBQ25CQyxHQUFHRyxNQUFNLENBQUNGLE9BQU9DO29CQUVqQiwwQkFBMEI7b0JBQzFCLE1BQU1FLFVBQVVKLEdBQUdLLFVBQVUsQ0FBQ0MsU0FBUyxDQUFDO29CQUV4QyxnQ0FBZ0M7b0JBQ2hDLE1BQU1DLE9BQU9DLFNBQVNDLGFBQWEsQ0FBQztvQkFDcEMsTUFBTUMsWUFBWSxJQUFJQyxPQUNuQkMsV0FBVyxHQUNYQyxPQUFPLENBQUMsTUFBTSxLQUNkQyxTQUFTLENBQUMsR0FBRztvQkFDaEJQLEtBQUtRLFFBQVEsR0FBRyxlQUF5QixPQUFWTCxXQUFVO29CQUN6Q0gsS0FBS1MsSUFBSSxHQUFHWjtvQkFDWkksU0FBU1MsSUFBSSxDQUFDQyxXQUFXLENBQUNYO29CQUMxQkEsS0FBS1ksS0FBSztvQkFFVixXQUFXO29CQUNYQyxXQUFXO3dCQUNUWixTQUFTUyxJQUFJLENBQUNJLFdBQVcsQ0FBQ2Q7b0JBQzVCLEdBQUc7b0JBRUhlLFFBQVFDLEdBQUcsQ0FBQztvQkFDWixPQUFPO2dCQUNULEVBQUUsT0FBT0MsT0FBTztvQkFDZEYsUUFBUUUsS0FBSyxDQUFDLCtCQUErQkE7b0JBQzdDLE9BQU87Z0JBQ1Q7WUFDRjtRQUNGO0lBQ0YsR0FBRztRQUFDeEI7UUFBSUM7UUFBT0M7UUFBUUg7S0FBZ0I7SUFFdkMsT0FBTztBQUNUO0dBeENNRDs7UUFDMEJuQyxrREFBUUE7OztLQURsQ21DO0FBMENOLGlEQUFpRDtBQUNqRCxNQUFNMkIsdUJBQXVCO1FBQUMsRUFBRUMsUUFBUSxFQUFFLEdBQUdDLE9BQU87SUFDbEQscUJBQU8sOERBQUNwRCx5REFBYUE7UUFBQ21ELFVBQVVBO1FBQVcsR0FBR0MsS0FBSzs7Ozs7O0FBQ3JEO01BRk1GO0FBSU4sZ0RBQWdEO0FBQ2hELE1BQU1HLG1CQUFtQjtRQUFDLEVBQUVDLFFBQVEsRUFBRUMsZUFBZSxFQUFFOztJQUNyRCxNQUFNLEVBQUU5QixFQUFFLEVBQUUsR0FBR3JDLHNEQUFRQTtJQUV2QkcsZ0RBQVNBLENBQUM7UUFDUixJQUFJZ0UsaUJBQWlCO1lBQ25CQSxnQkFBZ0I5QjtRQUNsQjtJQUNGLEdBQUc7UUFBQ0E7UUFBSThCO0tBQWdCO0lBRXhCLE9BQU87QUFDVDtJQVZNRjs7UUFDV2pFLGtEQUFRQTs7O01BRG5CaUU7QUFZUyxTQUFTRyxpQkFBaUIsS0FBMkI7UUFBM0IsRUFBRUMsUUFBUSxFQUFFQyxPQUFPLEVBQUVDLElBQUksRUFBRSxHQUEzQjs7SUFDdkMsTUFBTSxFQUFFQyxVQUFVLEVBQUUsR0FBR3RDLDZEQUFlQTtJQUV0QyxNQUFNdUMsWUFBWXJFLDZDQUFNQSxDQUFDO0lBQ3pCLE1BQU1zRSxtQkFBbUJ0RSw2Q0FBTUEsQ0FBQztJQUNoQyxNQUFNLENBQUN1RSxlQUFlQyxpQkFBaUIsR0FBR3ZFLCtDQUFRQSxDQUFDO0lBQ25ELE1BQU0sQ0FBQ3dFLG1CQUFtQkMscUJBQXFCLEdBQUd6RSwrQ0FBUUEsQ0FBQztJQUMzRCxNQUFNLENBQUMwRSxlQUFlQyxpQkFBaUIsR0FBRzNFLCtDQUFRQSxDQUFDZ0UsWUFBWTtJQUMvRCxNQUFNLENBQUNZLFlBQVlDLGNBQWMsR0FBRzdFLCtDQUFRQSxDQUFDO1FBQzNDOEUsVUFBVTtRQUNWQyxXQUFXO1FBQ1hDLFdBQVc7UUFDWEMsWUFBWTtJQUNkO0lBQ0EsTUFBTSxDQUFDQyxRQUFRQyxVQUFVLEdBQUduRiwrQ0FBUUEsQ0FBQztRQUNuQztZQUNFb0YsSUFBSTtZQUNKQyxNQUFNO1lBQ05DLFVBQVU7Z0JBQUM7Z0JBQUs7Z0JBQUs7YUFBRTtZQUN2QkMsV0FBVztZQUNYQyxPQUFPO1lBQ1BDLE1BQU07WUFDTkMsWUFBWTtZQUNaQyxlQUFlO1FBQ2pCO1FBQ0E7WUFDRVAsSUFBSTtZQUNKQyxNQUFNO1lBQ05DLFVBQVU7Z0JBQUM7Z0JBQUc7Z0JBQUc7YUFBRTtZQUNuQkMsV0FBVztZQUNYQyxPQUFPO1lBQ1BDLE1BQU07UUFDUjtRQUNBO1lBQ0VMLElBQUk7WUFDSkMsTUFBTTtZQUNOQyxVQUFVO2dCQUFDLENBQUM7Z0JBQUc7Z0JBQUcsQ0FBQzthQUFFO1lBQ3JCQyxXQUFXO1lBQ1hDLE9BQU87WUFDUEMsTUFBTTtRQUNSO1FBQ0E7WUFDRUwsSUFBSTtZQUNKQyxNQUFNO1lBQ05DLFVBQVU7Z0JBQUM7Z0JBQUc7Z0JBQUcsQ0FBQzthQUFFO1lBQ3BCQyxXQUFXO1lBQ1hDLE9BQU87WUFDUEMsTUFBTTtRQUNSO0tBQ0Q7SUFDRCxNQUFNRyxZQUFZN0YsNkNBQU1BLENBQUMsQ0FBQztJQUMxQixNQUFNLENBQUM4RixpQkFBaUJDLG1CQUFtQixHQUFHOUYsK0NBQVFBLENBQUMsRUFBRTtJQUN6RCxNQUFNLENBQUMrRixlQUFlQyxpQkFBaUIsR0FBR2hHLCtDQUFRQSxDQUFDO0lBQ25ELE1BQU0sQ0FBQ2lHLHNCQUFzQkMsd0JBQXdCLEdBQUdsRywrQ0FBUUEsQ0FBQyxDQUFDO0lBQ2xFLE1BQU0sQ0FBQ21HLGNBQWNDLGdCQUFnQixHQUFHcEcsK0NBQVFBLENBQUMsRUFBRTtJQUNuRCxNQUFNLENBQUNxRyxlQUFlQyxpQkFBaUIsR0FBR3RHLCtDQUFRQSxDQUFDO0lBQ25ELE1BQU11RyxvQkFBb0J4Ryw2Q0FBTUEsQ0FBQyxJQUFJRyx5Q0FBVztJQUVoRG9ELFFBQVFDLEdBQUcsQ0FBQztJQUVaLE1BQU0sQ0FBQ2tELGVBQWVDLGlCQUFpQixHQUFHMUcsK0NBQVFBLENBQUM7SUFFbkQsd0JBQXdCO0lBQ3hCLE1BQU0sQ0FBQzJHLGdCQUFnQkMsa0JBQWtCLEdBQUc1RywrQ0FBUUEsQ0FBQztJQUNyRCxNQUFNLENBQUM2RyxnQkFBZ0JDLGtCQUFrQixHQUFHOUcsK0NBQVFBLENBQUM7SUFFckQsdUJBQXVCO0lBQ3ZCLE1BQU0sQ0FBQytHLFVBQVVDLFlBQVksR0FBR2hILCtDQUFRQSxDQUFDO0lBQ3pDLE1BQU0sQ0FBQ2lILFdBQVdDLGFBQWEsR0FBR2xILCtDQUFRQSxDQUFDO0lBQzNDLE1BQU0sQ0FBQ21ILFlBQVlDLGNBQWMsR0FBR3BILCtDQUFRQSxDQUFDO0lBQzdDLE1BQU0sQ0FBQ3FILFdBQVdDLGFBQWEsR0FBR3RILCtDQUFRQSxDQUFDO0lBQzNDLE1BQU0sQ0FBQ3VILFNBQVNDLFdBQVcsR0FBR3hILCtDQUFRQSxDQUFDO0lBQ3ZDLE1BQU0sQ0FBQ3lILGlCQUFpQkMsbUJBQW1CLEdBQUcxSCwrQ0FBUUEsQ0FBQztJQUN2RCxNQUFNLENBQUMySCxrQkFBa0JDLG9CQUFvQixHQUFHNUgsK0NBQVFBLENBQUM7SUFDekQsTUFBTSxDQUFDNkgsWUFBWUMsY0FBYyxHQUFHOUgsK0NBQVFBLENBQUM7SUFDN0MsTUFBTSxDQUFDK0gsY0FBY0MsZ0JBQWdCLEdBQUdoSSwrQ0FBUUEsQ0FBQztJQUNqRCxNQUFNLENBQUNpSSxTQUFTQyxXQUFXLEdBQUdsSSwrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUNtSSxhQUFhQyxlQUFlLEdBQUdwSSwrQ0FBUUEsQ0FBQztJQUUvQyxNQUFNLENBQUNxSSx1QkFBdUJDLHlCQUF5QixHQUFHdEksK0NBQVFBLENBQUM7SUFDbkUsTUFBTSxDQUFDdUksd0JBQXdCQywwQkFBMEIsR0FBR3hJLCtDQUFRQSxDQUFDO1FBQ25FeUksV0FBVztZQUNUQyxTQUFTO1lBQ1RDLFlBQVk7UUFDZDtRQUNBQyxPQUFPO1lBQ0xGLFNBQVM7WUFDVG5ELFdBQVc7WUFDWHNELFdBQVc7WUFDWEMsUUFBUTtRQUNWO1FBQ0FDLEtBQUs7WUFDSEwsU0FBUztZQUNUTSxlQUFlO1lBQ2ZDLFVBQVU7WUFDVk4sWUFBWTtRQUNkO1FBQ0FPLFVBQVU7WUFDUlIsU0FBUztZQUNUUyxVQUFVO1lBQ1ZDLFFBQVE7UUFDVjtJQUNGO0lBRUEsc0JBQXNCO0lBQ3RCLE1BQU1DLGtCQUFrQnRKLDZDQUFNQSxDQUFDO1FBQzdCdUUsZUFBZTtRQUNmWSxRQUFRLEVBQUU7UUFDVm1DLFdBQVc7UUFDWEUsU0FBUztRQUNUZ0Isd0JBQXdCO1FBQ3hCeEIsVUFBVTtRQUNWRSxXQUFXO1FBQ1hFLFlBQVk7UUFDWk0saUJBQWlCO1FBQ2pCRSxrQkFBa0I7UUFDbEJJLGNBQWM7UUFDZEUsU0FBUztRQUNUcUIsZ0JBQWdCO1FBQ2hCQyxjQUFjO0lBQ2hCO0lBRUEsd0RBQXdEO0lBQ3hELE1BQU1DLHVCQUF1QnpKLDZDQUFNQSxDQUFDO1FBQ2xDMEosU0FBUyxFQUFFO0lBQ2I7SUFFQSw4Q0FBOEM7SUFDOUMzSixnREFBU0EsQ0FBQztRQUNSdUosZ0JBQWdCSyxPQUFPLEdBQUc7WUFDeEJwRjtZQUNBWSxRQUFRO21CQUFJQTthQUFPO1lBQ25CbUM7WUFDQUU7WUFDQWdCLHdCQUF3QjtnQkFBRSxHQUFHQSxzQkFBc0I7WUFBQztZQUNwRHhCO1lBQ0FFO1lBQ0FFO1lBQ0FNO1lBQ0FFO1lBQ0FJO1lBQ0FFO1lBQ0FxQixnQkFBZ0IsSUFBSXBKLDJDQUFhLENBQUMsR0FBRyxHQUFHO1lBQ3hDcUosY0FBYyxJQUFJckosMkNBQWEsQ0FBQyxHQUFHLEdBQUc7UUFDeEM7SUFDRixHQUFHLEVBQUU7SUFFTCxNQUFNMEosbUJBQW1CN0osNkNBQU1BO0lBQy9CLE1BQU04SixXQUFXOUosNkNBQU1BO0lBQ3ZCLE1BQU0sQ0FBQzJELFVBQVVvRyxZQUFZLEdBQUc5SiwrQ0FBUUEsQ0FBQztJQUV6Qyw4QkFBOEI7SUFDOUIsTUFBTSxDQUFDK0osU0FBU0MsV0FBVyxHQUFHaEssK0NBQVFBLENBQUMsRUFBRTtJQUN6QyxNQUFNLENBQUNpSyxjQUFjQyxnQkFBZ0IsR0FBR2xLLCtDQUFRQSxDQUFDLENBQUM7SUFDbEQsTUFBTSxDQUFDbUssa0JBQWtCQyxvQkFBb0IsR0FBR3BLLCtDQUFRQSxDQUFDO0lBRXpELE1BQU0sQ0FBQ3FLLGdCQUFnQkMsa0JBQWtCLEdBQUd0SywrQ0FBUUEsQ0FBQztJQUVyRCxpQ0FBaUM7SUFDakMsTUFBTXVLLGVBQWUxSyxrREFBV0EsQ0FDOUIsQ0FBQzJLO1FBQ0MsSUFBSUwsa0JBQWtCO1lBQ3BCQyxvQkFBb0I7WUFDcEI7UUFDRjtRQUVBLE1BQU1LLFdBQVd6SixtRkFBa0JBLENBQ2pDNkUsaUJBQ0FYLFFBQ0FVLFVBQVU4RCxPQUFPO1FBRW5CLE1BQU1nQixhQUFhWCxRQUFRWSxLQUFLLENBQUMsR0FBR1YsZUFBZTtRQUNuREQsV0FBVztlQUFJVTtZQUFZRDtTQUFTO1FBQ3BDUCxnQkFBZ0JELGVBQWU7SUFDakMsR0FDQTtRQUFDRjtRQUFTRTtRQUFjRTtRQUFrQnRFO1FBQWlCWDtLQUFPO0lBR3BFLGNBQWM7SUFDZCxNQUFNMEYsYUFBYS9LLGtEQUFXQSxDQUFDO1FBQzdCLElBQUlvSyxlQUFlLEdBQUc7WUFDcEJHLG9CQUFvQjtZQUNwQixNQUFNUyxZQUFZZCxPQUFPLENBQUNFLGVBQWUsRUFBRTtZQUMzQ2xKLGtGQUFpQkEsQ0FBQzhKLFdBQVcxRSxjQUFjUCxVQUFVOEQsT0FBTztZQUM1RFEsZ0JBQWdCRCxlQUFlO1FBQ2pDO0lBQ0YsR0FBRztRQUFDQTtRQUFjRjtRQUFTNUQ7S0FBYTtJQUV4QyxjQUFjO0lBQ2QsTUFBTTJFLGFBQWFqTCxrREFBV0EsQ0FBQztRQUM3QixJQUFJb0ssZUFBZUYsUUFBUWdCLE1BQU0sR0FBRyxHQUFHO1lBQ3JDWCxvQkFBb0I7WUFDcEIsTUFBTVksWUFBWWpCLE9BQU8sQ0FBQ0UsZUFBZSxFQUFFO1lBQzNDbEosa0ZBQWlCQSxDQUFDaUssV0FBVzdFLGNBQWNQLFVBQVU4RCxPQUFPO1lBQzVEUSxnQkFBZ0JELGVBQWU7UUFDakM7SUFDRixHQUFHO1FBQUNBO1FBQWNGO1FBQVM1RDtLQUFhO0lBRXhDLGtEQUFrRDtJQUNsRHJHLGdEQUFTQSxDQUFDO1FBQ1IsTUFBTW1MLGVBQWU7WUFDbkIsSUFBSXBGLGdCQUFnQmtGLE1BQU0sR0FBRyxLQUFLN0YsT0FBTzZGLE1BQU0sR0FBRyxHQUFHO2dCQUNuRFIsYUFBYTtvQkFDWDFFO29CQUNBWDtnQkFDRjtZQUNGO1FBQ0Y7UUFFQSw2Q0FBNkM7UUFDN0MsTUFBTWdHLFlBQVk5SCxXQUFXNkgsY0FBYztRQUMzQyxPQUFPLElBQU1FLGFBQWFEO0lBQzVCLEdBQUc7UUFBQ3JGO1FBQWlCWDtRQUFRcUY7S0FBYTtJQUUxQyxNQUFNYSxvQkFBb0J2TCxrREFBV0EsQ0FDbkMsQ0FBQ3dMO1FBQ0NBLEVBQUVDLGVBQWU7UUFFakIsOEZBQThGO1FBQzlGLElBQUkzRSxnQkFBZ0I7WUFDbEI7UUFDRjtRQUVBckQsUUFBUUMsR0FBRyxDQUFDLG1CQUFtQjhILEVBQUVFLE1BQU0sQ0FBQzlGLElBQUksRUFBRTRGLEVBQUVFLE1BQU07UUFFdEQsSUFBSUYsRUFBRUcsUUFBUSxFQUFFO1lBQ2QxRixtQkFBbUIsQ0FBQzJGO2dCQUNsQixpQ0FBaUM7Z0JBQ2pDLE1BQU1DLFlBQVlDLE1BQU1DLE9BQU8sQ0FBQ0gsUUFBUUEsT0FBTztvQkFBQ0E7aUJBQUssQ0FBQ0ksTUFBTSxDQUFDQztnQkFFN0QsSUFBSUosVUFBVUssUUFBUSxDQUFDVixFQUFFRSxNQUFNLEdBQUc7b0JBQ2hDLE9BQU9HLFVBQVVHLE1BQU0sQ0FBQyxDQUFDRyxNQUFRQSxRQUFRWCxFQUFFRSxNQUFNO2dCQUNuRDtnQkFDQSxPQUFPO3VCQUFJRztvQkFBV0wsRUFBRUUsTUFBTTtpQkFBQztZQUNqQztRQUNGLE9BQU87WUFDTHpGLG1CQUFtQjtnQkFBQ3VGLEVBQUVFLE1BQU07YUFBQztRQUMvQjtJQUNGLEdBQ0E7UUFBQzVFO1FBQWdCYjtLQUFtQjtJQUd0Qyw0Q0FBNEM7SUFDNUNoRyxnREFBU0EsQ0FBQztRQUNSd0QsUUFBUUMsR0FBRyxDQUFDLDRDQUE0Q3NDO1FBQ3hEdkMsUUFBUUMsR0FBRyxDQUNULDJDQUNBc0MsNEJBQUFBLHNDQUFBQSxnQkFBaUJrRixNQUFNO0lBRTNCLEdBQUc7UUFBQ2xGO0tBQWdCO0lBRXBCLHlDQUF5QztJQUN6Qy9GLGdEQUFTQSxDQUFDO1FBQ1J3RCxRQUFRQyxHQUFHLENBQUMseUNBQXlDNEM7UUFDckQ3QyxRQUFRQyxHQUFHLENBQUMsd0NBQXdDNEMseUJBQUFBLG1DQUFBQSxhQUFjNEUsTUFBTTtRQUN4RXpILFFBQVFDLEdBQUcsQ0FDVCx5Q0FDQTRDLHlCQUFBQSxtQ0FBQUEsYUFBYzhGLEdBQUcsQ0FBQyxDQUFDRCxNQUFTO2dCQUMxQnZHLElBQUksRUFBRXVHLGdCQUFBQSwwQkFBQUEsSUFBS3ZHLElBQUk7Z0JBQ2ZKLElBQUksRUFBRTJHLGdCQUFBQSwwQkFBQUEsSUFBSzNHLElBQUk7Z0JBQ2Y2RyxRQUFRLEVBQUVGLGdCQUFBQSwwQkFBQUEsSUFBS0UsUUFBUTtZQUN6QjtJQUVKLEdBQUc7UUFBQy9GO0tBQWE7SUFFakIsTUFBTWdHLHNCQUFzQnRNLGtEQUFXQSxDQUFDO1FBQ3RDLDBFQUEwRTtRQUMxRSxxRkFBcUY7UUFDckYsdUVBQXVFO1FBQ3ZFLElBQUk4RyxnQkFBZ0I7WUFDbEIsNEVBQTRFO1lBQzVFO1FBQ0Y7UUFDQWIsbUJBQW1CLEVBQUU7UUFDckJFLGlCQUFpQjtJQUNqQix1R0FBdUc7SUFDekcsR0FBRztRQUFDVztRQUFnQkM7S0FBa0I7SUFFdEMsdUNBQXVDO0lBQ3ZDOUcsZ0RBQVNBLENBQUM7UUFDUixnRUFBZ0U7UUFDaEUsT0FBTztZQUNMLHNCQUFzQjtZQUN0QixJQUFJeUcsa0JBQWtCbUQsT0FBTyxJQUFJbkQsa0JBQWtCbUQsT0FBTyxDQUFDMEMsTUFBTSxFQUFFO2dCQUNqRTdGLGtCQUFrQm1ELE9BQU8sQ0FBQzBDLE1BQU0sQ0FBQ0MsTUFBTSxDQUFDOUYsa0JBQWtCbUQsT0FBTztZQUNuRTtRQUNGO0lBQ0YsR0FBRyxFQUFFO0lBRUw1SixnREFBU0EsQ0FBQztRQUNSLDRDQUE0QztRQUM1QyxNQUFNd00sZUFBZVgsTUFBTUMsT0FBTyxDQUFDL0YsbUJBQy9CQSxrQkFDQTtZQUFDQTtTQUFnQixDQUFDZ0csTUFBTSxDQUFDQztRQUU3QixNQUFPdkYsa0JBQWtCbUQsT0FBTyxDQUFDN0YsUUFBUSxDQUFDa0gsTUFBTSxHQUFHLEVBQUc7WUFDcER4RSxrQkFBa0JtRCxPQUFPLENBQUMyQyxNQUFNLENBQUM5RixrQkFBa0JtRCxPQUFPLENBQUM3RixRQUFRLENBQUMsRUFBRTtRQUN4RTtRQUVBLElBQUl5SSxhQUFhdkIsTUFBTSxHQUFHLEdBQUc7WUFDM0IsTUFBTXdCLFNBQVMsSUFBSXJNLDJDQUFhO1lBQ2hDb00sYUFBYUUsT0FBTyxDQUFDLENBQUNSO2dCQUNwQk8sT0FBT0UsR0FBRyxDQUFDVCxJQUFJMUcsUUFBUTtZQUN6QjtZQUNBaUgsT0FBT0csWUFBWSxDQUFDSixhQUFhdkIsTUFBTTtZQUV2Q3hFLGtCQUFrQm1ELE9BQU8sQ0FBQ3BFLFFBQVEsQ0FBQ3FILElBQUksQ0FBQ0o7WUFFeENELGFBQWFFLE9BQU8sQ0FBQyxDQUFDUjtnQkFDcEIsTUFBTVksUUFBUVosSUFBSVksS0FBSztnQkFDdkIscUVBQXFFO2dCQUNyRUEsTUFBTUMsUUFBUSxDQUFDLENBQUNDO29CQUNkLElBQUlBLE1BQU1DLE1BQU0sRUFBRTt3QkFDaEJELE1BQU1FLE9BQU8sR0FBRztvQkFDbEI7Z0JBQ0Y7Z0JBQ0FKLE1BQU10SCxRQUFRLENBQUMySCxHQUFHLENBQUNWO2dCQUNuQmhHLGtCQUFrQm1ELE9BQU8sQ0FBQytDLEdBQUcsQ0FBQ0c7WUFDaEM7UUFDRjtJQUNGLEdBQUc7UUFBQy9HO0tBQWdCO0lBRXBCLE1BQU1xSCx5QkFBeUJyTixrREFBV0EsQ0FBQztRQUN6Qyw0Q0FBNEM7UUFDNUMsTUFBTXlNLGVBQWVYLE1BQU1DLE9BQU8sQ0FBQy9GLG1CQUMvQkEsa0JBQ0E7WUFBQ0E7U0FBZ0IsQ0FBQ2dHLE1BQU0sQ0FBQ0M7UUFFN0IsSUFBSVEsYUFBYXZCLE1BQU0sR0FBRyxLQUFLeEUsa0JBQWtCbUQsT0FBTyxFQUFFO1lBQ3hENEMsYUFBYUUsT0FBTyxDQUFDLENBQUNSLEtBQUttQjtnQkFDekIsSUFBSTVHLGtCQUFrQm1ELE9BQU8sQ0FBQzdGLFFBQVEsQ0FBQ3NKLE1BQU0sRUFBRTtvQkFDN0MsTUFBTUMsV0FBVyxJQUFJbE4sMkNBQWE7b0JBQ2xDcUcsa0JBQWtCbUQsT0FBTyxDQUFDN0YsUUFBUSxDQUFDc0osTUFBTSxDQUFDRSxnQkFBZ0IsQ0FBQ0Q7b0JBQzNEcEIsSUFBSTFHLFFBQVEsQ0FBQ3FILElBQUksQ0FBQ1M7b0JBRWxCcEIsSUFBSXNCLFFBQVEsQ0FBQ1gsSUFBSSxDQUFDcEcsa0JBQWtCbUQsT0FBTyxDQUFDNEQsUUFBUTtvQkFDcER0QixJQUFJdUIsS0FBSyxDQUFDWixJQUFJLENBQUNwRyxrQkFBa0JtRCxPQUFPLENBQUM2RCxLQUFLO2dCQUNoRDtZQUNGO1FBQ0Y7SUFDRixHQUFHO1FBQUMxSDtLQUFnQjtJQUVwQixNQUFNMkgsb0JBQW9CM04sa0RBQVdBLENBQUMsQ0FBQ29DO1lBR0FBO1FBRnJDcUIsUUFBUUMsR0FBRyxDQUFDLHdDQUF3Q3RCO1FBQ3BEcUIsUUFBUUMsR0FBRyxDQUFDLGVBQWV0QixNQUFNb0QsSUFBSTtRQUNyQy9CLFFBQVFDLEdBQUcsQ0FBQywwQkFBeUJ0QixrQkFBQUEsTUFBTTRCLFFBQVEsY0FBZDVCLHNDQUFBQSxnQkFBZ0I4SSxNQUFNO1FBRTNEM0UsZ0JBQWdCLEVBQUU7UUFDbEJOLG1CQUFtQixFQUFFO1FBRXJCLE1BQU0ySCxRQUFRdk0sa0ZBQW1CQSxDQUFDZTtRQUNsQzRDLGNBQWM0STtRQUNkdE0sZ0ZBQWlCQSxDQUFDYztRQUVsQixNQUFNd0gsVUFBVSxFQUFFO1FBQ2xCLE1BQU1pRSxpQkFBaUIsRUFBRTtRQUV6QnpMLE1BQU00SyxRQUFRLENBQUMsQ0FBQ3RCO1lBQ2RqSSxRQUFRQyxHQUFHLENBQ1Qsc0JBQ0FnSSxPQUFPOUYsSUFBSSxFQUNYOEYsT0FBT2xHLElBQUksRUFDWCxXQUNBa0csT0FBT3dCLE1BQU0sRUFDYixhQUNBeEIsT0FBT1csUUFBUTtZQUdqQixJQUFJWCxPQUFPd0IsTUFBTSxFQUFFO2dCQUNqQixJQUFJLENBQUN4QixPQUFPOUYsSUFBSSxFQUFFO29CQUNoQjhGLE9BQU85RixJQUFJLEdBQUcsUUFBa0IsT0FBVjhGLE9BQU9uRyxFQUFFO2dCQUNqQztnQkFFQTlCLFFBQVFDLEdBQUcsQ0FDVCwyQkFDQWdJLE9BQU85RixJQUFJLEVBQ1gsYUFDQThGLE9BQU9XLFFBQVE7Z0JBRWpCekMsUUFBUWtFLElBQUksQ0FBQ3BDO2dCQUVibUMsZUFBZUMsSUFBSSxDQUFDO29CQUNsQnZJLElBQUltRyxPQUFPcUMsSUFBSTtvQkFDZm5JLE1BQU04RixPQUFPOUYsSUFBSTtvQkFDakJILFVBQVVpRyxPQUFPakcsUUFBUSxDQUFDc0gsS0FBSztvQkFDL0JVLFVBQVUvQixPQUFPK0IsUUFBUSxDQUFDVixLQUFLO29CQUMvQlcsT0FBT2hDLE9BQU9nQyxLQUFLLENBQUNYLEtBQUs7b0JBQ3pCaUIsVUFBVXRDLE9BQU9zQyxRQUFRLEdBQ3JCO3dCQUNFckksT0FBTytGLE9BQU9zQyxRQUFRLENBQUNySSxLQUFLLEdBQ3hCK0YsT0FBT3NDLFFBQVEsQ0FBQ3JJLEtBQUssQ0FBQ3NJLE1BQU0sS0FDNUI7d0JBQ0pDLFdBQVd4QyxPQUFPc0MsUUFBUSxDQUFDRSxTQUFTO3dCQUNwQ0MsV0FBV3pDLE9BQU9zQyxRQUFRLENBQUNHLFNBQVM7d0JBQ3BDL0csV0FBV3NFLE9BQU9zQyxRQUFRLENBQUM1RyxTQUFTO3dCQUNwQ2dILGFBQWExQyxPQUFPc0MsUUFBUSxDQUFDSSxXQUFXO3dCQUN4Q0MsU0FBUzNDLE9BQU9zQyxRQUFRLENBQUNLLE9BQU87b0JBQ2xDLElBQ0E7Z0JBQ047WUFDRjtRQUNGO1FBRUE1SyxRQUFRQyxHQUFHLENBQ1QsU0FDQWtHLFFBQVFzQixNQUFNLEVBQ2QsaUJBQ0F0QixRQUFRd0MsR0FBRyxDQUFDLENBQUNELE1BQVM7Z0JBQUV2RyxNQUFNdUcsSUFBSXZHLElBQUk7Z0JBQUV5RyxVQUFVRixJQUFJRSxRQUFRO1lBQUM7UUFHakUxQyxxQkFBcUJFLE9BQU8sQ0FBQ0QsT0FBTyxHQUFHaUU7UUFDdkN0SCxnQkFBZ0JxRDtJQUNsQixHQUFHLEVBQUU7SUFFTCwwQ0FBMEM7SUFDMUMzSixnREFBU0EsQ0FBQztRQUNSLElBQUl3RSxrQkFBa0JFLG1CQUFtQjtZQUN2Qyw2Q0FBNkM7WUFDN0M0QixnQkFBZ0IsRUFBRTtZQUNsQk4sbUJBQW1CLEVBQUU7WUFDckJyQixxQkFBcUJIO1FBQ3ZCO0lBQ0YsR0FBRztRQUFDQTtRQUFlRTtLQUFrQjtJQUVyQyxNQUFNMkosbUJBQW1CdE8sa0RBQVdBLENBQUMsQ0FBQ3VPLFNBQVNDO1FBQzdDbkksd0JBQXdCLENBQUN1RixPQUFVO2dCQUNqQyxHQUFHQSxJQUFJO2dCQUNQLENBQUMyQyxRQUFRLEVBQUU7dUJBQUszQyxJQUFJLENBQUMyQyxRQUFRLElBQUksRUFBRTtvQkFBR0M7aUJBQVk7WUFDcEQ7SUFDRixHQUFHLEVBQUU7SUFFTCxNQUFNQyxnQkFBZ0J6TyxrREFBV0EsQ0FBQyxDQUFDdU87UUFDakNsSSx3QkFBd0IsQ0FBQ3VGO1lBQ3ZCLE1BQU0xQixVQUFVMEIsSUFBSSxDQUFDMkMsUUFBUSxJQUFJLEVBQUU7WUFDbkMsSUFBSXJFLFFBQVFnQixNQUFNLElBQUksR0FBRyxPQUFPVTtZQUVoQyxNQUFNZixhQUFhWCxRQUFRWSxLQUFLLENBQUMsR0FBRyxDQUFDO1lBQ3JDLE1BQU00RCxlQUFlN0QsVUFBVSxDQUFDQSxXQUFXSyxNQUFNLEdBQUcsRUFBRTtZQUV0RCxJQUFJbkYsVUFBVThELE9BQU8sQ0FBQzBFLFFBQVEsRUFBRTtnQkFDOUJ4SSxVQUFVOEQsT0FBTyxDQUFDMEUsUUFBUSxDQUFDOUksUUFBUSxDQUFDcUgsSUFBSSxDQUFDNEI7WUFDM0M7WUFFQSxPQUFPO2dCQUNMLEdBQUc5QyxJQUFJO2dCQUNQLENBQUMyQyxRQUFRLEVBQUUxRDtZQUNiO1FBQ0Y7SUFDRixHQUFHLEVBQUU7SUFFTDVLLGdEQUFTQSxDQUFDO1FBQ1IsTUFBTTBPLGdCQUFnQixDQUFDQztZQUNyQixzQ0FBc0M7WUFDdEMsSUFBSUEsTUFBTUMsT0FBTyxFQUFFO2dCQUNqQixJQUFJRCxNQUFNRSxHQUFHLEtBQUssS0FBSztvQkFDckJGLE1BQU1HLGNBQWM7b0JBQ3BCaEU7b0JBQ0E7Z0JBQ0Y7Z0JBQ0EsSUFBSTZELE1BQU1FLEdBQUcsS0FBSyxLQUFLO29CQUNyQkYsTUFBTUcsY0FBYztvQkFDcEI5RDtvQkFDQTtnQkFDRjtZQUNGO1lBRUEsd0NBQXdDO1lBQ3hDLElBQUkyRCxNQUFNRSxHQUFHLEtBQUssWUFBWWhJLGdCQUFnQjtnQkFDNUNDLGtCQUFrQjtnQkFDbEI7WUFDRjtZQUVBLG9CQUFvQjtZQUNwQixJQUFJNkgsTUFBTUMsT0FBTyxJQUFJRCxNQUFNRSxHQUFHLEtBQUssT0FBTzVJLGVBQWU7Z0JBQ3ZEdUksY0FBY3ZJO1lBQ2hCO1lBRUEsa0NBQWtDO1lBQ2xDLElBQUlGLGdCQUFnQmtGLE1BQU0sR0FBRyxHQUFHO2dCQUM5QixPQUFRMEQsTUFBTUUsR0FBRztvQkFDZixLQUFLO3dCQUNIckksaUJBQWlCO3dCQUNqQjtvQkFDRixLQUFLO3dCQUNIQSxpQkFBaUI7d0JBQ2pCO29CQUNGLEtBQUs7d0JBQ0hBLGlCQUFpQjt3QkFDakI7b0JBQ0YsS0FBSzt3QkFDSFIsbUJBQW1CLEVBQUU7d0JBQ3JCO2dCQUNKO1lBQ0Y7WUFFQSxvQkFBb0I7WUFDcEIsSUFBSTJJLE1BQU1DLE9BQU8sSUFBSUQsTUFBTUUsR0FBRyxLQUFLLEtBQUs7Z0JBQ3RDRixNQUFNRyxjQUFjO2dCQUNwQixJQUFJekksYUFBYTRFLE1BQU0sR0FBRyxHQUFHO29CQUMzQmpGLG1CQUFtQjsyQkFBSUs7cUJBQWE7Z0JBQ3RDO1lBQ0Y7UUFDRjtRQUVBMEksT0FBT0MsZ0JBQWdCLENBQUMsV0FBV047UUFDbkMsT0FBTyxJQUFNSyxPQUFPRSxtQkFBbUIsQ0FBQyxXQUFXUDtJQUNyRCxHQUFHO1FBQ0R6STtRQUNBdUk7UUFDQW5JO1FBQ0FOLGdCQUFnQmtGLE1BQU07UUFDdEJIO1FBQ0FFO1FBQ0FuRTtRQUNBQztLQUNEO0lBRUQ5RyxnREFBU0EsQ0FBQztRQUNSLE9BQU87WUFDTCxJQUFJNEUsZUFBZTtnQkFDakJzSyxJQUFJQyxlQUFlLENBQUN2SztZQUN0QjtZQUNBLElBQUltRCxZQUFZO2dCQUNkbUgsSUFBSUMsZUFBZSxDQUFDcEg7WUFDdEI7UUFDRjtJQUNGLEdBQUc7UUFBQ25EO1FBQWVtRDtLQUFXO0lBRTlCLE1BQU1xSCxtQkFBbUJyUCxrREFBV0EsQ0FBQyxDQUFDc1A7UUFDcENyTyw2RUFBYUEsQ0FBQzhJLGlCQUFpQkYsT0FBTyxFQUFFeUY7SUFDMUMsR0FBRyxFQUFFO0lBRUwsMERBQTBEO0lBQzFELE1BQU1DLFdBQVdyUCw2Q0FBTUEsQ0FBQztJQUV4QixNQUFNLENBQUNzUCxpQkFBaUJDLG1CQUFtQixHQUFHdFAsK0NBQVFBLENBQUM7SUFDdkQsTUFBTSxDQUFDdVAsb0JBQW9CQyxzQkFBc0IsR0FBR3hQLCtDQUFRQSxDQUFDO0lBQzdELE1BQU15UCxhQUFhMVAsNkNBQU1BO0lBRXpCLHlCQUF5QjtJQUN6QixNQUFNMlAsZ0JBQWdCN1Asa0RBQVdBLENBQy9CLENBQUM2QztRQUNDLElBQUksQ0FBQzJNLG1CQUFtQixDQUFDekYsaUJBQWlCRixPQUFPLEVBQUU7WUFDakQ7UUFDRjtRQUVBLHdDQUF3QztRQUN4QyxNQUFNaUcsWUFBWXZPLCtFQUFvQkEsQ0FBQ3dPLElBQUksQ0FDekMsQ0FBQ0MsSUFBTUEsRUFBRXpLLEVBQUUsS0FBS2lLO1FBRWxCLElBQUksQ0FBQ00sV0FBVztRQUVoQixJQUFJSix1QkFBdUIsR0FBRztZQUM1QkMsc0JBQXNCOU07UUFDeEI7UUFFQSxNQUFNb04sVUFBVXBOLFlBQVk2TTtRQUM1QixNQUFNUSxXQUFXLFVBQVdKLFVBQVVLLFFBQVEsR0FBSUwsVUFBVUssUUFBUTtRQUVwRSx1RUFBdUU7UUFDdkUsTUFBTUMsU0FBUyxJQUFJL1AsMkNBQWEsQ0FBQyxHQUFHLEdBQUc7UUFDdkMsSUFBSTJGLGdCQUFnQmtGLE1BQU0sR0FBRyxHQUFHO1lBQzlCLHVDQUF1QztZQUN2QyxNQUFNdUIsZUFBZVgsTUFBTUMsT0FBTyxDQUFDL0YsbUJBQy9CQSxrQkFDQTtnQkFBQ0E7YUFBZ0IsQ0FBQ2dHLE1BQU0sQ0FBQ0M7WUFFN0IsSUFBSVEsYUFBYXZCLE1BQU0sR0FBRyxHQUFHO2dCQUMzQixNQUFNd0IsU0FBUyxJQUFJck0sMkNBQWE7Z0JBQ2hDb00sYUFBYUUsT0FBTyxDQUFDLENBQUNSO29CQUNwQk8sT0FBT0UsR0FBRyxDQUFDVCxJQUFJMUcsUUFBUTtnQkFDekI7Z0JBQ0FpSCxPQUFPRyxZQUFZLENBQUNKLGFBQWF2QixNQUFNO2dCQUN2Q2tGLE9BQU90RCxJQUFJLENBQUNKO1lBQ2Q7UUFDRjtRQUVBLDhDQUE4QztRQUM5Q29ELFVBQVVPLE9BQU8sQ0FDZnRHLGlCQUFpQkYsT0FBTyxDQUFDNkIsTUFBTSxFQUMvQjNCLGlCQUFpQkYsT0FBTyxFQUN4QnFHLFVBQ0FFO1FBR0ZSLFdBQVcvRixPQUFPLEdBQUd5RyxzQkFBc0JUO0lBQzdDLEdBQ0E7UUFBQ0w7UUFBaUJ6RjtRQUFrQjJGO1FBQW9CMUo7S0FBZ0I7SUFHMUUsdUJBQXVCO0lBQ3ZCL0YsZ0RBQVNBLENBQUM7UUFDUixJQUFJdVAsaUJBQWlCO1lBQ25CRyxzQkFBc0I7WUFDdEJDLFdBQVcvRixPQUFPLEdBQUd5RyxzQkFBc0JUO1lBRTNDLDBDQUEwQztZQUMxQyxJQUFJOUYsaUJBQWlCRixPQUFPLEVBQUU7Z0JBQzVCRSxpQkFBaUJGLE9BQU8sQ0FBQ2hCLE9BQU8sR0FBRztZQUNyQztRQUNGLE9BQU87WUFDTCxnREFBZ0Q7WUFDaEQsSUFBSWtCLGlCQUFpQkYsT0FBTyxJQUFJN0QsZ0JBQWdCa0YsTUFBTSxLQUFLLEdBQUc7Z0JBQzVEbkIsaUJBQWlCRixPQUFPLENBQUNoQixPQUFPLEdBQUc7WUFDckM7UUFDRjtRQUVBLE9BQU87WUFDTCxJQUFJK0csV0FBVy9GLE9BQU8sRUFBRTtnQkFDdEIwRyxxQkFBcUJYLFdBQVcvRixPQUFPO1lBQ3pDO1FBQ0Y7SUFDRixHQUFHO1FBQUMyRjtRQUFpQks7UUFBZTdKO0tBQWdCO0lBRXBELDBDQUEwQztJQUMxQyxNQUFNd0ssc0JBQXNCeFEsa0RBQVdBLENBQUMsQ0FBQ3lRO1FBQ3ZDaEIsbUJBQW1CZ0I7SUFDckIsR0FBRyxFQUFFO0lBRUwsTUFBTUMsdUJBQXVCO1FBQzNCLE9BQU9sSjtJQUNUO0lBRUEsaUJBQWlCO0lBQ2pCLE1BQU1tSixjQUFjM1Esa0RBQVdBLENBQUM7UUFDOUIsaURBQWlEO1FBQ2pELE1BQU00USxhQUFhQyxhQUFhQyxPQUFPLENBQUM7UUFDeEMsSUFBSUYsWUFBWTtZQUNkLE1BQU1HLGVBQWUvQixPQUFPZ0MsT0FBTyxDQUNqQztZQUVGLElBQUksQ0FBQ0QsY0FBYztnQkFDakI7WUFDRjtZQUNBLDBDQUEwQztZQUMxQ0YsYUFBYUksVUFBVSxDQUFDO1FBQzFCO1FBRUF4TixRQUFRQyxHQUFHLENBQUM7UUFDWjZDLGdCQUFnQixFQUFFO1FBQ2xCTixtQkFBbUIsRUFBRTtRQUVyQix3Q0FBd0M7UUFDeENYLFVBQVU7WUFDUjtnQkFDRUMsSUFBSTtnQkFDSkMsTUFBTTtnQkFDTkMsVUFBVTtvQkFBQztvQkFBSztvQkFBSztpQkFBRTtnQkFDdkJDLFdBQVc7Z0JBQ1hDLE9BQU87Z0JBQ1BDLE1BQU07Z0JBQ05DLFlBQVk7Z0JBQ1pDLGVBQWU7WUFDakI7WUFDQTtnQkFDRVAsSUFBSTtnQkFDSkMsTUFBTTtnQkFDTkMsVUFBVTtvQkFBQztvQkFBRztvQkFBRztpQkFBRTtnQkFDbkJDLFdBQVc7Z0JBQ1hDLE9BQU87Z0JBQ1BDLE1BQU07WUFDUjtZQUNBO2dCQUNFTCxJQUFJO2dCQUNKQyxNQUFNO2dCQUNOQyxVQUFVO29CQUFDLENBQUM7b0JBQUc7b0JBQUcsQ0FBQztpQkFBRTtnQkFDckJDLFdBQVc7Z0JBQ1hDLE9BQU87Z0JBQ1BDLE1BQU07WUFDUjtZQUNBO2dCQUNFTCxJQUFJO2dCQUNKQyxNQUFNO2dCQUNOQyxVQUFVO29CQUFDO29CQUFHO29CQUFHLENBQUM7aUJBQUU7Z0JBQ3BCQyxXQUFXO2dCQUNYQyxPQUFPO2dCQUNQQyxNQUFNO1lBQ1I7U0FDRDtRQUVELDZCQUE2QjtRQUM3QjZCLGFBQWE7UUFDYkUsV0FBVztRQUNYRSxtQkFBbUI7UUFDbkJFLG9CQUFvQjtRQUNwQkUsY0FBYztRQUNkRSxnQkFBZ0I7UUFDaEJFLFdBQVc7UUFDWEUsZUFBZTtRQUVmLGlDQUFpQztRQUNqQ0UseUJBQXlCO1FBQ3pCRSwwQkFBMEI7WUFDeEJDLFdBQVc7Z0JBQ1RDLFNBQVM7Z0JBQ1RDLFlBQVk7WUFDZDtZQUNBQyxPQUFPO2dCQUNMRixTQUFTO2dCQUNUbkQsV0FBVztnQkFDWHNELFdBQVc7Z0JBQ1hDLFFBQVE7WUFDVjtZQUNBQyxLQUFLO2dCQUNITCxTQUFTO2dCQUNUTSxlQUFlO2dCQUNmQyxVQUFVO2dCQUNWTixZQUFZO1lBQ2Q7WUFDQU8sVUFBVTtnQkFDUlIsU0FBUztnQkFDVFMsVUFBVTtnQkFDVkMsUUFBUTtZQUNWO1FBQ0Y7UUFFQSx1QkFBdUI7UUFDdkJwQyxZQUFZO1FBQ1pFLGFBQWE7UUFDYkUsY0FBYztRQUVkLHdCQUF3QjtRQUN4QixJQUFJd0MsaUJBQWlCRixPQUFPLEVBQUU7WUFDNUJFLGlCQUFpQkYsT0FBTyxDQUFDNkIsTUFBTSxDQUFDakcsUUFBUSxDQUFDeUwsR0FBRyxDQUFDLEdBQUcsR0FBRztZQUNuRG5ILGlCQUFpQkYsT0FBTyxDQUFDdUcsTUFBTSxDQUFDYyxHQUFHLENBQUMsR0FBRyxHQUFHO1lBQzFDbkgsaUJBQWlCRixPQUFPLENBQUNzSCxNQUFNO1FBQ2pDO1FBRUEsd0JBQXdCO1FBQ3hCMUssaUJBQWlCO1FBQ2pCTixpQkFBaUI7UUFFakIseUJBQXlCO1FBQ3pCaUwsb0JBQW9CO1FBQ3BCQyx1QkFBdUI7UUFDdkJDLHNCQUFzQjtRQUV0QixnQkFBZ0I7UUFDaEJuSCxXQUFXLEVBQUU7UUFDYkUsZ0JBQWdCLENBQUM7UUFDakJoRSx3QkFBd0IsQ0FBQztRQUV6QixvQkFBb0I7UUFDcEJyQixjQUFjO1lBQ1pDLFVBQVU7WUFDVkMsV0FBVztZQUNYQyxXQUFXO1lBQ1hDLFlBQVk7UUFDZDtRQUVBLHdCQUF3QjtRQUN4QnlCLGlCQUFpQjtRQUVqQix5QkFBeUI7UUFDekI0SSxtQkFBbUI7UUFDbkJFLHNCQUFzQjtRQUN0QixJQUFJQyxXQUFXL0YsT0FBTyxFQUFFO1lBQ3RCMEcscUJBQXFCWCxXQUFXL0YsT0FBTztRQUN6QztRQUVBLCtDQUErQztRQUMvQyxJQUFJaEYsZUFBZTtZQUNqQnNLLElBQUlDLGVBQWUsQ0FBQ3ZLO1FBQ3RCO1FBQ0EsSUFBSW1ELFlBQVk7WUFDZG1ILElBQUlDLGVBQWUsQ0FBQ3BIO1FBQ3RCO1FBRUEsOERBQThEO1FBQzlEdEQsaUJBQWlCO1FBQ2pCLG1EQUFtRDtRQUNuRG5CLFdBQVc7WUFDVG1CLGlCQUFpQjtRQUNuQixHQUFHO0lBQ0wsR0FBRyxFQUFFO0lBRUwsTUFBTTZNLGdCQUFnQnJSLDZDQUFNQSxDQUFDO0lBRTdCLE1BQU1zUixvQkFBb0J4UixrREFBV0EsQ0FBQztRQUNwQyxJQUFJdVIsY0FBYzFILE9BQU8sRUFBRTtZQUN6QnBHLFFBQVFDLEdBQUcsQ0FBQztZQUNaNk4sY0FBYzFILE9BQU87UUFDdkIsT0FBTztZQUNMcEcsUUFBUUUsS0FBSyxDQUFDO1FBQ2hCO0lBQ0YsR0FBRyxFQUFFO0lBRUwsOENBQThDO0lBQzlDMUQsZ0RBQVNBLENBQUM7UUFDUixNQUFNd1IsaUJBQWlCO1lBQ3JCLE1BQU1iLGFBQWFDLGFBQWFDLE9BQU8sQ0FBQztZQUN4QyxJQUFJRixZQUFZO2dCQUNkLElBQUk7b0JBQ0YsTUFBTWMsY0FBY0MsS0FBS0MsS0FBSyxDQUFDaEI7b0JBQy9Cbk4sUUFBUUMsR0FBRyxDQUFDLHdCQUF3QmdPO29CQUVwQyxvREFBb0Q7b0JBQ3BELElBQUksQ0FBQy9NLG1CQUFtQjt3QkFDdEJsQixRQUFRQyxHQUFHLENBQ1QsdUNBQ0FnTyxZQUFZRyxLQUFLO3dCQUVuQm5OLGlCQUFpQixhQUFhLG9DQUFvQzt3QkFFbEUsaURBQWlEO3dCQUNqRFksVUFBVW9NLFlBQVlyTSxNQUFNO3dCQUM1Qm9DLGFBQWFpSyxZQUFZSSxXQUFXLENBQUNDLE1BQU07d0JBQzNDcEssV0FBVytKLFlBQVlJLFdBQVcsQ0FBQ3BLLE9BQU87d0JBQzFDRyxtQkFBbUI2SixZQUFZSSxXQUFXLENBQUNsSyxlQUFlO3dCQUMxREcsb0JBQW9CMkosWUFBWUksV0FBVyxDQUFDaEssZ0JBQWdCO3dCQUM1REssZ0JBQWdCdUosWUFBWUksV0FBVyxDQUFDcE0sU0FBUzt3QkFDakQyQyxXQUFXcUosWUFBWUksV0FBVyxDQUFDRSxJQUFJO3dCQUN2Q3pKLGVBQWVtSixZQUFZSSxXQUFXLENBQUNyRSxRQUFRO3dCQUMvQ2hGLHlCQUF5QmlKLFlBQVlPLGNBQWMsQ0FBQ3BKLE9BQU87d0JBQzNERiwwQkFBMEIrSSxZQUFZTyxjQUFjLENBQUNDLFFBQVE7d0JBQzdEL0ssWUFBWXVLLFlBQVlTLGFBQWEsQ0FBQ2pMLFFBQVE7d0JBQzlDRyxhQUFhcUssWUFBWVMsYUFBYSxDQUFDL0ssU0FBUzt3QkFDaERHLGNBQWNtSyxZQUFZUyxhQUFhLENBQUM3SyxVQUFVO3dCQUVsRCx3RUFBd0U7d0JBQ3hFL0QsV0FBVzs0QkFDVCxJQUFJd0csaUJBQWlCRixPQUFPLElBQUk2SCxZQUFZclAsTUFBTSxFQUFFO2dDQUNsRDBILGlCQUFpQkYsT0FBTyxDQUFDNkIsTUFBTSxDQUFDakcsUUFBUSxDQUFDMk0sU0FBUyxDQUNoRFYsWUFBWXJQLE1BQU0sQ0FBQ29ELFFBQVE7Z0NBRTdCc0UsaUJBQWlCRixPQUFPLENBQUN1RyxNQUFNLENBQUNnQyxTQUFTLENBQ3ZDVixZQUFZclAsTUFBTSxDQUFDK04sTUFBTTtnQ0FFM0JyRyxpQkFBaUJGLE9BQU8sQ0FBQ3NILE1BQU07NEJBQ2pDO3dCQUNGLEdBQUc7b0JBQ0wsT0FBTyxJQUFJNU0sVUFBVXNGLE9BQU8sRUFBRTt3QkFDNUIsd0RBQXdEO3dCQUN4RHBHLFFBQVFDLEdBQUcsQ0FBQzt3QkFDWjJPLGdCQUFnQlg7b0JBQ2xCO29CQUVBLHlDQUF5QztvQkFDekMsTUFBTVksbUJBQW1CO3dCQUN2QixJQUFJLENBQUNaLFlBQVl2TSxTQUFTLElBQUksQ0FBQ21CLGFBQWE0RSxNQUFNLEVBQUU7NEJBQ2xEekgsUUFBUUMsR0FBRyxDQUFDOzRCQUNaO3dCQUNGO3dCQUVBRCxRQUFRQyxHQUFHLENBQ1QsZ0RBQ0E0Qzt3QkFFRjdDLFFBQVFDLEdBQUcsQ0FBQyxvQkFBb0JnTyxZQUFZdk0sU0FBUzt3QkFFckQsTUFBTW9OLG9CQUFvQixJQUFJQyxJQUM1QmQsWUFBWXZNLFNBQVMsQ0FBQ2lILEdBQUcsQ0FBQyxDQUFDcUcsSUFBTTtnQ0FBQ0EsRUFBRTdNLElBQUk7Z0NBQUU2TSxFQUFFekUsUUFBUTs2QkFBQzt3QkFHdkQxSCxhQUFhcUcsT0FBTyxDQUFDLENBQUNSOzRCQUNwQixJQUFJQSxJQUFJNkIsUUFBUSxJQUFJdUUsa0JBQWtCRyxHQUFHLENBQUN2RyxJQUFJdkcsSUFBSSxHQUFHO2dDQUNuRCxNQUFNK00sZ0JBQWdCSixrQkFBa0JLLEdBQUcsQ0FBQ3pHLElBQUl2RyxJQUFJO2dDQUNwRG5DLFFBQVFDLEdBQUcsQ0FDVCxrQ0FDQXlJLElBQUl2RyxJQUFJLEVBQ1IrTTtnQ0FFRnZSLHVGQUFxQkEsQ0FBQytLLEtBQUt3Rzs0QkFDN0IsT0FBTztnQ0FDTGxQLFFBQVFDLEdBQUcsQ0FDVCw2Q0FDQXlJLElBQUl2RyxJQUFJOzRCQUVaO3dCQUNGO29CQUNGO29CQUVBLGdEQUFnRDtvQkFDaEQsTUFBTWlOLGdCQUFnQkMsWUFBWTt3QkFDaEMsSUFBSXhNLGFBQWE0RSxNQUFNLEdBQUcsR0FBRzs0QkFDM0I2SCxjQUFjRjs0QkFDZCxxRUFBcUU7NEJBQ3JFdFAsV0FBVytPLGtCQUFrQjt3QkFDL0I7b0JBQ0YsR0FBRztvQkFFSCw0RUFBNEU7b0JBQzVFL08sV0FBVzt3QkFDVHdQLGNBQWNGO29CQUNoQixHQUFHO29CQUVILHdFQUF3RTtvQkFDeEV0UCxXQUFXO3dCQUNULElBQUl3RyxpQkFBaUJGLE9BQU8sSUFBSTZILFlBQVlyUCxNQUFNLEVBQUU7NEJBQ2xEMEgsaUJBQWlCRixPQUFPLENBQUM2QixNQUFNLENBQUNqRyxRQUFRLENBQUMyTSxTQUFTLENBQ2hEVixZQUFZclAsTUFBTSxDQUFDb0QsUUFBUTs0QkFFN0JzRSxpQkFBaUJGLE9BQU8sQ0FBQ3VHLE1BQU0sQ0FBQ2dDLFNBQVMsQ0FDdkNWLFlBQVlyUCxNQUFNLENBQUMrTixNQUFNOzRCQUUzQnJHLGlCQUFpQkYsT0FBTyxDQUFDc0gsTUFBTTt3QkFDakM7b0JBQ0YsR0FBRztnQkFDTCxFQUFFLE9BQU94TixPQUFPO29CQUNkRixRQUFRRSxLQUFLLENBQUMsOEJBQThCQTtnQkFDOUM7WUFDRjtRQUNGO1FBRUEsd0NBQXdDO1FBQ3hDOE47UUFFQSx1REFBdUQ7UUFDdkQsTUFBTXVCLGtCQUFrQkYsWUFBWTtZQUNsQyxJQUFJOUksU0FBU0gsT0FBTyxJQUFJRSxpQkFBaUJGLE9BQU8sRUFBRTtnQkFDaERrSixjQUFjQztnQkFDZHZCO1lBQ0Y7UUFDRixHQUFHO1FBRUgsT0FBTyxJQUFNc0IsY0FBY0M7SUFDN0IsR0FBRztRQUFDMU07UUFBYzdCO0tBQWM7SUFFaEMsTUFBTSxDQUFDd08sYUFBYUMsZUFBZSxHQUFHL1MsK0NBQVFBLENBQUM7SUFFL0NGLGdEQUFTQSxDQUFDO1FBQ1IsSUFBSXFHLGFBQWE0RSxNQUFNLEdBQUcsS0FBSytILGVBQWVBLFlBQVk5TixTQUFTLEVBQUU7WUFDbkUsTUFBTWdPLGlCQUFpQjtnQkFDckIxUCxRQUFRQyxHQUFHLENBQUMsd0NBQXdDNEM7Z0JBQ3BEN0MsUUFBUUMsR0FBRyxDQUFDLGtDQUFrQ3VQLFlBQVk5TixTQUFTO2dCQUNuRW1CLGFBQWFxRyxPQUFPLENBQUMsQ0FBQ1I7b0JBQ3BCLE1BQU1pSCxpQkFBaUJILFlBQVk5TixTQUFTLENBQUM0SyxJQUFJLENBQy9DLENBQUMwQyxJQUFNQSxFQUFFN00sSUFBSSxLQUFLdUcsSUFBSXZHLElBQUksSUFBSTZNLEVBQUVsTixFQUFFLEtBQUs0RyxJQUFJNEIsSUFBSTtvQkFFakQsSUFBSXFGLGtCQUFrQkEsZUFBZXBGLFFBQVEsRUFBRTt3QkFDN0N2SyxRQUFRQyxHQUFHLENBQ1QscUNBQ0F5SSxJQUFJdkcsSUFBSSxFQUNSd04sZUFBZXBGLFFBQVE7d0JBRXpCNU0sdUZBQXFCQSxDQUFDK0ssS0FBS2lILGVBQWVwRixRQUFRO29CQUNwRCxPQUFPO3dCQUNMdkssUUFBUUMsR0FBRyxDQUFDLHdDQUF3Q3lJLElBQUl2RyxJQUFJO29CQUM5RDtnQkFDRjtZQUNGO1lBQ0EscUVBQXFFO1lBQ3JFLE1BQU15RixZQUFZOUgsV0FBVzRQLGdCQUFnQjtZQUM3QyxPQUFPLElBQU03SCxhQUFhRDtRQUM1QjtJQUNGLEdBQUc7UUFBQy9FO1FBQWMyTTtLQUFZO0lBRTlCLE1BQU1aLGtCQUFrQixDQUFDZ0I7UUFDdkIsSUFBSTtZQUNGNVAsUUFBUUMsR0FBRyxDQUFDLGdDQUFnQzJQO1lBRTVDLDhCQUE4QjtZQUM5QjlPLFVBQVVzRixPQUFPLEdBQUd3SjtZQUNwQjdPLGlCQUFpQnFGLE9BQU8sR0FBR3dKLFFBQVEsb0RBQW9EO1lBQ3ZGSCxlQUFlRyxTQUFTLDhCQUE4QjtZQUV0RCxpREFBaUQ7WUFDakQ5TSxnQkFBZ0IsRUFBRTtZQUNsQk4sbUJBQW1CLEVBQUU7WUFDckJyQixxQkFBcUI7WUFFckIseUJBQXlCO1lBQ3pCRixpQkFBaUI7WUFDakJZLFVBQVUrTixPQUFPaE8sTUFBTTtZQUN2Qm9DLGFBQWE0TCxPQUFPdkIsV0FBVyxDQUFDQyxNQUFNO1lBQ3RDcEssV0FBVzBMLE9BQU92QixXQUFXLENBQUNwSyxPQUFPO1lBQ3JDRyxtQkFBbUJ3TCxPQUFPdkIsV0FBVyxDQUFDbEssZUFBZTtZQUNyREcsb0JBQW9Cc0wsT0FBT3ZCLFdBQVcsQ0FBQ2hLLGdCQUFnQjtZQUN2REssZ0JBQWdCa0wsT0FBT3ZCLFdBQVcsQ0FBQ3BNLFNBQVM7WUFDNUMyQyxXQUFXZ0wsT0FBT3ZCLFdBQVcsQ0FBQ0UsSUFBSTtZQUNsQ3pKLGVBQWU4SyxPQUFPdkIsV0FBVyxDQUFDckUsUUFBUTtZQUMxQ2hGLHlCQUF5QjRLLE9BQU9wQixjQUFjLENBQUNwSixPQUFPO1lBQ3RERiwwQkFBMEIwSyxPQUFPcEIsY0FBYyxDQUFDQyxRQUFRO1lBQ3hEL0ssWUFBWWtNLE9BQU9sQixhQUFhLENBQUNqTCxRQUFRO1lBQ3pDRyxhQUFhZ00sT0FBT2xCLGFBQWEsQ0FBQy9LLFNBQVM7WUFDM0NHLGNBQWM4TCxPQUFPbEIsYUFBYSxDQUFDN0ssVUFBVTtZQUU3QywrREFBK0Q7WUFDL0QvRCxXQUFXO2dCQUNULElBQUl3RyxpQkFBaUJGLE9BQU8sSUFBSXdKLE9BQU9oUixNQUFNLEVBQUU7b0JBQzdDMEgsaUJBQWlCRixPQUFPLENBQUM2QixNQUFNLENBQUNqRyxRQUFRLENBQUMyTSxTQUFTLENBQ2hEaUIsT0FBT2hSLE1BQU0sQ0FBQ29ELFFBQVE7b0JBRXhCc0UsaUJBQWlCRixPQUFPLENBQUN1RyxNQUFNLENBQUNnQyxTQUFTLENBQUNpQixPQUFPaFIsTUFBTSxDQUFDK04sTUFBTTtvQkFDOURyRyxpQkFBaUJGLE9BQU8sQ0FBQ3NILE1BQU07Z0JBQ2pDO1lBQ0YsR0FBRztZQUVILGlEQUFpRDtZQUNqRCxNQUFNbUIsbUJBQW1CO2dCQUN2QixJQUFJLENBQUNlLE9BQU9sTyxTQUFTLElBQUksQ0FBQ21CLGFBQWE0RSxNQUFNLEVBQUU7b0JBQzdDekgsUUFBUUMsR0FBRyxDQUFDO29CQUNaO2dCQUNGO2dCQUNBRCxRQUFRQyxHQUFHLENBQUMsb0NBQW9DNEM7Z0JBQ2hELE1BQU1pTSxvQkFBb0IsSUFBSUMsSUFDNUJhLE9BQU9sTyxTQUFTLENBQUNpSCxHQUFHLENBQUMsQ0FBQ3FHLElBQU07d0JBQUNBLEVBQUU3TSxJQUFJO3dCQUFFNk0sRUFBRXpFLFFBQVE7cUJBQUM7Z0JBRWxEMUgsYUFBYXFHLE9BQU8sQ0FBQyxDQUFDUjtvQkFDcEIsSUFBSUEsSUFBSTZCLFFBQVEsSUFBSXVFLGtCQUFrQkcsR0FBRyxDQUFDdkcsSUFBSXZHLElBQUksR0FBRzt3QkFDbkQsTUFBTStNLGdCQUFnQkosa0JBQWtCSyxHQUFHLENBQUN6RyxJQUFJdkcsSUFBSTt3QkFDcERuQyxRQUFRQyxHQUFHLENBQ1Qsa0NBQ0F5SSxJQUFJdkcsSUFBSSxFQUNSK007d0JBRUZ2Uix1RkFBcUJBLENBQUMrSyxLQUFLd0c7b0JBQzdCLE9BQU87d0JBQ0xsUCxRQUFRQyxHQUFHLENBQUMsNkNBQTZDeUksSUFBSXZHLElBQUk7b0JBQ25FO2dCQUNGO1lBQ0Y7WUFDQSxJQUFJVSxhQUFhNEUsTUFBTSxHQUFHLEdBQUc7Z0JBQzNCM0gsV0FBVytPLGtCQUFrQjtZQUMvQixPQUFPO2dCQUNMLE1BQU1PLGdCQUFnQkMsWUFBWTtvQkFDaEMsSUFBSXhNLGFBQWE0RSxNQUFNLEdBQUcsR0FBRzt3QkFDM0I2SCxjQUFjRjt3QkFDZHRQLFdBQVcrTyxrQkFBa0I7b0JBQy9CO2dCQUNGLEdBQUc7Z0JBQ0gvTyxXQUFXO29CQUNUd1AsY0FBY0Y7Z0JBQ2hCLEdBQUc7WUFDTDtRQUNGLEVBQUUsT0FBT2xQLE9BQU87WUFDZEYsUUFBUUUsS0FBSyxDQUFDLHNDQUFzQ0E7UUFDdEQ7SUFDRjtJQUVBLE1BQU0yUCxrQkFBa0IsT0FBT0MsV0FBV0MsYUFBYUM7UUFDckQsOEZBQThGO1FBQzlGLElBQUkvQjtRQUNKLElBQUlsTixpQkFBaUJxRixPQUFPLEVBQUU7WUFDNUIsb0ZBQW9GO1lBQ3BGNkgsY0FBYztnQkFDWixHQUFHbE4saUJBQWlCcUYsT0FBTztnQkFDM0JqRSxNQUFNMk47Z0JBQ04xUSxXQUFXLElBQUlDLE9BQU9DLFdBQVc7WUFDbkM7UUFDRixPQUFPO2dCQStCU2dILDJCQUNGQTtZQS9CWixrQ0FBa0M7WUFDbEMySCxjQUFjO2dCQUNaOUwsTUFBTTJOO2dCQUNOMVEsV0FBVyxJQUFJQyxPQUFPQyxXQUFXO2dCQUNqQzhPLE9BQU9wTjtnQkFDUFksUUFBUUEsT0FBTytHLEdBQUcsQ0FBQyxDQUFDc0g7d0JBR2hCM047MkJBSDJCO3dCQUM3QixHQUFHMk4sS0FBSzt3QkFDUmpPLFVBQ0VNLEVBQUFBLDhCQUFBQSxVQUFVOEQsT0FBTyxDQUFDNkosTUFBTW5PLEVBQUUsQ0FBQyxjQUEzQlEsa0RBQUFBLDRCQUE2Qk4sUUFBUSxDQUFDa08sT0FBTyxPQUFNRCxNQUFNak8sUUFBUTtvQkFDckU7O2dCQUNBcU0sYUFBYTtvQkFDWEMsUUFBUXZLO29CQUNSOUIsV0FBV3dDO29CQUNYOEosTUFBTTVKO29CQUNOcUYsVUFBVW5GO29CQUNWVjtvQkFDQUY7b0JBQ0FNO2dCQUNGO2dCQUNBaUssZ0JBQWdCO29CQUNkcEosU0FBU0w7b0JBQ1QwSixVQUFVeEo7Z0JBQ1o7Z0JBQ0F5SixlQUFlO29CQUNiakw7b0JBQ0FFO29CQUNBRTtvQkFDQVE7Z0JBQ0Y7Z0JBQ0F6RixRQUFRO29CQUNOb0QsUUFBUSxHQUFFc0UsNEJBQUFBLGlCQUFpQkYsT0FBTyxjQUF4QkUsZ0RBQUFBLDBCQUEwQjJCLE1BQU0sQ0FBQ2pHLFFBQVEsQ0FBQ2tPLE9BQU87b0JBQzNEdkQsTUFBTSxHQUFFckcsNkJBQUFBLGlCQUFpQkYsT0FBTyxjQUF4QkUsaURBQUFBLDJCQUEwQnFHLE1BQU0sQ0FBQ3VELE9BQU87Z0JBQ2xEO2dCQUNBeE8sV0FBV21CLGFBQWE4RixHQUFHLENBQUMsQ0FBQ0Q7b0JBQzNCLE1BQU02QixXQUFXN0IsSUFBSTZCLFFBQVE7b0JBQzdCLElBQUksQ0FBQ0EsVUFDSCxPQUFPO3dCQUFFekksSUFBSTRHLElBQUk0QixJQUFJO3dCQUFFbkksTUFBTXVHLElBQUl2RyxJQUFJO3dCQUFFb0ksVUFBVTtvQkFBSztvQkFDeEQsT0FBTzt3QkFDTHpJLElBQUk0RyxJQUFJNEIsSUFBSTt3QkFDWm5JLE1BQU11RyxJQUFJdkcsSUFBSTt3QkFDZG9JLFVBQVU7NEJBQ1J4SSxNQUFNd0ksU0FBU3hJLElBQUk7NEJBQ25CRyxPQUFPcUksU0FBU3JJLEtBQUssR0FBR3FJLFNBQVNySSxLQUFLLENBQUNpTyxZQUFZLEtBQUs7NEJBQ3hEMUYsV0FBV0YsU0FBU0UsU0FBUzs0QkFDN0JDLFdBQVdILFNBQVNHLFNBQVM7NEJBQzdCL0csV0FBVzRHLFNBQVM1RyxTQUFTOzRCQUM3QmdILGFBQWFKLFNBQVNJLFdBQVc7NEJBQ2pDQyxTQUFTTCxTQUFTSyxPQUFPOzRCQUN6QndGLGlCQUFpQjdGLFNBQVM2RixlQUFlOzRCQUN6QyxHQUFJN0YsU0FBU3hJLElBQUksS0FBSyw0QkFBNEI7Z0NBQ2hEc08sb0JBQW9COUYsU0FBUzhGLGtCQUFrQjtnQ0FDL0NDLFlBQVkvRixTQUFTK0YsVUFBVTtnQ0FDL0JDLEtBQUtoRyxTQUFTZ0csR0FBRztnQ0FDakJDLGlCQUFpQmpHLFNBQVNpRyxlQUFlOzRCQUMzQyxDQUFDO3dCQUNIO29CQUNGO2dCQUNGO1lBQ0Y7UUFDRjtRQUVBLElBQUlSLGFBQWEsU0FBUztZQUN4Qix1QkFBdUI7WUFDdkI1QyxhQUFhcUQsT0FBTyxDQUFDLHFCQUFxQnZDLEtBQUt3QyxTQUFTLENBQUN6QztRQUMzRCxPQUFPLElBQUkrQixhQUFhLE1BQU07WUFDNUIscUJBQXFCO1lBQ3JCLElBQUk7Z0JBQ0YsTUFBTVcsVUFBVSxNQUFNdlMsNEVBQWFBLENBQUM7b0JBQ2xDd1MsUUFBUWhRLEtBQUtrQixFQUFFO29CQUNmK08sYUFBYWxRLFFBQVFtUSxHQUFHO29CQUN4QjNPLE1BQU0yTjtvQkFDTkYsUUFBUTNCO29CQUNSOEIsYUFBYUE7Z0JBQ2Y7Z0JBQ0EvUCxRQUFRQyxHQUFHLENBQUMsV0FBVzBRO2dCQUN2QixJQUFJQSxTQUFTO29CQUNYOVAsV0FBV0YsUUFBUW1RLEdBQUcsRUFBRUg7Z0JBQzFCO2dCQUNBclMsMENBQUtBLENBQUN5UyxPQUFPLENBQUM7WUFDaEIsRUFBRSxPQUFPQyxLQUFLO2dCQUNaMVMsMENBQUtBLENBQUM0QixLQUFLLENBQUM7WUFDZDtRQUNGLE9BQU87WUFDTCxpQkFBaUI7WUFDakIsTUFBTStRLE9BQU8sSUFBSUMsS0FBSztnQkFBQ2hELEtBQUt3QyxTQUFTLENBQUN6QyxhQUFhLE1BQU07YUFBRyxFQUFFO2dCQUM1RGxNLE1BQU07WUFDUjtZQUNBLE1BQU1vUCxNQUFNekYsSUFBSTBGLGVBQWUsQ0FBQ0g7WUFDaEMsTUFBTTFFLElBQUlyTixTQUFTQyxhQUFhLENBQUM7WUFDakNvTixFQUFFN00sSUFBSSxHQUFHeVI7WUFDVDVFLEVBQUU5TSxRQUFRLEdBQUcsR0FBa0MsT0FBL0JxUSxVQUFVdlEsT0FBTyxDQUFDLFFBQVEsTUFBSztZQUMvQ0wsU0FBU1MsSUFBSSxDQUFDQyxXQUFXLENBQUMyTTtZQUMxQkEsRUFBRTFNLEtBQUs7WUFDUFgsU0FBU1MsSUFBSSxDQUFDSSxXQUFXLENBQUN3TTtZQUMxQmIsSUFBSUMsZUFBZSxDQUFDd0Y7UUFDdEI7SUFDRjtJQUVBLGtCQUFrQjtJQUNsQixNQUFNLENBQUNFLGVBQWVDLGlCQUFpQixHQUFHNVUsK0NBQVFBLENBQUM7SUFDbkQsTUFBTSxDQUFDNlUsZUFBZUMsaUJBQWlCLEdBQUc5VSwrQ0FBUUEsQ0FBQztRQUFDO1FBQUc7UUFBTTtLQUFFO0lBQy9ELE1BQU0sQ0FBQytVLFlBQVlDLGNBQWMsR0FBR2hWLCtDQUFRQSxDQUFDO1FBQUM7UUFBSztRQUFLO0tBQUk7SUFDNUQsTUFBTSxDQUFDaVYsZUFBZUMsaUJBQWlCLEdBQUdsViwrQ0FBUUEsQ0FBQztRQUFDLENBQUNtVixLQUFLQyxFQUFFLEdBQUc7UUFBRztRQUFHO0tBQUU7SUFDdkUsTUFBTSxDQUFDQyxZQUFZQyxjQUFjLEdBQUd0ViwrQ0FBUUEsQ0FBQztJQUM3QyxNQUFNLENBQUN1VixjQUFjQyxnQkFBZ0IsR0FBR3hWLCtDQUFRQSxDQUM5QztJQUdGLHVCQUF1QjtJQUN2QixNQUFNLENBQUN5VixrQkFBa0J4RSxvQkFBb0IsR0FBR2pSLCtDQUFRQSxDQUFDO0lBQ3pELE1BQU0sQ0FBQzBWLHFCQUFxQnhFLHVCQUF1QixHQUFHbFIsK0NBQVFBLENBQUM7SUFDL0QsTUFBTSxDQUFDMlYsb0JBQW9CeEUsc0JBQXNCLEdBQUduUiwrQ0FBUUEsQ0FBQztJQUU3RCxNQUFNNFYsb0JBQW9CL1Ysa0RBQVdBLENBQUMsQ0FBQ2dXO1FBQ3JDakIsaUJBQWlCaUI7UUFDakIsaUZBQWlGO1FBQ2pGbkYsYUFBYXFELE9BQU8sQ0FBQyxpQkFBaUI4QjtJQUN4QyxHQUFHLEVBQUU7SUFFTCxNQUFNQyxtQkFBbUJqVyxrREFBV0EsQ0FBQyxDQUFDa1c7UUFDcENQLGdCQUFnQk87UUFDaEJyRixhQUFhcUQsT0FBTyxDQUFDLGlCQUFpQmdDO0lBQ3hDLEdBQUcsRUFBRTtJQUVMLHFDQUFxQztJQUNyQ2pXLGdEQUFTQSxDQUFDO1FBQ1IsTUFBTWtXLHFCQUFxQnRGLGFBQWFDLE9BQU8sQ0FBQztRQUNoRCxJQUFJcUYsb0JBQW9CO1lBQ3RCcEIsaUJBQWlCb0I7UUFDbkI7SUFDRixHQUFHLEVBQUU7SUFFTCxNQUFNQyx1QkFBdUJwVyxrREFBV0EsQ0FDdEMsQ0FBQzRPO1FBQ0N3SCxxQkFDRXhILE9BQ0E3SCxtQkFDQWtPLGtCQUNBSTtJQUVKLEdBQ0E7UUFBQ0o7UUFBa0JJO1FBQWtCdE87S0FBa0I7SUFHekQseURBQXlEO0lBQ3pELE1BQU1zUCwwQkFBMEJyVyxrREFBV0EsQ0FBQyxDQUFDc1c7UUFDM0MsTUFBTUMsZUFBZTtZQUNuQkMsWUFBWTtnQkFBQztnQkFBRztnQkFBTTthQUFFO1lBQ3hCQyxVQUFVO2dCQUFDO2dCQUFHO2dCQUFNO2FBQUU7WUFDdEJDLFlBQVk7Z0JBQUM7Z0JBQUc7Z0JBQUs7YUFBRTtZQUN2QkMsWUFBWTtnQkFBQztnQkFBRztnQkFBSzthQUFFO1lBQ3ZCQyxPQUFPO2dCQUFDO2dCQUFHO2dCQUFNO2FBQUU7WUFDbkJDLE9BQU87Z0JBQUM7Z0JBQUc7Z0JBQUs7YUFBRTtZQUNsQkMsT0FBTztnQkFBQztnQkFBRztnQkFBTTthQUFFO1FBQ3JCO1FBQ0EsT0FBT1AsWUFBWSxDQUFDRCxRQUFRLElBQUk7WUFBQztZQUFHO1lBQU07U0FBRTtJQUM5QyxHQUFHLEVBQUU7SUFFTCxNQUFNUywwQkFBMEIvVyxrREFBV0EsQ0FBQyxDQUFDc1c7UUFDM0MsTUFBTUMsZUFBZTtZQUNuQkMsWUFBWTtnQkFBQyxDQUFDbEIsS0FBS0MsRUFBRSxHQUFHO2dCQUFHO2dCQUFHO2FBQUU7WUFDaENrQixVQUFVO2dCQUFDLENBQUNuQixLQUFLQyxFQUFFLEdBQUc7Z0JBQUc7Z0JBQUc7YUFBRTtZQUM5Qm1CLFlBQVk7Z0JBQUMsQ0FBQ3BCLEtBQUtDLEVBQUUsR0FBRztnQkFBRztnQkFBRzthQUFFO1lBQ2hDb0IsWUFBWTtnQkFBQyxDQUFDckIsS0FBS0MsRUFBRSxHQUFHO2dCQUFHO2dCQUFHO2FBQUU7WUFDaENxQixPQUFPO2dCQUFDLENBQUN0QixLQUFLQyxFQUFFLEdBQUc7Z0JBQUc7Z0JBQUc7YUFBRTtZQUMzQnNCLE9BQU87Z0JBQUMsQ0FBQ3ZCLEtBQUtDLEVBQUUsR0FBRztnQkFBRztnQkFBRzthQUFFO1lBQzNCdUIsT0FBTztnQkFBQyxDQUFDeEIsS0FBS0MsRUFBRSxHQUFHO2dCQUFHO2dCQUFHO2FBQUU7UUFDN0I7UUFDQSxPQUFPZ0IsWUFBWSxDQUFDRCxRQUFRLElBQUk7WUFBQyxDQUFDaEIsS0FBS0MsRUFBRSxHQUFHO1lBQUc7WUFBRztTQUFFO0lBQ3RELEdBQUcsRUFBRTtJQUVMLE1BQU15Qix1QkFBdUJoWCxrREFBV0EsQ0FBQyxDQUFDc1c7UUFDeEMsTUFBTUMsZUFBZTtZQUNuQkMsWUFBWTtnQkFBQztnQkFBSztnQkFBSzthQUFJO1lBQzNCQyxVQUFVO2dCQUFDO2dCQUFLO2dCQUFLO2FBQUk7WUFDekJDLFlBQVk7Z0JBQUM7Z0JBQUs7Z0JBQUs7YUFBSTtZQUMzQkMsWUFBWTtnQkFBQztnQkFBSztnQkFBSzthQUFJO1lBQzNCQyxPQUFPO2dCQUFDO2dCQUFLO2dCQUFLO2FBQUk7WUFDdEJDLE9BQU87Z0JBQUM7Z0JBQUs7Z0JBQUs7YUFBSTtZQUN0QkMsT0FBTztnQkFBQztnQkFBSztnQkFBSzthQUFJO1FBQ3hCO1FBQ0EsT0FBT1AsWUFBWSxDQUFDRCxRQUFRLElBQUk7WUFBQztZQUFLO1lBQUs7U0FBSTtJQUNqRCxHQUFHLEVBQUU7SUFFTCw4REFBOEQ7SUFDOUQsTUFBTVcsd0JBQXdCalgsa0RBQVdBLENBQUMsQ0FBQ2tYLFdBQVdsQixNQUFNTTtRQUMxRCxJQUFJLENBQUNOLFFBQVFBLEtBQUs5SyxNQUFNLEtBQUssR0FBRyxPQUFPZ007UUFFdkMsTUFBTUMsYUFBYW5CLEtBQUs5SyxNQUFNO1FBQzlCLElBQUksQ0FBQ2tNLFFBQVFDLFFBQVFDLE9BQU8sR0FBR0o7UUFFL0Isa0RBQWtEO1FBQ2xELElBQUlLLGtCQUFrQjtRQUV0QixJQUFJSixhQUFhLElBQUk7WUFDbkIsaURBQWlEO1lBQ2pESSxrQkFBa0IsTUFBTSxDQUFDSixhQUFhLEVBQUMsSUFBSztRQUM5QyxPQUFPLElBQUlBLGFBQWEsR0FBRztZQUN6Qiw4Q0FBOEM7WUFDOUNJLGtCQUFrQixNQUFNLENBQUNKLGFBQWEsS0FBSztRQUM3QyxPQUFPLElBQUlBLGFBQWEsR0FBRztZQUN6QixzRUFBc0U7WUFDdEVJLGtCQUFrQixNQUFNSixhQUFhO1FBQ3ZDO1FBRUEsd0RBQXdEO1FBQ3hEQyxTQUFTQSxTQUFTRztRQUVsQixzREFBc0Q7UUFDdEQsSUFBSUosYUFBYSxHQUFHO1lBQ2xCRSxTQUFTQSxTQUFTL0IsS0FBS2tDLEdBQUcsQ0FBQyxLQUFLLE1BQU0sQ0FBQ0wsYUFBYSxLQUFLO1FBQzNEO1FBRUEsOEVBQThFO1FBQzlFLE1BQU1NLFdBQVc7WUFDZmpCLFlBQVk7Z0JBQUM7Z0JBQU07Z0JBQUs7YUFBSTtZQUM1QkMsVUFBVTtnQkFBQztnQkFBTTtnQkFBSzthQUFJO1lBQzFCQyxZQUFZO2dCQUFDO2dCQUFLO2dCQUFLO2FBQUk7WUFDM0JDLFlBQVk7Z0JBQUM7Z0JBQUs7Z0JBQUs7YUFBSTtZQUMzQkMsT0FBTztnQkFBQztnQkFBSztnQkFBSzthQUFJO1lBQ3RCQyxPQUFPO2dCQUFDO2dCQUFLO2dCQUFLO2FBQUk7WUFDdEJDLE9BQU87Z0JBQUM7Z0JBQUs7Z0JBQUs7YUFBSTtRQUN4QjtRQUVBLE1BQU1ZLFNBQVNELFFBQVEsQ0FBQ25CLFFBQVEsSUFBSTtZQUFDO1lBQUs7WUFBSztTQUFJO1FBQ25EYyxTQUFTOUIsS0FBS2tDLEdBQUcsQ0FBQ0osUUFBUU0sTUFBTSxDQUFDLEVBQUU7UUFDbkNMLFNBQVMvQixLQUFLa0MsR0FBRyxDQUFDSCxRQUFRSyxNQUFNLENBQUMsRUFBRTtRQUNuQ0osU0FBU2hDLEtBQUtrQyxHQUFHLENBQUNGLFFBQVFJLE1BQU0sQ0FBQyxFQUFFO1FBRW5DLE9BQU87WUFBQ047WUFBUUM7WUFBUUM7U0FBTztJQUNqQyxHQUFHLEVBQUU7SUFFTCxpQ0FBaUM7SUFDakNyWCxnREFBU0EsQ0FBQztRQUNSLElBQUl3RSxpQkFBaUJxUSxlQUFlO1lBQ2xDLE1BQU1vQyxZQUFZRixxQkFBcUJ2UztZQUN2QyxNQUFNa1QsZUFBZVYsc0JBQ25CQyxXQUNBcEMsZUFDQXJRO1lBRUYwUSxjQUFjd0M7UUFDaEI7SUFDRixHQUFHO1FBQ0Q3QztRQUNBclE7UUFDQXVTO1FBQ0FDO0tBQ0Q7SUFFRCxvREFBb0Q7SUFDcERoWCxnREFBU0EsQ0FBQztRQUNSLElBQUl3RSxlQUFlO1lBQ2pCd1EsaUJBQWlCb0Isd0JBQXdCNVI7WUFDekM0USxpQkFBaUIwQix3QkFBd0J0UztZQUN6QywwREFBMEQ7WUFDMUQsSUFBSSxDQUFDcVEsZUFBZTtnQkFDbEJLLGNBQWM2QixxQkFBcUJ2UztZQUNyQztRQUNGO0lBQ0YsR0FBRztRQUNEQTtRQUNBNFI7UUFDQVU7UUFDQUM7UUFDQWxDO0tBQ0Q7SUFFRCxNQUFNLENBQUM4QyxjQUFjQyxnQkFBZ0IsR0FBRzFYLCtDQUFRQSxDQUFDO0lBRWpELDJDQUEyQztJQUMzQyxNQUFNLENBQUMyWCxVQUFVQyxZQUFZLEdBQUc1WCwrQ0FBUUEsQ0FBQztJQUV6Q0YsZ0RBQVNBLENBQUM7UUFDUixNQUFNK1gsY0FBYztZQUNsQkQsWUFBWS9JLE9BQU9pSixVQUFVLEdBQUc7UUFDbEM7UUFFQUQ7UUFDQWhKLE9BQU9DLGdCQUFnQixDQUFDLFVBQVUrSTtRQUVsQyxPQUFPLElBQU1oSixPQUFPRSxtQkFBbUIsQ0FBQyxVQUFVOEk7SUFDcEQsR0FBRyxFQUFFO0lBRUwsa0ZBQWtGO0lBQ2xGLE1BQU0sQ0FBQ0UsbUJBQW1CQyxxQkFBcUIsR0FBR2hZLCtDQUFRQSxDQUFDO1FBQ3pELHNFQUFzRTtRQUN0RSxJQUFJLElBQTZCLEVBQUU7WUFDakMsT0FBTzZPLE9BQU9pSixVQUFVLEdBQUc7UUFDN0I7UUFDQSxPQUFPLE9BQU8sMkNBQTJDO0lBQzNEO0lBRUEsb0VBQW9FO0lBQ3BFaFksZ0RBQVNBLENBQUM7UUFDUixJQUFJNlgsWUFBWWxSLGVBQWU7WUFDN0J1UixxQkFBcUI7UUFDdkI7SUFDRixHQUFHO1FBQUNMO1FBQVVsUjtLQUFjO0lBRTVCLHNFQUFzRTtJQUN0RTNHLGdEQUFTQSxDQUFDO1FBQ1IsaUZBQWlGO1FBQ2pGa1kscUJBQXFCTDtJQUN2QixHQUFHO1FBQUNBO0tBQVM7SUFFYixNQUFNTSxvQkFBb0I7UUFDeEIsSUFBSU4sVUFBVTtZQUNaLE9BQU87Z0JBQUMsQ0FBQztnQkFBTTtnQkFBTTthQUFJO1FBQzNCO1FBQ0EsT0FBTztZQUFDLENBQUM7WUFBTTtZQUFNO1NBQUk7SUFDM0I7SUFFQTdYLGdEQUFTQSxDQUFDO1FBQ1IsZ0VBQWdFO1FBQ2hFLE1BQU1vWSw4QkFBOEI7WUFDbEMsSUFBSTtnQkFDRixNQUFNQyxpQkFBaUIsTUFBTXhXLGdGQUFpQkEsQ0FBQztvQkFDN0N1UyxRQUFRaFEsS0FBS2tCLEVBQUU7b0JBQ2YrTyxhQUFhbFEsUUFBUW1RLEdBQUc7Z0JBQzFCO2dCQUNBLElBQUkrRCxrQkFBa0JBLGVBQWVqRixNQUFNLEVBQUU7b0JBQzNDaEIsZ0JBQWdCaUcsZUFBZWpGLE1BQU07Z0JBQ3ZDO1lBQ0YsRUFBRSxPQUFPb0IsS0FBSztnQkFDWmhSLFFBQVFFLEtBQUssQ0FBQywwQ0FBMEM4UTtZQUMxRDtRQUNGO1FBQ0E0RDtJQUNBLG9CQUFvQjtJQUNwQix1REFBdUQ7SUFDekQsR0FBRyxFQUFFO0lBRUwscUJBQ0U7OzBCQUNFLDhEQUFDalksMERBQWNBOzs7OzswQkFDZiw4REFBQ0ksK0NBQU1BO2dCQUNMK1gsU0FBUzVIO2dCQUNUNkgsY0FBY2hIO2dCQUNkaUgsYUFBYW5GO2dCQUNib0YsYUFBYXJHO2dCQUNidUYsY0FBY0E7Z0JBQ2RlLG9CQUFvQixJQUFNZCxnQkFBZ0IsQ0FBQ0Q7Z0JBQzNDeFQsU0FBU0E7Z0JBQ1RDLE1BQU1BOzs7Ozs7MEJBR1IsOERBQUM1QyxzRUFBbUJBOzs7OzswQkFHcEIsOERBQUNtWDtnQkFDQ0MsV0FBVyx3RkFFVixPQURDakIsZUFBZSxrQ0FBa0M7MEJBR25ELDRFQUFDN1csaURBQU9BO29CQUNOeUYsZUFBZUE7b0JBQ2ZDLGtCQUFrQkE7b0JBQ2xCc0UsWUFBWUE7b0JBQ1pFLFlBQVlBO29CQUNackUsZUFBZUE7b0JBQ2ZDLGtCQUFrQkE7b0JBQ2xCaVMsYUFBWTtvQkFDWnhTLGNBQWNBO29CQUNkTCxvQkFBb0JBO29CQUNwQkQsaUJBQWlCQTtvQkFDakJ1SixVQUFVQTtvQkFDVnVJLFVBQVVBO29CQUNWSyxzQkFBc0JBOzs7Ozs7Ozs7OztZQUt6QnZSLCtCQUNDLDhEQUFDZ1M7Z0JBQ0NHLEtBQUt4SjtnQkFDTHNKLFdBQVU7O29CQUVUalMsa0JBQWtCLDZCQUNqQiw4REFBQzVGLDREQUFjQTt3QkFDYmdGLGlCQUFpQkE7d0JBQ2pCUSxlQUFlQTt3QkFDZkMsa0JBQWtCQTt3QkFDbEJzRSxZQUFZQTt3QkFDWkUsWUFBWUE7Ozs7OztvQkFJZnJFLGtCQUFrQiw0QkFDakIsOERBQUNsRyx5REFBYUE7d0JBQ1pzRixpQkFBaUJBO3dCQUNqQjRQLGtCQUFrQkE7d0JBQ2xCeEUscUJBQXFCQTt3QkFDckJ5RSxxQkFBcUJBO3dCQUNyQnhFLHdCQUF3QkE7d0JBQ3hCeUUsb0JBQW9CQTt3QkFDcEJ4RSx1QkFBdUJBO3dCQUN2QnpOLFVBQVVBOzs7Ozs7b0JBSWIrQyxrQkFBa0Isa0NBQ2pCLDhEQUFDL0Ysc0VBQW1CQTt3QkFDbEIySCx1QkFBdUJBO3dCQUN2QkMsMEJBQTBCQTt3QkFDMUJDLHdCQUF3QkE7d0JBQ3hCQywyQkFBMkJBOzs7Ozs7b0JBSTlCL0Isa0JBQWtCLCtCQUNqQiw4REFBQ3JHLCtEQUFnQkE7d0JBQ2ZpSCxXQUFXQTt3QkFDWEMsY0FBY0E7d0JBQ2RDLFNBQVNBO3dCQUNUQyxZQUFZQTt3QkFDWkMsaUJBQWlCQTt3QkFDakJDLG9CQUFvQkE7d0JBQ3BCRyxZQUFZQTt3QkFDWkMsZUFBZUE7d0JBQ2ZDLGNBQWNBO3dCQUNkQyxpQkFBaUJBO3dCQUNqQnFDLGdCQUFnQkE7d0JBQ2hCQyxtQkFBbUJBO3dCQUNuQnJDLFNBQVNBO3dCQUNUQyxZQUFZQTt3QkFDWkMsYUFBYUE7d0JBQ2JDLGdCQUFnQkE7Ozs7OztvQkFJbkIzQixrQkFBa0IsMEJBQ2pCLDhEQUFDbkcsbURBQVVBO3dCQUNUNEUsUUFBUUE7d0JBQ1JDLFdBQVdBO3dCQUNYUyxXQUFXQTt3QkFDWG1CLFVBQVVBO3dCQUNWQyxhQUFhQTt3QkFDYkMsV0FBV0E7d0JBQ1hDLGNBQWNBO3dCQUNkUyxrQkFBa0JBO3dCQUNsQkMscUJBQXFCQTt3QkFDckJpUixlQUFlOzRCQUNiLHdDQUF3Qzs0QkFDeEMsTUFBTUMsZ0JBQWdCO2dDQUNwQjtvQ0FDRTFULElBQUk7b0NBQ0pDLE1BQU07b0NBQ05DLFVBQVU7d0NBQUM7d0NBQUs7d0NBQUs7cUNBQUU7b0NBQ3ZCQyxXQUFXO29DQUNYQyxPQUFPO29DQUNQQyxNQUFNO29DQUNOQyxZQUFZO29DQUNaQyxlQUFlO2dDQUNqQjtnQ0FDQTtvQ0FDRVAsSUFBSTtvQ0FDSkMsTUFBTTtvQ0FDTkMsVUFBVTt3Q0FBQzt3Q0FBRzt3Q0FBRztxQ0FBRTtvQ0FDbkJDLFdBQVc7b0NBQ1hDLE9BQU87b0NBQ1BDLE1BQU07Z0NBQ1I7Z0NBQ0E7b0NBQ0VMLElBQUk7b0NBQ0pDLE1BQU07b0NBQ05DLFVBQVU7d0NBQUMsQ0FBQzt3Q0FBRzt3Q0FBRyxDQUFDO3FDQUFFO29DQUNyQkMsV0FBVztvQ0FDWEMsT0FBTztvQ0FDUEMsTUFBTTtnQ0FDUjtnQ0FDQTtvQ0FDRUwsSUFBSTtvQ0FDSkMsTUFBTTtvQ0FDTkMsVUFBVTt3Q0FBQzt3Q0FBRzt3Q0FBRyxDQUFDO3FDQUFFO29DQUNwQkMsV0FBVztvQ0FDWEMsT0FBTztvQ0FDUEMsTUFBTTtnQ0FDUjs2QkFDRDs0QkFDRE4sVUFBVTJUOzRCQUNWLHVCQUF1Qjs0QkFDdkI5UyxpQkFBaUI7NEJBQ2pCLCtCQUErQjs0QkFDL0JFLHdCQUF3QixDQUFDO3dCQUMzQjs7Ozs7O29CQUlITyxrQkFBa0IsNkJBQ2pCLDhEQUFDcEYsNERBQWNBO3dCQUNiMFgsZUFBZW5EO3dCQUNmb0QsdUJBQXVCbEU7d0JBQ3ZCbUUsb0JBQW9CakU7d0JBQ3BCa0UsdUJBQXVCaEU7d0JBQ3ZCTCxlQUFlQTt3QkFDZkUsWUFBWUE7d0JBQ1pFLGVBQWVBO3dCQUNmSSxZQUFZQTt3QkFDWjhELG9CQUFvQjdEO3dCQUNwQjhELGNBQWN0RDt3QkFDZFAsY0FBY0E7d0JBQ2Q1TyxnQkFBZ0JBO3dCQUNoQkMsbUJBQW1CQTt3QkFDbkJ5UyxXQUFXeFM7d0JBQ1h5UyxtQkFBbUJ4Uzs7Ozs7Ozs7Ozs7OzBCQU8zQiw4REFBQzJSO2dCQUNDQyxXQUFXLDREQUVWLE9BRENqQixlQUFlLGtDQUFrQzswQkFHbkQsNEVBQUNnQjtvQkFBSUMsV0FBVTs4QkFDYiw0RUFBQ2pZLCtDQUFRQTt3QkFDUDBGLGNBQWNBO3dCQUNkTixpQkFBaUJBO3dCQUNqQjBULGdCQUFnQnpUO3dCQUNoQjBULGNBQWN0Szt3QkFDZHVLLGlCQUFpQnBKO3dCQUNqQmlJLGFBQWFuRjt3QkFDYmhNLFlBQVlBO3dCQUNaQyxlQUFlQTt3QkFDZnNTLFdBQVczQjt3QkFDWDRCLGNBQWMzQjs7Ozs7Ozs7Ozs7Ozs7OzswQkFNcEIsOERBQUNTO2dCQUNDQyxXQUFVO2dCQUNWa0IsT0FBTztvQkFDTEMsWUFBWTtnQkFDZDswQkFFQSw0RUFBQ25hLHVEQUFNQTtvQkFDTGdaLFdBQVU7b0JBQ1Z4VyxRQUFRO3dCQUNOb0QsVUFBVTJTO3dCQUNWNkIsS0FBSzt3QkFDTEMsTUFBTTt3QkFDTkMsS0FBSztvQkFDUDtvQkFDQUMsT0FBTztvQkFDUHJCLEtBQUsvTztvQkFDTDdILElBQUk7d0JBQ0ZrWSxXQUFXO3dCQUNYQyx1QkFBdUI7b0JBQ3pCO29CQUNBQyxpQkFBaUJqTztvQkFDakJrTyxXQUFXO3dCQUNUQyxnQkFBZ0IsQ0FBQ2pQLElBQU87Z0NBQUVrUCxTQUFTbFAsRUFBRW1QLE9BQU87Z0NBQUVDLFNBQVNwUCxFQUFFcVAsT0FBTzs0QkFBQztvQkFDbkU7b0JBQ0FDLFdBQVU7OEJBRVYsNEVBQUMvYSwyQ0FBUUE7d0JBQ1BnYix3QkFDRSw4REFBQ3ZiLG9EQUFJQTtzQ0FDSCw0RUFBQ0Msc0RBQU1BOzs7Ozs7Ozs7OzswQ0FNWCw4REFBQ3dDO2dDQUNDQyxpQkFBaUIsQ0FBQzhZO29DQUNoQnpKLGNBQWMxSCxPQUFPLEdBQUdtUjtnQ0FDMUI7Ozs7OzswQ0FHRiw4REFBQ2pYO2dDQUNDRSxpQkFBaUIsQ0FBQzlCO29DQUNoQjhILFlBQVk5SDtnQ0FDZDs7Ozs7OzBDQUdGLDhEQUFDckIscURBQVdBO2dDQUNWdUUsUUFBUUE7Z0NBQ1JVLFdBQVdBO2dDQUNYRyxlQUFlQTtnQ0FDZitVLGVBQWU5VTtnQ0FDZitVLGNBQWM1TTtnQ0FDZHhHLGtCQUFrQkE7Z0NBQ2xCcVQsb0JBQW9COzs7Ozs7MENBRXRCLDhEQUFDN2EscURBQVdBO2dDQUNWeVIsUUFBUXJCO2dDQUNSc0osWUFBWXBTO2dDQUNaRixTQUFTQTtnQ0FDVE0sWUFBWUE7Z0NBQ1p0QyxXQUFXd0M7Z0NBQ1g4SixNQUFNNUo7Z0NBQ05FLGFBQWFBOzs7Ozs7MENBR2YsOERBQUMxRyxnRUFBZ0JBO2dDQUFDb0UsaUJBQWlCQTswQ0FDakMsNEVBQUN0RSxvREFBVUE7b0NBQ1QrQyxlQUFlQTtvQ0FDZjJDLFdBQVdBO29DQUNYdUcsbUJBQW1CQTtvQ0FDbkJwQyxtQkFBbUJBO29DQUNuQjFHLGVBQWVBO29DQUNmeUMsWUFBWUE7b0NBQ1p3TixlQUFlQTtvQ0FDZnRGLGlCQUFpQkE7b0NBQ2pCd0YsZUFBZUE7b0NBQ2ZFLFlBQVlBO29DQUNaRSxlQUFlQTtvQ0FDZkksWUFBWUE7b0NBQ1pVLE1BQU1SO29DQUNONU8sZ0JBQWdCQTtvQ0FDaEJzVSxrQkFBa0JoRjtvQ0FDbEJwUCxnQkFBZ0JBO29DQUNoQlYsY0FBY0E7b0NBQ2RzUCxrQkFBa0JBO29DQUNsQkMscUJBQXFCQTtvQ0FDckJDLG9CQUFvQkE7Ozs7Ozs7Ozs7OzBDQUl4Qiw4REFBQ25XLDZEQUFhQTtnQ0FDWm9aLEtBQUtoUDtnQ0FDTHFHLFFBQVE7b0NBQUM7b0NBQUc7b0NBQUs7aUNBQUU7Z0NBQ25CaUwsV0FBVztnQ0FDWEMsZUFBZTtnQ0FDZixvQkFBb0I7Z0NBQ3BCLGlDQUFpQztnQ0FDakMsb0JBQW9CO2dDQUNwQkMsYUFBYTs7Ozs7OzRCQUdkdlYsZ0JBQWdCa0YsTUFBTSxHQUFHLEtBQUt0RSxrQkFBa0IsNkJBQy9DLDhEQUFDaEgsaUVBQWlCQTtnQ0FDaEI4TCxRQUNFMUYsZ0JBQWdCa0YsTUFBTSxLQUFLLElBQ3ZCbEYsZUFBZSxDQUFDLEVBQUUsR0FDbEJVLGtCQUFrQm1ELE9BQU87Z0NBRS9CMlIsTUFBTWhWO2dDQUNOaVYsYUFBYTtvQ0FDWCxJQUFJMVIsaUJBQWlCRixPQUFPLEVBQzFCRSxpQkFBaUJGLE9BQU8sQ0FBQ2hCLE9BQU8sR0FBRztnQ0FDdkM7Z0NBQ0E2UyxXQUFXO29DQUNULElBQUkzUixpQkFBaUJGLE9BQU8sRUFDMUJFLGlCQUFpQkYsT0FBTyxDQUFDaEIsT0FBTyxHQUFHO29DQUNyQyxJQUFJN0MsZ0JBQWdCa0YsTUFBTSxHQUFHLEdBQUdtQztnQ0FDbEM7Z0NBQ0FzTyxnQkFBZ0I7b0NBQ2QsSUFBSTNWLGdCQUFnQmtGLE1BQU0sR0FBRyxHQUFHbUM7Z0NBQ2xDOzs7Ozs7NEJBR0gvRixlQUFlLHdCQUNkLDhEQUFDc1U7Z0NBQVdDLE1BQU07b0NBQUM7b0NBQUk7b0NBQUk7b0NBQVc7aUNBQVU7Z0NBQUVDLGFBQWE7Ozs7Ozs0QkFFaEV4VSxlQUFlLHlCQUNkLDhEQUFDeVU7Z0NBQ0NDLGNBQVksQ0FBQzFHLEtBQUtDLEVBQUUsR0FBRztnQ0FDdkJ1RyxhQUFhO2dDQUNiclcsVUFBVTtvQ0FBQztvQ0FBRyxDQUFDO29DQUFNO2lDQUFFOztrREFFdkIsOERBQUN3Vzt3Q0FBY0osTUFBTTs0Q0FBQzs0Q0FBSzt5Q0FBSTs7Ozs7O2tEQUMvQiw4REFBQ0s7d0NBQ0N2VyxPQUFNO3dDQUNOeUIsV0FBV0E7d0NBQ1hnSCxhQUFhO3dDQUNiQyxTQUFTOzs7Ozs7Ozs7Ozs7NEJBSWQvRyxlQUFlLDhCQUNkLDhEQUFDeVU7Z0NBQ0NELGFBQWE7Z0NBQ2JyTyxVQUFVO29DQUFDLENBQUM2SCxLQUFLQyxFQUFFLEdBQUc7b0NBQUc7b0NBQUc7aUNBQUU7Z0NBQzlCOVAsVUFBVTtvQ0FBQztvQ0FBRyxDQUFDO29DQUFNO2lDQUFFOztrREFFdkIsOERBQUN3Vzt3Q0FBY0osTUFBTTs0Q0FBQzs0Q0FBSTt5Q0FBRzs7Ozs7O2tEQUM3Qiw4REFBQ25jLHFFQUFxQkE7d0NBQ3BCc1MsTUFBTTs0Q0FBQzs0Q0FBSzt5Q0FBSTt3Q0FDaEJtSyxZQUFZO3dDQUNaQyxTQUFTO3dDQUNUQyxhQUFhO3dDQUNibk8sV0FBVzt3Q0FDWG9PLFlBQVk7d0NBQ1pDLG1CQUFtQjt3Q0FDbkJDLG1CQUFtQjt3Q0FDbkI3VyxPQUFNO3dDQUNOd0ksV0FBVzt3Q0FDWHNPLFFBQVE7d0NBQ1JyTyxhQUFhO3dDQUNiQyxTQUFTOzs7Ozs7Ozs7Ozs7NEJBSWQvRyxlQUFlLHdCQUNkLDhEQUFDeVU7Z0NBQ0NELGFBQWE7Z0NBQ2JyTyxVQUFVO29DQUFDLENBQUM2SCxLQUFLQyxFQUFFLEdBQUc7b0NBQUc7b0NBQUc7aUNBQUU7Z0NBQzlCOVAsVUFBVTtvQ0FBQztvQ0FBRyxDQUFDO29DQUFNO2lDQUFFOztrREFFdkIsOERBQUN3Vzt3Q0FBY0osTUFBTTs0Q0FBQzs0Q0FBSzt5Q0FBSTs7Ozs7O2tEQUMvQiw4REFBQ2E7d0NBQ0NoUixRQUNFLElBQUlyTCxrREFBb0IsQ0FBQzs0Q0FDdkJnTyxTQUFTOzRDQUNUMUksT0FBTyxJQUFJdEYseUNBQVcsQ0FBQzs0Q0FDdkIrTixhQUFhO3dDQUNmOzs7Ozs7Ozs7Ozs7MENBS1IsOERBQUM5TywyREFBV0E7Z0NBQUN1ZCxXQUFVO2dDQUFlQyxRQUFRO29DQUFDO29DQUFJO2lDQUFHOzBDQUNwRCw0RUFBQ3ZkLDZEQUFhQTtvQ0FDWndkLFlBQVc7b0NBQ1hDLFlBQVk7d0NBQUM7d0NBQU87d0NBQVM7cUNBQU87Ozs7Ozs7Ozs7OzBDQUl4Qyw4REFBQ047Z0NBQVVoUixRQUFRaEYsa0JBQWtCbUQsT0FBTzs7Ozs7OzBDQUU1Qyw4REFBQ2xJLDBFQUFxQkE7Z0NBQ3BCa0gsU0FBU0w7Z0NBQ1QwSixVQUFVeEo7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7WUFNakI4QixnQ0FBa0IsOERBQUM3SixtREFBVUE7Z0JBQUNpTixPQUFPN0k7Ozs7Ozs7O0FBRzVDO0lBbnZEd0JiOztRQUNDbEMseURBQWVBOzs7TUFEaEJrQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvY29tcG9uZW50cy9BcnRpc3RUb29sL0VkaXRvckV4cGVyaWVuY2UuanN4P2Q5YjciXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5cbmltcG9ydCB7XG4gIEdpem1vSGVscGVyLFxuICBHaXptb1ZpZXdwb3J0LFxuICBIdG1sLFxuICBMb2FkZXIsXG4gIE1lc2hSZWZsZWN0b3JNYXRlcmlhbCxcbiAgT3JiaXRDb250cm9scyxcbiAgVHJhbnNmb3JtQ29udHJvbHMsXG59IGZyb20gXCJAcmVhY3QtdGhyZWUvZHJlaVwiO1xuaW1wb3J0IHsgQ2FudmFzLCB1c2VUaHJlZSB9IGZyb20gXCJAcmVhY3QtdGhyZWUvZmliZXJcIjtcbmltcG9ydCB7IFN1c3BlbnNlLCB1c2VDYWxsYmFjaywgdXNlRWZmZWN0LCB1c2VSZWYsIHVzZVN0YXRlIH0gZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQgeyBUb2FzdENvbnRhaW5lciB9IGZyb20gXCJyZWFjdC10b2FzdGlmeVwiO1xuXG5pbXBvcnQgKiBhcyBUSFJFRSBmcm9tIFwidGhyZWVcIjtcbmltcG9ydCB7IEVudmlyb25tZW50IH0gZnJvbSBcIi4vRW52aXJvbm1lbnRcIjtcbmltcG9ydCB7IEVudmlyb25tZW50UGFuZWwgfSBmcm9tIFwiLi9FbnZpcm9ubWVudFBhbmVsXCI7XG5pbXBvcnQgSGVhZGVyIGZyb20gXCIuL0hlYWRlclwiO1xuaW1wb3J0IHsgTGlnaHRQYW5lbCB9IGZyb20gXCIuL0xpZ2h0UGFuZWxcIjtcbmltcG9ydCB7IE1hdGVyaWFsUGFuZWwgfSBmcm9tIFwiLi9NYXRlcmlhbFBhbmVsXCI7XG5pbXBvcnQgeyBNb2RlbFN0YXRzIH0gZnJvbSBcIi4vTW9kZWxTdGF0c1wiO1xuaW1wb3J0IHsgT3V0bGluZXIgfSBmcm9tIFwiLi9PdXRsaW5lclwiO1xuaW1wb3J0IHsgUG9zdFByb2Nlc3NpbmdQYW5lbCB9IGZyb20gXCIuL1Bvc3RQcm9jZXNzaW5nUGFuZWxcIjtcbmltcG9ydCBTY2VuZUxpZ2h0cyBmcm9tIFwiLi9TY2VuZUxpZ2h0c1wiO1xuaW1wb3J0IFRvb2xiYXIgZnJvbSBcIi4vVG9vbGJhclwiO1xuaW1wb3J0IHsgVHJhbnNmb3JtUGFuZWwgfSBmcm9tIFwiLi9UcmFuc2Zvcm1QYW5lbFwiO1xuXG5pbXBvcnQgeyBzZXRDYW1lcmFWaWV3IH0gZnJvbSBcIi4uLy4uL3V0aWxzL0FydGlzdFRvb2wvY2FtZXJhVXRpbHNcIjtcbmltcG9ydCB7XG4gIGFwcGx5SGlzdG9yeVN0YXRlLFxuICBjcmVhdGVIaXN0b3J5U3RhdGUsXG59IGZyb20gXCIuLi8uLi91dGlscy9BcnRpc3RUb29sL2hpc3RvcnlVdGlsc1wiO1xuaW1wb3J0IHsgYXBwbHlNYXRlcmlhbFRvT2JqZWN0IH0gZnJvbSBcIi4uLy4uL3V0aWxzL0FydGlzdFRvb2wvbWF0ZXJpYWxVdGlsc1wiO1xuaW1wb3J0IHtcbiAgY2FsY3VsYXRlTW9kZWxTdGF0cyxcbiAgc2V0U2hhZG93c09uTW9kZWwsXG59IGZyb20gXCIuLi8uLi91dGlscy9BcnRpc3RUb29sL3NjZW5lVXRpbHNcIjtcbmltcG9ydCB7IHByZWRlZmluZWRBbmltYXRpb25zIH0gZnJvbSBcIi4vYW5pbWF0aW9ucy9DYW1lcmFBbmltYXRpb25zXCI7XG5pbXBvcnQgeyBFbmdyYXZpbmdQYW5lbCB9IGZyb20gXCIuL0VuZ3JhdmluZ1BhbmVsXCI7XG5pbXBvcnQgeyBJbml0aWFsT3ZlcmxheVBhbmVsIH0gZnJvbSBcIi4vSW5pdGlhbE92ZXJsYXlQYW5lbFwiO1xuaW1wb3J0IHsgTW9kZWxTdGFnZSB9IGZyb20gXCIuL01vZGVsU3RhZ2VcIjtcbmltcG9ydCB7IFBvc3RQcm9jZXNzaW5nRWZmZWN0cyB9IGZyb20gXCIuL1Bvc3RQcm9jZXNzaW5nRWZmZWN0c1wiO1xuaW1wb3J0IHsgU2VsZWN0aW9uT3V0bGluZSB9IGZyb20gXCIuL1NlbGVjdGlvbk91dGxpbmVcIjtcbmltcG9ydCB7XG4gIGNyZWF0ZVZlcnNpb24sXG4gIGdldEN1cnJlbnRWZXJzaW9uLFxufSBmcm9tIFwiQC9saWIvYWN0aW9ucy92ZXJzaW9uLmFjdGlvbnNcIjtcbmltcG9ydCB7IHRvYXN0IH0gZnJvbSBcInNvbm5lclwiO1xuaW1wb3J0IHsgdXNlVmVyc2lvblN0YXRlIH0gZnJvbSBcIkAvbGliL3N0YXRlc1wiO1xuXG5jb25zdCBTY3JlZW5zaG90VGFrZXIgPSAoeyBvblNjcmVlbnNob3RSZWYgfSkgPT4ge1xuICBjb25zdCB7IGdsLCBzY2VuZSwgY2FtZXJhIH0gPSB1c2VUaHJlZSgpO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKHR5cGVvZiBvblNjcmVlbnNob3RSZWYgPT09IFwiZnVuY3Rpb25cIikge1xuICAgICAgb25TY3JlZW5zaG90UmVmKCgpID0+IHtcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICAvLyBSZW5kZXIgdGhlIHNjZW5lXG4gICAgICAgICAgZ2wucmVuZGVyKHNjZW5lLCBjYW1lcmEpO1xuXG4gICAgICAgICAgLy8gR2V0IHRoZSBjYW52YXMgZGF0YSBVUkxcbiAgICAgICAgICBjb25zdCBkYXRhVVJMID0gZ2wuZG9tRWxlbWVudC50b0RhdGFVUkwoXCJpbWFnZS9wbmdcIik7XG5cbiAgICAgICAgICAvLyBDcmVhdGUgYW5kIHRyaWdnZXIgYSBkb3dubG9hZFxuICAgICAgICAgIGNvbnN0IGxpbmsgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KFwiYVwiKTtcbiAgICAgICAgICBjb25zdCB0aW1lc3RhbXAgPSBuZXcgRGF0ZSgpXG4gICAgICAgICAgICAudG9JU09TdHJpbmcoKVxuICAgICAgICAgICAgLnJlcGxhY2UoLzovZywgXCItXCIpXG4gICAgICAgICAgICAuc3Vic3RyaW5nKDAsIDE5KTtcbiAgICAgICAgICBsaW5rLmRvd25sb2FkID0gYGFnYXBlLXNjZW5lLSR7dGltZXN0YW1wfS5wbmdgO1xuICAgICAgICAgIGxpbmsuaHJlZiA9IGRhdGFVUkw7XG4gICAgICAgICAgZG9jdW1lbnQuYm9keS5hcHBlbmRDaGlsZChsaW5rKTtcbiAgICAgICAgICBsaW5rLmNsaWNrKCk7XG5cbiAgICAgICAgICAvLyBDbGVhbiB1cFxuICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICAgICAgZG9jdW1lbnQuYm9keS5yZW1vdmVDaGlsZChsaW5rKTtcbiAgICAgICAgICB9LCAxMDApO1xuXG4gICAgICAgICAgY29uc29sZS5sb2coXCJTY3JlZW5zaG90IGNhcHR1cmVkIHN1Y2Nlc3NmdWxseVwiKTtcbiAgICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICBjb25zb2xlLmVycm9yKFwiRXJyb3IgY2FwdHVyaW5nIHNjcmVlbnNob3Q6XCIsIGVycm9yKTtcbiAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgIH1cbiAgICAgIH0pO1xuICAgIH1cbiAgfSwgW2dsLCBzY2VuZSwgY2FtZXJhLCBvblNjcmVlbnNob3RSZWZdKTtcblxuICByZXR1cm4gbnVsbDtcbn07XG5cbi8vIENvbXBvbmVudCB0byBwcm92aWRlIHJlbmRlcmVyIHRvIE1hdGVyaWFsUGFuZWxcbmNvbnN0IE1hdGVyaWFsUGFuZWxXcmFwcGVyID0gKHsgcmVuZGVyZXIsIC4uLnByb3BzIH0pID0+IHtcbiAgcmV0dXJuIDxNYXRlcmlhbFBhbmVsIHJlbmRlcmVyPXtyZW5kZXJlcn0gey4uLnByb3BzfSAvPjtcbn07XG5cbi8vIENvbXBvbmVudCB0byBnZXQgcmVuZGVyZXIgZnJvbSBDYW52YXMgY29udGV4dFxuY29uc3QgUmVuZGVyZXJQcm92aWRlciA9ICh7IGNoaWxkcmVuLCBvblJlbmRlcmVyUmVhZHkgfSkgPT4ge1xuICBjb25zdCB7IGdsIH0gPSB1c2VUaHJlZSgpO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKG9uUmVuZGVyZXJSZWFkeSkge1xuICAgICAgb25SZW5kZXJlclJlYWR5KGdsKTtcbiAgICB9XG4gIH0sIFtnbCwgb25SZW5kZXJlclJlYWR5XSk7XG5cbiAgcmV0dXJuIG51bGw7XG59O1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBFZGl0b3JFeHBlcmllbmNlKHsgbW9kZWxVcmwsIHByb2plY3QsIHVzZXIgfSkge1xuICBjb25zdCB7IGFkZFZlcnNpb24gfSA9IHVzZVZlcnNpb25TdGF0ZSgpO1xuXG4gIGNvbnN0IGNvbmZpZ1JlZiA9IHVzZVJlZihudWxsKTtcbiAgY29uc3Qgd29ya2luZ0NvbmZpZ1JlZiA9IHVzZVJlZihudWxsKTtcbiAgY29uc3QgW3NlbGVjdGVkTW9kZWwsIHNldFNlbGVjdGVkTW9kZWxdID0gdXNlU3RhdGUoXCJ1cGxvYWRlZFwiKTtcbiAgY29uc3QgW2xhc3RTZWxlY3RlZE1vZGVsLCBzZXRMYXN0U2VsZWN0ZWRNb2RlbF0gPSB1c2VTdGF0ZShudWxsKTtcbiAgY29uc3QgW3VwbG9hZGVkTW9kZWwsIHNldFVwbG9hZGVkTW9kZWxdID0gdXNlU3RhdGUobW9kZWxVcmwgfHwgbnVsbCk7XG4gIGNvbnN0IFttb2RlbFN0YXRzLCBzZXRNb2RlbFN0YXRzXSA9IHVzZVN0YXRlKHtcbiAgICB2ZXJ0aWNlczogMCxcbiAgICB0cmlhbmdsZXM6IDAsXG4gICAgbWF0ZXJpYWxzOiAwLFxuICAgIGFuaW1hdGlvbnM6IDAsXG4gIH0pO1xuICBjb25zdCBbbGlnaHRzLCBzZXRMaWdodHNdID0gdXNlU3RhdGUoW1xuICAgIHtcbiAgICAgIGlkOiBcImRlZmF1bHREaXJlY3Rpb25hbFwiLFxuICAgICAgdHlwZTogXCJkaXJlY3Rpb25hbFwiLFxuICAgICAgcG9zaXRpb246IFsyLjUsIDUuNSwgNV0sXG4gICAgICBpbnRlbnNpdHk6IDEuMCxcbiAgICAgIGNvbG9yOiBcIiNmZmZmZmZcIixcbiAgICAgIG5hbWU6IFwiTWFpbiBEaXJlY3Rpb25hbCBMaWdodFwiLFxuICAgICAgY2FzdFNoYWRvdzogdHJ1ZSxcbiAgICAgIGhlbHBlclZpc2libGU6IGZhbHNlLFxuICAgIH0sXG4gICAge1xuICAgICAgaWQ6IFwia2V5TGlnaHRcIixcbiAgICAgIHR5cGU6IFwicG9pbnRcIixcbiAgICAgIHBvc2l0aW9uOiBbNSwgNSwgNV0sXG4gICAgICBpbnRlbnNpdHk6IDguMCxcbiAgICAgIGNvbG9yOiBcIiNmZmZmZmZcIixcbiAgICAgIG5hbWU6IFwiS2V5IExpZ2h0XCIsXG4gICAgfSxcbiAgICB7XG4gICAgICBpZDogXCJmaWxsTGlnaHRcIixcbiAgICAgIHR5cGU6IFwicG9pbnRcIixcbiAgICAgIHBvc2l0aW9uOiBbLTUsIDUsIC01XSxcbiAgICAgIGludGVuc2l0eTogNC4wLFxuICAgICAgY29sb3I6IFwiI2ZmZmZmZlwiLFxuICAgICAgbmFtZTogXCJGaWxsIExpZ2h0XCIsXG4gICAgfSxcbiAgICB7XG4gICAgICBpZDogXCJiYWNrTGlnaHRcIixcbiAgICAgIHR5cGU6IFwicG9pbnRcIixcbiAgICAgIHBvc2l0aW9uOiBbMCwgNSwgLTVdLFxuICAgICAgaW50ZW5zaXR5OiAyLjAsXG4gICAgICBjb2xvcjogXCIjZmZmZmZmXCIsXG4gICAgICBuYW1lOiBcIkJhY2sgTGlnaHRcIixcbiAgICB9LFxuICBdKTtcbiAgY29uc3QgbGlnaHRSZWZzID0gdXNlUmVmKHt9KTtcbiAgY29uc3QgW3NlbGVjdGVkT2JqZWN0cywgc2V0U2VsZWN0ZWRPYmplY3RzXSA9IHVzZVN0YXRlKFtdKTtcbiAgY29uc3QgW3NlbGVjdGVkTGlnaHQsIHNldFNlbGVjdGVkTGlnaHRdID0gdXNlU3RhdGUobnVsbCk7XG4gIGNvbnN0IFtsaWdodFBvc2l0aW9uSGlzdG9yeSwgc2V0TGlnaHRQb3NpdGlvbkhpc3RvcnldID0gdXNlU3RhdGUoe30pO1xuICBjb25zdCBbc2NlbmVPYmplY3RzLCBzZXRTY2VuZU9iamVjdHNdID0gdXNlU3RhdGUoW10pO1xuICBjb25zdCBbdHJhbnNmb3JtTW9kZSwgc2V0VHJhbnNmb3JtTW9kZV0gPSB1c2VTdGF0ZShcInRyYW5zbGF0ZVwiKTtcbiAgY29uc3Qgc2VsZWN0aW9uR3JvdXBSZWYgPSB1c2VSZWYobmV3IFRIUkVFLkdyb3VwKCkpO1xuXG4gIGNvbnNvbGUubG9nKFwicmVuZGVyaW5nZ2dnIGVkaXRvciBleHBlcmllbmNlXCIpO1xuXG4gIGNvbnN0IFtyaWdodFBhbmVsVGFiLCBzZXRSaWdodFBhbmVsVGFiXSA9IHVzZVN0YXRlKG51bGwpO1xuXG4gIC8vIERlY2FsIHBsYWNlbWVudCBzdGF0ZVxuICBjb25zdCBbaXNQbGFjaW5nRGVjYWwsIHNldElzUGxhY2luZ0RlY2FsXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW3Nob3dEZWNhbERlYnVnLCBzZXRTaG93RGVjYWxEZWJ1Z10gPSB1c2VTdGF0ZShmYWxzZSk7XG5cbiAgLy8gU2NlbmUgY29udHJvbHMgc3RhdGVcbiAgY29uc3QgW3Nob3dHcmlkLCBzZXRTaG93R3JpZF0gPSB1c2VTdGF0ZSh0cnVlKTtcbiAgY29uc3QgW3dpcmVmcmFtZSwgc2V0V2lyZWZyYW1lXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2dyb3VuZFR5cGUsIHNldEdyb3VuZFR5cGVdID0gdXNlU3RhdGUoXCJub25lXCIpO1xuICBjb25zdCBbZW52UHJlc2V0LCBzZXRFbnZQcmVzZXRdID0gdXNlU3RhdGUoXCJoZHJpXzE1XCIpO1xuICBjb25zdCBbYmdDb2xvciwgc2V0QmdDb2xvcl0gPSB1c2VTdGF0ZShcIiMyNzI2MzFcIik7XG4gIGNvbnN0IFtzaG93RW52aXJvbm1lbnQsIHNldFNob3dFbnZpcm9ubWVudF0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtzaG93TGlnaHRTcGhlcmVzLCBzZXRTaG93TGlnaHRTcGhlcmVzXSA9IHVzZVN0YXRlKHRydWUpO1xuICBjb25zdCBbY3VzdG9tSGRyaSwgc2V0Q3VzdG9tSGRyaV0gPSB1c2VTdGF0ZShudWxsKTtcbiAgY29uc3QgW2VudkludGVuc2l0eSwgc2V0RW52SW50ZW5zaXR5XSA9IHVzZVN0YXRlKDEuODUpO1xuICBjb25zdCBbZW52Qmx1ciwgc2V0RW52Qmx1cl0gPSB1c2VTdGF0ZSgwLjM1KTtcbiAgY29uc3QgW2VudlJvdGF0aW9uLCBzZXRFbnZSb3RhdGlvbl0gPSB1c2VTdGF0ZSgwKTtcblxuICBjb25zdCBbcG9zdFByb2Nlc3NpbmdFbmFibGVkLCBzZXRQb3N0UHJvY2Vzc2luZ0VuYWJsZWRdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbcG9zdFByb2Nlc3NpbmdTZXR0aW5ncywgc2V0UG9zdFByb2Nlc3NpbmdTZXR0aW5nc10gPSB1c2VTdGF0ZSh7XG4gICAgYXV0b2ZvY3VzOiB7XG4gICAgICBlbmFibGVkOiBmYWxzZSxcbiAgICAgIGJva2VoU2NhbGU6IDE4LFxuICAgIH0sXG4gICAgYmxvb206IHtcbiAgICAgIGVuYWJsZWQ6IHRydWUsXG4gICAgICBpbnRlbnNpdHk6IDAuMjIsXG4gICAgICB0aHJlc2hvbGQ6IDEsXG4gICAgICByYWRpdXM6IDAuNSxcbiAgICB9LFxuICAgIGRvZjoge1xuICAgICAgZW5hYmxlZDogZmFsc2UsXG4gICAgICBmb2N1c0Rpc3RhbmNlOiAwLFxuICAgICAgYXBlcnR1cmU6IDAuMDEsXG4gICAgICBib2tlaFNjYWxlOiAzLFxuICAgIH0sXG4gICAgdmlnbmV0dGU6IHtcbiAgICAgIGVuYWJsZWQ6IHRydWUsXG4gICAgICBkYXJrbmVzczogMC41LFxuICAgICAgb2Zmc2V0OiAwLjEsXG4gICAgfSxcbiAgfSk7XG5cbiAgLy8gU3RvcmUgaW5pdGlhbCBzdGF0ZVxuICBjb25zdCBpbml0aWFsU3RhdGVSZWYgPSB1c2VSZWYoe1xuICAgIHNlbGVjdGVkTW9kZWw6IG51bGwsXG4gICAgbGlnaHRzOiBbXSxcbiAgICBlbnZQcmVzZXQ6IG51bGwsXG4gICAgYmdDb2xvcjogbnVsbCxcbiAgICBwb3N0UHJvY2Vzc2luZ1NldHRpbmdzOiBudWxsLFxuICAgIHNob3dHcmlkOiBudWxsLFxuICAgIHdpcmVmcmFtZTogbnVsbCxcbiAgICBncm91bmRUeXBlOiBudWxsLFxuICAgIHNob3dFbnZpcm9ubWVudDogbnVsbCxcbiAgICBzaG93TGlnaHRTcGhlcmVzOiBudWxsLFxuICAgIGVudkludGVuc2l0eTogbnVsbCxcbiAgICBlbnZCbHVyOiBudWxsLFxuICAgIGNhbWVyYVBvc2l0aW9uOiBudWxsLFxuICAgIGNhbWVyYVRhcmdldDogbnVsbCxcbiAgfSk7XG5cbiAgLy8gQWRkIGEgcmVmIHRvIHN0b3JlIHRoZSBpbml0aWFsIHN0YXRlIG9mIG1vZGVsIG9iamVjdHNcbiAgY29uc3QgbW9kZWxJbml0aWFsU3RhdGVSZWYgPSB1c2VSZWYoe1xuICAgIG9iamVjdHM6IFtdLFxuICB9KTtcblxuICAvLyBDYXB0dXJlIGluaXRpYWwgc3RhdGUgd2hlbiBjb21wb25lbnQgbW91bnRzXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaW5pdGlhbFN0YXRlUmVmLmN1cnJlbnQgPSB7XG4gICAgICBzZWxlY3RlZE1vZGVsLFxuICAgICAgbGlnaHRzOiBbLi4ubGlnaHRzXSxcbiAgICAgIGVudlByZXNldCxcbiAgICAgIGJnQ29sb3IsXG4gICAgICBwb3N0UHJvY2Vzc2luZ1NldHRpbmdzOiB7IC4uLnBvc3RQcm9jZXNzaW5nU2V0dGluZ3MgfSxcbiAgICAgIHNob3dHcmlkLFxuICAgICAgd2lyZWZyYW1lLFxuICAgICAgZ3JvdW5kVHlwZSxcbiAgICAgIHNob3dFbnZpcm9ubWVudCxcbiAgICAgIHNob3dMaWdodFNwaGVyZXMsXG4gICAgICBlbnZJbnRlbnNpdHksXG4gICAgICBlbnZCbHVyLFxuICAgICAgY2FtZXJhUG9zaXRpb246IG5ldyBUSFJFRS5WZWN0b3IzKDUsIDIsIDUpLFxuICAgICAgY2FtZXJhVGFyZ2V0OiBuZXcgVEhSRUUuVmVjdG9yMygwLCAxLCAwKSxcbiAgICB9O1xuICB9LCBbXSk7XG5cbiAgY29uc3Qgb3JiaXRDb250cm9sc1JlZiA9IHVzZVJlZigpO1xuICBjb25zdCBzY2VuZVJlZiA9IHVzZVJlZigpO1xuICBjb25zdCBbcmVuZGVyZXIsIHNldFJlbmRlcmVyXSA9IHVzZVN0YXRlKG51bGwpO1xuXG4gIC8vIEhpc3Rvcnkgc3RhdGUgZm9yIHVuZG8vcmVkb1xuICBjb25zdCBbaGlzdG9yeSwgc2V0SGlzdG9yeV0gPSB1c2VTdGF0ZShbXSk7XG4gIGNvbnN0IFtoaXN0b3J5SW5kZXgsIHNldEhpc3RvcnlJbmRleF0gPSB1c2VTdGF0ZSgtMSk7XG4gIGNvbnN0IFtpc1VuZG9SZWRvQWN0aW9uLCBzZXRJc1VuZG9SZWRvQWN0aW9uXSA9IHVzZVN0YXRlKGZhbHNlKTtcblxuICBjb25zdCBbc2hvd01vZGVsU3RhdHMsIHNldFNob3dNb2RlbFN0YXRzXSA9IHVzZVN0YXRlKGZhbHNlKTtcblxuICAvLyBBZGQgYSBuZXcgc3RhdGUgdG8gdGhlIGhpc3RvcnlcbiAgY29uc3QgYWRkVG9IaXN0b3J5ID0gdXNlQ2FsbGJhY2soXG4gICAgKHN0YXRlKSA9PiB7XG4gICAgICBpZiAoaXNVbmRvUmVkb0FjdGlvbikge1xuICAgICAgICBzZXRJc1VuZG9SZWRvQWN0aW9uKGZhbHNlKTtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuXG4gICAgICBjb25zdCBuZXdTdGF0ZSA9IGNyZWF0ZUhpc3RvcnlTdGF0ZShcbiAgICAgICAgc2VsZWN0ZWRPYmplY3RzLFxuICAgICAgICBsaWdodHMsXG4gICAgICAgIGxpZ2h0UmVmcy5jdXJyZW50XG4gICAgICApO1xuICAgICAgY29uc3QgbmV3SGlzdG9yeSA9IGhpc3Rvcnkuc2xpY2UoMCwgaGlzdG9yeUluZGV4ICsgMSk7XG4gICAgICBzZXRIaXN0b3J5KFsuLi5uZXdIaXN0b3J5LCBuZXdTdGF0ZV0pO1xuICAgICAgc2V0SGlzdG9yeUluZGV4KGhpc3RvcnlJbmRleCArIDEpO1xuICAgIH0sXG4gICAgW2hpc3RvcnksIGhpc3RvcnlJbmRleCwgaXNVbmRvUmVkb0FjdGlvbiwgc2VsZWN0ZWRPYmplY3RzLCBsaWdodHNdXG4gICk7XG5cbiAgLy8gVW5kbyBhY3Rpb25cbiAgY29uc3QgdW5kb0FjdGlvbiA9IHVzZUNhbGxiYWNrKCgpID0+IHtcbiAgICBpZiAoaGlzdG9yeUluZGV4ID4gMCkge1xuICAgICAgc2V0SXNVbmRvUmVkb0FjdGlvbih0cnVlKTtcbiAgICAgIGNvbnN0IHByZXZTdGF0ZSA9IGhpc3RvcnlbaGlzdG9yeUluZGV4IC0gMV07XG4gICAgICBhcHBseUhpc3RvcnlTdGF0ZShwcmV2U3RhdGUsIHNjZW5lT2JqZWN0cywgbGlnaHRSZWZzLmN1cnJlbnQpO1xuICAgICAgc2V0SGlzdG9yeUluZGV4KGhpc3RvcnlJbmRleCAtIDEpO1xuICAgIH1cbiAgfSwgW2hpc3RvcnlJbmRleCwgaGlzdG9yeSwgc2NlbmVPYmplY3RzXSk7XG5cbiAgLy8gUmVkbyBhY3Rpb25cbiAgY29uc3QgcmVkb0FjdGlvbiA9IHVzZUNhbGxiYWNrKCgpID0+IHtcbiAgICBpZiAoaGlzdG9yeUluZGV4IDwgaGlzdG9yeS5sZW5ndGggLSAxKSB7XG4gICAgICBzZXRJc1VuZG9SZWRvQWN0aW9uKHRydWUpO1xuICAgICAgY29uc3QgbmV4dFN0YXRlID0gaGlzdG9yeVtoaXN0b3J5SW5kZXggKyAxXTtcbiAgICAgIGFwcGx5SGlzdG9yeVN0YXRlKG5leHRTdGF0ZSwgc2NlbmVPYmplY3RzLCBsaWdodFJlZnMuY3VycmVudCk7XG4gICAgICBzZXRIaXN0b3J5SW5kZXgoaGlzdG9yeUluZGV4ICsgMSk7XG4gICAgfVxuICB9LCBbaGlzdG9yeUluZGV4LCBoaXN0b3J5LCBzY2VuZU9iamVjdHNdKTtcblxuICAvLyBUcmFjayBjaGFuZ2VzIHRvIG9iamVjdHMgYW5kIGxpZ2h0cyBmb3IgaGlzdG9yeVxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IHRyYWNrQ2hhbmdlcyA9ICgpID0+IHtcbiAgICAgIGlmIChzZWxlY3RlZE9iamVjdHMubGVuZ3RoID4gMCB8fCBsaWdodHMubGVuZ3RoID4gMCkge1xuICAgICAgICBhZGRUb0hpc3Rvcnkoe1xuICAgICAgICAgIHNlbGVjdGVkT2JqZWN0cyxcbiAgICAgICAgICBsaWdodHMsXG4gICAgICAgIH0pO1xuICAgICAgfVxuICAgIH07XG5cbiAgICAvLyBEZWJvdW5jZSB0byBhdm9pZCB0b28gbWFueSBoaXN0b3J5IGVudHJpZXNcbiAgICBjb25zdCB0aW1lb3V0SWQgPSBzZXRUaW1lb3V0KHRyYWNrQ2hhbmdlcywgNTAwKTtcbiAgICByZXR1cm4gKCkgPT4gY2xlYXJUaW1lb3V0KHRpbWVvdXRJZCk7XG4gIH0sIFtzZWxlY3RlZE9iamVjdHMsIGxpZ2h0cywgYWRkVG9IaXN0b3J5XSk7XG5cbiAgY29uc3QgaGFuZGxlT2JqZWN0Q2xpY2sgPSB1c2VDYWxsYmFjayhcbiAgICAoZSkgPT4ge1xuICAgICAgZS5zdG9wUHJvcGFnYXRpb24oKTtcblxuICAgICAgLy8gSWYgcGxhY2luZyBkZWNhbCwgbGV0IHRoZSBkZWNhbCBwbGFjZW1lbnQgaGFuZGxlciBkbyBpdHMgam9iICh0cmlnZ2VyZWQgYnkgbW9kZWwncyBvbkNsaWNrKVxuICAgICAgaWYgKGlzUGxhY2luZ0RlY2FsKSB7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cblxuICAgICAgY29uc29sZS5sb2coXCJPYmplY3QgY2xpY2tlZDpcIiwgZS5vYmplY3QubmFtZSwgZS5vYmplY3QpO1xuXG4gICAgICBpZiAoZS5zaGlmdEtleSkge1xuICAgICAgICBzZXRTZWxlY3RlZE9iamVjdHMoKHByZXYpID0+IHtcbiAgICAgICAgICAvLyBFbnN1cmUgcHJldiBpcyBhbHdheXMgYW4gYXJyYXlcbiAgICAgICAgICBjb25zdCBwcmV2QXJyYXkgPSBBcnJheS5pc0FycmF5KHByZXYpID8gcHJldiA6IFtwcmV2XS5maWx0ZXIoQm9vbGVhbik7XG5cbiAgICAgICAgICBpZiAocHJldkFycmF5LmluY2x1ZGVzKGUub2JqZWN0KSkge1xuICAgICAgICAgICAgcmV0dXJuIHByZXZBcnJheS5maWx0ZXIoKG9iaikgPT4gb2JqICE9PSBlLm9iamVjdCk7XG4gICAgICAgICAgfVxuICAgICAgICAgIHJldHVybiBbLi4ucHJldkFycmF5LCBlLm9iamVjdF07XG4gICAgICAgIH0pO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgc2V0U2VsZWN0ZWRPYmplY3RzKFtlLm9iamVjdF0pO1xuICAgICAgfVxuICAgIH0sXG4gICAgW2lzUGxhY2luZ0RlY2FsLCBzZXRTZWxlY3RlZE9iamVjdHNdXG4gICk7XG5cbiAgLy8gQWRkIGRlYnVnZ2luZyBmb3Igc2VsZWN0ZWRPYmplY3RzIGNoYW5nZXNcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zb2xlLmxvZyhcIkV4cGVyaWVuY2VOZXcgLSBzZWxlY3RlZE9iamVjdHMgY2hhbmdlZDpcIiwgc2VsZWN0ZWRPYmplY3RzKTtcbiAgICBjb25zb2xlLmxvZyhcbiAgICAgIFwiRXhwZXJpZW5jZU5ldyAtIHNlbGVjdGVkT2JqZWN0cyBsZW5ndGg6XCIsXG4gICAgICBzZWxlY3RlZE9iamVjdHM/Lmxlbmd0aFxuICAgICk7XG4gIH0sIFtzZWxlY3RlZE9iamVjdHNdKTtcblxuICAvLyBBZGQgZGVidWdnaW5nIGZvciBzY2VuZU9iamVjdHMgY2hhbmdlc1xuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnNvbGUubG9nKFwiRXhwZXJpZW5jZU5ldyAtIHNjZW5lT2JqZWN0cyBjaGFuZ2VkOlwiLCBzY2VuZU9iamVjdHMpO1xuICAgIGNvbnNvbGUubG9nKFwiRXhwZXJpZW5jZU5ldyAtIHNjZW5lT2JqZWN0cyBsZW5ndGg6XCIsIHNjZW5lT2JqZWN0cz8ubGVuZ3RoKTtcbiAgICBjb25zb2xlLmxvZyhcbiAgICAgIFwiRXhwZXJpZW5jZU5ldyAtIHNjZW5lT2JqZWN0cyBkZXRhaWxzOlwiLFxuICAgICAgc2NlbmVPYmplY3RzPy5tYXAoKG9iaikgPT4gKHtcbiAgICAgICAgbmFtZTogb2JqPy5uYW1lLFxuICAgICAgICB0eXBlOiBvYmo/LnR5cGUsXG4gICAgICAgIHVzZXJEYXRhOiBvYmo/LnVzZXJEYXRhLFxuICAgICAgfSkpXG4gICAgKTtcbiAgfSwgW3NjZW5lT2JqZWN0c10pO1xuXG4gIGNvbnN0IGhhbmRsZVBvaW50ZXJNaXNzZWQgPSB1c2VDYWxsYmFjaygoKSA9PiB7XG4gICAgLy8gSWYgaW4gZGVjYWwgcGxhY2VtZW50IG1vZGUsIGEgXCJtaXNzZWRcIiBjbGljayAob24gdGhlIGNhbnZhcyBiYWNrZ3JvdW5kKVxuICAgIC8vIHNob3VsZCBwcm9iYWJseSBjYW5jZWwgcGxhY2VtZW50IG1vZGUsIG9yIGRvIG5vdGhpbmcgdG8gYWxsb3cgbW9kZWwgY2xpY2stdGhyb3VnaC5cbiAgICAvLyBGb3Igbm93LCBsZXQncyBwcmV2ZW50IGl0IGZyb20gZGVzZWxlY3Rpbmcgb2JqZWN0cyBpZiBwbGFjaW5nIGRlY2FsLlxuICAgIGlmIChpc1BsYWNpbmdEZWNhbCkge1xuICAgICAgLy8gc2V0SXNQbGFjaW5nRGVjYWwoZmFsc2UpOyAvLyBPcHRpb25hbGx5LCBjYW5jZWwgcGxhY2VtZW50IG9uIG1pc3NlZCBjbGlja1xuICAgICAgcmV0dXJuO1xuICAgIH1cbiAgICBzZXRTZWxlY3RlZE9iamVjdHMoW10pO1xuICAgIHNldFNlbGVjdGVkTGlnaHQobnVsbCk7XG4gICAgLy8gRG8gbm90IGNsb3NlIHRoZSByaWdodFBhbmVsVGFiIGhlcmUsIGFzIHRoYXQncyB1c3VhbGx5IGhhbmRsZWQgYnkgY2xpY2tpbmcgb3V0c2lkZSB0aGUgcGFuZWwgaXRzZWxmLlxuICB9LCBbaXNQbGFjaW5nRGVjYWwsIHNldElzUGxhY2luZ0RlY2FsXSk7XG5cbiAgLy8gQWRkIHRoZSBzZWxlY3Rpb24gZ3JvdXAgdG8gdGhlIHNjZW5lXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgLy8gVGhpcyBlZmZlY3QgcnVucyBvbmNlIHRvIGFkZCB0aGUgc2VsZWN0aW9uIGdyb3VwIHRvIHRoZSBzY2VuZVxuICAgIHJldHVybiAoKSA9PiB7XG4gICAgICAvLyBDbGVhbiB1cCBvbiB1bm1vdW50XG4gICAgICBpZiAoc2VsZWN0aW9uR3JvdXBSZWYuY3VycmVudCAmJiBzZWxlY3Rpb25Hcm91cFJlZi5jdXJyZW50LnBhcmVudCkge1xuICAgICAgICBzZWxlY3Rpb25Hcm91cFJlZi5jdXJyZW50LnBhcmVudC5yZW1vdmUoc2VsZWN0aW9uR3JvdXBSZWYuY3VycmVudCk7XG4gICAgICB9XG4gICAgfTtcbiAgfSwgW10pO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgLy8gRW5zdXJlIHNlbGVjdGVkT2JqZWN0cyBpcyBhbHdheXMgYW4gYXJyYXlcbiAgICBjb25zdCBvYmplY3RzQXJyYXkgPSBBcnJheS5pc0FycmF5KHNlbGVjdGVkT2JqZWN0cylcbiAgICAgID8gc2VsZWN0ZWRPYmplY3RzXG4gICAgICA6IFtzZWxlY3RlZE9iamVjdHNdLmZpbHRlcihCb29sZWFuKTtcblxuICAgIHdoaWxlIChzZWxlY3Rpb25Hcm91cFJlZi5jdXJyZW50LmNoaWxkcmVuLmxlbmd0aCA+IDApIHtcbiAgICAgIHNlbGVjdGlvbkdyb3VwUmVmLmN1cnJlbnQucmVtb3ZlKHNlbGVjdGlvbkdyb3VwUmVmLmN1cnJlbnQuY2hpbGRyZW5bMF0pO1xuICAgIH1cblxuICAgIGlmIChvYmplY3RzQXJyYXkubGVuZ3RoID4gMCkge1xuICAgICAgY29uc3QgY2VudGVyID0gbmV3IFRIUkVFLlZlY3RvcjMoKTtcbiAgICAgIG9iamVjdHNBcnJheS5mb3JFYWNoKChvYmopID0+IHtcbiAgICAgICAgY2VudGVyLmFkZChvYmoucG9zaXRpb24pO1xuICAgICAgfSk7XG4gICAgICBjZW50ZXIuZGl2aWRlU2NhbGFyKG9iamVjdHNBcnJheS5sZW5ndGgpO1xuXG4gICAgICBzZWxlY3Rpb25Hcm91cFJlZi5jdXJyZW50LnBvc2l0aW9uLmNvcHkoY2VudGVyKTtcblxuICAgICAgb2JqZWN0c0FycmF5LmZvckVhY2goKG9iaikgPT4ge1xuICAgICAgICBjb25zdCBjbG9uZSA9IG9iai5jbG9uZSgpO1xuICAgICAgICAvLyBNYWtlIHRoZSBjbG9uZSBpbnZpc2libGUgd2hpbGUga2VlcGluZyBpdHMgZ2VvbWV0cnkgZm9yIHRyYW5zZm9ybXNcbiAgICAgICAgY2xvbmUudHJhdmVyc2UoKGNoaWxkKSA9PiB7XG4gICAgICAgICAgaWYgKGNoaWxkLmlzTWVzaCkge1xuICAgICAgICAgICAgY2hpbGQudmlzaWJsZSA9IGZhbHNlO1xuICAgICAgICAgIH1cbiAgICAgICAgfSk7XG4gICAgICAgIGNsb25lLnBvc2l0aW9uLnN1YihjZW50ZXIpO1xuICAgICAgICBzZWxlY3Rpb25Hcm91cFJlZi5jdXJyZW50LmFkZChjbG9uZSk7XG4gICAgICB9KTtcbiAgICB9XG4gIH0sIFtzZWxlY3RlZE9iamVjdHNdKTtcblxuICBjb25zdCB1cGRhdGVPYmplY3RzRnJvbUdyb3VwID0gdXNlQ2FsbGJhY2soKCkgPT4ge1xuICAgIC8vIEVuc3VyZSBzZWxlY3RlZE9iamVjdHMgaXMgYWx3YXlzIGFuIGFycmF5XG4gICAgY29uc3Qgb2JqZWN0c0FycmF5ID0gQXJyYXkuaXNBcnJheShzZWxlY3RlZE9iamVjdHMpXG4gICAgICA/IHNlbGVjdGVkT2JqZWN0c1xuICAgICAgOiBbc2VsZWN0ZWRPYmplY3RzXS5maWx0ZXIoQm9vbGVhbik7XG5cbiAgICBpZiAob2JqZWN0c0FycmF5Lmxlbmd0aCA+IDAgJiYgc2VsZWN0aW9uR3JvdXBSZWYuY3VycmVudCkge1xuICAgICAgb2JqZWN0c0FycmF5LmZvckVhY2goKG9iaiwgaW5kZXgpID0+IHtcbiAgICAgICAgaWYgKHNlbGVjdGlvbkdyb3VwUmVmLmN1cnJlbnQuY2hpbGRyZW5baW5kZXhdKSB7XG4gICAgICAgICAgY29uc3Qgd29ybGRQb3MgPSBuZXcgVEhSRUUuVmVjdG9yMygpO1xuICAgICAgICAgIHNlbGVjdGlvbkdyb3VwUmVmLmN1cnJlbnQuY2hpbGRyZW5baW5kZXhdLmdldFdvcmxkUG9zaXRpb24od29ybGRQb3MpO1xuICAgICAgICAgIG9iai5wb3NpdGlvbi5jb3B5KHdvcmxkUG9zKTtcblxuICAgICAgICAgIG9iai5yb3RhdGlvbi5jb3B5KHNlbGVjdGlvbkdyb3VwUmVmLmN1cnJlbnQucm90YXRpb24pO1xuICAgICAgICAgIG9iai5zY2FsZS5jb3B5KHNlbGVjdGlvbkdyb3VwUmVmLmN1cnJlbnQuc2NhbGUpO1xuICAgICAgICB9XG4gICAgICB9KTtcbiAgICB9XG4gIH0sIFtzZWxlY3RlZE9iamVjdHNdKTtcblxuICBjb25zdCBoYW5kbGVTY2VuZVVwZGF0ZSA9IHVzZUNhbGxiYWNrKChzY2VuZSkgPT4ge1xuICAgIGNvbnNvbGUubG9nKFwiaGFuZGxlU2NlbmVVcGRhdGUgY2FsbGVkIHdpdGggc2NlbmU6XCIsIHNjZW5lKTtcbiAgICBjb25zb2xlLmxvZyhcIlNjZW5lIHR5cGU6XCIsIHNjZW5lLnR5cGUpO1xuICAgIGNvbnNvbGUubG9nKFwiU2NlbmUgY2hpbGRyZW4gY291bnQ6XCIsIHNjZW5lLmNoaWxkcmVuPy5sZW5ndGgpO1xuXG4gICAgc2V0U2NlbmVPYmplY3RzKFtdKTtcbiAgICBzZXRTZWxlY3RlZE9iamVjdHMoW10pO1xuXG4gICAgY29uc3Qgc3RhdHMgPSBjYWxjdWxhdGVNb2RlbFN0YXRzKHNjZW5lKTtcbiAgICBzZXRNb2RlbFN0YXRzKHN0YXRzKTtcbiAgICBzZXRTaGFkb3dzT25Nb2RlbChzY2VuZSk7XG5cbiAgICBjb25zdCBvYmplY3RzID0gW107XG4gICAgY29uc3QgaW5pdGlhbE9iamVjdHMgPSBbXTtcblxuICAgIHNjZW5lLnRyYXZlcnNlKChvYmplY3QpID0+IHtcbiAgICAgIGNvbnNvbGUubG9nKFxuICAgICAgICBcIlRyYXZlcnNpbmcgb2JqZWN0OlwiLFxuICAgICAgICBvYmplY3QubmFtZSxcbiAgICAgICAgb2JqZWN0LnR5cGUsXG4gICAgICAgIFwiaXNNZXNoOlwiLFxuICAgICAgICBvYmplY3QuaXNNZXNoLFxuICAgICAgICBcInVzZXJEYXRhOlwiLFxuICAgICAgICBvYmplY3QudXNlckRhdGFcbiAgICAgICk7XG5cbiAgICAgIGlmIChvYmplY3QuaXNNZXNoKSB7XG4gICAgICAgIGlmICghb2JqZWN0Lm5hbWUpIHtcbiAgICAgICAgICBvYmplY3QubmFtZSA9IGBNZXNoXyR7b2JqZWN0LmlkfWA7XG4gICAgICAgIH1cblxuICAgICAgICBjb25zb2xlLmxvZyhcbiAgICAgICAgICBcIkFkZGluZyBtZXNoIHRvIG9iamVjdHM6XCIsXG4gICAgICAgICAgb2JqZWN0Lm5hbWUsXG4gICAgICAgICAgXCJ1c2VyRGF0YTpcIixcbiAgICAgICAgICBvYmplY3QudXNlckRhdGFcbiAgICAgICAgKTtcbiAgICAgICAgb2JqZWN0cy5wdXNoKG9iamVjdCk7XG5cbiAgICAgICAgaW5pdGlhbE9iamVjdHMucHVzaCh7XG4gICAgICAgICAgaWQ6IG9iamVjdC51dWlkLFxuICAgICAgICAgIG5hbWU6IG9iamVjdC5uYW1lLFxuICAgICAgICAgIHBvc2l0aW9uOiBvYmplY3QucG9zaXRpb24uY2xvbmUoKSxcbiAgICAgICAgICByb3RhdGlvbjogb2JqZWN0LnJvdGF0aW9uLmNsb25lKCksXG4gICAgICAgICAgc2NhbGU6IG9iamVjdC5zY2FsZS5jbG9uZSgpLFxuICAgICAgICAgIG1hdGVyaWFsOiBvYmplY3QubWF0ZXJpYWxcbiAgICAgICAgICAgID8ge1xuICAgICAgICAgICAgICAgIGNvbG9yOiBvYmplY3QubWF0ZXJpYWwuY29sb3JcbiAgICAgICAgICAgICAgICAgID8gb2JqZWN0Lm1hdGVyaWFsLmNvbG9yLmdldEhleCgpXG4gICAgICAgICAgICAgICAgICA6IDB4ZmZmZmZmLFxuICAgICAgICAgICAgICAgIHJvdWdobmVzczogb2JqZWN0Lm1hdGVyaWFsLnJvdWdobmVzcyxcbiAgICAgICAgICAgICAgICBtZXRhbG5lc3M6IG9iamVjdC5tYXRlcmlhbC5tZXRhbG5lc3MsXG4gICAgICAgICAgICAgICAgd2lyZWZyYW1lOiBvYmplY3QubWF0ZXJpYWwud2lyZWZyYW1lLFxuICAgICAgICAgICAgICAgIHRyYW5zcGFyZW50OiBvYmplY3QubWF0ZXJpYWwudHJhbnNwYXJlbnQsXG4gICAgICAgICAgICAgICAgb3BhY2l0eTogb2JqZWN0Lm1hdGVyaWFsLm9wYWNpdHksXG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIDogbnVsbCxcbiAgICAgICAgfSk7XG4gICAgICB9XG4gICAgfSk7XG5cbiAgICBjb25zb2xlLmxvZyhcbiAgICAgIFwiRm91bmRcIixcbiAgICAgIG9iamVjdHMubGVuZ3RoLFxuICAgICAgXCJtZXNoIG9iamVjdHM6XCIsXG4gICAgICBvYmplY3RzLm1hcCgob2JqKSA9PiAoeyBuYW1lOiBvYmoubmFtZSwgdXNlckRhdGE6IG9iai51c2VyRGF0YSB9KSlcbiAgICApO1xuXG4gICAgbW9kZWxJbml0aWFsU3RhdGVSZWYuY3VycmVudC5vYmplY3RzID0gaW5pdGlhbE9iamVjdHM7XG4gICAgc2V0U2NlbmVPYmplY3RzKG9iamVjdHMpO1xuICB9LCBbXSk7XG5cbiAgLy8gQWRkIHRoaXMgZWZmZWN0IHRvIGRldGVjdCBtb2RlbCBjaGFuZ2VzXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKHNlbGVjdGVkTW9kZWwgIT09IGxhc3RTZWxlY3RlZE1vZGVsKSB7XG4gICAgICAvLyBDbGVhciB0aGUgc2NlbmUgb2JqZWN0cyB3aGVuIG1vZGVsIGNoYW5nZXNcbiAgICAgIHNldFNjZW5lT2JqZWN0cyhbXSk7XG4gICAgICBzZXRTZWxlY3RlZE9iamVjdHMoW10pO1xuICAgICAgc2V0TGFzdFNlbGVjdGVkTW9kZWwoc2VsZWN0ZWRNb2RlbCk7XG4gICAgfVxuICB9LCBbc2VsZWN0ZWRNb2RlbCwgbGFzdFNlbGVjdGVkTW9kZWxdKTtcblxuICBjb25zdCBoYW5kbGVMaWdodE1vdmVkID0gdXNlQ2FsbGJhY2soKGxpZ2h0SWQsIG5ld1Bvc2l0aW9uKSA9PiB7XG4gICAgc2V0TGlnaHRQb3NpdGlvbkhpc3RvcnkoKHByZXYpID0+ICh7XG4gICAgICAuLi5wcmV2LFxuICAgICAgW2xpZ2h0SWRdOiBbLi4uKHByZXZbbGlnaHRJZF0gfHwgW10pLCBuZXdQb3NpdGlvbl0sXG4gICAgfSkpO1xuICB9LCBbXSk7XG5cbiAgY29uc3QgdW5kb0xpZ2h0TW92ZSA9IHVzZUNhbGxiYWNrKChsaWdodElkKSA9PiB7XG4gICAgc2V0TGlnaHRQb3NpdGlvbkhpc3RvcnkoKHByZXYpID0+IHtcbiAgICAgIGNvbnN0IGhpc3RvcnkgPSBwcmV2W2xpZ2h0SWRdIHx8IFtdO1xuICAgICAgaWYgKGhpc3RvcnkubGVuZ3RoIDw9IDEpIHJldHVybiBwcmV2O1xuXG4gICAgICBjb25zdCBuZXdIaXN0b3J5ID0gaGlzdG9yeS5zbGljZSgwLCAtMSk7XG4gICAgICBjb25zdCBsYXN0UG9zaXRpb24gPSBuZXdIaXN0b3J5W25ld0hpc3RvcnkubGVuZ3RoIC0gMV07XG5cbiAgICAgIGlmIChsaWdodFJlZnMuY3VycmVudFtsaWdodElkXSkge1xuICAgICAgICBsaWdodFJlZnMuY3VycmVudFtsaWdodElkXS5wb3NpdGlvbi5jb3B5KGxhc3RQb3NpdGlvbik7XG4gICAgICB9XG5cbiAgICAgIHJldHVybiB7XG4gICAgICAgIC4uLnByZXYsXG4gICAgICAgIFtsaWdodElkXTogbmV3SGlzdG9yeSxcbiAgICAgIH07XG4gICAgfSk7XG4gIH0sIFtdKTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IGhhbmRsZUtleURvd24gPSAoZXZlbnQpID0+IHtcbiAgICAgIC8vIEhhbmRsZSB1bmRvL3JlZG8ga2V5Ym9hcmQgc2hvcnRjdXRzXG4gICAgICBpZiAoZXZlbnQuY3RybEtleSkge1xuICAgICAgICBpZiAoZXZlbnQua2V5ID09PSBcInpcIikge1xuICAgICAgICAgIGV2ZW50LnByZXZlbnREZWZhdWx0KCk7XG4gICAgICAgICAgdW5kb0FjdGlvbigpO1xuICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgICBpZiAoZXZlbnQua2V5ID09PSBcInlcIikge1xuICAgICAgICAgIGV2ZW50LnByZXZlbnREZWZhdWx0KCk7XG4gICAgICAgICAgcmVkb0FjdGlvbigpO1xuICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICAvLyBIYW5kbGUgRXNjYXBlIGtleSBmb3IgZGVjYWwgcGxhY2VtZW50XG4gICAgICBpZiAoZXZlbnQua2V5ID09PSBcIkVzY2FwZVwiICYmIGlzUGxhY2luZ0RlY2FsKSB7XG4gICAgICAgIHNldElzUGxhY2luZ0RlY2FsKGZhbHNlKTtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuXG4gICAgICAvLyBIYW5kbGUgbGlnaHQgdW5kb1xuICAgICAgaWYgKGV2ZW50LmN0cmxLZXkgJiYgZXZlbnQua2V5ID09PSBcInpcIiAmJiBzZWxlY3RlZExpZ2h0KSB7XG4gICAgICAgIHVuZG9MaWdodE1vdmUoc2VsZWN0ZWRMaWdodCk7XG4gICAgICB9XG5cbiAgICAgIC8vIEhhbmRsZSB0cmFuc2Zvcm0gbW9kZSBzaG9ydGN1dHNcbiAgICAgIGlmIChzZWxlY3RlZE9iamVjdHMubGVuZ3RoID4gMCkge1xuICAgICAgICBzd2l0Y2ggKGV2ZW50LmtleSkge1xuICAgICAgICAgIGNhc2UgXCJnXCI6XG4gICAgICAgICAgICBzZXRUcmFuc2Zvcm1Nb2RlKFwidHJhbnNsYXRlXCIpO1xuICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgY2FzZSBcInJcIjpcbiAgICAgICAgICAgIHNldFRyYW5zZm9ybU1vZGUoXCJyb3RhdGVcIik7XG4gICAgICAgICAgICBicmVhaztcbiAgICAgICAgICBjYXNlIFwic1wiOlxuICAgICAgICAgICAgc2V0VHJhbnNmb3JtTW9kZShcInNjYWxlXCIpO1xuICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgY2FzZSBcIkVzY2FwZVwiOlxuICAgICAgICAgICAgc2V0U2VsZWN0ZWRPYmplY3RzKFtdKTtcbiAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIC8vIEhhbmRsZSBzZWxlY3QgYWxsXG4gICAgICBpZiAoZXZlbnQuY3RybEtleSAmJiBldmVudC5rZXkgPT09IFwiYVwiKSB7XG4gICAgICAgIGV2ZW50LnByZXZlbnREZWZhdWx0KCk7XG4gICAgICAgIGlmIChzY2VuZU9iamVjdHMubGVuZ3RoID4gMCkge1xuICAgICAgICAgIHNldFNlbGVjdGVkT2JqZWN0cyhbLi4uc2NlbmVPYmplY3RzXSk7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9O1xuXG4gICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoXCJrZXlkb3duXCIsIGhhbmRsZUtleURvd24pO1xuICAgIHJldHVybiAoKSA9PiB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcihcImtleWRvd25cIiwgaGFuZGxlS2V5RG93bik7XG4gIH0sIFtcbiAgICBzZWxlY3RlZExpZ2h0LFxuICAgIHVuZG9MaWdodE1vdmUsXG4gICAgc2NlbmVPYmplY3RzLFxuICAgIHNlbGVjdGVkT2JqZWN0cy5sZW5ndGgsXG4gICAgdW5kb0FjdGlvbixcbiAgICByZWRvQWN0aW9uLFxuICAgIGlzUGxhY2luZ0RlY2FsLFxuICAgIHNldElzUGxhY2luZ0RlY2FsLFxuICBdKTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIHJldHVybiAoKSA9PiB7XG4gICAgICBpZiAodXBsb2FkZWRNb2RlbCkge1xuICAgICAgICBVUkwucmV2b2tlT2JqZWN0VVJMKHVwbG9hZGVkTW9kZWwpO1xuICAgICAgfVxuICAgICAgaWYgKGN1c3RvbUhkcmkpIHtcbiAgICAgICAgVVJMLnJldm9rZU9iamVjdFVSTChjdXN0b21IZHJpKTtcbiAgICAgIH1cbiAgICB9O1xuICB9LCBbdXBsb2FkZWRNb2RlbCwgY3VzdG9tSGRyaV0pO1xuXG4gIGNvbnN0IGhhbmRsZUNhbWVyYVZpZXcgPSB1c2VDYWxsYmFjaygodmlldykgPT4ge1xuICAgIHNldENhbWVyYVZpZXcob3JiaXRDb250cm9sc1JlZi5jdXJyZW50LCB2aWV3KTtcbiAgfSwgW10pO1xuXG4gIC8vIEFkZCB0aGUgcGFuZWxSZWYgdG8gRXhwZXJpZW5jZU5ldyBpbnN0ZWFkIG9mIGluIFRvb2xiYXJcbiAgY29uc3QgcGFuZWxSZWYgPSB1c2VSZWYobnVsbCk7XG5cbiAgY29uc3QgW2FjdGl2ZUFuaW1hdGlvbiwgc2V0QWN0aXZlQW5pbWF0aW9uXSA9IHVzZVN0YXRlKG51bGwpO1xuICBjb25zdCBbYW5pbWF0aW9uU3RhcnRUaW1lLCBzZXRBbmltYXRpb25TdGFydFRpbWVdID0gdXNlU3RhdGUoMCk7XG4gIGNvbnN0IHJlcXVlc3RSZWYgPSB1c2VSZWYoKTtcblxuICAvLyBBbmltYXRpb24gbG9vcCBoYW5kbGVyXG4gIGNvbnN0IGFuaW1hdGVDYW1lcmEgPSB1c2VDYWxsYmFjayhcbiAgICAodGltZXN0YW1wKSA9PiB7XG4gICAgICBpZiAoIWFjdGl2ZUFuaW1hdGlvbiB8fCAhb3JiaXRDb250cm9sc1JlZi5jdXJyZW50KSB7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cblxuICAgICAgLy8gQ2FsY3VsYXRlIGFuaW1hdGlvbiBwcm9ncmVzcyAoMCB0byAxKVxuICAgICAgY29uc3QgYW5pbWF0aW9uID0gcHJlZGVmaW5lZEFuaW1hdGlvbnMuZmluZChcbiAgICAgICAgKGEpID0+IGEuaWQgPT09IGFjdGl2ZUFuaW1hdGlvblxuICAgICAgKTtcbiAgICAgIGlmICghYW5pbWF0aW9uKSByZXR1cm47XG5cbiAgICAgIGlmIChhbmltYXRpb25TdGFydFRpbWUgPT09IDApIHtcbiAgICAgICAgc2V0QW5pbWF0aW9uU3RhcnRUaW1lKHRpbWVzdGFtcCk7XG4gICAgICB9XG5cbiAgICAgIGNvbnN0IGVsYXBzZWQgPSB0aW1lc3RhbXAgLSBhbmltYXRpb25TdGFydFRpbWU7XG4gICAgICBjb25zdCBwcm9ncmVzcyA9IChlbGFwc2VkICUgYW5pbWF0aW9uLmR1cmF0aW9uKSAvIGFuaW1hdGlvbi5kdXJhdGlvbjtcblxuICAgICAgLy8gR2V0IHRoZSB0YXJnZXQgcG9zaXRpb24gKGNlbnRlciBvZiBzZWxlY3RlZCBvYmplY3RzIG9yIHNjZW5lIGNlbnRlcilcbiAgICAgIGNvbnN0IHRhcmdldCA9IG5ldyBUSFJFRS5WZWN0b3IzKDAsIDAsIDApO1xuICAgICAgaWYgKHNlbGVjdGVkT2JqZWN0cy5sZW5ndGggPiAwKSB7XG4gICAgICAgIC8vIENhbGN1bGF0ZSBjZW50ZXIgb2Ygc2VsZWN0ZWQgb2JqZWN0c1xuICAgICAgICBjb25zdCBvYmplY3RzQXJyYXkgPSBBcnJheS5pc0FycmF5KHNlbGVjdGVkT2JqZWN0cylcbiAgICAgICAgICA/IHNlbGVjdGVkT2JqZWN0c1xuICAgICAgICAgIDogW3NlbGVjdGVkT2JqZWN0c10uZmlsdGVyKEJvb2xlYW4pO1xuXG4gICAgICAgIGlmIChvYmplY3RzQXJyYXkubGVuZ3RoID4gMCkge1xuICAgICAgICAgIGNvbnN0IGNlbnRlciA9IG5ldyBUSFJFRS5WZWN0b3IzKCk7XG4gICAgICAgICAgb2JqZWN0c0FycmF5LmZvckVhY2goKG9iaikgPT4ge1xuICAgICAgICAgICAgY2VudGVyLmFkZChvYmoucG9zaXRpb24pO1xuICAgICAgICAgIH0pO1xuICAgICAgICAgIGNlbnRlci5kaXZpZGVTY2FsYXIob2JqZWN0c0FycmF5Lmxlbmd0aCk7XG4gICAgICAgICAgdGFyZ2V0LmNvcHkoY2VudGVyKTtcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICAvLyBBbmltYXRlIGNhbWVyYSB1c2luZyB0aGUgYW5pbWF0aW9uIGZ1bmN0aW9uXG4gICAgICBhbmltYXRpb24uYW5pbWF0ZShcbiAgICAgICAgb3JiaXRDb250cm9sc1JlZi5jdXJyZW50Lm9iamVjdCxcbiAgICAgICAgb3JiaXRDb250cm9sc1JlZi5jdXJyZW50LFxuICAgICAgICBwcm9ncmVzcyxcbiAgICAgICAgdGFyZ2V0XG4gICAgICApO1xuXG4gICAgICByZXF1ZXN0UmVmLmN1cnJlbnQgPSByZXF1ZXN0QW5pbWF0aW9uRnJhbWUoYW5pbWF0ZUNhbWVyYSk7XG4gICAgfSxcbiAgICBbYWN0aXZlQW5pbWF0aW9uLCBvcmJpdENvbnRyb2xzUmVmLCBhbmltYXRpb25TdGFydFRpbWUsIHNlbGVjdGVkT2JqZWN0c11cbiAgKTtcblxuICAvLyBTZXR1cCBhbmltYXRpb24gbG9vcFxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmIChhY3RpdmVBbmltYXRpb24pIHtcbiAgICAgIHNldEFuaW1hdGlvblN0YXJ0VGltZSgwKTtcbiAgICAgIHJlcXVlc3RSZWYuY3VycmVudCA9IHJlcXVlc3RBbmltYXRpb25GcmFtZShhbmltYXRlQ2FtZXJhKTtcblxuICAgICAgLy8gRGlzYWJsZSBvcmJpdCBjb250cm9scyBkdXJpbmcgYW5pbWF0aW9uXG4gICAgICBpZiAob3JiaXRDb250cm9sc1JlZi5jdXJyZW50KSB7XG4gICAgICAgIG9yYml0Q29udHJvbHNSZWYuY3VycmVudC5lbmFibGVkID0gZmFsc2U7XG4gICAgICB9XG4gICAgfSBlbHNlIHtcbiAgICAgIC8vIFJlLWVuYWJsZSBvcmJpdCBjb250cm9scyB3aGVuIGFuaW1hdGlvbiBzdG9wc1xuICAgICAgaWYgKG9yYml0Q29udHJvbHNSZWYuY3VycmVudCAmJiBzZWxlY3RlZE9iamVjdHMubGVuZ3RoID09PSAwKSB7XG4gICAgICAgIG9yYml0Q29udHJvbHNSZWYuY3VycmVudC5lbmFibGVkID0gdHJ1ZTtcbiAgICAgIH1cbiAgICB9XG5cbiAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgaWYgKHJlcXVlc3RSZWYuY3VycmVudCkge1xuICAgICAgICBjYW5jZWxBbmltYXRpb25GcmFtZShyZXF1ZXN0UmVmLmN1cnJlbnQpO1xuICAgICAgfVxuICAgIH07XG4gIH0sIFthY3RpdmVBbmltYXRpb24sIGFuaW1hdGVDYW1lcmEsIHNlbGVjdGVkT2JqZWN0c10pO1xuXG4gIC8vIEhhbmRsZSBhbmltYXRpb24gcGxheWJhY2sgZnJvbSBPdXRsaW5lclxuICBjb25zdCBoYW5kbGVQbGF5QW5pbWF0aW9uID0gdXNlQ2FsbGJhY2soKGFuaW1hdGlvbklkKSA9PiB7XG4gICAgc2V0QWN0aXZlQW5pbWF0aW9uKGFuaW1hdGlvbklkKTtcbiAgfSwgW10pO1xuXG4gIGNvbnN0IGdldEVudmlyb25tZW50UHJlc2V0ID0gKCkgPT4ge1xuICAgIHJldHVybiBlbnZQcmVzZXQ7XG4gIH07XG5cbiAgLy8gUmVzZXQgZnVuY3Rpb25cbiAgY29uc3QgaGFuZGxlUmVzZXQgPSB1c2VDYWxsYmFjaygoKSA9PiB7XG4gICAgLy8gQ2hlY2sgaWYgdGhlcmUncyBhIHNhdmVkIHNjZW5lIGluIGxvY2FsU3RvcmFnZVxuICAgIGNvbnN0IHNhdmVkU2NlbmUgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbShcImFnYXBlX3NhdmVkX3NjZW5lXCIpO1xuICAgIGlmIChzYXZlZFNjZW5lKSB7XG4gICAgICBjb25zdCBjb25maXJtUmVzZXQgPSB3aW5kb3cuY29uZmlybShcbiAgICAgICAgXCJUaGVyZSBpcyBhIHNhdmVkIHNjZW5lIGluIHlvdXIgYnJvd3NlciBzdG9yYWdlLiBSZXNldHRpbmcgd2lsbCBjbGVhciB0aGlzIHNhdmVkIHNjZW5lLiBEbyB5b3Ugd2FudCB0byBjb250aW51ZT9cIlxuICAgICAgKTtcbiAgICAgIGlmICghY29uZmlybVJlc2V0KSB7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cbiAgICAgIC8vIENsZWFyIHRoZSBzYXZlZCBzY2VuZSBmcm9tIGxvY2FsU3RvcmFnZVxuICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oXCJhZ2FwZV9zYXZlZF9zY2VuZVwiKTtcbiAgICB9XG5cbiAgICBjb25zb2xlLmxvZyhcIlJlc2V0dGluZyBzY2VuZSB0byBpbml0aWFsIHN0YXRlXCIpO1xuICAgIHNldFNjZW5lT2JqZWN0cyhbXSk7XG4gICAgc2V0U2VsZWN0ZWRPYmplY3RzKFtdKTtcblxuICAgIC8vIFJlc2V0IGxpZ2h0cyB0byBkZWZhdWx0IGNvbmZpZ3VyYXRpb25cbiAgICBzZXRMaWdodHMoW1xuICAgICAge1xuICAgICAgICBpZDogXCJkZWZhdWx0RGlyZWN0aW9uYWxcIixcbiAgICAgICAgdHlwZTogXCJkaXJlY3Rpb25hbFwiLFxuICAgICAgICBwb3NpdGlvbjogWzIuNSwgNS41LCA1XSxcbiAgICAgICAgaW50ZW5zaXR5OiAxLjAsXG4gICAgICAgIGNvbG9yOiBcIiNmZmZmZmZcIixcbiAgICAgICAgbmFtZTogXCJNYWluIERpcmVjdGlvbmFsIExpZ2h0XCIsXG4gICAgICAgIGNhc3RTaGFkb3c6IHRydWUsXG4gICAgICAgIGhlbHBlclZpc2libGU6IGZhbHNlLFxuICAgICAgfSxcbiAgICAgIHtcbiAgICAgICAgaWQ6IFwia2V5TGlnaHRcIixcbiAgICAgICAgdHlwZTogXCJwb2ludFwiLFxuICAgICAgICBwb3NpdGlvbjogWzUsIDUsIDVdLFxuICAgICAgICBpbnRlbnNpdHk6IDguMCxcbiAgICAgICAgY29sb3I6IFwiI2ZmZmZmZlwiLFxuICAgICAgICBuYW1lOiBcIktleSBMaWdodFwiLFxuICAgICAgfSxcbiAgICAgIHtcbiAgICAgICAgaWQ6IFwiZmlsbExpZ2h0XCIsXG4gICAgICAgIHR5cGU6IFwicG9pbnRcIixcbiAgICAgICAgcG9zaXRpb246IFstNSwgNSwgLTVdLFxuICAgICAgICBpbnRlbnNpdHk6IDQuMCxcbiAgICAgICAgY29sb3I6IFwiI2ZmZmZmZlwiLFxuICAgICAgICBuYW1lOiBcIkZpbGwgTGlnaHRcIixcbiAgICAgIH0sXG4gICAgICB7XG4gICAgICAgIGlkOiBcImJhY2tMaWdodFwiLFxuICAgICAgICB0eXBlOiBcInBvaW50XCIsXG4gICAgICAgIHBvc2l0aW9uOiBbMCwgNSwgLTVdLFxuICAgICAgICBpbnRlbnNpdHk6IDIuMCxcbiAgICAgICAgY29sb3I6IFwiI2ZmZmZmZlwiLFxuICAgICAgICBuYW1lOiBcIkJhY2sgTGlnaHRcIixcbiAgICAgIH0sXG4gICAgXSk7XG5cbiAgICAvLyBSZXNldCBlbnZpcm9ubWVudCBzZXR0aW5nc1xuICAgIHNldEVudlByZXNldChcImhkcmlfMTVcIik7XG4gICAgc2V0QmdDb2xvcihcIiMyMDIwMjBcIik7XG4gICAgc2V0U2hvd0Vudmlyb25tZW50KGZhbHNlKTtcbiAgICBzZXRTaG93TGlnaHRTcGhlcmVzKHRydWUpO1xuICAgIHNldEN1c3RvbUhkcmkobnVsbCk7XG4gICAgc2V0RW52SW50ZW5zaXR5KDEuNSk7XG4gICAgc2V0RW52Qmx1cigwLjM1KTtcbiAgICBzZXRFbnZSb3RhdGlvbigwKTtcblxuICAgIC8vIFJlc2V0IHBvc3QtcHJvY2Vzc2luZyBzZXR0aW5nc1xuICAgIHNldFBvc3RQcm9jZXNzaW5nRW5hYmxlZChmYWxzZSk7XG4gICAgc2V0UG9zdFByb2Nlc3NpbmdTZXR0aW5ncyh7XG4gICAgICBhdXRvZm9jdXM6IHtcbiAgICAgICAgZW5hYmxlZDogZmFsc2UsXG4gICAgICAgIGJva2VoU2NhbGU6IDE4LFxuICAgICAgfSxcbiAgICAgIGJsb29tOiB7XG4gICAgICAgIGVuYWJsZWQ6IHRydWUsXG4gICAgICAgIGludGVuc2l0eTogMC4xMixcbiAgICAgICAgdGhyZXNob2xkOiAwLjgsXG4gICAgICAgIHJhZGl1czogMC40LFxuICAgICAgfSxcbiAgICAgIGRvZjoge1xuICAgICAgICBlbmFibGVkOiBmYWxzZSxcbiAgICAgICAgZm9jdXNEaXN0YW5jZTogMCxcbiAgICAgICAgYXBlcnR1cmU6IDAuMDEsXG4gICAgICAgIGJva2VoU2NhbGU6IDMsXG4gICAgICB9LFxuICAgICAgdmlnbmV0dGU6IHtcbiAgICAgICAgZW5hYmxlZDogdHJ1ZSxcbiAgICAgICAgZGFya25lc3M6IDAuNSxcbiAgICAgICAgb2Zmc2V0OiAwLjEsXG4gICAgICB9LFxuICAgIH0pO1xuXG4gICAgLy8gUmVzZXQgc2NlbmUgY29udHJvbHNcbiAgICBzZXRTaG93R3JpZCh0cnVlKTtcbiAgICBzZXRXaXJlZnJhbWUoZmFsc2UpO1xuICAgIHNldEdyb3VuZFR5cGUoXCJub25lXCIpO1xuXG4gICAgLy8gUmVzZXQgY2FtZXJhIHBvc2l0aW9uXG4gICAgaWYgKG9yYml0Q29udHJvbHNSZWYuY3VycmVudCkge1xuICAgICAgb3JiaXRDb250cm9sc1JlZi5jdXJyZW50Lm9iamVjdC5wb3NpdGlvbi5zZXQoNSwgMiwgNSk7XG4gICAgICBvcmJpdENvbnRyb2xzUmVmLmN1cnJlbnQudGFyZ2V0LnNldCgwLCAxLCAwKTtcbiAgICAgIG9yYml0Q29udHJvbHNSZWYuY3VycmVudC51cGRhdGUoKTtcbiAgICB9XG5cbiAgICAvLyBSZXNldCB0cmFuc2Zvcm0gc3RhdGVcbiAgICBzZXRUcmFuc2Zvcm1Nb2RlKFwidHJhbnNsYXRlXCIpO1xuICAgIHNldFNlbGVjdGVkTGlnaHQobnVsbCk7XG5cbiAgICAvLyBSZXNldCBtYXRlcmlhbCBzaGFkZXJzXG4gICAgc2V0VXNlRGlhbW9uZFNoYWRlcihmYWxzZSk7XG4gICAgc2V0VXNlUHJlbWl1bVdoaXRlR29sZChmYWxzZSk7XG4gICAgc2V0VXNlUHJlbWl1bVJvc2VHb2xkKGZhbHNlKTtcblxuICAgIC8vIENsZWFyIGhpc3RvcnlcbiAgICBzZXRIaXN0b3J5KFtdKTtcbiAgICBzZXRIaXN0b3J5SW5kZXgoLTEpO1xuICAgIHNldExpZ2h0UG9zaXRpb25IaXN0b3J5KHt9KTtcblxuICAgIC8vIFJlc2V0IG1vZGVsIHN0YXRzXG4gICAgc2V0TW9kZWxTdGF0cyh7XG4gICAgICB2ZXJ0aWNlczogMCxcbiAgICAgIHRyaWFuZ2xlczogMCxcbiAgICAgIG1hdGVyaWFsczogMCxcbiAgICAgIGFuaW1hdGlvbnM6IDAsXG4gICAgfSk7XG5cbiAgICAvLyBSZXNldCByaWdodCBwYW5lbCB0YWJcbiAgICBzZXRSaWdodFBhbmVsVGFiKG51bGwpO1xuXG4gICAgLy8gUmVzZXQgYWN0aXZlIGFuaW1hdGlvblxuICAgIHNldEFjdGl2ZUFuaW1hdGlvbihudWxsKTtcbiAgICBzZXRBbmltYXRpb25TdGFydFRpbWUoMCk7XG4gICAgaWYgKHJlcXVlc3RSZWYuY3VycmVudCkge1xuICAgICAgY2FuY2VsQW5pbWF0aW9uRnJhbWUocmVxdWVzdFJlZi5jdXJyZW50KTtcbiAgICB9XG5cbiAgICAvLyBDbGVhciBhbnkgY3VzdG9tIEhEUkkgb3IgdXBsb2FkZWQgbW9kZWwgVVJMc1xuICAgIGlmICh1cGxvYWRlZE1vZGVsKSB7XG4gICAgICBVUkwucmV2b2tlT2JqZWN0VVJMKHVwbG9hZGVkTW9kZWwpO1xuICAgIH1cbiAgICBpZiAoY3VzdG9tSGRyaSkge1xuICAgICAgVVJMLnJldm9rZU9iamVjdFVSTChjdXN0b21IZHJpKTtcbiAgICB9XG5cbiAgICAvLyBGb3JjZSBhIG1vZGVsIHJlbG9hZCBieSBzZXR0aW5nIHNlbGVjdGVkTW9kZWwgdG8gbnVsbCBmaXJzdFxuICAgIHNldFNlbGVjdGVkTW9kZWwobnVsbCk7XG4gICAgLy8gVGhlbiBzZXQgaXQgYmFjayB0byB1cGxvYWRlZCBhZnRlciBhIHNtYWxsIGRlbGF5XG4gICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICBzZXRTZWxlY3RlZE1vZGVsKFwidXBsb2FkZWRcIik7XG4gICAgfSwgMTAwKTtcbiAgfSwgW10pO1xuXG4gIGNvbnN0IHNjcmVlbnNob3RSZWYgPSB1c2VSZWYobnVsbCk7XG5cbiAgY29uc3QgY2FwdHVyZVNjcmVlbnNob3QgPSB1c2VDYWxsYmFjaygoKSA9PiB7XG4gICAgaWYgKHNjcmVlbnNob3RSZWYuY3VycmVudCkge1xuICAgICAgY29uc29sZS5sb2coXCJUYWtpbmcgc2NyZWVuc2hvdFwiKTtcbiAgICAgIHNjcmVlbnNob3RSZWYuY3VycmVudCgpO1xuICAgIH0gZWxzZSB7XG4gICAgICBjb25zb2xlLmVycm9yKFwiU2NyZWVuc2hvdCBmdW5jdGlvbiBub3QgYXZhaWxhYmxlIHlldFwiKTtcbiAgICB9XG4gIH0sIFtdKTtcblxuICAvLyBMb2FkIHNhdmVkIHNjZW5lIGZyb20gbG9jYWxTdG9yYWdlIG9uIG1vdW50XG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgbG9hZFNhdmVkU2NlbmUgPSAoKSA9PiB7XG4gICAgICBjb25zdCBzYXZlZFNjZW5lID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oXCJhZ2FwZV9zYXZlZF9zY2VuZVwiKTtcbiAgICAgIGlmIChzYXZlZFNjZW5lKSB7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgY29uc3Qgc2NlbmVDb25maWcgPSBKU09OLnBhcnNlKHNhdmVkU2NlbmUpO1xuICAgICAgICAgIGNvbnNvbGUubG9nKFwiTG9hZGluZyBzYXZlZCBzY2VuZTpcIiwgc2NlbmVDb25maWcpO1xuXG4gICAgICAgICAgLy8gT25seSBsb2FkIHRoZSBzYXZlZCBjb25maWd1cmF0aW9uIG9uIGluaXRpYWwgbG9hZFxuICAgICAgICAgIGlmICghbGFzdFNlbGVjdGVkTW9kZWwpIHtcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKFxuICAgICAgICAgICAgICBcIkluaXRpYWwgbG9hZCAtIHNldHRpbmcgc2F2ZWQgbW9kZWw6XCIsXG4gICAgICAgICAgICAgIHNjZW5lQ29uZmlnLm1vZGVsXG4gICAgICAgICAgICApO1xuICAgICAgICAgICAgc2V0U2VsZWN0ZWRNb2RlbChcInVwbG9hZGVkXCIpOyAvLyBBbHdheXMgdXNlIHVwbG9hZGVkIGZvciBTMyBtb2RlbHNcblxuICAgICAgICAgICAgLy8gTG9hZCB0aGUgcmVzdCBvZiB0aGUgc2F2ZWQgc2NlbmUgY29uZmlndXJhdGlvblxuICAgICAgICAgICAgc2V0TGlnaHRzKHNjZW5lQ29uZmlnLmxpZ2h0cyk7XG4gICAgICAgICAgICBzZXRFbnZQcmVzZXQoc2NlbmVDb25maWcuZW52aXJvbm1lbnQucHJlc2V0KTtcbiAgICAgICAgICAgIHNldEJnQ29sb3Ioc2NlbmVDb25maWcuZW52aXJvbm1lbnQuYmdDb2xvcik7XG4gICAgICAgICAgICBzZXRTaG93RW52aXJvbm1lbnQoc2NlbmVDb25maWcuZW52aXJvbm1lbnQuc2hvd0Vudmlyb25tZW50KTtcbiAgICAgICAgICAgIHNldFNob3dMaWdodFNwaGVyZXMoc2NlbmVDb25maWcuZW52aXJvbm1lbnQuc2hvd0xpZ2h0U3BoZXJlcyk7XG4gICAgICAgICAgICBzZXRFbnZJbnRlbnNpdHkoc2NlbmVDb25maWcuZW52aXJvbm1lbnQuaW50ZW5zaXR5KTtcbiAgICAgICAgICAgIHNldEVudkJsdXIoc2NlbmVDb25maWcuZW52aXJvbm1lbnQuYmx1cik7XG4gICAgICAgICAgICBzZXRFbnZSb3RhdGlvbihzY2VuZUNvbmZpZy5lbnZpcm9ubWVudC5yb3RhdGlvbik7XG4gICAgICAgICAgICBzZXRQb3N0UHJvY2Vzc2luZ0VuYWJsZWQoc2NlbmVDb25maWcucG9zdFByb2Nlc3NpbmcuZW5hYmxlZCk7XG4gICAgICAgICAgICBzZXRQb3N0UHJvY2Vzc2luZ1NldHRpbmdzKHNjZW5lQ29uZmlnLnBvc3RQcm9jZXNzaW5nLnNldHRpbmdzKTtcbiAgICAgICAgICAgIHNldFNob3dHcmlkKHNjZW5lQ29uZmlnLnNjZW5lQ29udHJvbHMuc2hvd0dyaWQpO1xuICAgICAgICAgICAgc2V0V2lyZWZyYW1lKHNjZW5lQ29uZmlnLnNjZW5lQ29udHJvbHMud2lyZWZyYW1lKTtcbiAgICAgICAgICAgIHNldEdyb3VuZFR5cGUoc2NlbmVDb25maWcuc2NlbmVDb250cm9scy5ncm91bmRUeXBlKTtcblxuICAgICAgICAgICAgLy8gUmVzdG9yZSBjYW1lcmEgcG9zaXRpb24gYWZ0ZXIgYSBzaG9ydCBkZWxheSB0byBlbnN1cmUgc2NlbmUgaXMgbG9hZGVkXG4gICAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgICAgICAgICAgaWYgKG9yYml0Q29udHJvbHNSZWYuY3VycmVudCAmJiBzY2VuZUNvbmZpZy5jYW1lcmEpIHtcbiAgICAgICAgICAgICAgICBvcmJpdENvbnRyb2xzUmVmLmN1cnJlbnQub2JqZWN0LnBvc2l0aW9uLmZyb21BcnJheShcbiAgICAgICAgICAgICAgICAgIHNjZW5lQ29uZmlnLmNhbWVyYS5wb3NpdGlvblxuICAgICAgICAgICAgICAgICk7XG4gICAgICAgICAgICAgICAgb3JiaXRDb250cm9sc1JlZi5jdXJyZW50LnRhcmdldC5mcm9tQXJyYXkoXG4gICAgICAgICAgICAgICAgICBzY2VuZUNvbmZpZy5jYW1lcmEudGFyZ2V0XG4gICAgICAgICAgICAgICAgKTtcbiAgICAgICAgICAgICAgICBvcmJpdENvbnRyb2xzUmVmLmN1cnJlbnQudXBkYXRlKCk7XG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0sIDEwMDApO1xuICAgICAgICAgIH0gZWxzZSBpZiAoY29uZmlnUmVmLmN1cnJlbnQpIHtcbiAgICAgICAgICAgIC8vIEZvciBleHBsaWNpdCBsb2FkcyB3aXRoIGNvbmZpZ1JlZiwgZG8gYSBjb21wbGV0ZSBsb2FkXG4gICAgICAgICAgICBjb25zb2xlLmxvZyhcIkV4cGxpY2l0IGxvYWQgd2l0aCBjb25maWdSZWZcIik7XG4gICAgICAgICAgICBoYW5kbGVMb2FkU2NlbmUoc2NlbmVDb25maWcpO1xuICAgICAgICAgIH1cblxuICAgICAgICAgIC8vIENyZWF0ZSBhIGZ1bmN0aW9uIHRvIHJlc3RvcmUgbWF0ZXJpYWxzXG4gICAgICAgICAgY29uc3QgcmVzdG9yZU1hdGVyaWFscyA9ICgpID0+IHtcbiAgICAgICAgICAgIGlmICghc2NlbmVDb25maWcubWF0ZXJpYWxzIHx8ICFzY2VuZU9iamVjdHMubGVuZ3RoKSB7XG4gICAgICAgICAgICAgIGNvbnNvbGUubG9nKFwiTm8gbWF0ZXJpYWxzIHRvIHJlc3RvcmUgb3Igc2NlbmUgb2JqZWN0cyBub3QgcmVhZHlcIik7XG4gICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgY29uc29sZS5sb2coXG4gICAgICAgICAgICAgIFwiQXR0ZW1wdGluZyB0byByZXN0b3JlIG1hdGVyaWFscyBmb3Igb2JqZWN0czpcIixcbiAgICAgICAgICAgICAgc2NlbmVPYmplY3RzXG4gICAgICAgICAgICApO1xuICAgICAgICAgICAgY29uc29sZS5sb2coXCJTYXZlZCBtYXRlcmlhbHM6XCIsIHNjZW5lQ29uZmlnLm1hdGVyaWFscyk7XG5cbiAgICAgICAgICAgIGNvbnN0IHNhdmVkTWF0ZXJpYWxzTWFwID0gbmV3IE1hcChcbiAgICAgICAgICAgICAgc2NlbmVDb25maWcubWF0ZXJpYWxzLm1hcCgobSkgPT4gW20ubmFtZSwgbS5tYXRlcmlhbF0pXG4gICAgICAgICAgICApO1xuXG4gICAgICAgICAgICBzY2VuZU9iamVjdHMuZm9yRWFjaCgob2JqKSA9PiB7XG4gICAgICAgICAgICAgIGlmIChvYmoubWF0ZXJpYWwgJiYgc2F2ZWRNYXRlcmlhbHNNYXAuaGFzKG9iai5uYW1lKSkge1xuICAgICAgICAgICAgICAgIGNvbnN0IHNhdmVkTWF0ZXJpYWwgPSBzYXZlZE1hdGVyaWFsc01hcC5nZXQob2JqLm5hbWUpO1xuICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKFxuICAgICAgICAgICAgICAgICAgXCJSZXN0b3JpbmcgbWF0ZXJpYWwgZm9yIG9iamVjdDpcIixcbiAgICAgICAgICAgICAgICAgIG9iai5uYW1lLFxuICAgICAgICAgICAgICAgICAgc2F2ZWRNYXRlcmlhbFxuICAgICAgICAgICAgICAgICk7XG4gICAgICAgICAgICAgICAgYXBwbHlNYXRlcmlhbFRvT2JqZWN0KG9iaiwgc2F2ZWRNYXRlcmlhbCk7XG4gICAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgY29uc29sZS5sb2coXG4gICAgICAgICAgICAgICAgICBcIkNvdWxkIG5vdCBmaW5kIHNhdmVkIG1hdGVyaWFsIGZvciBvYmplY3Q6XCIsXG4gICAgICAgICAgICAgICAgICBvYmoubmFtZVxuICAgICAgICAgICAgICAgICk7XG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgIH07XG5cbiAgICAgICAgICAvLyBTZXQgdXAgYW4gaW50ZXJ2YWwgdG8gY2hlY2sgZm9yIHNjZW5lIG9iamVjdHNcbiAgICAgICAgICBjb25zdCBjaGVja0ludGVydmFsID0gc2V0SW50ZXJ2YWwoKCkgPT4ge1xuICAgICAgICAgICAgaWYgKHNjZW5lT2JqZWN0cy5sZW5ndGggPiAwKSB7XG4gICAgICAgICAgICAgIGNsZWFySW50ZXJ2YWwoY2hlY2tJbnRlcnZhbCk7XG4gICAgICAgICAgICAgIC8vIEFkZCBhIHNtYWxsIGRlbGF5IHRvIGVuc3VyZSBhbGwgbWF0ZXJpYWxzIGFyZSBwcm9wZXJseSBpbml0aWFsaXplZFxuICAgICAgICAgICAgICBzZXRUaW1lb3V0KHJlc3RvcmVNYXRlcmlhbHMsIDUwMCk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgfSwgMTAwKTtcblxuICAgICAgICAgIC8vIENsZWFuIHVwIGludGVydmFsIGFmdGVyIDUgc2Vjb25kcyBpZiBzY2VuZSBvYmplY3RzIG5ldmVyIGJlY29tZSBhdmFpbGFibGVcbiAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgICAgICAgIGNsZWFySW50ZXJ2YWwoY2hlY2tJbnRlcnZhbCk7XG4gICAgICAgICAgfSwgNTAwMCk7XG5cbiAgICAgICAgICAvLyBSZXN0b3JlIGNhbWVyYSBwb3NpdGlvbiBhZnRlciBhIHNob3J0IGRlbGF5IHRvIGVuc3VyZSBzY2VuZSBpcyBsb2FkZWRcbiAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgICAgICAgIGlmIChvcmJpdENvbnRyb2xzUmVmLmN1cnJlbnQgJiYgc2NlbmVDb25maWcuY2FtZXJhKSB7XG4gICAgICAgICAgICAgIG9yYml0Q29udHJvbHNSZWYuY3VycmVudC5vYmplY3QucG9zaXRpb24uZnJvbUFycmF5KFxuICAgICAgICAgICAgICAgIHNjZW5lQ29uZmlnLmNhbWVyYS5wb3NpdGlvblxuICAgICAgICAgICAgICApO1xuICAgICAgICAgICAgICBvcmJpdENvbnRyb2xzUmVmLmN1cnJlbnQudGFyZ2V0LmZyb21BcnJheShcbiAgICAgICAgICAgICAgICBzY2VuZUNvbmZpZy5jYW1lcmEudGFyZ2V0XG4gICAgICAgICAgICAgICk7XG4gICAgICAgICAgICAgIG9yYml0Q29udHJvbHNSZWYuY3VycmVudC51cGRhdGUoKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9LCAxMDAwKTtcbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICBjb25zb2xlLmVycm9yKFwiRXJyb3IgbG9hZGluZyBzYXZlZCBzY2VuZTpcIiwgZXJyb3IpO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfTtcblxuICAgIC8vIExvYWQgc2F2ZWQgc2NlbmUgaW1tZWRpYXRlbHkgb24gbW91bnRcbiAgICBsb2FkU2F2ZWRTY2VuZSgpO1xuXG4gICAgLy8gQWxzbyBzZXQgdXAgYW4gaW50ZXJ2YWwgdG8gY2hlY2sgZm9yIHNjZW5lIHJlYWRpbmVzc1xuICAgIGNvbnN0IGNoZWNrU2NlbmVSZWFkeSA9IHNldEludGVydmFsKCgpID0+IHtcbiAgICAgIGlmIChzY2VuZVJlZi5jdXJyZW50ICYmIG9yYml0Q29udHJvbHNSZWYuY3VycmVudCkge1xuICAgICAgICBjbGVhckludGVydmFsKGNoZWNrU2NlbmVSZWFkeSk7XG4gICAgICAgIGxvYWRTYXZlZFNjZW5lKCk7XG4gICAgICB9XG4gICAgfSwgMTAwKTtcblxuICAgIHJldHVybiAoKSA9PiBjbGVhckludGVydmFsKGNoZWNrU2NlbmVSZWFkeSk7XG4gIH0sIFtzY2VuZU9iamVjdHMsIHNlbGVjdGVkTW9kZWxdKTtcblxuICBjb25zdCBbY29uZmlnU3RhdGUsIHNldENvbmZpZ1N0YXRlXSA9IHVzZVN0YXRlKG51bGwpO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKHNjZW5lT2JqZWN0cy5sZW5ndGggPiAwICYmIGNvbmZpZ1N0YXRlICYmIGNvbmZpZ1N0YXRlLm1hdGVyaWFscykge1xuICAgICAgY29uc3QgYXBwbHlNYXRlcmlhbHMgPSAoKSA9PiB7XG4gICAgICAgIGNvbnNvbGUubG9nKFwiQXBwbHlpbmcgbWF0ZXJpYWxzIHRvIHNjZW5lIG9iamVjdHM6XCIsIHNjZW5lT2JqZWN0cyk7XG4gICAgICAgIGNvbnNvbGUubG9nKFwiU2F2ZWQgbWF0ZXJpYWxzIGNvbmZpZ3VyYXRpb246XCIsIGNvbmZpZ1N0YXRlLm1hdGVyaWFscyk7XG4gICAgICAgIHNjZW5lT2JqZWN0cy5mb3JFYWNoKChvYmopID0+IHtcbiAgICAgICAgICBjb25zdCBtYXRlcmlhbENvbmZpZyA9IGNvbmZpZ1N0YXRlLm1hdGVyaWFscy5maW5kKFxuICAgICAgICAgICAgKG0pID0+IG0ubmFtZSA9PT0gb2JqLm5hbWUgfHwgbS5pZCA9PT0gb2JqLnV1aWRcbiAgICAgICAgICApO1xuICAgICAgICAgIGlmIChtYXRlcmlhbENvbmZpZyAmJiBtYXRlcmlhbENvbmZpZy5tYXRlcmlhbCkge1xuICAgICAgICAgICAgY29uc29sZS5sb2coXG4gICAgICAgICAgICAgIFwiRm91bmQgbWF0ZXJpYWwgY29uZmlnIGZvciBvYmplY3Q6XCIsXG4gICAgICAgICAgICAgIG9iai5uYW1lLFxuICAgICAgICAgICAgICBtYXRlcmlhbENvbmZpZy5tYXRlcmlhbFxuICAgICAgICAgICAgKTtcbiAgICAgICAgICAgIGFwcGx5TWF0ZXJpYWxUb09iamVjdChvYmosIG1hdGVyaWFsQ29uZmlnLm1hdGVyaWFsKTtcbiAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgY29uc29sZS5sb2coXCJObyBtYXRlcmlhbCBjb25maWcgZm91bmQgZm9yIG9iamVjdDpcIiwgb2JqLm5hbWUpO1xuICAgICAgICAgIH1cbiAgICAgICAgfSk7XG4gICAgICB9O1xuICAgICAgLy8gQWRkIGEgc21hbGwgZGVsYXkgdG8gZW5zdXJlIGFsbCBtYXRlcmlhbHMgYXJlIHByb3Blcmx5IGluaXRpYWxpemVkXG4gICAgICBjb25zdCB0aW1lb3V0SWQgPSBzZXRUaW1lb3V0KGFwcGx5TWF0ZXJpYWxzLCA1MDApO1xuICAgICAgcmV0dXJuICgpID0+IGNsZWFyVGltZW91dCh0aW1lb3V0SWQpO1xuICAgIH1cbiAgfSwgW3NjZW5lT2JqZWN0cywgY29uZmlnU3RhdGVdKTtcblxuICBjb25zdCBoYW5kbGVMb2FkU2NlbmUgPSAoY29uZmlnKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnNvbGUubG9nKFwiTG9hZGluZyBzY2VuZSBjb25maWd1cmF0aW9uOlwiLCBjb25maWcpO1xuXG4gICAgICAvLyBTdG9yZSB0aGUgY29uZmlnIGluIHRoZSByZWZcbiAgICAgIGNvbmZpZ1JlZi5jdXJyZW50ID0gY29uZmlnO1xuICAgICAgd29ya2luZ0NvbmZpZ1JlZi5jdXJyZW50ID0gY29uZmlnOyAvLyBBbHdheXMga2VlcCB0aGlzIGluIHN5bmMgd2l0aCBsYXN0IGFwcGxpZWQgY29uZmlnXG4gICAgICBzZXRDb25maWdTdGF0ZShjb25maWcpOyAvLyA8LS0ga2VlcCBpbiBzeW5jIGZvciBlZmZlY3RcblxuICAgICAgLy8gQ2xlYXIgc2NlbmUgb2JqZWN0cyBhbmQgc2VsZWN0ZWQgb2JqZWN0cyBmaXJzdFxuICAgICAgc2V0U2NlbmVPYmplY3RzKFtdKTtcbiAgICAgIHNldFNlbGVjdGVkT2JqZWN0cyhbXSk7XG4gICAgICBzZXRMYXN0U2VsZWN0ZWRNb2RlbChudWxsKTtcblxuICAgICAgLy8gUmVzdG9yZSBzY2VuZSBzZXR0aW5nc1xuICAgICAgc2V0U2VsZWN0ZWRNb2RlbChcInVwbG9hZGVkXCIpO1xuICAgICAgc2V0TGlnaHRzKGNvbmZpZy5saWdodHMpO1xuICAgICAgc2V0RW52UHJlc2V0KGNvbmZpZy5lbnZpcm9ubWVudC5wcmVzZXQpO1xuICAgICAgc2V0QmdDb2xvcihjb25maWcuZW52aXJvbm1lbnQuYmdDb2xvcik7XG4gICAgICBzZXRTaG93RW52aXJvbm1lbnQoY29uZmlnLmVudmlyb25tZW50LnNob3dFbnZpcm9ubWVudCk7XG4gICAgICBzZXRTaG93TGlnaHRTcGhlcmVzKGNvbmZpZy5lbnZpcm9ubWVudC5zaG93TGlnaHRTcGhlcmVzKTtcbiAgICAgIHNldEVudkludGVuc2l0eShjb25maWcuZW52aXJvbm1lbnQuaW50ZW5zaXR5KTtcbiAgICAgIHNldEVudkJsdXIoY29uZmlnLmVudmlyb25tZW50LmJsdXIpO1xuICAgICAgc2V0RW52Um90YXRpb24oY29uZmlnLmVudmlyb25tZW50LnJvdGF0aW9uKTtcbiAgICAgIHNldFBvc3RQcm9jZXNzaW5nRW5hYmxlZChjb25maWcucG9zdFByb2Nlc3NpbmcuZW5hYmxlZCk7XG4gICAgICBzZXRQb3N0UHJvY2Vzc2luZ1NldHRpbmdzKGNvbmZpZy5wb3N0UHJvY2Vzc2luZy5zZXR0aW5ncyk7XG4gICAgICBzZXRTaG93R3JpZChjb25maWcuc2NlbmVDb250cm9scy5zaG93R3JpZCk7XG4gICAgICBzZXRXaXJlZnJhbWUoY29uZmlnLnNjZW5lQ29udHJvbHMud2lyZWZyYW1lKTtcbiAgICAgIHNldEdyb3VuZFR5cGUoY29uZmlnLnNjZW5lQ29udHJvbHMuZ3JvdW5kVHlwZSk7XG5cbiAgICAgIC8vIFdhaXQgZm9yIHRoZSBzY2VuZSB0byB1cGRhdGUgYmVmb3JlIGFwcGx5aW5nIGNhbWVyYSBwb3NpdGlvblxuICAgICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgIGlmIChvcmJpdENvbnRyb2xzUmVmLmN1cnJlbnQgJiYgY29uZmlnLmNhbWVyYSkge1xuICAgICAgICAgIG9yYml0Q29udHJvbHNSZWYuY3VycmVudC5vYmplY3QucG9zaXRpb24uZnJvbUFycmF5KFxuICAgICAgICAgICAgY29uZmlnLmNhbWVyYS5wb3NpdGlvblxuICAgICAgICAgICk7XG4gICAgICAgICAgb3JiaXRDb250cm9sc1JlZi5jdXJyZW50LnRhcmdldC5mcm9tQXJyYXkoY29uZmlnLmNhbWVyYS50YXJnZXQpO1xuICAgICAgICAgIG9yYml0Q29udHJvbHNSZWYuY3VycmVudC51cGRhdGUoKTtcbiAgICAgICAgfVxuICAgICAgfSwgMTAwMCk7XG5cbiAgICAgIC8vIFJlc3RvcmUgbWF0ZXJpYWxzIGFmdGVyIHNjZW5lT2JqZWN0cyBhcmUgcmVhZHlcbiAgICAgIGNvbnN0IHJlc3RvcmVNYXRlcmlhbHMgPSAoKSA9PiB7XG4gICAgICAgIGlmICghY29uZmlnLm1hdGVyaWFscyB8fCAhc2NlbmVPYmplY3RzLmxlbmd0aCkge1xuICAgICAgICAgIGNvbnNvbGUubG9nKFwiTm8gbWF0ZXJpYWxzIHRvIHJlc3RvcmUgb3Igc2NlbmUgb2JqZWN0cyBub3QgcmVhZHlcIik7XG4gICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG4gICAgICAgIGNvbnNvbGUubG9nKFwiUmVzdG9yaW5nIG1hdGVyaWFscyBmb3Igb2JqZWN0czpcIiwgc2NlbmVPYmplY3RzKTtcbiAgICAgICAgY29uc3Qgc2F2ZWRNYXRlcmlhbHNNYXAgPSBuZXcgTWFwKFxuICAgICAgICAgIGNvbmZpZy5tYXRlcmlhbHMubWFwKChtKSA9PiBbbS5uYW1lLCBtLm1hdGVyaWFsXSlcbiAgICAgICAgKTtcbiAgICAgICAgc2NlbmVPYmplY3RzLmZvckVhY2goKG9iaikgPT4ge1xuICAgICAgICAgIGlmIChvYmoubWF0ZXJpYWwgJiYgc2F2ZWRNYXRlcmlhbHNNYXAuaGFzKG9iai5uYW1lKSkge1xuICAgICAgICAgICAgY29uc3Qgc2F2ZWRNYXRlcmlhbCA9IHNhdmVkTWF0ZXJpYWxzTWFwLmdldChvYmoubmFtZSk7XG4gICAgICAgICAgICBjb25zb2xlLmxvZyhcbiAgICAgICAgICAgICAgXCJSZXN0b3JpbmcgbWF0ZXJpYWwgZm9yIG9iamVjdDpcIixcbiAgICAgICAgICAgICAgb2JqLm5hbWUsXG4gICAgICAgICAgICAgIHNhdmVkTWF0ZXJpYWxcbiAgICAgICAgICAgICk7XG4gICAgICAgICAgICBhcHBseU1hdGVyaWFsVG9PYmplY3Qob2JqLCBzYXZlZE1hdGVyaWFsKTtcbiAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgY29uc29sZS5sb2coXCJDb3VsZCBub3QgZmluZCBzYXZlZCBtYXRlcmlhbCBmb3Igb2JqZWN0OlwiLCBvYmoubmFtZSk7XG4gICAgICAgICAgfVxuICAgICAgICB9KTtcbiAgICAgIH07XG4gICAgICBpZiAoc2NlbmVPYmplY3RzLmxlbmd0aCA+IDApIHtcbiAgICAgICAgc2V0VGltZW91dChyZXN0b3JlTWF0ZXJpYWxzLCA1MDApO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgY29uc3QgY2hlY2tJbnRlcnZhbCA9IHNldEludGVydmFsKCgpID0+IHtcbiAgICAgICAgICBpZiAoc2NlbmVPYmplY3RzLmxlbmd0aCA+IDApIHtcbiAgICAgICAgICAgIGNsZWFySW50ZXJ2YWwoY2hlY2tJbnRlcnZhbCk7XG4gICAgICAgICAgICBzZXRUaW1lb3V0KHJlc3RvcmVNYXRlcmlhbHMsIDUwMCk7XG4gICAgICAgICAgfVxuICAgICAgICB9LCAxMDApO1xuICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgICAgICBjbGVhckludGVydmFsKGNoZWNrSW50ZXJ2YWwpO1xuICAgICAgICB9LCA1MDAwKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcihcIkVycm9yIGxvYWRpbmcgc2NlbmUgY29uZmlndXJhdGlvbjpcIiwgZXJyb3IpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVTYXZlU2NlbmUgPSBhc3luYyAoc2NlbmVOYW1lLCBkZXNjcmlwdGlvbiwgc2F2ZVR5cGUpID0+IHtcbiAgICAvLyBVc2Ugd29ya2luZ0NvbmZpZ1JlZiBpZiBpdCBleGlzdHMgYW5kIG1hdGNoZXMgdGhlIGN1cnJlbnQgc3RhdGUsIG90aGVyd2lzZSBidWlsZCBmcm9tIHN0YXRlXG4gICAgbGV0IHNjZW5lQ29uZmlnO1xuICAgIGlmICh3b3JraW5nQ29uZmlnUmVmLmN1cnJlbnQpIHtcbiAgICAgIC8vIFVzZSBhIHNoYWxsb3cgY29tcGFyZSBmb3IgdG9wLWxldmVsIGtleXMgKG9yIGFsd2F5cyB1c2Ugd29ya2luZ0NvbmZpZ1JlZiBmb3Igbm93KVxuICAgICAgc2NlbmVDb25maWcgPSB7XG4gICAgICAgIC4uLndvcmtpbmdDb25maWdSZWYuY3VycmVudCxcbiAgICAgICAgbmFtZTogc2NlbmVOYW1lLFxuICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgIH07XG4gICAgfSBlbHNlIHtcbiAgICAgIC8vIGZhbGxiYWNrIHRvIGJ1aWxkaW5nIGZyb20gc3RhdGVcbiAgICAgIHNjZW5lQ29uZmlnID0ge1xuICAgICAgICBuYW1lOiBzY2VuZU5hbWUsXG4gICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgICAgICBtb2RlbDogc2VsZWN0ZWRNb2RlbCxcbiAgICAgICAgbGlnaHRzOiBsaWdodHMubWFwKChsaWdodCkgPT4gKHtcbiAgICAgICAgICAuLi5saWdodCxcbiAgICAgICAgICBwb3NpdGlvbjpcbiAgICAgICAgICAgIGxpZ2h0UmVmcy5jdXJyZW50W2xpZ2h0LmlkXT8ucG9zaXRpb24udG9BcnJheSgpIHx8IGxpZ2h0LnBvc2l0aW9uLFxuICAgICAgICB9KSksXG4gICAgICAgIGVudmlyb25tZW50OiB7XG4gICAgICAgICAgcHJlc2V0OiBlbnZQcmVzZXQsXG4gICAgICAgICAgaW50ZW5zaXR5OiBlbnZJbnRlbnNpdHksXG4gICAgICAgICAgYmx1cjogZW52Qmx1cixcbiAgICAgICAgICByb3RhdGlvbjogZW52Um90YXRpb24sXG4gICAgICAgICAgc2hvd0Vudmlyb25tZW50LFxuICAgICAgICAgIGJnQ29sb3IsXG4gICAgICAgICAgY3VzdG9tSGRyaSxcbiAgICAgICAgfSxcbiAgICAgICAgcG9zdFByb2Nlc3Npbmc6IHtcbiAgICAgICAgICBlbmFibGVkOiBwb3N0UHJvY2Vzc2luZ0VuYWJsZWQsXG4gICAgICAgICAgc2V0dGluZ3M6IHBvc3RQcm9jZXNzaW5nU2V0dGluZ3MsXG4gICAgICAgIH0sXG4gICAgICAgIHNjZW5lQ29udHJvbHM6IHtcbiAgICAgICAgICBzaG93R3JpZCxcbiAgICAgICAgICB3aXJlZnJhbWUsXG4gICAgICAgICAgZ3JvdW5kVHlwZSxcbiAgICAgICAgICBzaG93TGlnaHRTcGhlcmVzLFxuICAgICAgICB9LFxuICAgICAgICBjYW1lcmE6IHtcbiAgICAgICAgICBwb3NpdGlvbjogb3JiaXRDb250cm9sc1JlZi5jdXJyZW50Py5vYmplY3QucG9zaXRpb24udG9BcnJheSgpLFxuICAgICAgICAgIHRhcmdldDogb3JiaXRDb250cm9sc1JlZi5jdXJyZW50Py50YXJnZXQudG9BcnJheSgpLFxuICAgICAgICB9LFxuICAgICAgICBtYXRlcmlhbHM6IHNjZW5lT2JqZWN0cy5tYXAoKG9iaikgPT4ge1xuICAgICAgICAgIGNvbnN0IG1hdGVyaWFsID0gb2JqLm1hdGVyaWFsO1xuICAgICAgICAgIGlmICghbWF0ZXJpYWwpXG4gICAgICAgICAgICByZXR1cm4geyBpZDogb2JqLnV1aWQsIG5hbWU6IG9iai5uYW1lLCBtYXRlcmlhbDogbnVsbCB9O1xuICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICBpZDogb2JqLnV1aWQsXG4gICAgICAgICAgICBuYW1lOiBvYmoubmFtZSxcbiAgICAgICAgICAgIG1hdGVyaWFsOiB7XG4gICAgICAgICAgICAgIHR5cGU6IG1hdGVyaWFsLnR5cGUsXG4gICAgICAgICAgICAgIGNvbG9yOiBtYXRlcmlhbC5jb2xvciA/IG1hdGVyaWFsLmNvbG9yLmdldEhleFN0cmluZygpIDogXCJmZmZmZmZcIixcbiAgICAgICAgICAgICAgcm91Z2huZXNzOiBtYXRlcmlhbC5yb3VnaG5lc3MsXG4gICAgICAgICAgICAgIG1ldGFsbmVzczogbWF0ZXJpYWwubWV0YWxuZXNzLFxuICAgICAgICAgICAgICB3aXJlZnJhbWU6IG1hdGVyaWFsLndpcmVmcmFtZSxcbiAgICAgICAgICAgICAgdHJhbnNwYXJlbnQ6IG1hdGVyaWFsLnRyYW5zcGFyZW50LFxuICAgICAgICAgICAgICBvcGFjaXR5OiBtYXRlcmlhbC5vcGFjaXR5LFxuICAgICAgICAgICAgICBlbnZNYXBJbnRlbnNpdHk6IG1hdGVyaWFsLmVudk1hcEludGVuc2l0eSxcbiAgICAgICAgICAgICAgLi4uKG1hdGVyaWFsLnR5cGUgPT09IFwiTWVzaFJlZnJhY3Rpb25NYXRlcmlhbFwiICYmIHtcbiAgICAgICAgICAgICAgICBhYmVycmF0aW9uU3RyZW5ndGg6IG1hdGVyaWFsLmFiZXJyYXRpb25TdHJlbmd0aCxcbiAgICAgICAgICAgICAgICB0b25lTWFwcGVkOiBtYXRlcmlhbC50b25lTWFwcGVkLFxuICAgICAgICAgICAgICAgIGlvcjogbWF0ZXJpYWwuaW9yLFxuICAgICAgICAgICAgICAgIGNvbG9yRW52TWFwUm90WTogbWF0ZXJpYWwuY29sb3JFbnZNYXBSb3RZLFxuICAgICAgICAgICAgICB9KSxcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgfTtcbiAgICAgICAgfSksXG4gICAgICB9O1xuICAgIH1cblxuICAgIGlmIChzYXZlVHlwZSA9PT0gXCJsb2NhbFwiKSB7XG4gICAgICAvLyBTYXZlIHRvIGxvY2FsU3RvcmFnZVxuICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oXCJhZ2FwZV9zYXZlZF9zY2VuZVwiLCBKU09OLnN0cmluZ2lmeShzY2VuZUNvbmZpZykpO1xuICAgIH0gZWxzZSBpZiAoc2F2ZVR5cGUgPT09IFwiZGJcIikge1xuICAgICAgLy8gU2F2ZSB0byBEQiAoY2xvdWQpXG4gICAgICB0cnkge1xuICAgICAgICBjb25zdCB2ZXJzaW9uID0gYXdhaXQgY3JlYXRlVmVyc2lvbih7XG4gICAgICAgICAgdXNlcklkOiB1c2VyLmlkLFxuICAgICAgICAgIHdvcmtzcGFjZUlkOiBwcm9qZWN0Ll9pZCxcbiAgICAgICAgICBuYW1lOiBzY2VuZU5hbWUsXG4gICAgICAgICAgY29uZmlnOiBzY2VuZUNvbmZpZyxcbiAgICAgICAgICBkZXNjcmlwdGlvbjogZGVzY3JpcHRpb24sXG4gICAgICAgIH0pO1xuICAgICAgICBjb25zb2xlLmxvZyhcInZlcnNpb25cIiwgdmVyc2lvbik7XG4gICAgICAgIGlmICh2ZXJzaW9uKSB7XG4gICAgICAgICAgYWRkVmVyc2lvbihwcm9qZWN0Ll9pZCwgdmVyc2lvbik7XG4gICAgICAgIH1cbiAgICAgICAgdG9hc3Quc3VjY2VzcyhcIlNjZW5lIHNhdmVkIHRvIGNsb3VkIVwiKTtcbiAgICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgICB0b2FzdC5lcnJvcihcIkZhaWxlZCB0byBzYXZlIHNjZW5lIHRvIGNsb3VkXCIpO1xuICAgICAgfVxuICAgIH0gZWxzZSB7XG4gICAgICAvLyBFeHBvcnQgYXMgZmlsZVxuICAgICAgY29uc3QgYmxvYiA9IG5ldyBCbG9iKFtKU09OLnN0cmluZ2lmeShzY2VuZUNvbmZpZywgbnVsbCwgMildLCB7XG4gICAgICAgIHR5cGU6IFwiYXBwbGljYXRpb24vanNvblwiLFxuICAgICAgfSk7XG4gICAgICBjb25zdCB1cmwgPSBVUkwuY3JlYXRlT2JqZWN0VVJMKGJsb2IpO1xuICAgICAgY29uc3QgYSA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoXCJhXCIpO1xuICAgICAgYS5ocmVmID0gdXJsO1xuICAgICAgYS5kb3dubG9hZCA9IGAke3NjZW5lTmFtZS5yZXBsYWNlKC9cXHMrL2csIFwiX1wiKX0uanNvbmA7XG4gICAgICBkb2N1bWVudC5ib2R5LmFwcGVuZENoaWxkKGEpO1xuICAgICAgYS5jbGljaygpO1xuICAgICAgZG9jdW1lbnQuYm9keS5yZW1vdmVDaGlsZChhKTtcbiAgICAgIFVSTC5yZXZva2VPYmplY3RVUkwodXJsKTtcbiAgICB9XG4gIH07XG5cbiAgLy8gRW5ncmF2aW5nIHN0YXRlXG4gIGNvbnN0IFtlbmdyYXZpbmdUZXh0LCBzZXRFbmdyYXZpbmdUZXh0XSA9IHVzZVN0YXRlKFwiXCIpO1xuICBjb25zdCBbZGVjYWxQb3NpdGlvbiwgc2V0RGVjYWxQb3NpdGlvbl0gPSB1c2VTdGF0ZShbMCwgMS42OCwgMF0pO1xuICBjb25zdCBbZGVjYWxTY2FsZSwgc2V0RGVjYWxTY2FsZV0gPSB1c2VTdGF0ZShbMi44LCAwLjksIDEuNV0pO1xuICBjb25zdCBbZGVjYWxSb3RhdGlvbiwgc2V0RGVjYWxSb3RhdGlvbl0gPSB1c2VTdGF0ZShbLU1hdGguUEkgLyAyLCAwLCAwXSk7XG4gIGNvbnN0IFtkZWNhbENvbG9yLCBzZXREZWNhbENvbG9yXSA9IHVzZVN0YXRlKFwiI2NiY2JjYlwiKTtcbiAgY29uc3QgW3NlbGVjdGVkRm9udCwgc2V0U2VsZWN0ZWRGb250XSA9IHVzZVN0YXRlKFxuICAgIFwiL2ZvbnRzL3R0Zi9Sb2JvdG9TZXJpZi1SZWd1bGFyLnR0ZlwiXG4gICk7XG5cbiAgLy8gRGlhbW9uZCBzaGFkZXIgc3RhdGVcbiAgY29uc3QgW3VzZURpYW1vbmRTaGFkZXIsIHNldFVzZURpYW1vbmRTaGFkZXJdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbdXNlUHJlbWl1bVdoaXRlR29sZCwgc2V0VXNlUHJlbWl1bVdoaXRlR29sZF0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFt1c2VQcmVtaXVtUm9zZUdvbGQsIHNldFVzZVByZW1pdW1Sb3NlR29sZF0gPSB1c2VTdGF0ZShmYWxzZSk7XG5cbiAgY29uc3QgaGFuZGxlRW5ncmF2ZVRleHQgPSB1c2VDYWxsYmFjaygodGV4dCkgPT4ge1xuICAgIHNldEVuZ3JhdmluZ1RleHQodGV4dCk7XG4gICAgLy8gU3RvcmUgaW4gbG9jYWxTdG9yYWdlIHRvIG1ha2UgaXQgcGVyc2lzdCBhY3Jvc3MgcGFuZWwgY2hhbmdlcyBhbmQgcGFnZSByZWxvYWRzXG4gICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oXCJlbmdyYXZpbmdUZXh0XCIsIHRleHQpO1xuICB9LCBbXSk7XG5cbiAgY29uc3QgaGFuZGxlRm9udENoYW5nZSA9IHVzZUNhbGxiYWNrKChmb250KSA9PiB7XG4gICAgc2V0U2VsZWN0ZWRGb250KGZvbnQpO1xuICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKFwiZW5ncmF2aW5nRm9udFwiLCBmb250KTtcbiAgfSwgW10pO1xuXG4gIC8vIExvYWQgc2F2ZWQgZW5ncmF2aW5nIHRleHQgb24gbW91bnRcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCBzYXZlZEVuZ3JhdmluZ1RleHQgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbShcImVuZ3JhdmluZ1RleHRcIik7XG4gICAgaWYgKHNhdmVkRW5ncmF2aW5nVGV4dCkge1xuICAgICAgc2V0RW5ncmF2aW5nVGV4dChzYXZlZEVuZ3JhdmluZ1RleHQpO1xuICAgIH1cbiAgfSwgW10pO1xuXG4gIGNvbnN0IGhhbmRsZURlY2FsUGxhY2VtZW50ID0gdXNlQ2FsbGJhY2soXG4gICAgKGV2ZW50KSA9PiB7XG4gICAgICBoYW5kbGVEZWNhbFBsYWNlbWVudChcbiAgICAgICAgZXZlbnQsXG4gICAgICAgIHNldElzUGxhY2luZ0RlY2FsLFxuICAgICAgICBzZXREZWNhbFBvc2l0aW9uLFxuICAgICAgICBzZXREZWNhbFJvdGF0aW9uXG4gICAgICApO1xuICAgIH0sXG4gICAgW3NldERlY2FsUG9zaXRpb24sIHNldERlY2FsUm90YXRpb24sIHNldElzUGxhY2luZ0RlY2FsXVxuICApO1xuXG4gIC8vIEdldCBwcmVkZWZpbmVkIGRlY2FsIHBvc2l0aW9ucyBiYXNlZCBvbiBzZWxlY3RlZCBtb2RlbFxuICBjb25zdCBnZXREZWZhdWx0RGVjYWxQb3NpdGlvbiA9IHVzZUNhbGxiYWNrKChtb2RlbElkKSA9PiB7XG4gICAgY29uc3QgbW9kZWxDb25maWdzID0ge1xuICAgICAgY2hvc2VucmluZzogWzAsIDEuNjgsIDBdLFxuICAgICAgdGVzdHJpbmc6IFswLCAxLjY4LCAwXSxcbiAgICAgIGFnYXBlcmluZzE6IFswLCAxLjIsIDBdLFxuICAgICAgYWdhcGVyaW5nMjogWzAsIDEuMiwgMF0sXG4gICAgICByaW5nYTogWzAsIDAuMjYsIDBdLFxuICAgICAgcmluZ2I6IFswLCAwLjUsIDBdLFxuICAgICAgcmluZ2M6IFswLCAwLjI2LCAwXSxcbiAgICB9O1xuICAgIHJldHVybiBtb2RlbENvbmZpZ3NbbW9kZWxJZF0gfHwgWzAsIDEuNjgsIDBdO1xuICB9LCBbXSk7XG5cbiAgY29uc3QgZ2V0RGVmYXVsdERlY2FsUm90YXRpb24gPSB1c2VDYWxsYmFjaygobW9kZWxJZCkgPT4ge1xuICAgIGNvbnN0IG1vZGVsQ29uZmlncyA9IHtcbiAgICAgIGNob3NlbnJpbmc6IFstTWF0aC5QSSAvIDIsIDAsIDBdLFxuICAgICAgdGVzdHJpbmc6IFstTWF0aC5QSSAvIDIsIDAsIDBdLFxuICAgICAgYWdhcGVyaW5nMTogWy1NYXRoLlBJIC8gMiwgMCwgMF0sXG4gICAgICBhZ2FwZXJpbmcyOiBbLU1hdGguUEkgLyAyLCAwLCAwXSxcbiAgICAgIHJpbmdhOiBbLU1hdGguUEkgLyAyLCAwLCAwXSxcbiAgICAgIHJpbmdiOiBbLU1hdGguUEkgLyAyLCAwLCAwXSxcbiAgICAgIHJpbmdjOiBbLU1hdGguUEkgLyAyLCAwLCAwXSxcbiAgICB9O1xuICAgIHJldHVybiBtb2RlbENvbmZpZ3NbbW9kZWxJZF0gfHwgWy1NYXRoLlBJIC8gMiwgMCwgMF07XG4gIH0sIFtdKTtcblxuICBjb25zdCBnZXREZWZhdWx0RGVjYWxTY2FsZSA9IHVzZUNhbGxiYWNrKChtb2RlbElkKSA9PiB7XG4gICAgY29uc3QgbW9kZWxDb25maWdzID0ge1xuICAgICAgY2hvc2VucmluZzogWzYuMCwgMi4wLCAxLjVdLFxuICAgICAgdGVzdHJpbmc6IFs2LjAsIDIuMCwgMS41XSxcbiAgICAgIGFnYXBlcmluZzE6IFszLjIsIDEuOCwgMS4wXSxcbiAgICAgIGFnYXBlcmluZzI6IFszLjIsIDEuOCwgMS4wXSxcbiAgICAgIHJpbmdhOiBbMS44LCAyLjAsIDAuOF0sXG4gICAgICByaW5nYjogWzEuOCwgMi4wLCAwLjhdLFxuICAgICAgcmluZ2M6IFsxLjgsIDIuMCwgMC44XSxcbiAgICB9O1xuICAgIHJldHVybiBtb2RlbENvbmZpZ3NbbW9kZWxJZF0gfHwgWzQuMiwgMi4wLCAxLjVdO1xuICB9LCBbXSk7XG5cbiAgLy8gQ2FsY3VsYXRlIGR5bmFtaWMgc2NhbGUgYmFzZWQgb24gdGV4dCBsZW5ndGggYW5kIG1vZGVsIHR5cGVcbiAgY29uc3QgY2FsY3VsYXRlRHluYW1pY1NjYWxlID0gdXNlQ2FsbGJhY2soKGJhc2VTY2FsZSwgdGV4dCwgbW9kZWxJZCkgPT4ge1xuICAgIGlmICghdGV4dCB8fCB0ZXh0Lmxlbmd0aCA9PT0gMCkgcmV0dXJuIGJhc2VTY2FsZTtcblxuICAgIGNvbnN0IHRleHRMZW5ndGggPSB0ZXh0Lmxlbmd0aDtcbiAgICBsZXQgW3NjYWxlWCwgc2NhbGVZLCBzY2FsZVpdID0gYmFzZVNjYWxlO1xuXG4gICAgLy8gQ2FsY3VsYXRlIHNjYWxlIG11bHRpcGxpZXIgYmFzZWQgb24gdGV4dCBsZW5ndGhcbiAgICBsZXQgc2NhbGVNdWx0aXBsaWVyID0gMS4wO1xuXG4gICAgaWYgKHRleHRMZW5ndGggPiAxMCkge1xuICAgICAgLy8gRm9yIHZlcnkgbG9uZyB0ZXh0LCBzY2FsZSB1cCBtb3JlIGFnZ3Jlc3NpdmVseVxuICAgICAgc2NhbGVNdWx0aXBsaWVyID0gMS4zICsgKHRleHRMZW5ndGggLSAxMCkgKiAwLjA4O1xuICAgIH0gZWxzZSBpZiAodGV4dExlbmd0aCA+IDYpIHtcbiAgICAgIC8vIEZvciBtZWRpdW0gbGVuZ3RoIHRleHQsIHNjYWxlIHVwIG1vZGVyYXRlbHlcbiAgICAgIHNjYWxlTXVsdGlwbGllciA9IDEuMCArICh0ZXh0TGVuZ3RoIC0gNikgKiAwLjA4O1xuICAgIH0gZWxzZSBpZiAodGV4dExlbmd0aCA8IDQpIHtcbiAgICAgIC8vIEZvciBzaG9ydCB0ZXh0LCBzY2FsZSBkb3duIHNsaWdodGx5IHRvIHByZXZlbnQgb3ZlcnNpemVkIGFwcGVhcmFuY2VcbiAgICAgIHNjYWxlTXVsdGlwbGllciA9IDAuOCArIHRleHRMZW5ndGggKiAwLjA1O1xuICAgIH1cblxuICAgIC8vIEFwcGx5IHRleHQgbGVuZ3RoIHNjYWxpbmcgdG8gWC1heGlzICh3aWR0aCkgcHJpbWFyaWx5XG4gICAgc2NhbGVYID0gc2NhbGVYICogc2NhbGVNdWx0aXBsaWVyO1xuXG4gICAgLy8gRm9yIHZlcnkgbG9uZyB0ZXh0LCBhbHNvIGFkanVzdCB0aGUgaGVpZ2h0IHNsaWdodGx5XG4gICAgaWYgKHRleHRMZW5ndGggPiA4KSB7XG4gICAgICBzY2FsZVkgPSBzY2FsZVkgKiBNYXRoLm1pbigxLjIsIDEuMCArICh0ZXh0TGVuZ3RoIC0gOCkgKiAwLjAyKTtcbiAgICB9XG5cbiAgICAvLyBDYXAgbWF4aW11bSBzY2FsZSB0byBwcmV2ZW50IGV4Y2Vzc2l2ZSBzaXppbmcgKHVwZGF0ZWQgZm9yIGxhcmdlciBZIHZhbHVlcylcbiAgICBjb25zdCBtYXhTY2FsZSA9IHtcbiAgICAgIGNob3NlbnJpbmc6IFsxMi4wLCAyLjUsIDIuMF0sXG4gICAgICB0ZXN0cmluZzogWzEyLjAsIDIuNSwgMi4wXSxcbiAgICAgIGFnYXBlcmluZzE6IFs2LjUsIDIuMiwgMS41XSxcbiAgICAgIGFnYXBlcmluZzI6IFs2LjUsIDIuMiwgMS41XSxcbiAgICAgIHJpbmdhOiBbNC4wLCAyLjUsIDEuMl0sXG4gICAgICByaW5nYjogWzQuMCwgMi41LCAxLjJdLFxuICAgICAgcmluZ2M6IFs0LjAsIDIuNSwgMS4yXSxcbiAgICB9O1xuXG4gICAgY29uc3QgbGltaXRzID0gbWF4U2NhbGVbbW9kZWxJZF0gfHwgWzguNSwgMi41LCAyLjBdO1xuICAgIHNjYWxlWCA9IE1hdGgubWluKHNjYWxlWCwgbGltaXRzWzBdKTtcbiAgICBzY2FsZVkgPSBNYXRoLm1pbihzY2FsZVksIGxpbWl0c1sxXSk7XG4gICAgc2NhbGVaID0gTWF0aC5taW4oc2NhbGVaLCBsaW1pdHNbMl0pO1xuXG4gICAgcmV0dXJuIFtzY2FsZVgsIHNjYWxlWSwgc2NhbGVaXTtcbiAgfSwgW10pO1xuXG4gIC8vIFVwZGF0ZSBzY2FsZSB3aGVuIHRleHQgY2hhbmdlc1xuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmIChzZWxlY3RlZE1vZGVsICYmIGVuZ3JhdmluZ1RleHQpIHtcbiAgICAgIGNvbnN0IGJhc2VTY2FsZSA9IGdldERlZmF1bHREZWNhbFNjYWxlKHNlbGVjdGVkTW9kZWwpO1xuICAgICAgY29uc3QgZHluYW1pY1NjYWxlID0gY2FsY3VsYXRlRHluYW1pY1NjYWxlKFxuICAgICAgICBiYXNlU2NhbGUsXG4gICAgICAgIGVuZ3JhdmluZ1RleHQsXG4gICAgICAgIHNlbGVjdGVkTW9kZWxcbiAgICAgICk7XG4gICAgICBzZXREZWNhbFNjYWxlKGR5bmFtaWNTY2FsZSk7XG4gICAgfVxuICB9LCBbXG4gICAgZW5ncmF2aW5nVGV4dCxcbiAgICBzZWxlY3RlZE1vZGVsLFxuICAgIGdldERlZmF1bHREZWNhbFNjYWxlLFxuICAgIGNhbGN1bGF0ZUR5bmFtaWNTY2FsZSxcbiAgXSk7XG5cbiAgLy8gSW5pdGlhbGl6ZSBkZWNhbCBwb3NpdGlvbiBiYXNlZCBvbiBzZWxlY3RlZCBtb2RlbFxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmIChzZWxlY3RlZE1vZGVsKSB7XG4gICAgICBzZXREZWNhbFBvc2l0aW9uKGdldERlZmF1bHREZWNhbFBvc2l0aW9uKHNlbGVjdGVkTW9kZWwpKTtcbiAgICAgIHNldERlY2FsUm90YXRpb24oZ2V0RGVmYXVsdERlY2FsUm90YXRpb24oc2VsZWN0ZWRNb2RlbCkpO1xuICAgICAgLy8gT25seSBzZXQgaW5pdGlhbCBzY2FsZSBpZiB0aGVyZSdzIG5vIGVuZ3JhdmluZyB0ZXh0IHlldFxuICAgICAgaWYgKCFlbmdyYXZpbmdUZXh0KSB7XG4gICAgICAgIHNldERlY2FsU2NhbGUoZ2V0RGVmYXVsdERlY2FsU2NhbGUoc2VsZWN0ZWRNb2RlbCkpO1xuICAgICAgfVxuICAgIH1cbiAgfSwgW1xuICAgIHNlbGVjdGVkTW9kZWwsXG4gICAgZ2V0RGVmYXVsdERlY2FsUG9zaXRpb24sXG4gICAgZ2V0RGVmYXVsdERlY2FsUm90YXRpb24sXG4gICAgZ2V0RGVmYXVsdERlY2FsU2NhbGUsXG4gICAgZW5ncmF2aW5nVGV4dCxcbiAgXSk7XG5cbiAgY29uc3QgW2lzRnVsbHNjcmVlbiwgc2V0SXNGdWxsc2NyZWVuXSA9IHVzZVN0YXRlKGZhbHNlKTtcblxuICAvLyBHZXQgY2FtZXJhIHBvc2l0aW9uIGJhc2VkIG9uIGRldmljZSB0eXBlXG4gIGNvbnN0IFtpc01vYmlsZSwgc2V0SXNNb2JpbGVdID0gdXNlU3RhdGUoZmFsc2UpO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgY2hlY2tNb2JpbGUgPSAoKSA9PiB7XG4gICAgICBzZXRJc01vYmlsZSh3aW5kb3cuaW5uZXJXaWR0aCA8IDc2OCk7XG4gICAgfTtcblxuICAgIGNoZWNrTW9iaWxlKCk7XG4gICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoXCJyZXNpemVcIiwgY2hlY2tNb2JpbGUpO1xuXG4gICAgcmV0dXJuICgpID0+IHdpbmRvdy5yZW1vdmVFdmVudExpc3RlbmVyKFwicmVzaXplXCIsIGNoZWNrTW9iaWxlKTtcbiAgfSwgW10pO1xuXG4gIC8vIEFkZCBzdGF0ZSB0byBjb250cm9sIG91dGxpbmVyIGNvbGxhcHNlZCBzdGF0ZSAtIGluaXRpYWxpemUgYmFzZWQgb24gZGV2aWNlIHR5cGVcbiAgY29uc3QgW291dGxpbmVyQ29sbGFwc2VkLCBzZXRPdXRsaW5lckNvbGxhcHNlZF0gPSB1c2VTdGF0ZSgoKSA9PiB7XG4gICAgLy8gSW5pdGlhbGl6ZSBiYXNlZCBvbiBkZXZpY2UgdHlwZSAtIGNsb3NlZCBvbiBtb2JpbGUsIG9wZW4gb24gZGVza3RvcFxuICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSBcInVuZGVmaW5lZFwiKSB7XG4gICAgICByZXR1cm4gd2luZG93LmlubmVyV2lkdGggPCA3Njg7XG4gICAgfVxuICAgIHJldHVybiBmYWxzZTsgLy8gRGVmYXVsdCB0byBvcGVuIG9uIHNlcnZlci1zaWRlIHJlbmRlcmluZ1xuICB9KTtcblxuICAvLyBFZmZlY3QgdG8gY2xvc2Ugb3V0bGluZXIgd2hlbiB0b29sYmFyIHBhbmVscyBhcmUgb3BlbmVkIG9uIG1vYmlsZVxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmIChpc01vYmlsZSAmJiByaWdodFBhbmVsVGFiKSB7XG4gICAgICBzZXRPdXRsaW5lckNvbGxhcHNlZCh0cnVlKTtcbiAgICB9XG4gIH0sIFtpc01vYmlsZSwgcmlnaHRQYW5lbFRhYl0pO1xuXG4gIC8vIEVmZmVjdCB0byB1cGRhdGUgb3V0bGluZXIgY29sbGFwc2VkIHN0YXRlIHdoZW4gbW9iaWxlIHN0YXRlIGNoYW5nZXNcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICAvLyBVcGRhdGUgb3V0bGluZXIgc3RhdGUgYmFzZWQgb24gZGV2aWNlIHR5cGUgLSBjbG9zZWQgb24gbW9iaWxlLCBvcGVuIG9uIGRlc2t0b3BcbiAgICBzZXRPdXRsaW5lckNvbGxhcHNlZChpc01vYmlsZSk7XG4gIH0sIFtpc01vYmlsZV0pO1xuXG4gIGNvbnN0IGdldENhbWVyYVBvc2l0aW9uID0gKCkgPT4ge1xuICAgIGlmIChpc01vYmlsZSkge1xuICAgICAgcmV0dXJuIFstMS44NSwgMy4yNSwgNC41XTtcbiAgICB9XG4gICAgcmV0dXJuIFstMS44NSwgMS41NSwgMy41XTtcbiAgfTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIC8vIE9uIGluaXRpYWwgbW91bnQsIGZldGNoIGFuZCBhcHBseSB0aGUgY3VycmVudCB2ZXJzaW9uIGZyb20gREJcbiAgICBjb25zdCBmZXRjaEFuZEFwcGx5Q3VycmVudFZlcnNpb24gPSBhc3luYyAoKSA9PiB7XG4gICAgICB0cnkge1xuICAgICAgICBjb25zdCBjdXJyZW50VmVyc2lvbiA9IGF3YWl0IGdldEN1cnJlbnRWZXJzaW9uKHtcbiAgICAgICAgICB1c2VySWQ6IHVzZXIuaWQsXG4gICAgICAgICAgd29ya3NwYWNlSWQ6IHByb2plY3QuX2lkLFxuICAgICAgICB9KTtcbiAgICAgICAgaWYgKGN1cnJlbnRWZXJzaW9uICYmIGN1cnJlbnRWZXJzaW9uLmNvbmZpZykge1xuICAgICAgICAgIGhhbmRsZUxvYWRTY2VuZShjdXJyZW50VmVyc2lvbi5jb25maWcpO1xuICAgICAgICB9XG4gICAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcihcIkZhaWxlZCB0byBsb2FkIGN1cnJlbnQgdmVyc2lvbiBmcm9tIERCXCIsIGVycik7XG4gICAgICB9XG4gICAgfTtcbiAgICBmZXRjaEFuZEFwcGx5Q3VycmVudFZlcnNpb24oKTtcbiAgICAvLyBPbmx5IHJ1biBvbiBtb3VudFxuICAgIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSByZWFjdC1ob29rcy9leGhhdXN0aXZlLWRlcHNcbiAgfSwgW10pO1xuXG4gIHJldHVybiAoXG4gICAgPD5cbiAgICAgIDxUb2FzdENvbnRhaW5lciAvPlxuICAgICAgPEhlYWRlclxuICAgICAgICBvblJlc2V0PXtoYW5kbGVSZXNldH1cbiAgICAgICAgb25TY3JlZW5zaG90PXtjYXB0dXJlU2NyZWVuc2hvdH1cbiAgICAgICAgb25TYXZlU2NlbmU9e2hhbmRsZVNhdmVTY2VuZX1cbiAgICAgICAgb25Mb2FkU2NlbmU9e2hhbmRsZUxvYWRTY2VuZX1cbiAgICAgICAgaXNGdWxsc2NyZWVuPXtpc0Z1bGxzY3JlZW59XG4gICAgICAgIG9uVG9nZ2xlRnVsbHNjcmVlbj17KCkgPT4gc2V0SXNGdWxsc2NyZWVuKCFpc0Z1bGxzY3JlZW4pfVxuICAgICAgICBwcm9qZWN0PXtwcm9qZWN0fVxuICAgICAgICB1c2VyPXt1c2VyfVxuICAgICAgLz5cblxuICAgICAgPEluaXRpYWxPdmVybGF5UGFuZWwgLz5cblxuICAgICAgey8qIExlZnQgVG9vbGJhciAqL31cbiAgICAgIDxkaXZcbiAgICAgICAgY2xhc3NOYW1lPXtgZml4ZWQgbGVmdC01IHRvcC0xLzIgdHJhbnNmb3JtIC10cmFuc2xhdGUteS0xLzIgei1bMTAwMF0gdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwICR7XG4gICAgICAgICAgaXNGdWxsc2NyZWVuID8gXCJvcGFjaXR5LTAgcG9pbnRlci1ldmVudHMtbm9uZVwiIDogXCJvcGFjaXR5LTEwMFwiXG4gICAgICAgIH1gfVxuICAgICAgPlxuICAgICAgICA8VG9vbGJhclxuICAgICAgICAgIHRyYW5zZm9ybU1vZGU9e3RyYW5zZm9ybU1vZGV9XG4gICAgICAgICAgc2V0VHJhbnNmb3JtTW9kZT17c2V0VHJhbnNmb3JtTW9kZX1cbiAgICAgICAgICB1bmRvQWN0aW9uPXt1bmRvQWN0aW9ufVxuICAgICAgICAgIHJlZG9BY3Rpb249e3JlZG9BY3Rpb259XG4gICAgICAgICAgcmlnaHRQYW5lbFRhYj17cmlnaHRQYW5lbFRhYn1cbiAgICAgICAgICBzZXRSaWdodFBhbmVsVGFiPXtzZXRSaWdodFBhbmVsVGFifVxuICAgICAgICAgIG9yaWVudGF0aW9uPVwidmVydGljYWxcIlxuICAgICAgICAgIHNjZW5lT2JqZWN0cz17c2NlbmVPYmplY3RzfVxuICAgICAgICAgIHNldFNlbGVjdGVkT2JqZWN0cz17c2V0U2VsZWN0ZWRPYmplY3RzfVxuICAgICAgICAgIHNlbGVjdGVkT2JqZWN0cz17c2VsZWN0ZWRPYmplY3RzfVxuICAgICAgICAgIHBhbmVsUmVmPXtwYW5lbFJlZn1cbiAgICAgICAgICBpc01vYmlsZT17aXNNb2JpbGV9XG4gICAgICAgICAgc2V0T3V0bGluZXJDb2xsYXBzZWQ9e3NldE91dGxpbmVyQ29sbGFwc2VkfVxuICAgICAgICAvPlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBEcm9wZG93biBQYW5lbHMgZm9yIGVhY2ggdG9vbGJhciBvcHRpb24gLSBhcHBlYXJzIGJlc2lkZSB0b29sYmFyIHdoZW4gc2VsZWN0ZWQgKi99XG4gICAgICB7cmlnaHRQYW5lbFRhYiAmJiAoXG4gICAgICAgIDxkaXZcbiAgICAgICAgICByZWY9e3BhbmVsUmVmfVxuICAgICAgICAgIGNsYXNzTmFtZT1cImZpeGVkIGxlZnQtMjAgdG9wLTEvMiB0cmFuc2Zvcm0gLXRyYW5zbGF0ZS15LTEvMiBiZy1ncmFkaWVudC10by10bCBmcm9tLVsjMzIzNDNEXSB0by1bIzE0MTkyRF0gcC00IHJvdW5kZWQtbGcgc2hhZG93LWxnIHotWzEwMDBdIG1heC1oLVs4MHZoXSBvdmVyZmxvdy15LWF1dG8gdy1bMjUwcHhdIG1kOnctWzMwMHB4XSBib3JkZXIgYm9yZGVyLVsjQTNBM0EzXS8zMCBzY3JvbGxiYXItbm9uZVwiXG4gICAgICAgID5cbiAgICAgICAgICB7cmlnaHRQYW5lbFRhYiA9PT0gXCJ0cmFuc2Zvcm1cIiAmJiAoXG4gICAgICAgICAgICA8VHJhbnNmb3JtUGFuZWxcbiAgICAgICAgICAgICAgc2VsZWN0ZWRPYmplY3RzPXtzZWxlY3RlZE9iamVjdHN9XG4gICAgICAgICAgICAgIHRyYW5zZm9ybU1vZGU9e3RyYW5zZm9ybU1vZGV9XG4gICAgICAgICAgICAgIHNldFRyYW5zZm9ybU1vZGU9e3NldFRyYW5zZm9ybU1vZGV9XG4gICAgICAgICAgICAgIHVuZG9BY3Rpb249e3VuZG9BY3Rpb259XG4gICAgICAgICAgICAgIHJlZG9BY3Rpb249e3JlZG9BY3Rpb259XG4gICAgICAgICAgICAvPlxuICAgICAgICAgICl9XG5cbiAgICAgICAgICB7cmlnaHRQYW5lbFRhYiA9PT0gXCJtYXRlcmlhbFwiICYmIChcbiAgICAgICAgICAgIDxNYXRlcmlhbFBhbmVsXG4gICAgICAgICAgICAgIHNlbGVjdGVkT2JqZWN0cz17c2VsZWN0ZWRPYmplY3RzfVxuICAgICAgICAgICAgICB1c2VEaWFtb25kU2hhZGVyPXt1c2VEaWFtb25kU2hhZGVyfVxuICAgICAgICAgICAgICBzZXRVc2VEaWFtb25kU2hhZGVyPXtzZXRVc2VEaWFtb25kU2hhZGVyfVxuICAgICAgICAgICAgICB1c2VQcmVtaXVtV2hpdGVHb2xkPXt1c2VQcmVtaXVtV2hpdGVHb2xkfVxuICAgICAgICAgICAgICBzZXRVc2VQcmVtaXVtV2hpdGVHb2xkPXtzZXRVc2VQcmVtaXVtV2hpdGVHb2xkfVxuICAgICAgICAgICAgICB1c2VQcmVtaXVtUm9zZUdvbGQ9e3VzZVByZW1pdW1Sb3NlR29sZH1cbiAgICAgICAgICAgICAgc2V0VXNlUHJlbWl1bVJvc2VHb2xkPXtzZXRVc2VQcmVtaXVtUm9zZUdvbGR9XG4gICAgICAgICAgICAgIHJlbmRlcmVyPXtyZW5kZXJlcn1cbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgKX1cblxuICAgICAgICAgIHtyaWdodFBhbmVsVGFiID09PSBcInBvc3Rwcm9jZXNzaW5nXCIgJiYgKFxuICAgICAgICAgICAgPFBvc3RQcm9jZXNzaW5nUGFuZWxcbiAgICAgICAgICAgICAgcG9zdFByb2Nlc3NpbmdFbmFibGVkPXtwb3N0UHJvY2Vzc2luZ0VuYWJsZWR9XG4gICAgICAgICAgICAgIHNldFBvc3RQcm9jZXNzaW5nRW5hYmxlZD17c2V0UG9zdFByb2Nlc3NpbmdFbmFibGVkfVxuICAgICAgICAgICAgICBwb3N0UHJvY2Vzc2luZ1NldHRpbmdzPXtwb3N0UHJvY2Vzc2luZ1NldHRpbmdzfVxuICAgICAgICAgICAgICBzZXRQb3N0UHJvY2Vzc2luZ1NldHRpbmdzPXtzZXRQb3N0UHJvY2Vzc2luZ1NldHRpbmdzfVxuICAgICAgICAgICAgLz5cbiAgICAgICAgICApfVxuXG4gICAgICAgICAge3JpZ2h0UGFuZWxUYWIgPT09IFwiZW52aXJvbm1lbnRcIiAmJiAoXG4gICAgICAgICAgICA8RW52aXJvbm1lbnRQYW5lbFxuICAgICAgICAgICAgICBlbnZQcmVzZXQ9e2VudlByZXNldH1cbiAgICAgICAgICAgICAgc2V0RW52UHJlc2V0PXtzZXRFbnZQcmVzZXR9XG4gICAgICAgICAgICAgIGJnQ29sb3I9e2JnQ29sb3J9XG4gICAgICAgICAgICAgIHNldEJnQ29sb3I9e3NldEJnQ29sb3J9XG4gICAgICAgICAgICAgIHNob3dFbnZpcm9ubWVudD17c2hvd0Vudmlyb25tZW50fVxuICAgICAgICAgICAgICBzZXRTaG93RW52aXJvbm1lbnQ9e3NldFNob3dFbnZpcm9ubWVudH1cbiAgICAgICAgICAgICAgY3VzdG9tSGRyaT17Y3VzdG9tSGRyaX1cbiAgICAgICAgICAgICAgc2V0Q3VzdG9tSGRyaT17c2V0Q3VzdG9tSGRyaX1cbiAgICAgICAgICAgICAgZW52SW50ZW5zaXR5PXtlbnZJbnRlbnNpdHl9XG4gICAgICAgICAgICAgIHNldEVudkludGVuc2l0eT17c2V0RW52SW50ZW5zaXR5fVxuICAgICAgICAgICAgICBzaG93TW9kZWxTdGF0cz17c2hvd01vZGVsU3RhdHN9XG4gICAgICAgICAgICAgIHNldFNob3dNb2RlbFN0YXRzPXtzZXRTaG93TW9kZWxTdGF0c31cbiAgICAgICAgICAgICAgZW52Qmx1cj17ZW52Qmx1cn1cbiAgICAgICAgICAgICAgc2V0RW52Qmx1cj17c2V0RW52Qmx1cn1cbiAgICAgICAgICAgICAgZW52Um90YXRpb249e2VudlJvdGF0aW9ufVxuICAgICAgICAgICAgICBzZXRFbnZSb3RhdGlvbj17c2V0RW52Um90YXRpb259XG4gICAgICAgICAgICAvPlxuICAgICAgICAgICl9XG5cbiAgICAgICAgICB7cmlnaHRQYW5lbFRhYiA9PT0gXCJsaWdodHNcIiAmJiAoXG4gICAgICAgICAgICA8TGlnaHRQYW5lbFxuICAgICAgICAgICAgICBsaWdodHM9e2xpZ2h0c31cbiAgICAgICAgICAgICAgc2V0TGlnaHRzPXtzZXRMaWdodHN9XG4gICAgICAgICAgICAgIGxpZ2h0UmVmcz17bGlnaHRSZWZzfVxuICAgICAgICAgICAgICBzaG93R3JpZD17c2hvd0dyaWR9XG4gICAgICAgICAgICAgIHNldFNob3dHcmlkPXtzZXRTaG93R3JpZH1cbiAgICAgICAgICAgICAgd2lyZWZyYW1lPXt3aXJlZnJhbWV9XG4gICAgICAgICAgICAgIHNldFdpcmVmcmFtZT17c2V0V2lyZWZyYW1lfVxuICAgICAgICAgICAgICBzaG93TGlnaHRTcGhlcmVzPXtzaG93TGlnaHRTcGhlcmVzfVxuICAgICAgICAgICAgICBzZXRTaG93TGlnaHRTcGhlcmVzPXtzZXRTaG93TGlnaHRTcGhlcmVzfVxuICAgICAgICAgICAgICBvblJlc2V0TGlnaHRzPXsoKSA9PiB7XG4gICAgICAgICAgICAgICAgLy8gUmVzZXQgbGlnaHRzIHRvIGRlZmF1bHQgY29uZmlndXJhdGlvblxuICAgICAgICAgICAgICAgIGNvbnN0IGRlZmF1bHRMaWdodHMgPSBbXG4gICAgICAgICAgICAgICAgICB7XG4gICAgICAgICAgICAgICAgICAgIGlkOiBcImRlZmF1bHREaXJlY3Rpb25hbFwiLFxuICAgICAgICAgICAgICAgICAgICB0eXBlOiBcImRpcmVjdGlvbmFsXCIsXG4gICAgICAgICAgICAgICAgICAgIHBvc2l0aW9uOiBbMi41LCA1LjUsIDVdLFxuICAgICAgICAgICAgICAgICAgICBpbnRlbnNpdHk6IDEuMCxcbiAgICAgICAgICAgICAgICAgICAgY29sb3I6IFwiI2ZmZmZmZlwiLFxuICAgICAgICAgICAgICAgICAgICBuYW1lOiBcIk1haW4gRGlyZWN0aW9uYWwgTGlnaHRcIixcbiAgICAgICAgICAgICAgICAgICAgY2FzdFNoYWRvdzogdHJ1ZSxcbiAgICAgICAgICAgICAgICAgICAgaGVscGVyVmlzaWJsZTogZmFsc2UsXG4gICAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICAgICAge1xuICAgICAgICAgICAgICAgICAgICBpZDogXCJrZXlMaWdodFwiLFxuICAgICAgICAgICAgICAgICAgICB0eXBlOiBcInBvaW50XCIsXG4gICAgICAgICAgICAgICAgICAgIHBvc2l0aW9uOiBbNSwgNSwgNV0sXG4gICAgICAgICAgICAgICAgICAgIGludGVuc2l0eTogOC4wLFxuICAgICAgICAgICAgICAgICAgICBjb2xvcjogXCIjZmZmZmZmXCIsXG4gICAgICAgICAgICAgICAgICAgIG5hbWU6IFwiS2V5IExpZ2h0XCIsXG4gICAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICAgICAge1xuICAgICAgICAgICAgICAgICAgICBpZDogXCJmaWxsTGlnaHRcIixcbiAgICAgICAgICAgICAgICAgICAgdHlwZTogXCJwb2ludFwiLFxuICAgICAgICAgICAgICAgICAgICBwb3NpdGlvbjogWy01LCA1LCAtNV0sXG4gICAgICAgICAgICAgICAgICAgIGludGVuc2l0eTogNC4wLFxuICAgICAgICAgICAgICAgICAgICBjb2xvcjogXCIjZmZmZmZmXCIsXG4gICAgICAgICAgICAgICAgICAgIG5hbWU6IFwiRmlsbCBMaWdodFwiLFxuICAgICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICAgIHtcbiAgICAgICAgICAgICAgICAgICAgaWQ6IFwiYmFja0xpZ2h0XCIsXG4gICAgICAgICAgICAgICAgICAgIHR5cGU6IFwicG9pbnRcIixcbiAgICAgICAgICAgICAgICAgICAgcG9zaXRpb246IFswLCA1LCAtNV0sXG4gICAgICAgICAgICAgICAgICAgIGludGVuc2l0eTogMi4wLFxuICAgICAgICAgICAgICAgICAgICBjb2xvcjogXCIjZmZmZmZmXCIsXG4gICAgICAgICAgICAgICAgICAgIG5hbWU6IFwiQmFjayBMaWdodFwiLFxuICAgICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICBdO1xuICAgICAgICAgICAgICAgIHNldExpZ2h0cyhkZWZhdWx0TGlnaHRzKTtcbiAgICAgICAgICAgICAgICAvLyBDbGVhciBzZWxlY3RlZCBsaWdodFxuICAgICAgICAgICAgICAgIHNldFNlbGVjdGVkTGlnaHQobnVsbCk7XG4gICAgICAgICAgICAgICAgLy8gQ2xlYXIgbGlnaHQgcG9zaXRpb24gaGlzdG9yeVxuICAgICAgICAgICAgICAgIHNldExpZ2h0UG9zaXRpb25IaXN0b3J5KHt9KTtcbiAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgKX1cblxuICAgICAgICAgIHtyaWdodFBhbmVsVGFiID09PSBcImVuZ3JhdmluZ1wiICYmIChcbiAgICAgICAgICAgIDxFbmdyYXZpbmdQYW5lbFxuICAgICAgICAgICAgICBvbkVuZ3JhdmVUZXh0PXtoYW5kbGVFbmdyYXZlVGV4dH1cbiAgICAgICAgICAgICAgb25EZWNhbFBvc2l0aW9uQ2hhbmdlPXtzZXREZWNhbFBvc2l0aW9ufVxuICAgICAgICAgICAgICBvbkRlY2FsU2NhbGVDaGFuZ2U9e3NldERlY2FsU2NhbGV9XG4gICAgICAgICAgICAgIG9uRGVjYWxSb3RhdGlvbkNoYW5nZT17c2V0RGVjYWxSb3RhdGlvbn1cbiAgICAgICAgICAgICAgZGVjYWxQb3NpdGlvbj17ZGVjYWxQb3NpdGlvbn1cbiAgICAgICAgICAgICAgZGVjYWxTY2FsZT17ZGVjYWxTY2FsZX1cbiAgICAgICAgICAgICAgZGVjYWxSb3RhdGlvbj17ZGVjYWxSb3RhdGlvbn1cbiAgICAgICAgICAgICAgZGVjYWxDb2xvcj17ZGVjYWxDb2xvcn1cbiAgICAgICAgICAgICAgb25EZWNhbENvbG9yQ2hhbmdlPXtzZXREZWNhbENvbG9yfVxuICAgICAgICAgICAgICBvbkZvbnRDaGFuZ2U9e2hhbmRsZUZvbnRDaGFuZ2V9XG4gICAgICAgICAgICAgIHNlbGVjdGVkRm9udD17c2VsZWN0ZWRGb250fVxuICAgICAgICAgICAgICBpc1BsYWNpbmdEZWNhbD17aXNQbGFjaW5nRGVjYWx9XG4gICAgICAgICAgICAgIHNldElzUGxhY2luZ0RlY2FsPXtzZXRJc1BsYWNpbmdEZWNhbH1cbiAgICAgICAgICAgICAgc2hvd0RlYnVnPXtzaG93RGVjYWxEZWJ1Z31cbiAgICAgICAgICAgICAgb25TaG93RGVidWdDaGFuZ2U9e3NldFNob3dEZWNhbERlYnVnfVxuICAgICAgICAgICAgLz5cbiAgICAgICAgICApfVxuICAgICAgICA8L2Rpdj5cbiAgICAgICl9XG5cbiAgICAgIHsvKiBSaWdodCBQYW5lbCAtIENvbGxhcHNpYmxlIE91dGxpbmVyICovfVxuICAgICAgPGRpdlxuICAgICAgICBjbGFzc05hbWU9e2BmaXhlZCByaWdodC01IHRvcC0yNCB6LVs5OTldIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCAke1xuICAgICAgICAgIGlzRnVsbHNjcmVlbiA/IFwib3BhY2l0eS0wIHBvaW50ZXItZXZlbnRzLW5vbmVcIiA6IFwib3BhY2l0eS0xMDBcIlxuICAgICAgICB9YH1cbiAgICAgID5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmFkaWVudC10by10bCBmcm9tLVsjMzIzNDNEXSB0by1bIzE0MTkyRF0gc2hhZG93LWxnIHJvdW5kZWQtbGcgb3ZlcmZsb3ctdmlzaWJsZSBzY3JvbGxiYXItbm9uZVwiPlxuICAgICAgICAgIDxPdXRsaW5lclxuICAgICAgICAgICAgc2NlbmVPYmplY3RzPXtzY2VuZU9iamVjdHN9XG4gICAgICAgICAgICBzZWxlY3RlZE9iamVjdHM9e3NlbGVjdGVkT2JqZWN0c31cbiAgICAgICAgICAgIG9uU2VsZWN0T2JqZWN0PXtzZXRTZWxlY3RlZE9iamVjdHN9XG4gICAgICAgICAgICBvbkNhbWVyYVZpZXc9e2hhbmRsZUNhbWVyYVZpZXd9XG4gICAgICAgICAgICBvblBsYXlBbmltYXRpb249e2hhbmRsZVBsYXlBbmltYXRpb259XG4gICAgICAgICAgICBvblNhdmVTY2VuZT17aGFuZGxlU2F2ZVNjZW5lfVxuICAgICAgICAgICAgZ3JvdW5kVHlwZT17Z3JvdW5kVHlwZX1cbiAgICAgICAgICAgIHNldEdyb3VuZFR5cGU9e3NldEdyb3VuZFR5cGV9XG4gICAgICAgICAgICBjb2xsYXBzZWQ9e291dGxpbmVyQ29sbGFwc2VkfVxuICAgICAgICAgICAgc2V0Q29sbGFwc2VkPXtzZXRPdXRsaW5lckNvbGxhcHNlZH1cbiAgICAgICAgICAvPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogQ2FudmFzIC0gYWRqdXN0IHRvIGFjY291bnQgZm9yIGhlYWRlciBhbmQgdG9vbGJhciAqL31cbiAgICAgIDxkaXZcbiAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGgtZnVsbFwiXG4gICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgYmFja2dyb3VuZDogXCJsaW5lYXItZ3JhZGllbnQodG8gYm90dG9tIHJpZ2h0LCAjMzYzNjQzLCAjNUM1QzZFKVwiLFxuICAgICAgICB9fVxuICAgICAgPlxuICAgICAgICA8Q2FudmFzXG4gICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGgtZnVsbFwiXG4gICAgICAgICAgY2FtZXJhPXt7XG4gICAgICAgICAgICBwb3NpdGlvbjogZ2V0Q2FtZXJhUG9zaXRpb24oKSxcbiAgICAgICAgICAgIGZvdjogNDUsXG4gICAgICAgICAgICBuZWFyOiAwLjEsXG4gICAgICAgICAgICBmYXI6IDUwLFxuICAgICAgICAgIH19XG4gICAgICAgICAgc2hhZG93c1xuICAgICAgICAgIHJlZj17c2NlbmVSZWZ9XG4gICAgICAgICAgZ2w9e3tcbiAgICAgICAgICAgIGFudGlhbGlhczogdHJ1ZSxcbiAgICAgICAgICAgIHByZXNlcnZlRHJhd2luZ0J1ZmZlcjogdHJ1ZSxcbiAgICAgICAgICB9fVxuICAgICAgICAgIG9uUG9pbnRlck1pc3NlZD17aGFuZGxlUG9pbnRlck1pc3NlZH1cbiAgICAgICAgICByYXljYXN0ZXI9e3tcbiAgICAgICAgICAgIGNvbXB1dGVPZmZzZXRzOiAoZSkgPT4gKHsgb2Zmc2V0WDogZS5jbGllbnRYLCBvZmZzZXRZOiBlLmNsaWVudFkgfSksXG4gICAgICAgICAgfX1cbiAgICAgICAgICBmcmFtZWxvb3A9XCJkZW1hbmRcIlxuICAgICAgICA+XG4gICAgICAgICAgPFN1c3BlbnNlXG4gICAgICAgICAgICBmYWxsYmFjaz17XG4gICAgICAgICAgICAgIDxIdG1sPlxuICAgICAgICAgICAgICAgIDxMb2FkZXIgLz5cbiAgICAgICAgICAgICAgPC9IdG1sPlxuICAgICAgICAgICAgfVxuICAgICAgICAgID5cbiAgICAgICAgICAgIHsvKiA8Zm9nIGF0dGFjaD1cImZvZ1wiIGFyZ3M9e1tiZ0NvbG9yIHx8IFwiIzM2MzY0M1wiLCAxNSwgNDBdfSAvPiAqL31cblxuICAgICAgICAgICAgPFNjcmVlbnNob3RUYWtlclxuICAgICAgICAgICAgICBvblNjcmVlbnNob3RSZWY9eyhmbikgPT4ge1xuICAgICAgICAgICAgICAgIHNjcmVlbnNob3RSZWYuY3VycmVudCA9IGZuO1xuICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgLz5cblxuICAgICAgICAgICAgPFJlbmRlcmVyUHJvdmlkZXJcbiAgICAgICAgICAgICAgb25SZW5kZXJlclJlYWR5PXsoZ2wpID0+IHtcbiAgICAgICAgICAgICAgICBzZXRSZW5kZXJlcihnbCk7XG4gICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAvPlxuXG4gICAgICAgICAgICA8U2NlbmVMaWdodHNcbiAgICAgICAgICAgICAgbGlnaHRzPXtsaWdodHN9XG4gICAgICAgICAgICAgIGxpZ2h0UmVmcz17bGlnaHRSZWZzfVxuICAgICAgICAgICAgICBzZWxlY3RlZExpZ2h0PXtzZWxlY3RlZExpZ2h0fVxuICAgICAgICAgICAgICBvbkxpZ2h0U2VsZWN0PXtzZXRTZWxlY3RlZExpZ2h0fVxuICAgICAgICAgICAgICBvbkxpZ2h0TW92ZWQ9e2hhbmRsZUxpZ2h0TW92ZWR9XG4gICAgICAgICAgICAgIHNob3dMaWdodFNwaGVyZXM9e3Nob3dMaWdodFNwaGVyZXN9XG4gICAgICAgICAgICAgIHRocmVlUG9pbnRMaWdodGluZz17dHJ1ZX1cbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8RW52aXJvbm1lbnRcbiAgICAgICAgICAgICAgcHJlc2V0PXtnZXRFbnZpcm9ubWVudFByZXNldCgpfVxuICAgICAgICAgICAgICBiYWNrZ3JvdW5kPXtzaG93RW52aXJvbm1lbnR9XG4gICAgICAgICAgICAgIGJnQ29sb3I9e2JnQ29sb3J9XG4gICAgICAgICAgICAgIGN1c3RvbUhkcmk9e2N1c3RvbUhkcml9XG4gICAgICAgICAgICAgIGludGVuc2l0eT17ZW52SW50ZW5zaXR5fVxuICAgICAgICAgICAgICBibHVyPXtlbnZCbHVyfVxuICAgICAgICAgICAgICBlbnZSb3RhdGlvbj17ZW52Um90YXRpb259XG4gICAgICAgICAgICAvPlxuXG4gICAgICAgICAgICA8U2VsZWN0aW9uT3V0bGluZSBzZWxlY3RlZE9iamVjdHM9e3NlbGVjdGVkT2JqZWN0c30+XG4gICAgICAgICAgICAgIDxNb2RlbFN0YWdlXG4gICAgICAgICAgICAgICAgc2VsZWN0ZWRNb2RlbD17c2VsZWN0ZWRNb2RlbH1cbiAgICAgICAgICAgICAgICB3aXJlZnJhbWU9e3dpcmVmcmFtZX1cbiAgICAgICAgICAgICAgICBoYW5kbGVTY2VuZVVwZGF0ZT17aGFuZGxlU2NlbmVVcGRhdGV9XG4gICAgICAgICAgICAgICAgaGFuZGxlT2JqZWN0Q2xpY2s9e2hhbmRsZU9iamVjdENsaWNrfVxuICAgICAgICAgICAgICAgIHVwbG9hZGVkTW9kZWw9e3VwbG9hZGVkTW9kZWx9XG4gICAgICAgICAgICAgICAgZ3JvdW5kVHlwZT17Z3JvdW5kVHlwZX1cbiAgICAgICAgICAgICAgICBlbmdyYXZpbmdUZXh0PXtlbmdyYXZpbmdUZXh0fVxuICAgICAgICAgICAgICAgIGFjdGl2ZUFuaW1hdGlvbj17YWN0aXZlQW5pbWF0aW9ufVxuICAgICAgICAgICAgICAgIGRlY2FsUG9zaXRpb249e2RlY2FsUG9zaXRpb259XG4gICAgICAgICAgICAgICAgZGVjYWxTY2FsZT17ZGVjYWxTY2FsZX1cbiAgICAgICAgICAgICAgICBkZWNhbFJvdGF0aW9uPXtkZWNhbFJvdGF0aW9ufVxuICAgICAgICAgICAgICAgIGRlY2FsQ29sb3I9e2RlY2FsQ29sb3J9XG4gICAgICAgICAgICAgICAgZm9udD17c2VsZWN0ZWRGb250fVxuICAgICAgICAgICAgICAgIGlzUGxhY2luZ0RlY2FsPXtpc1BsYWNpbmdEZWNhbH1cbiAgICAgICAgICAgICAgICBvbkRlY2FsUGxhY2VtZW50PXtoYW5kbGVEZWNhbFBsYWNlbWVudH1cbiAgICAgICAgICAgICAgICBzaG93RGVjYWxEZWJ1Zz17c2hvd0RlY2FsRGVidWd9XG4gICAgICAgICAgICAgICAgc2NlbmVPYmplY3RzPXtzY2VuZU9iamVjdHN9XG4gICAgICAgICAgICAgICAgdXNlRGlhbW9uZFNoYWRlcj17dXNlRGlhbW9uZFNoYWRlcn1cbiAgICAgICAgICAgICAgICB1c2VQcmVtaXVtV2hpdGVHb2xkPXt1c2VQcmVtaXVtV2hpdGVHb2xkfVxuICAgICAgICAgICAgICAgIHVzZVByZW1pdW1Sb3NlR29sZD17dXNlUHJlbWl1bVJvc2VHb2xkfVxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgPC9TZWxlY3Rpb25PdXRsaW5lPlxuXG4gICAgICAgICAgICA8T3JiaXRDb250cm9sc1xuICAgICAgICAgICAgICByZWY9e29yYml0Q29udHJvbHNSZWZ9XG4gICAgICAgICAgICAgIHRhcmdldD17WzAsIDAuNSwgMF19XG4gICAgICAgICAgICAgIG1ha2VEZWZhdWx0XG4gICAgICAgICAgICAgIGVuYWJsZURhbXBpbmc9e3RydWV9XG4gICAgICAgICAgICAgIC8vIG1pblBvbGFyQW5nbGU9ezB9XG4gICAgICAgICAgICAgIC8vIG1heFBvbGFyQW5nbGU9e01hdGguUEkgLyAyLjI1fVxuICAgICAgICAgICAgICAvLyBtaW5EaXN0YW5jZT17Mi41fVxuICAgICAgICAgICAgICBtYXhEaXN0YW5jZT17MjB9XG4gICAgICAgICAgICAvPlxuXG4gICAgICAgICAgICB7c2VsZWN0ZWRPYmplY3RzLmxlbmd0aCA+IDAgJiYgcmlnaHRQYW5lbFRhYiA9PT0gXCJ0cmFuc2Zvcm1cIiAmJiAoXG4gICAgICAgICAgICAgIDxUcmFuc2Zvcm1Db250cm9sc1xuICAgICAgICAgICAgICAgIG9iamVjdD17XG4gICAgICAgICAgICAgICAgICBzZWxlY3RlZE9iamVjdHMubGVuZ3RoID09PSAxXG4gICAgICAgICAgICAgICAgICAgID8gc2VsZWN0ZWRPYmplY3RzWzBdXG4gICAgICAgICAgICAgICAgICAgIDogc2VsZWN0aW9uR3JvdXBSZWYuY3VycmVudFxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBtb2RlPXt0cmFuc2Zvcm1Nb2RlfVxuICAgICAgICAgICAgICAgIG9uTW91c2VEb3duPXsoKSA9PiB7XG4gICAgICAgICAgICAgICAgICBpZiAob3JiaXRDb250cm9sc1JlZi5jdXJyZW50KVxuICAgICAgICAgICAgICAgICAgICBvcmJpdENvbnRyb2xzUmVmLmN1cnJlbnQuZW5hYmxlZCA9IGZhbHNlO1xuICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgb25Nb3VzZVVwPXsoKSA9PiB7XG4gICAgICAgICAgICAgICAgICBpZiAob3JiaXRDb250cm9sc1JlZi5jdXJyZW50KVxuICAgICAgICAgICAgICAgICAgICBvcmJpdENvbnRyb2xzUmVmLmN1cnJlbnQuZW5hYmxlZCA9IHRydWU7XG4gICAgICAgICAgICAgICAgICBpZiAoc2VsZWN0ZWRPYmplY3RzLmxlbmd0aCA+IDEpIHVwZGF0ZU9iamVjdHNGcm9tR3JvdXAoKTtcbiAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgIG9uT2JqZWN0Q2hhbmdlPXsoKSA9PiB7XG4gICAgICAgICAgICAgICAgICBpZiAoc2VsZWN0ZWRPYmplY3RzLmxlbmd0aCA+IDEpIHVwZGF0ZU9iamVjdHNGcm9tR3JvdXAoKTtcbiAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIHtncm91bmRUeXBlID09PSBcImdyaWRcIiAmJiAoXG4gICAgICAgICAgICAgIDxncmlkSGVscGVyIGFyZ3M9e1s1MCwgNTAsIFwiIzY2NjY2NlwiLCBcIiMzMzMzMzNcIl19IHJlY2VpdmVTaGFkb3cgLz5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgICB7Z3JvdW5kVHlwZSA9PT0gXCJzb2xpZFwiICYmIChcbiAgICAgICAgICAgICAgPG1lc2hcbiAgICAgICAgICAgICAgICByb3RhdGlvbi14PXstTWF0aC5QSSAvIDJ9XG4gICAgICAgICAgICAgICAgcmVjZWl2ZVNoYWRvd1xuICAgICAgICAgICAgICAgIHBvc2l0aW9uPXtbMCwgLTAuMDEsIDBdfVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPHBsYW5lR2VvbWV0cnkgYXJncz17WzIwMCwgMjAwXX0gLz5cbiAgICAgICAgICAgICAgICA8bWVzaFN0YW5kYXJkTWF0ZXJpYWxcbiAgICAgICAgICAgICAgICAgIGNvbG9yPVwiIzVkNWQ1ZFwiXG4gICAgICAgICAgICAgICAgICB3aXJlZnJhbWU9e3dpcmVmcmFtZX1cbiAgICAgICAgICAgICAgICAgIHRyYW5zcGFyZW50PXt0cnVlfVxuICAgICAgICAgICAgICAgICAgb3BhY2l0eT17MC43fVxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIDwvbWVzaD5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgICB7Z3JvdW5kVHlwZSA9PT0gXCJyZWZsZWN0aXZlXCIgJiYgKFxuICAgICAgICAgICAgICA8bWVzaFxuICAgICAgICAgICAgICAgIHJlY2VpdmVTaGFkb3dcbiAgICAgICAgICAgICAgICByb3RhdGlvbj17Wy1NYXRoLlBJIC8gMiwgMCwgMF19XG4gICAgICAgICAgICAgICAgcG9zaXRpb249e1swLCAtMC4wMSwgMF19XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8cGxhbmVHZW9tZXRyeSBhcmdzPXtbODAsIDgwXX0gLz5cbiAgICAgICAgICAgICAgICA8TWVzaFJlZmxlY3Rvck1hdGVyaWFsXG4gICAgICAgICAgICAgICAgICBibHVyPXtbODAwLCA4MDBdfVxuICAgICAgICAgICAgICAgICAgcmVzb2x1dGlvbj17MjA0OH1cbiAgICAgICAgICAgICAgICAgIG1peEJsdXI9ezAuNX1cbiAgICAgICAgICAgICAgICAgIG1peFN0cmVuZ3RoPXsxMH1cbiAgICAgICAgICAgICAgICAgIHJvdWdobmVzcz17MX1cbiAgICAgICAgICAgICAgICAgIGRlcHRoU2NhbGU9ezF9XG4gICAgICAgICAgICAgICAgICBtaW5EZXB0aFRocmVzaG9sZD17MC40fVxuICAgICAgICAgICAgICAgICAgbWF4RGVwdGhUaHJlc2hvbGQ9ezEuMn1cbiAgICAgICAgICAgICAgICAgIGNvbG9yPVwiIzM2MzY0M1wiXG4gICAgICAgICAgICAgICAgICBtZXRhbG5lc3M9ezAuMDF9XG4gICAgICAgICAgICAgICAgICBtaXJyb3I9ezAuM31cbiAgICAgICAgICAgICAgICAgIHRyYW5zcGFyZW50PXt0cnVlfVxuICAgICAgICAgICAgICAgICAgb3BhY2l0eT17MC41fVxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIDwvbWVzaD5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgICB7Z3JvdW5kVHlwZSA9PT0gXCJub25lXCIgJiYgKFxuICAgICAgICAgICAgICA8bWVzaFxuICAgICAgICAgICAgICAgIHJlY2VpdmVTaGFkb3dcbiAgICAgICAgICAgICAgICByb3RhdGlvbj17Wy1NYXRoLlBJIC8gMiwgMCwgMF19XG4gICAgICAgICAgICAgICAgcG9zaXRpb249e1swLCAtMC4wMSwgMF19XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8cGxhbmVHZW9tZXRyeSBhcmdzPXtbMjAwLCAyMDBdfSAvPlxuICAgICAgICAgICAgICAgIDxwcmltaXRpdmVcbiAgICAgICAgICAgICAgICAgIG9iamVjdD17XG4gICAgICAgICAgICAgICAgICAgIG5ldyBUSFJFRS5TaGFkb3dNYXRlcmlhbCh7XG4gICAgICAgICAgICAgICAgICAgICAgb3BhY2l0eTogMC4xLFxuICAgICAgICAgICAgICAgICAgICAgIGNvbG9yOiBuZXcgVEhSRUUuQ29sb3IoMHgwMDAwMDApLFxuICAgICAgICAgICAgICAgICAgICAgIHRyYW5zcGFyZW50OiB0cnVlLFxuICAgICAgICAgICAgICAgICAgICB9KVxuICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIDwvbWVzaD5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgICA8R2l6bW9IZWxwZXIgYWxpZ25tZW50PVwiYm90dG9tLXJpZ2h0XCIgbWFyZ2luPXtbODAsIDgwXX0+XG4gICAgICAgICAgICAgIDxHaXptb1ZpZXdwb3J0XG4gICAgICAgICAgICAgICAgbGFiZWxDb2xvcj1cIndoaXRlXCJcbiAgICAgICAgICAgICAgICBheGlzQ29sb3JzPXtbXCJyZWRcIiwgXCJncmVlblwiLCBcImJsdWVcIl19XG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8L0dpem1vSGVscGVyPlxuXG4gICAgICAgICAgICA8cHJpbWl0aXZlIG9iamVjdD17c2VsZWN0aW9uR3JvdXBSZWYuY3VycmVudH0gLz5cblxuICAgICAgICAgICAgPFBvc3RQcm9jZXNzaW5nRWZmZWN0c1xuICAgICAgICAgICAgICBlbmFibGVkPXtwb3N0UHJvY2Vzc2luZ0VuYWJsZWR9XG4gICAgICAgICAgICAgIHNldHRpbmdzPXtwb3N0UHJvY2Vzc2luZ1NldHRpbmdzfVxuICAgICAgICAgICAgLz5cbiAgICAgICAgICA8L1N1c3BlbnNlPlxuICAgICAgICA8L0NhbnZhcz5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7c2hvd01vZGVsU3RhdHMgJiYgPE1vZGVsU3RhdHMgc3RhdHM9e21vZGVsU3RhdHN9IC8+fVxuICAgIDwvPlxuICApO1xufVxuIl0sIm5hbWVzIjpbIkdpem1vSGVscGVyIiwiR2l6bW9WaWV3cG9ydCIsIkh0bWwiLCJMb2FkZXIiLCJNZXNoUmVmbGVjdG9yTWF0ZXJpYWwiLCJPcmJpdENvbnRyb2xzIiwiVHJhbnNmb3JtQ29udHJvbHMiLCJDYW52YXMiLCJ1c2VUaHJlZSIsIlN1c3BlbnNlIiwidXNlQ2FsbGJhY2siLCJ1c2VFZmZlY3QiLCJ1c2VSZWYiLCJ1c2VTdGF0ZSIsIlRvYXN0Q29udGFpbmVyIiwiVEhSRUUiLCJFbnZpcm9ubWVudCIsIkVudmlyb25tZW50UGFuZWwiLCJIZWFkZXIiLCJMaWdodFBhbmVsIiwiTWF0ZXJpYWxQYW5lbCIsIk1vZGVsU3RhdHMiLCJPdXRsaW5lciIsIlBvc3RQcm9jZXNzaW5nUGFuZWwiLCJTY2VuZUxpZ2h0cyIsIlRvb2xiYXIiLCJUcmFuc2Zvcm1QYW5lbCIsInNldENhbWVyYVZpZXciLCJhcHBseUhpc3RvcnlTdGF0ZSIsImNyZWF0ZUhpc3RvcnlTdGF0ZSIsImFwcGx5TWF0ZXJpYWxUb09iamVjdCIsImNhbGN1bGF0ZU1vZGVsU3RhdHMiLCJzZXRTaGFkb3dzT25Nb2RlbCIsInByZWRlZmluZWRBbmltYXRpb25zIiwiRW5ncmF2aW5nUGFuZWwiLCJJbml0aWFsT3ZlcmxheVBhbmVsIiwiTW9kZWxTdGFnZSIsIlBvc3RQcm9jZXNzaW5nRWZmZWN0cyIsIlNlbGVjdGlvbk91dGxpbmUiLCJjcmVhdGVWZXJzaW9uIiwiZ2V0Q3VycmVudFZlcnNpb24iLCJ0b2FzdCIsInVzZVZlcnNpb25TdGF0ZSIsIlNjcmVlbnNob3RUYWtlciIsIm9uU2NyZWVuc2hvdFJlZiIsImdsIiwic2NlbmUiLCJjYW1lcmEiLCJyZW5kZXIiLCJkYXRhVVJMIiwiZG9tRWxlbWVudCIsInRvRGF0YVVSTCIsImxpbmsiLCJkb2N1bWVudCIsImNyZWF0ZUVsZW1lbnQiLCJ0aW1lc3RhbXAiLCJEYXRlIiwidG9JU09TdHJpbmciLCJyZXBsYWNlIiwic3Vic3RyaW5nIiwiZG93bmxvYWQiLCJocmVmIiwiYm9keSIsImFwcGVuZENoaWxkIiwiY2xpY2siLCJzZXRUaW1lb3V0IiwicmVtb3ZlQ2hpbGQiLCJjb25zb2xlIiwibG9nIiwiZXJyb3IiLCJNYXRlcmlhbFBhbmVsV3JhcHBlciIsInJlbmRlcmVyIiwicHJvcHMiLCJSZW5kZXJlclByb3ZpZGVyIiwiY2hpbGRyZW4iLCJvblJlbmRlcmVyUmVhZHkiLCJFZGl0b3JFeHBlcmllbmNlIiwibW9kZWxVcmwiLCJwcm9qZWN0IiwidXNlciIsImFkZFZlcnNpb24iLCJjb25maWdSZWYiLCJ3b3JraW5nQ29uZmlnUmVmIiwic2VsZWN0ZWRNb2RlbCIsInNldFNlbGVjdGVkTW9kZWwiLCJsYXN0U2VsZWN0ZWRNb2RlbCIsInNldExhc3RTZWxlY3RlZE1vZGVsIiwidXBsb2FkZWRNb2RlbCIsInNldFVwbG9hZGVkTW9kZWwiLCJtb2RlbFN0YXRzIiwic2V0TW9kZWxTdGF0cyIsInZlcnRpY2VzIiwidHJpYW5nbGVzIiwibWF0ZXJpYWxzIiwiYW5pbWF0aW9ucyIsImxpZ2h0cyIsInNldExpZ2h0cyIsImlkIiwidHlwZSIsInBvc2l0aW9uIiwiaW50ZW5zaXR5IiwiY29sb3IiLCJuYW1lIiwiY2FzdFNoYWRvdyIsImhlbHBlclZpc2libGUiLCJsaWdodFJlZnMiLCJzZWxlY3RlZE9iamVjdHMiLCJzZXRTZWxlY3RlZE9iamVjdHMiLCJzZWxlY3RlZExpZ2h0Iiwic2V0U2VsZWN0ZWRMaWdodCIsImxpZ2h0UG9zaXRpb25IaXN0b3J5Iiwic2V0TGlnaHRQb3NpdGlvbkhpc3RvcnkiLCJzY2VuZU9iamVjdHMiLCJzZXRTY2VuZU9iamVjdHMiLCJ0cmFuc2Zvcm1Nb2RlIiwic2V0VHJhbnNmb3JtTW9kZSIsInNlbGVjdGlvbkdyb3VwUmVmIiwiR3JvdXAiLCJyaWdodFBhbmVsVGFiIiwic2V0UmlnaHRQYW5lbFRhYiIsImlzUGxhY2luZ0RlY2FsIiwic2V0SXNQbGFjaW5nRGVjYWwiLCJzaG93RGVjYWxEZWJ1ZyIsInNldFNob3dEZWNhbERlYnVnIiwic2hvd0dyaWQiLCJzZXRTaG93R3JpZCIsIndpcmVmcmFtZSIsInNldFdpcmVmcmFtZSIsImdyb3VuZFR5cGUiLCJzZXRHcm91bmRUeXBlIiwiZW52UHJlc2V0Iiwic2V0RW52UHJlc2V0IiwiYmdDb2xvciIsInNldEJnQ29sb3IiLCJzaG93RW52aXJvbm1lbnQiLCJzZXRTaG93RW52aXJvbm1lbnQiLCJzaG93TGlnaHRTcGhlcmVzIiwic2V0U2hvd0xpZ2h0U3BoZXJlcyIsImN1c3RvbUhkcmkiLCJzZXRDdXN0b21IZHJpIiwiZW52SW50ZW5zaXR5Iiwic2V0RW52SW50ZW5zaXR5IiwiZW52Qmx1ciIsInNldEVudkJsdXIiLCJlbnZSb3RhdGlvbiIsInNldEVudlJvdGF0aW9uIiwicG9zdFByb2Nlc3NpbmdFbmFibGVkIiwic2V0UG9zdFByb2Nlc3NpbmdFbmFibGVkIiwicG9zdFByb2Nlc3NpbmdTZXR0aW5ncyIsInNldFBvc3RQcm9jZXNzaW5nU2V0dGluZ3MiLCJhdXRvZm9jdXMiLCJlbmFibGVkIiwiYm9rZWhTY2FsZSIsImJsb29tIiwidGhyZXNob2xkIiwicmFkaXVzIiwiZG9mIiwiZm9jdXNEaXN0YW5jZSIsImFwZXJ0dXJlIiwidmlnbmV0dGUiLCJkYXJrbmVzcyIsIm9mZnNldCIsImluaXRpYWxTdGF0ZVJlZiIsImNhbWVyYVBvc2l0aW9uIiwiY2FtZXJhVGFyZ2V0IiwibW9kZWxJbml0aWFsU3RhdGVSZWYiLCJvYmplY3RzIiwiY3VycmVudCIsIlZlY3RvcjMiLCJvcmJpdENvbnRyb2xzUmVmIiwic2NlbmVSZWYiLCJzZXRSZW5kZXJlciIsImhpc3RvcnkiLCJzZXRIaXN0b3J5IiwiaGlzdG9yeUluZGV4Iiwic2V0SGlzdG9yeUluZGV4IiwiaXNVbmRvUmVkb0FjdGlvbiIsInNldElzVW5kb1JlZG9BY3Rpb24iLCJzaG93TW9kZWxTdGF0cyIsInNldFNob3dNb2RlbFN0YXRzIiwiYWRkVG9IaXN0b3J5Iiwic3RhdGUiLCJuZXdTdGF0ZSIsIm5ld0hpc3RvcnkiLCJzbGljZSIsInVuZG9BY3Rpb24iLCJwcmV2U3RhdGUiLCJyZWRvQWN0aW9uIiwibGVuZ3RoIiwibmV4dFN0YXRlIiwidHJhY2tDaGFuZ2VzIiwidGltZW91dElkIiwiY2xlYXJUaW1lb3V0IiwiaGFuZGxlT2JqZWN0Q2xpY2siLCJlIiwic3RvcFByb3BhZ2F0aW9uIiwib2JqZWN0Iiwic2hpZnRLZXkiLCJwcmV2IiwicHJldkFycmF5IiwiQXJyYXkiLCJpc0FycmF5IiwiZmlsdGVyIiwiQm9vbGVhbiIsImluY2x1ZGVzIiwib2JqIiwibWFwIiwidXNlckRhdGEiLCJoYW5kbGVQb2ludGVyTWlzc2VkIiwicGFyZW50IiwicmVtb3ZlIiwib2JqZWN0c0FycmF5IiwiY2VudGVyIiwiZm9yRWFjaCIsImFkZCIsImRpdmlkZVNjYWxhciIsImNvcHkiLCJjbG9uZSIsInRyYXZlcnNlIiwiY2hpbGQiLCJpc01lc2giLCJ2aXNpYmxlIiwic3ViIiwidXBkYXRlT2JqZWN0c0Zyb21Hcm91cCIsImluZGV4Iiwid29ybGRQb3MiLCJnZXRXb3JsZFBvc2l0aW9uIiwicm90YXRpb24iLCJzY2FsZSIsImhhbmRsZVNjZW5lVXBkYXRlIiwic3RhdHMiLCJpbml0aWFsT2JqZWN0cyIsInB1c2giLCJ1dWlkIiwibWF0ZXJpYWwiLCJnZXRIZXgiLCJyb3VnaG5lc3MiLCJtZXRhbG5lc3MiLCJ0cmFuc3BhcmVudCIsIm9wYWNpdHkiLCJoYW5kbGVMaWdodE1vdmVkIiwibGlnaHRJZCIsIm5ld1Bvc2l0aW9uIiwidW5kb0xpZ2h0TW92ZSIsImxhc3RQb3NpdGlvbiIsImhhbmRsZUtleURvd24iLCJldmVudCIsImN0cmxLZXkiLCJrZXkiLCJwcmV2ZW50RGVmYXVsdCIsIndpbmRvdyIsImFkZEV2ZW50TGlzdGVuZXIiLCJyZW1vdmVFdmVudExpc3RlbmVyIiwiVVJMIiwicmV2b2tlT2JqZWN0VVJMIiwiaGFuZGxlQ2FtZXJhVmlldyIsInZpZXciLCJwYW5lbFJlZiIsImFjdGl2ZUFuaW1hdGlvbiIsInNldEFjdGl2ZUFuaW1hdGlvbiIsImFuaW1hdGlvblN0YXJ0VGltZSIsInNldEFuaW1hdGlvblN0YXJ0VGltZSIsInJlcXVlc3RSZWYiLCJhbmltYXRlQ2FtZXJhIiwiYW5pbWF0aW9uIiwiZmluZCIsImEiLCJlbGFwc2VkIiwicHJvZ3Jlc3MiLCJkdXJhdGlvbiIsInRhcmdldCIsImFuaW1hdGUiLCJyZXF1ZXN0QW5pbWF0aW9uRnJhbWUiLCJjYW5jZWxBbmltYXRpb25GcmFtZSIsImhhbmRsZVBsYXlBbmltYXRpb24iLCJhbmltYXRpb25JZCIsImdldEVudmlyb25tZW50UHJlc2V0IiwiaGFuZGxlUmVzZXQiLCJzYXZlZFNjZW5lIiwibG9jYWxTdG9yYWdlIiwiZ2V0SXRlbSIsImNvbmZpcm1SZXNldCIsImNvbmZpcm0iLCJyZW1vdmVJdGVtIiwic2V0IiwidXBkYXRlIiwic2V0VXNlRGlhbW9uZFNoYWRlciIsInNldFVzZVByZW1pdW1XaGl0ZUdvbGQiLCJzZXRVc2VQcmVtaXVtUm9zZUdvbGQiLCJzY3JlZW5zaG90UmVmIiwiY2FwdHVyZVNjcmVlbnNob3QiLCJsb2FkU2F2ZWRTY2VuZSIsInNjZW5lQ29uZmlnIiwiSlNPTiIsInBhcnNlIiwibW9kZWwiLCJlbnZpcm9ubWVudCIsInByZXNldCIsImJsdXIiLCJwb3N0UHJvY2Vzc2luZyIsInNldHRpbmdzIiwic2NlbmVDb250cm9scyIsImZyb21BcnJheSIsImhhbmRsZUxvYWRTY2VuZSIsInJlc3RvcmVNYXRlcmlhbHMiLCJzYXZlZE1hdGVyaWFsc01hcCIsIk1hcCIsIm0iLCJoYXMiLCJzYXZlZE1hdGVyaWFsIiwiZ2V0IiwiY2hlY2tJbnRlcnZhbCIsInNldEludGVydmFsIiwiY2xlYXJJbnRlcnZhbCIsImNoZWNrU2NlbmVSZWFkeSIsImNvbmZpZ1N0YXRlIiwic2V0Q29uZmlnU3RhdGUiLCJhcHBseU1hdGVyaWFscyIsIm1hdGVyaWFsQ29uZmlnIiwiY29uZmlnIiwiaGFuZGxlU2F2ZVNjZW5lIiwic2NlbmVOYW1lIiwiZGVzY3JpcHRpb24iLCJzYXZlVHlwZSIsImxpZ2h0IiwidG9BcnJheSIsImdldEhleFN0cmluZyIsImVudk1hcEludGVuc2l0eSIsImFiZXJyYXRpb25TdHJlbmd0aCIsInRvbmVNYXBwZWQiLCJpb3IiLCJjb2xvckVudk1hcFJvdFkiLCJzZXRJdGVtIiwic3RyaW5naWZ5IiwidmVyc2lvbiIsInVzZXJJZCIsIndvcmtzcGFjZUlkIiwiX2lkIiwic3VjY2VzcyIsImVyciIsImJsb2IiLCJCbG9iIiwidXJsIiwiY3JlYXRlT2JqZWN0VVJMIiwiZW5ncmF2aW5nVGV4dCIsInNldEVuZ3JhdmluZ1RleHQiLCJkZWNhbFBvc2l0aW9uIiwic2V0RGVjYWxQb3NpdGlvbiIsImRlY2FsU2NhbGUiLCJzZXREZWNhbFNjYWxlIiwiZGVjYWxSb3RhdGlvbiIsInNldERlY2FsUm90YXRpb24iLCJNYXRoIiwiUEkiLCJkZWNhbENvbG9yIiwic2V0RGVjYWxDb2xvciIsInNlbGVjdGVkRm9udCIsInNldFNlbGVjdGVkRm9udCIsInVzZURpYW1vbmRTaGFkZXIiLCJ1c2VQcmVtaXVtV2hpdGVHb2xkIiwidXNlUHJlbWl1bVJvc2VHb2xkIiwiaGFuZGxlRW5ncmF2ZVRleHQiLCJ0ZXh0IiwiaGFuZGxlRm9udENoYW5nZSIsImZvbnQiLCJzYXZlZEVuZ3JhdmluZ1RleHQiLCJoYW5kbGVEZWNhbFBsYWNlbWVudCIsImdldERlZmF1bHREZWNhbFBvc2l0aW9uIiwibW9kZWxJZCIsIm1vZGVsQ29uZmlncyIsImNob3NlbnJpbmciLCJ0ZXN0cmluZyIsImFnYXBlcmluZzEiLCJhZ2FwZXJpbmcyIiwicmluZ2EiLCJyaW5nYiIsInJpbmdjIiwiZ2V0RGVmYXVsdERlY2FsUm90YXRpb24iLCJnZXREZWZhdWx0RGVjYWxTY2FsZSIsImNhbGN1bGF0ZUR5bmFtaWNTY2FsZSIsImJhc2VTY2FsZSIsInRleHRMZW5ndGgiLCJzY2FsZVgiLCJzY2FsZVkiLCJzY2FsZVoiLCJzY2FsZU11bHRpcGxpZXIiLCJtaW4iLCJtYXhTY2FsZSIsImxpbWl0cyIsImR5bmFtaWNTY2FsZSIsImlzRnVsbHNjcmVlbiIsInNldElzRnVsbHNjcmVlbiIsImlzTW9iaWxlIiwic2V0SXNNb2JpbGUiLCJjaGVja01vYmlsZSIsImlubmVyV2lkdGgiLCJvdXRsaW5lckNvbGxhcHNlZCIsInNldE91dGxpbmVyQ29sbGFwc2VkIiwiZ2V0Q2FtZXJhUG9zaXRpb24iLCJmZXRjaEFuZEFwcGx5Q3VycmVudFZlcnNpb24iLCJjdXJyZW50VmVyc2lvbiIsIm9uUmVzZXQiLCJvblNjcmVlbnNob3QiLCJvblNhdmVTY2VuZSIsIm9uTG9hZFNjZW5lIiwib25Ub2dnbGVGdWxsc2NyZWVuIiwiZGl2IiwiY2xhc3NOYW1lIiwib3JpZW50YXRpb24iLCJyZWYiLCJvblJlc2V0TGlnaHRzIiwiZGVmYXVsdExpZ2h0cyIsIm9uRW5ncmF2ZVRleHQiLCJvbkRlY2FsUG9zaXRpb25DaGFuZ2UiLCJvbkRlY2FsU2NhbGVDaGFuZ2UiLCJvbkRlY2FsUm90YXRpb25DaGFuZ2UiLCJvbkRlY2FsQ29sb3JDaGFuZ2UiLCJvbkZvbnRDaGFuZ2UiLCJzaG93RGVidWciLCJvblNob3dEZWJ1Z0NoYW5nZSIsIm9uU2VsZWN0T2JqZWN0Iiwib25DYW1lcmFWaWV3Iiwib25QbGF5QW5pbWF0aW9uIiwiY29sbGFwc2VkIiwic2V0Q29sbGFwc2VkIiwic3R5bGUiLCJiYWNrZ3JvdW5kIiwiZm92IiwibmVhciIsImZhciIsInNoYWRvd3MiLCJhbnRpYWxpYXMiLCJwcmVzZXJ2ZURyYXdpbmdCdWZmZXIiLCJvblBvaW50ZXJNaXNzZWQiLCJyYXljYXN0ZXIiLCJjb21wdXRlT2Zmc2V0cyIsIm9mZnNldFgiLCJjbGllbnRYIiwib2Zmc2V0WSIsImNsaWVudFkiLCJmcmFtZWxvb3AiLCJmYWxsYmFjayIsImZuIiwib25MaWdodFNlbGVjdCIsIm9uTGlnaHRNb3ZlZCIsInRocmVlUG9pbnRMaWdodGluZyIsIm9uRGVjYWxQbGFjZW1lbnQiLCJtYWtlRGVmYXVsdCIsImVuYWJsZURhbXBpbmciLCJtYXhEaXN0YW5jZSIsIm1vZGUiLCJvbk1vdXNlRG93biIsIm9uTW91c2VVcCIsIm9uT2JqZWN0Q2hhbmdlIiwiZ3JpZEhlbHBlciIsImFyZ3MiLCJyZWNlaXZlU2hhZG93IiwibWVzaCIsInJvdGF0aW9uLXgiLCJwbGFuZUdlb21ldHJ5IiwibWVzaFN0YW5kYXJkTWF0ZXJpYWwiLCJyZXNvbHV0aW9uIiwibWl4Qmx1ciIsIm1peFN0cmVuZ3RoIiwiZGVwdGhTY2FsZSIsIm1pbkRlcHRoVGhyZXNob2xkIiwibWF4RGVwdGhUaHJlc2hvbGQiLCJtaXJyb3IiLCJwcmltaXRpdmUiLCJTaGFkb3dNYXRlcmlhbCIsIkNvbG9yIiwiYWxpZ25tZW50IiwibWFyZ2luIiwibGFiZWxDb2xvciIsImF4aXNDb2xvcnMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ArtistTool/EditorExperience.jsx\n"));

/***/ })

});