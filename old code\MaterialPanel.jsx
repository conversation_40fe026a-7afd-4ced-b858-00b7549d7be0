"use client";

import { useState, useEffect } from "react";
import * as THREE from "three";
import { ChromePicker } from "react-color";
import { DiamondShader } from "../utils/refraction_materials/Diamond_Shader.js";
import { useHistoryDebounce } from "../hooks/useDebounce";

// Define material presets
const MATERIAL_PRESETS = {
  gems: [
    {
      name: "Diamond",
      color: "#ffffff",
      roughness: 0.05,
      metalness: 0.1,
      clearcoat: 1.0,
      clearcoatRoughness: 0.02,
      transparent: false,
      opacity: 1.0,
      envMapIntensity: 1.5,
    },
    {
      name: "<PERSON>",
      color: "#22dfa3",
      roughness: 0.1,
      metalness: 0.0,
      clearcoat: 0.8,
      clearcoatRoughness: 0.05,
      transparent: true,
      opacity: 0.85,
      envMapIntensity: 1.2,
    },
    {
      name: "Ruby",
      color: "#e14276",
      roughness: 0.08,
      metalness: 0.0,
      clearcoat: 0.9,
      clearcoatRoughness: 0.03,
      transparent: true,
      opacity: 0.9,
      envMapIntensity: 1.3,
    },
  ],
  metals: [
    {
      name: "Platinum",
      color: "#e5e4e2",
      roughness: 0.15,
      metalness: 1.0,
      clearcoat: 0.3,
      clearcoatRoughness: 0.1,
      transparent: false,
      opacity: 1.0,
      envMapIntensity: 1.0,
    },
    {
      name: "Yellow Gold",
      color: "#E5b377",
      roughness: 0.12,
      metalness: 1.0,
      clearcoat: 0.2,
      clearcoatRoughness: 0.08,
      transparent: false,
      opacity: 1.0,
      envMapIntensity: 0.9,
    },
    {
      name: "Rose Gold",
      color: "#e8b4a0",
      roughness: 0.14,
      metalness: 1.0,
      clearcoat: 0.25,
      clearcoatRoughness: 0.09,
      transparent: false,
      opacity: 1.0,
      envMapIntensity: 0.95,
    },
  ],
};

export function MaterialPanel({
  selectedObjects,
  useDiamondShader,
  setUseDiamondShader,
  usePremiumWhiteGold,
  setUsePremiumWhiteGold,
  usePremiumRoseGold,
  setUsePremiumRoseGold,
  forceUpdate,
  setMaterialVersion,
  addToHistory,
}) {
  const [materialType, setMaterialType] = useState("standard");
  const [materialProps, setMaterialProps] = useState({
    color: "#ffffff",
    roughness: 0.5,
    metalness: 0.5,
    clearcoat: 0.5,
    clearcoatRoughness: 0.5,
    wireframe: false,
    transparent: false,
    opacity: 1.0,
  });
  const [displayColorPicker, setDisplayColorPicker] = useState(false);

  // Helper function to check if an object is a decal
  const isDecalObject = (obj) => {
    if (!obj) return false;

    // Check if the object is a decal based on its name or userData
    const isDecalByName =
      obj.name &&
      (obj.name.toLowerCase().includes("decal") ||
        obj.name.toLowerCase().includes("engraving") ||
        obj.name.toLowerCase().includes("text"));

    const isDecalByUserData =
      obj.userData &&
      (obj.userData.isDecal || obj.userData.isEngraving || obj.userData.isText);

    // Check if the object has a parent that's a decal
    const hasDecalParent =
      obj.parent &&
      ((obj.parent.name && obj.parent.name.toLowerCase().includes("decal")) ||
        (obj.parent.userData && obj.parent.userData.isDecal));

    return isDecalByName || isDecalByUserData || hasDecalParent;
  };

  // Helper function to filter out decal objects
  const filterOutDecals = (objects) => {
    return objects.filter((obj) => !isDecalObject(obj));
  };

  // Helper function to check if diamond parts are selected
  const hasDiamondPartsSelected = () => {
    const nonDecalObjects = filterOutDecals(selectedObjects);
    return nonDecalObjects.some(
      (obj) => obj && obj.userData && obj.userData.part === "diamond"
    );
  };

  // Helper function to count diamond parts
  const getDiamondPartsCount = () => {
    const nonDecalObjects = filterOutDecals(selectedObjects);
    return nonDecalObjects.filter(
      (obj) => obj && obj.userData && obj.userData.part === "diamond"
    ).length;
  };

  // Setup debounced history for slider inputs
  const { addToHistoryImmediate, addToHistoryDebounced } = useHistoryDebounce(
    addToHistory || (() => {}),
    900
  );

  // Update local state when selected objects change
  useEffect(() => {
    console.log("MaterialPanel - selectedObjects changed:", selectedObjects);
    console.log(
      "MaterialPanel - selectedObjects length:",
      selectedObjects?.length
    );
    console.log(
      "MaterialPanel - selectedObjects details:",
      selectedObjects?.map((obj) => ({
        name: obj?.name,
        userData: obj?.userData,
        type: obj?.type,
        isMesh: obj?.isMesh,
      }))
    );

    // Ensure selectedObjects is always an array and filter out decals
    const objectsArray = Array.isArray(selectedObjects)
      ? selectedObjects
      : [selectedObjects].filter(Boolean);

    const nonDecalObjects = filterOutDecals(objectsArray);

    if (nonDecalObjects.length === 0) return;

    // Use the first selected non-decal object to determine material type and properties
    const obj = nonDecalObjects[0];
    if (!obj.material) return;

    // Determine material type
    let type = "standard";
    if (obj.material.type === "MeshBasicMaterial") type = "basic";
    else if (obj.material.type === "MeshStandardMaterial") type = "standard";
    else if (obj.material.type === "MeshPhysicalMaterial") type = "physical";
    else if (obj.material.type === "MeshToonMaterial") type = "toon";
    else if (obj.material.type === "MeshNormalMaterial") type = "normal";

    setMaterialType(type);

    // Update material properties
    const props = {
      color: obj.material.color
        ? "#" + obj.material.color.getHexString()
        : "#ffffff",
      roughness:
        obj.material.roughness !== undefined ? obj.material.roughness : 0.5,
      metalness:
        obj.material.metalness !== undefined ? obj.material.metalness : 0.5,
      clearcoat:
        obj.material.clearcoat !== undefined ? obj.material.clearcoat : 0.5,
      clearcoatRoughness:
        obj.material.clearcoatRoughness !== undefined
          ? obj.material.clearcoatRoughness
          : 0.5,
      wireframe: obj.material.wireframe || false,
      transparent: obj.material.transparent || false,
      opacity: obj.material.opacity !== undefined ? obj.material.opacity : 1.0,
    };

    setMaterialProps(props);
  }, [selectedObjects]);

  if (filterOutDecals(selectedObjects).length === 0) {
    return (
      <div className="p-2 text-center text-gray-600">
        Select an object to edit its material
      </div>
    );
  }

  const applyAdvancedPreset = (preset) => {
    console.log("Applying advanced preset:", preset);
    console.log("Selected objects:", selectedObjects);

    if (preset.type === "diamond") {
      // Apply diamond shader by setting the state
      setUseDiamondShader(true);
      setMaterialType("diamond");

      // Also clear other advanced material flags
      setUsePremiumWhiteGold(false);
      setUsePremiumRoseGold(false);
    } else if (preset.type === "premium_metal") {
      // Don't clear diamond shader when applying premium metals - preserve existing state

      if (preset.name === "Premium White Gold") {
        setUsePremiumWhiteGold(true);
        setUsePremiumRoseGold(false);
      } else if (preset.name === "Premium Rose Gold") {
        setUsePremiumRoseGold(true);
        setUsePremiumWhiteGold(false);
      }

      // Update local state
      setMaterialProps(preset);

      // Apply premium material properties to selected objects (excluding decals)
      const nonDecalObjects = filterOutDecals(selectedObjects);
      nonDecalObjects.forEach((obj) => {
        if (!obj || !obj.material) return;

        const material = obj.material;

        // Update color if the material has a color property
        if (material.color) {
          material.color.set(preset.color);
        }

        // Update properties that exist on the material
        if (material.roughness !== undefined) {
          material.roughness = preset.roughness;
        }
        if (material.metalness !== undefined) {
          material.metalness = preset.metalness;
        }
        if (material.envMapIntensity !== undefined) {
          material.envMapIntensity = preset.envMapIntensity || 1.0;
        }

        // Mark material for update
        material.needsUpdate = true;
      });
    }
  };

  const applyPreset = (preset) => {
    if (preset.isAdvanced) {
      applyAdvancedPreset(preset);
      return;
    }

    // Only clear diamond shader when applying gem presets, not metal presets
    const isGemPreset = MATERIAL_PRESETS.gems.some(
      (gem) => gem.name === preset.name
    );
    const isMetalPreset = MATERIAL_PRESETS.metals.some(
      (metal) => metal.name === preset.name
    );

    // Check if we have any diamond parts selected
    const selectedDiamondParts = selectedObjects.filter(
      (obj) => obj && obj.userData && obj.userData.part === "diamond"
    );
    const selectedNonDiamondParts = selectedObjects.filter(
      (obj) => !obj || !obj.userData || obj.userData.part !== "diamond"
    );

    // Only clear diamond shader if we're applying gem presets to non-diamond parts
    // Keep diamond shader active for diamond parts
    if (
      isGemPreset &&
      selectedNonDiamondParts.length > 0 &&
      selectedDiamondParts.length === 0
    ) {
      // Only clear diamond shader if no diamond parts are selected
      setUseDiamondShader(false);
    }

    // Clear premium material flags when applying regular presets (both gems and metals)
    setUsePremiumWhiteGold(false);
    setUsePremiumRoseGold(false);

    // Update local state
    setMaterialProps(preset);

    // Update selected objects (excluding decals)
    const nonDecalObjects = filterOutDecals(selectedObjects);
    nonDecalObjects.forEach((obj) => {
      if (!obj || !obj.material) return;

      // If this is a gem preset being applied to a diamond part, handle diamond shader color
      if (isGemPreset && obj.userData && obj.userData.part === "diamond") {
        // For diamond parts with gem presets, we keep the diamond shader active
        // but we need to update the diamond shader's color property
        console.log(
          `Updating diamond shader color for ${preset.name} on diamond part`
        );

        // Check if the material has diamond shader properties (uniforms)
        if (obj.material.uniforms && obj.material.uniforms.color) {
          obj.material.uniforms.color.value.set(preset.color);
          obj.material.needsUpdate = true;
        }
        // Also try to set color directly if it's available
        else if (obj.material.color) {
          obj.material.color.set(preset.color);
          obj.material.needsUpdate = true;
        }
        return;
      }

      const material = obj.material;

      // Update color if the material has a color property
      if (material.color) {
        material.color.set(preset.color);
      }

      // Update properties that exist on the material
      if (material.roughness !== undefined) {
        material.roughness = preset.roughness;
      }
      if (material.metalness !== undefined) {
        material.metalness = preset.metalness;
      }
      if (material.clearcoat !== undefined) {
        material.clearcoat = preset.clearcoat;
      }
      if (material.clearcoatRoughness !== undefined) {
        material.clearcoatRoughness = preset.clearcoatRoughness;
      }
      if (material.transparent !== undefined) {
        material.transparent = preset.transparent;
      }
      if (material.opacity !== undefined) {
        material.opacity = preset.opacity;
      }
      if (material.envMapIntensity !== undefined) {
        material.envMapIntensity = preset.envMapIntensity || 1.0;
      }

      // Mark material for update
      material.needsUpdate = true;
    });
    if (forceUpdate) forceUpdate();
    if (setMaterialVersion) setMaterialVersion((v) => v + 1);

    // Add to history for preset application
    if (addToHistory) {
      addToHistoryImmediate();
    }
  };

  const updateMaterialProperty = (property, value, isSlider = false) => {
    // Update local state
    setMaterialProps((prev) => ({
      ...prev,
      [property]: value,
    }));

    // Update all selected objects (excluding decals)
    const nonDecalObjects = filterOutDecals(selectedObjects);
    nonDecalObjects.forEach((obj) => {
      if (obj && obj.material) {
        // Special handling for diamond parts with diamond shader
        if (
          property === "color" &&
          obj.userData &&
          obj.userData.part === "diamond" &&
          useDiamondShader
        ) {
          // Try to update diamond shader color uniform
          if (obj.material.uniforms && obj.material.uniforms.color) {
            obj.material.uniforms.color.value.set(value);
          }
          // Also try to set color directly
          else if (obj.material.color) {
            obj.material.color.set(value);
          }
        } else {
          // Standard material property update
          if (property === "color") {
            obj.material.color = new THREE.Color(value);
          } else {
            obj.material[property] = value;
          }
        }
        obj.material.needsUpdate = true;
      }
    });
    if (forceUpdate) forceUpdate();
    if (setMaterialVersion) setMaterialVersion((v) => v + 1);

    // Add to history with appropriate debouncing
    if (addToHistory) {
      if (isSlider) {
        addToHistoryDebounced();
      } else {
        addToHistoryImmediate();
      }
    }
  };

  const changeMaterialType = (type) => {
    // Check if we have any diamond parts selected
    const selectedDiamondObjects = selectedObjects.filter(
      (obj) => obj && obj.userData && obj.userData.part === "diamond"
    );

    if (type === "diamond") {
      // If diamond type is selected and we have diamond parts selected, apply diamond shader to all gems
      if (selectedDiamondObjects.length > 0) {
        setUseDiamondShader(true);
        console.log("Applying diamond shader to all gems");
      }
    } else {
      // If switching away from diamond type and diamond objects are selected, clear diamond shader
      if (selectedDiamondObjects.length > 0) {
        setUseDiamondShader(false);
      }
    }

    // Clear premium material flags when changing material type
    setUsePremiumWhiteGold(false);
    setUsePremiumRoseGold(false);

    // Update local state
    setMaterialType(type);

    // Update selected objects, but be careful with diamond parts (excluding decals)
    const nonDecalObjects = filterOutDecals(selectedObjects);
    nonDecalObjects.forEach((obj) => {
      if (!obj) return;

      // If this is a diamond part and we're trying to keep diamond shader, skip material change
      if (
        obj.userData &&
        obj.userData.part === "diamond" &&
        useDiamondShader &&
        type === "diamond"
      ) {
        return;
      }

      const currentColor =
        obj.material && obj.material.color
          ? obj.material.color.getHex()
          : 0xffffff;

      let newMaterial;

      switch (type) {
        case "basic":
          newMaterial = new THREE.MeshBasicMaterial({ color: currentColor });
          break;
        case "standard":
          newMaterial = new THREE.MeshStandardMaterial({
            color: currentColor,
            roughness: materialProps.roughness,
            metalness: materialProps.metalness,
          });
          break;
        case "physical":
          newMaterial = new THREE.MeshPhysicalMaterial({
            color: currentColor,
            roughness: materialProps.roughness,
            metalness: materialProps.metalness,
            clearcoat: materialProps.clearcoat,
            clearcoatRoughness: materialProps.clearcoatRoughness,
          });
          break;
        case "toon":
          newMaterial = new THREE.MeshToonMaterial({ color: currentColor });
          break;
        case "normal":
          newMaterial = new THREE.MeshNormalMaterial();
          break;
        case "diamond":
          // For diamond type, don't create a new material here - let the diamond shader handle it
          return;
        default:
          newMaterial = new THREE.MeshStandardMaterial({ color: currentColor });
      }

      // Copy other properties
      newMaterial.wireframe = materialProps.wireframe;
      newMaterial.transparent = materialProps.transparent;
      newMaterial.opacity = materialProps.opacity;

      obj.material = newMaterial;
    });
    if (forceUpdate) forceUpdate();
    if (setMaterialVersion) setMaterialVersion((v) => v + 1);

    // Add to history for material type changes
    if (addToHistory) {
      addToHistoryImmediate();
    }
  };

  const handleColorClick = () => {
    setDisplayColorPicker(!displayColorPicker);
  };

  const handleColorClose = () => {
    setDisplayColorPicker(false);
  };

  const handleColorChange = (color) => {
    const hexColor = color.hex;
    setMaterialProps((prev) => ({ ...prev, color: hexColor }));
    updateMaterialProperty("color", hexColor);
  };

  return (
    <div className="w-full overflow-y-auto no-scrollbar text-[10px] md:text-xs">
      <div className="mb-4">
        <div className="mb-2 border-b border-gray-700 text-[#FDE9CE]">
          Material{" "}
          {(() => {
            const nonDecalObjects = filterOutDecals(selectedObjects);
            if (nonDecalObjects.length > 1) {
              return `(${nonDecalObjects.length} objects)`;
            } else if (nonDecalObjects.length === 1) {
              return `(${nonDecalObjects[0].name || "Unnamed object"})`;
            }
            return "";
          })()}
        </div>

        {/* Material Presets Section */}
        <div className="mb-4 p-4 bg-gradient-to-br from-[#2A2D3A] to-[#1A1D2A] rounded-xl border border-[#A3A3A3]/10 shadow-lg">
          <h3 className="text-[#FDE9CE] font-medium mb-4 text-center text-xs md:text-sm tracking-wide">
            Material Presets
          </h3>

          {/* Gems Section */}
          <div className="mb-5">
            <h4 className="text-[#A3A3A3] text-[10px] uppercase tracking-wider mb-3 font-semibold flex items-center">
              <span className="w-2 h-2 rounded-full bg-emerald-500 mr-2"></span>
              Gems
            </h4>
            <div className="grid grid-cols-3 gap-3">
              {MATERIAL_PRESETS.gems.map((preset) => (
                <button
                  key={preset.name}
                  onClick={() => applyPreset(preset)}
                  className="group relative overflow-hidden rounded-xl border border-[#A3A3A3]/20 hover:border-[#FDE9CE]/50 transition-all duration-300 transform hover:scale-105 hover:shadow-lg hover:shadow-[#FDE9CE]/5"
                >
                  <div
                    className="h-10 w-full relative"
                    style={{
                      background: `linear-gradient(135deg, ${preset.color} 0%, ${preset.color}dd 50%, ${preset.color}bb 100%)`,
                      boxShadow: `inset 0 1px 2px rgba(255,255,255,0.3), inset 0 -1px 2px rgba(0,0,0,0.3)`,
                    }}
                  >
                    <div className="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent opacity-60"></div>
                    {preset.transparent && (
                      <div className="absolute inset-0 bg-gradient-to-t from-black/10 to-transparent"></div>
                    )}
                    <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      <span className="text-[10px] font-bold text-white bg-black/30 px-2 py-1 rounded-full">
                        Apply
                      </span>
                    </div>
                  </div>
                  <div className="p-1.5 bg-gradient-to-b from-[#2A2D3A] to-[#1F222F]">
                    <span className="text-[10px] text-[#FDE9CE] group-hover:text-white transition-colors duration-200 font-medium">
                      {preset.name}
                    </span>
                  </div>
                </button>
              ))}
            </div>
          </div>

          {/* Metals Section */}
          <div>
            <h4 className="text-[#A3A3A3] text-[10px] uppercase tracking-wider mb-3 font-semibold flex items-center">
              <span className="w-2 h-2 rounded-full bg-amber-500 mr-2"></span>
              Metals
            </h4>
            <div className="grid grid-cols-3 gap-3">
              {MATERIAL_PRESETS.metals.map((preset) => (
                <button
                  key={preset.name}
                  onClick={() => applyPreset(preset)}
                  className="group relative overflow-hidden rounded-xl border border-[#A3A3A3]/20 hover:border-[#FDE9CE]/50 transition-all duration-300 transform hover:scale-105 hover:shadow-lg hover:shadow-[#FDE9CE]/5"
                >
                  <div
                    className="h-10 w-full relative"
                    style={{
                      background: `linear-gradient(135deg, ${preset.color} 0%, ${preset.color}cc 50%, ${preset.color}aa 100%)`,
                      boxShadow: `inset 0 1px 2px rgba(255,255,255,0.4), inset 0 -1px 2px rgba(0,0,0,0.4)`,
                    }}
                  >
                    <div className="absolute inset-0 bg-gradient-to-br from-white/30 to-transparent opacity-50"></div>
                    <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-30"></div>
                    <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      <span className="text-[10px] font-bold text-white bg-black/30 px-2 py-1 rounded-full">
                        Apply
                      </span>
                    </div>
                  </div>
                  <div className="p-1.5 bg-gradient-to-b from-[#2A2D3A] to-[#1F222F]">
                    <span className="text-[10px] text-[#FDE9CE] group-hover:text-white transition-colors duration-200 font-medium text-nowrap">
                      {preset.name}
                    </span>
                  </div>
                </button>
              ))}
            </div>
          </div>
        </div>

        <div className="mb-3 w-[95%]">
          <label className="text-[#FDE9CE]">Material Type</label>
          <select
            value={materialType}
            onChange={(e) => changeMaterialType(e.target.value)}
            className="ml-2 bg-[#1A1A1A] border border-gray-700 rounded px-2 py-1 w-full mt-1 text-[#FDE9CE]"
          >
            <option value="standard">Standard</option>
            <option value="physical">Physical</option>
            <option value="basic">Basic</option>
            <option value="toon">Toon</option>
            <option value="normal">Normal</option>
            <option value="diamond">Diamond Shader</option>
          </select>
        </div>

        <div className="mb-3">
          <label className="text-[#FDE9CE]">Color</label>
          <div className="flex items-center gap-2 mt-1">
            <div className="relative">
              <div
                className="w-12 h-12 rounded cursor-pointer border border-gray-700"
                style={{ backgroundColor: materialProps.color || "#ffffff" }}
                onClick={handleColorClick}
              />
              {displayColorPicker && (
                <div style={{ position: "absolute", zIndex: "2" }}>
                  <div
                    style={{
                      position: "fixed",
                      top: "0px",
                      right: "0px",
                      bottom: "0px",
                      left: "0px",
                    }}
                    onClick={handleColorClose}
                  />
                  <ChromePicker
                    color={materialProps.color || "#ffffff"}
                    onChange={handleColorChange}
                    disableAlpha
                  />
                </div>
              )}
            </div>
            <input
              type="text"
              value={materialProps.color || "#ffffff"}
              onChange={(e) => {
                const value = e.target.value;
                setMaterialProps((prev) => ({ ...prev, color: value }));
                if (/^#[0-9A-Fa-f]{6}$/.test(value)) {
                  updateMaterialProperty("color", value);
                }
              }}
              onBlur={(e) => {
                const value = e.target.value;
                if (!/^#[0-9A-Fa-f]{6}$/.test(value)) {
                  setMaterialProps((prev) => ({
                    ...prev,
                    color: materialProps.color || "#ffffff",
                  }));
                }
              }}
              className="bg-[#1A1A1A] border border-gray-700 rounded px-2 py-1 w-24 text-[#FDE9CE]"
              placeholder="#FFFFFF"
            />
          </div>
        </div>

        {(materialType === "standard" || materialType === "physical") && (
          <>
            <div className="mb-3">
              <label className="text-[#FDE9CE]">Roughness</label>
              <div className="flex items-center gap-2 mt-1">
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.01"
                  value={materialProps.roughness || 0.5}
                  onChange={(e) =>
                    updateMaterialProperty(
                      "roughness",
                      parseFloat(e.target.value),
                      true
                    )
                  }
                  className="w-full accent-[#FDE9CE]"
                />
                <span className="w-10 text-right text-[#FDE9CE]">
                  {(materialProps.roughness || 0.5).toFixed(2)}
                </span>
              </div>
            </div>

            <div className="mb-3">
              <label className="text-[#FDE9CE]">Metalness</label>
              <div className="flex items-center gap-2 mt-1">
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.01"
                  value={materialProps.metalness || 0.5}
                  onChange={(e) =>
                    updateMaterialProperty(
                      "metalness",
                      parseFloat(e.target.value),
                      true
                    )
                  }
                  className="w-full accent-[#FDE9CE]"
                />
                <span className="w-10 text-right text-[#FDE9CE]">
                  {(materialProps.metalness || 0.5).toFixed(2)}
                </span>
              </div>
            </div>
          </>
        )}

        {materialType === "physical" && (
          <>
            <div className="mb-3">
              <label className="text-[#FDE9CE]">Clearcoat</label>
              <div className="flex items-center gap-2 mt-1">
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.01"
                  value={materialProps.clearcoat || 0.5}
                  onChange={(e) =>
                    updateMaterialProperty(
                      "clearcoat",
                      parseFloat(e.target.value),
                      true
                    )
                  }
                  className="w-full accent-[#FDE9CE]"
                />
                <span className="w-10 text-right text-[#FDE9CE]">
                  {(materialProps.clearcoat || 0.5).toFixed(2)}
                </span>
              </div>
            </div>

            <div className="mb-3">
              <label className="text-[#FDE9CE]">Clearcoat Roughness</label>
              <div className="flex items-center gap-2 mt-1">
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.01"
                  value={materialProps.clearcoatRoughness || 0.5}
                  onChange={(e) =>
                    updateMaterialProperty(
                      "clearcoatRoughness",
                      parseFloat(e.target.value),
                      true
                    )
                  }
                  className="w-full accent-[#FDE9CE]"
                />
                <span className="w-10 text-right text-[#FDE9CE]">
                  {(materialProps.clearcoatRoughness || 0.5).toFixed(2)}
                </span>
              </div>
            </div>
          </>
        )}

        <div className="mb-3">
          <label className="flex items-center gap-2 text-[#FDE9CE]">
            <input
              type="checkbox"
              checked={materialProps.wireframe || false}
              onChange={(e) =>
                updateMaterialProperty("wireframe", e.target.checked)
              }
              className="accent-[#FDE9CE]"
            />
            Wireframe
          </label>
        </div>

        <div className="mb-3">
          <label className="flex items-center gap-2 text-[#FDE9CE]">
            <input
              type="checkbox"
              checked={materialProps.transparent || false}
              onChange={(e) =>
                updateMaterialProperty("transparent", e.target.checked)
              }
              className="accent-[#FDE9CE]"
            />
            Transparent
          </label>
        </div>

        {materialProps.transparent && (
          <div className="mb-3">
            <label className="text-[#FDE9CE]">Opacity</label>
            <div className="flex items-center gap-2 mt-1">
              <input
                type="range"
                min="0"
                max="1"
                step="0.01"
                value={materialProps.opacity || 1.0}
                onChange={(e) =>
                  updateMaterialProperty(
                    "opacity",
                    parseFloat(e.target.value),
                    true
                  )
                }
                className="w-full accent-[#FDE9CE]"
              />
              <span className="w-10 text-right text-[#FDE9CE]">
                {(materialProps.opacity || 1.0).toFixed(2)}
              </span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
