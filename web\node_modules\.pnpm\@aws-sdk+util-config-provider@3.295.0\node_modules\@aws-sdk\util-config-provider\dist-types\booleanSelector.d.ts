export declare enum SelectorType {
    ENV = "env",
    CONFIG = "shared config entry"
}
/**
 * Returns boolean value true/false for string value "true"/"false",
 * if the string is defined in obj[key]
 * Returns undefined, if obj[key] is not defined.
 * Throws error for all other cases.
 *
 * @internal
 */
export declare const booleanSelector: (obj: Record<string, string | undefined>, key: string, type: SelectorType) => boolean | undefined;
