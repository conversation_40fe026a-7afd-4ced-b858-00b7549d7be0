"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/projects/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/ArtistTool/EditorExperience.jsx":
/*!********************************************************!*\
  !*** ./src/components/ArtistTool/EditorExperience.jsx ***!
  \********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ EditorExperience; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/web/Html.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/web/Loader.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/OrbitControls.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/TransformControls.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/MeshReflectorMaterial.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/GizmoHelper.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/GizmoViewport.js\");\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @react-three/fiber */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_three@0.167.1/node_modules/@react-three/fiber/dist/events-776716bd.esm.js\");\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @react-three/fiber */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_three@0.167.1/node_modules/@react-three/fiber/dist/react-three-fiber.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/.pnpm/react-toastify@11.0.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/react-toastify/dist/index.mjs\");\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! three */ \"(app-pages-browser)/./node_modules/.pnpm/three@0.167.1/node_modules/three/build/three.module.js\");\n/* harmony import */ var _Environment__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Environment */ \"(app-pages-browser)/./src/components/ArtistTool/Environment.tsx\");\n/* harmony import */ var _EnvironmentPanel__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./EnvironmentPanel */ \"(app-pages-browser)/./src/components/ArtistTool/EnvironmentPanel.jsx\");\n/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Header */ \"(app-pages-browser)/./src/components/ArtistTool/Header.jsx\");\n/* harmony import */ var _LightPanel__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./LightPanel */ \"(app-pages-browser)/./src/components/ArtistTool/LightPanel.jsx\");\n/* harmony import */ var _MaterialPanel__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./MaterialPanel */ \"(app-pages-browser)/./src/components/ArtistTool/MaterialPanel.jsx\");\n/* harmony import */ var _ModelStats__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./ModelStats */ \"(app-pages-browser)/./src/components/ArtistTool/ModelStats.jsx\");\n/* harmony import */ var _Outliner__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./Outliner */ \"(app-pages-browser)/./src/components/ArtistTool/Outliner.jsx\");\n/* harmony import */ var _PostProcessingPanel__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./PostProcessingPanel */ \"(app-pages-browser)/./src/components/ArtistTool/PostProcessingPanel.jsx\");\n/* harmony import */ var _SceneLights__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./SceneLights */ \"(app-pages-browser)/./src/components/ArtistTool/SceneLights.jsx\");\n/* harmony import */ var _Toolbar__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./Toolbar */ \"(app-pages-browser)/./src/components/ArtistTool/Toolbar.jsx\");\n/* harmony import */ var _TransformPanel__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./TransformPanel */ \"(app-pages-browser)/./src/components/ArtistTool/TransformPanel.jsx\");\n/* harmony import */ var _utils_ArtistTool_cameraUtils__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../../utils/ArtistTool/cameraUtils */ \"(app-pages-browser)/./src/utils/ArtistTool/cameraUtils.js\");\n/* harmony import */ var _utils_ArtistTool_materialUtils__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../../utils/ArtistTool/materialUtils */ \"(app-pages-browser)/./src/utils/ArtistTool/materialUtils.js\");\n/* harmony import */ var _utils_ArtistTool_sceneUtils__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../../utils/ArtistTool/sceneUtils */ \"(app-pages-browser)/./src/utils/ArtistTool/sceneUtils.js\");\n/* harmony import */ var _animations_CameraAnimations__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./animations/CameraAnimations */ \"(app-pages-browser)/./src/components/ArtistTool/animations/CameraAnimations.js\");\n/* harmony import */ var _EngravingPanel__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./EngravingPanel */ \"(app-pages-browser)/./src/components/ArtistTool/EngravingPanel.jsx\");\n/* harmony import */ var _InitialOverlayPanel__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./InitialOverlayPanel */ \"(app-pages-browser)/./src/components/ArtistTool/InitialOverlayPanel.jsx\");\n/* harmony import */ var _ModelStage__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./ModelStage */ \"(app-pages-browser)/./src/components/ArtistTool/ModelStage.jsx\");\n/* harmony import */ var _PostProcessingEffects__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./PostProcessingEffects */ \"(app-pages-browser)/./src/components/ArtistTool/PostProcessingEffects.jsx\");\n/* harmony import */ var _SelectionOutline__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ./SelectionOutline */ \"(app-pages-browser)/./src/components/ArtistTool/SelectionOutline.jsx\");\n/* harmony import */ var _lib_actions_version_actions__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/lib/actions/version.actions */ \"(app-pages-browser)/./src/lib/actions/version.actions.ts\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/.pnpm/sonner@1.5.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _lib_states__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @/lib/states */ \"(app-pages-browser)/./src/lib/states.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst ScreenshotTaker = (param)=>{\n    let { onScreenshotRef } = param;\n    _s();\n    const { gl, scene, camera } = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.D)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (typeof onScreenshotRef === \"function\") {\n            onScreenshotRef(()=>{\n                try {\n                    // Render the scene\n                    gl.render(scene, camera);\n                    // Get the canvas data URL\n                    const dataURL = gl.domElement.toDataURL(\"image/png\");\n                    // Create and trigger a download\n                    const link = document.createElement(\"a\");\n                    const timestamp = new Date().toISOString().replace(/:/g, \"-\").substring(0, 19);\n                    link.download = \"agape-scene-\".concat(timestamp, \".png\");\n                    link.href = dataURL;\n                    document.body.appendChild(link);\n                    link.click();\n                    // Clean up\n                    setTimeout(()=>{\n                        document.body.removeChild(link);\n                    }, 100);\n                    console.log(\"Screenshot captured successfully\");\n                    return true;\n                } catch (error) {\n                    console.error(\"Error capturing screenshot:\", error);\n                    return false;\n                }\n            });\n        }\n    }, [\n        gl,\n        scene,\n        camera,\n        onScreenshotRef\n    ]);\n    return null;\n};\n_s(ScreenshotTaker, \"RVTFHuczdlyvGFUsZJPSpjurI/Y=\", false, function() {\n    return [\n        _react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.D\n    ];\n});\n_c = ScreenshotTaker;\n// Component to provide renderer to MaterialPanel\nconst MaterialPanelWrapper = (param)=>{\n    let { renderer, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MaterialPanel__WEBPACK_IMPORTED_MODULE_7__.MaterialPanel, {\n        renderer: renderer,\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n        lineNumber: 93,\n        columnNumber: 10\n    }, undefined);\n};\n_c1 = MaterialPanelWrapper;\n// Component to get renderer from Canvas context\nconst RendererProvider = (param)=>{\n    let { children, onRendererReady } = param;\n    _s1();\n    const { gl } = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.D)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (onRendererReady) {\n            onRendererReady(gl);\n        }\n    }, [\n        gl,\n        onRendererReady\n    ]);\n    return null;\n};\n_s1(RendererProvider, \"iTWbOa64ZxlA5nuyNExgWK+vjCI=\", false, function() {\n    return [\n        _react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.D\n    ];\n});\n_c2 = RendererProvider;\nfunction EditorExperience(param) {\n    let { modelUrl, project, user } = param;\n    _s2();\n    const { addVersion } = (0,_lib_states__WEBPACK_IMPORTED_MODULE_25__.useVersionState)();\n    const configRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const workingConfigRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [selectedModel, setSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"uploaded\");\n    const [lastSelectedModel, setLastSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [uploadedModel, setUploadedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(modelUrl || null);\n    const [modelStats, setModelStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        vertices: 0,\n        triangles: 0,\n        materials: 0,\n        animations: 0\n    });\n    const [lights, setLights] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: \"defaultDirectional\",\n            type: \"directional\",\n            position: [\n                2.5,\n                5.5,\n                5\n            ],\n            intensity: 1.0,\n            color: \"#ffffff\",\n            name: \"Main Directional Light\",\n            castShadow: true,\n            helperVisible: false\n        },\n        {\n            id: \"keyLight\",\n            type: \"point\",\n            position: [\n                5,\n                5,\n                5\n            ],\n            intensity: 8.0,\n            color: \"#ffffff\",\n            name: \"Key Light\"\n        },\n        {\n            id: \"fillLight\",\n            type: \"point\",\n            position: [\n                -5,\n                5,\n                -5\n            ],\n            intensity: 4.0,\n            color: \"#ffffff\",\n            name: \"Fill Light\"\n        },\n        {\n            id: \"backLight\",\n            type: \"point\",\n            position: [\n                0,\n                5,\n                -5\n            ],\n            intensity: 2.0,\n            color: \"#ffffff\",\n            name: \"Back Light\"\n        }\n    ]);\n    const lightRefs = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({});\n    const [selectedObjects, setSelectedObjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedLight, setSelectedLight] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [lightPositionHistory, setLightPositionHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [sceneObjects, setSceneObjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [transformMode, setTransformMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"translate\");\n    const selectionGroupRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(new three__WEBPACK_IMPORTED_MODULE_27__.Group());\n    console.log(\"renderingggg editor experience\");\n    const [rightPanelTab, setRightPanelTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Decal placement state\n    const [isPlacingDecal, setIsPlacingDecal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showDecalDebug, setShowDecalDebug] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Scene controls state\n    const [showGrid, setShowGrid] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [wireframe, setWireframe] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [groundType, setGroundType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"none\");\n    const [envPreset, setEnvPreset] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"hdri_15\");\n    const [bgColor, setBgColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"#272631\");\n    const [showEnvironment, setShowEnvironment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showLightSpheres, setShowLightSpheres] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [customHdri, setCustomHdri] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [envIntensity, setEnvIntensity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1.85);\n    const [envBlur, setEnvBlur] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0.35);\n    const [envRotation, setEnvRotation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [postProcessingEnabled, setPostProcessingEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [postProcessingSettings, setPostProcessingSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        autofocus: {\n            enabled: false,\n            bokehScale: 18\n        },\n        bloom: {\n            enabled: true,\n            intensity: 0.22,\n            threshold: 1,\n            radius: 0.5\n        },\n        dof: {\n            enabled: false,\n            focusDistance: 0,\n            aperture: 0.01,\n            bokehScale: 3\n        },\n        vignette: {\n            enabled: true,\n            darkness: 0.5,\n            offset: 0.1\n        }\n    });\n    // Store initial state\n    const initialStateRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        selectedModel: null,\n        lights: [],\n        envPreset: null,\n        bgColor: null,\n        postProcessingSettings: null,\n        showGrid: null,\n        wireframe: null,\n        groundType: null,\n        showEnvironment: null,\n        showLightSpheres: null,\n        envIntensity: null,\n        envBlur: null,\n        cameraPosition: null,\n        cameraTarget: null\n    });\n    // Add a ref to store the initial state of model objects\n    const modelInitialStateRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        objects: []\n    });\n    // Capture initial state when component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        initialStateRef.current = {\n            selectedModel,\n            lights: [\n                ...lights\n            ],\n            envPreset,\n            bgColor,\n            postProcessingSettings: {\n                ...postProcessingSettings\n            },\n            showGrid,\n            wireframe,\n            groundType,\n            showEnvironment,\n            showLightSpheres,\n            envIntensity,\n            envBlur,\n            cameraPosition: new three__WEBPACK_IMPORTED_MODULE_27__.Vector3(5, 2, 5),\n            cameraTarget: new three__WEBPACK_IMPORTED_MODULE_27__.Vector3(0, 1, 0)\n        };\n    }, []);\n    const orbitControlsRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const sceneRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const [renderer, setRenderer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // History state for undo/redo\n    const [history, setHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [historyIndex, setHistoryIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(-1);\n    const isUndoRedoActionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const skipHistoryTrackingRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const isUndoRedoInProgressRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const [showModelStats, setShowModelStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Debug function to inspect history state\n    const debugHistory = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        console.log(\"=== HISTORY DEBUG ===\");\n        console.log(\"Current historyIndex:\", historyIndex);\n        console.log(\"History length:\", history.length);\n        console.log(\"Can undo:\", historyIndex > 0);\n        console.log(\"Can redo:\", historyIndex < history.length - 1);\n        console.log(\"isUndoRedoActionRef.current:\", isUndoRedoActionRef.current);\n        console.log(\"skipHistoryTrackingRef.current:\", skipHistoryTrackingRef.current);\n        console.log(\"isUndoRedoInProgressRef.current:\", isUndoRedoInProgressRef.current);\n        console.log(\"History entries:\", history.map((_, i)=>\"\".concat(i).concat(i === historyIndex ? \" (current)\" : \"\")));\n        console.log(\"==================\");\n    }, [\n        historyIndex,\n        history\n    ]);\n    // Expose debug function to window for testing\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        window.debugHistory = debugHistory;\n        return ()=>{\n            delete window.debugHistory;\n        };\n    }, [\n        debugHistory\n    ]);\n    // Get full configuration for history\n    const getFullConfig = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        return JSON.parse(JSON.stringify({\n            selectedModel,\n            uploadedModel,\n            lights,\n            envPreset,\n            bgColor,\n            showEnvironment,\n            showLightSpheres,\n            customHdri,\n            envIntensity,\n            envBlur,\n            envRotation,\n            postProcessingEnabled,\n            postProcessingSettings,\n            showGrid,\n            wireframe,\n            groundType,\n            selectedObjects: selectedObjects.map((obj)=>obj.uuid),\n            // Store object states for transforms and materials\n            objectStates: sceneObjects.map((obj)=>({\n                    uuid: obj.uuid,\n                    name: obj.name,\n                    position: obj.position.toArray(),\n                    rotation: obj.rotation.toArray(),\n                    scale: obj.scale.toArray(),\n                    material: obj.material ? {\n                        type: obj.material.type,\n                        color: obj.material.color ? obj.material.color.getHexString() : \"ffffff\",\n                        roughness: obj.material.roughness,\n                        metalness: obj.material.metalness,\n                        clearcoat: obj.material.clearcoat,\n                        clearcoatRoughness: obj.material.clearcoatRoughness,\n                        wireframe: obj.material.wireframe,\n                        transparent: obj.material.transparent,\n                        opacity: obj.material.opacity,\n                        envMapIntensity: obj.material.envMapIntensity\n                    } : null\n                }))\n        }));\n    }, [\n        selectedModel,\n        uploadedModel,\n        lights,\n        envPreset,\n        bgColor,\n        showEnvironment,\n        showLightSpheres,\n        customHdri,\n        envIntensity,\n        envBlur,\n        envRotation,\n        postProcessingEnabled,\n        postProcessingSettings,\n        showGrid,\n        wireframe,\n        groundType,\n        selectedObjects,\n        sceneObjects\n    ]);\n    // Add initial state to history when component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (history.length === 0) {\n            console.log(\"[Initial] Adding initial state to history\");\n            const initialState = getFullConfig();\n            setHistory([\n                initialState\n            ]);\n            setHistoryIndex(0);\n        }\n    }, [\n        history.length,\n        getFullConfig\n    ]);\n    // Add a new state to the history\n    const addToHistory = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (isUndoRedoActionRef.current || skipHistoryTrackingRef.current) {\n            console.log(\"[addToHistory] Skipping - undo/redo action in progress\");\n            return;\n        }\n        const newState = getFullConfig();\n        // Check if the new state is different from the current state\n        if (history.length > 0 && historyIndex >= 0) {\n            const currentState = history[historyIndex];\n            if (JSON.stringify(currentState) === JSON.stringify(newState)) {\n                console.log(\"[addToHistory] Skipping - state unchanged\");\n                return;\n            }\n        }\n        const newHistory = history.slice(0, historyIndex + 1);\n        setHistory([\n            ...newHistory,\n            newState\n        ]);\n        const newIndex = historyIndex + 1;\n        setHistoryIndex(newIndex);\n        console.log(\"[addToHistory] Added state. New historyIndex:\", newIndex, \"History length:\", newHistory.length + 1, \"Can redo:\", newIndex < newHistory.length);\n    }, [\n        history,\n        historyIndex,\n        getFullConfig\n    ]);\n    const pendingSelectedObjectsUUIDs = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Define forceUpdate and version setters early so they can be used in restoreConfig\n    const [_, setForceUpdate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const forceUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>setForceUpdate((f)=>f + 1), []);\n    const [transformVersion, setTransformVersion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [materialVersion, setMaterialVersion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const restoreConfig = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((config)=>{\n        setSelectedModel(config.selectedModel);\n        setUploadedModel(config.uploadedModel);\n        setLights(config.lights);\n        setEnvPreset(config.envPreset);\n        setBgColor(config.bgColor);\n        setShowEnvironment(config.showEnvironment);\n        setShowLightSpheres(config.showLightSpheres);\n        setCustomHdri(config.customHdri);\n        setEnvIntensity(config.envIntensity);\n        setEnvBlur(config.envBlur);\n        setEnvRotation(config.envRotation);\n        setPostProcessingEnabled(config.postProcessingEnabled);\n        setPostProcessingSettings(config.postProcessingSettings);\n        setShowGrid(config.showGrid);\n        setWireframe(config.wireframe);\n        setGroundType(config.groundType);\n        // Restore object states (transforms and materials)\n        if (config.objectStates && sceneObjects.length > 0) {\n            config.objectStates.forEach((savedState)=>{\n                const obj = sceneObjects.find((o)=>o.uuid === savedState.uuid);\n                if (obj) {\n                    // Restore transform\n                    obj.position.fromArray(savedState.position);\n                    obj.rotation.fromArray(savedState.rotation);\n                    obj.scale.fromArray(savedState.scale);\n                    // Restore material properties\n                    if (savedState.material && obj.material) {\n                        const material = obj.material;\n                        if (material.color) {\n                            material.color.setHex(parseInt(savedState.material.color, 16));\n                        }\n                        if (savedState.material.roughness !== undefined) {\n                            material.roughness = savedState.material.roughness;\n                        }\n                        if (savedState.material.metalness !== undefined) {\n                            material.metalness = savedState.material.metalness;\n                        }\n                        if (savedState.material.clearcoat !== undefined) {\n                            material.clearcoat = savedState.material.clearcoat;\n                        }\n                        if (savedState.material.clearcoatRoughness !== undefined) {\n                            material.clearcoatRoughness = savedState.material.clearcoatRoughness;\n                        }\n                        if (savedState.material.wireframe !== undefined) {\n                            material.wireframe = savedState.material.wireframe;\n                        }\n                        if (savedState.material.transparent !== undefined) {\n                            material.transparent = savedState.material.transparent;\n                        }\n                        if (savedState.material.opacity !== undefined) {\n                            material.opacity = savedState.material.opacity;\n                        }\n                        if (savedState.material.envMapIntensity !== undefined) {\n                            material.envMapIntensity = savedState.material.envMapIntensity;\n                        }\n                        material.needsUpdate = true;\n                    }\n                }\n            });\n            // Force update to reflect changes\n            forceUpdate();\n            // Don't increment versions during restore to avoid triggering history loop\n            if (!isUndoRedoActionRef.current) {\n                setMaterialVersion((v)=>v + 1);\n                setTransformVersion((v)=>v + 1);\n            }\n        }\n        // Store selectedObjects UUIDs to be restored after sceneObjects update\n        pendingSelectedObjectsUUIDs.current = config.selectedObjects || [];\n        console.log(\"[restoreConfig] Restoring config\", config);\n    }, [\n        sceneObjects,\n        forceUpdate,\n        setMaterialVersion,\n        setTransformVersion\n    ]);\n    // After sceneObjects update, restore selectedObjects from UUIDs\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (pendingSelectedObjectsUUIDs.current && sceneObjects.length > 0) {\n            const uuids = pendingSelectedObjectsUUIDs.current;\n            const newSelected = sceneObjects.filter((obj)=>uuids.includes(obj.uuid));\n            setSelectedObjects(newSelected);\n            pendingSelectedObjectsUUIDs.current = null;\n            console.log(\"[restoreConfig] Restored selectedObjects\", newSelected);\n        }\n    }, [\n        sceneObjects\n    ]);\n    // Undo action\n    const undoAction = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        // Prevent rapid undo operations\n        if (isUndoRedoInProgressRef.current) {\n            console.log(\"[undoAction] Skipping - undo/redo already in progress\");\n            return;\n        }\n        console.log(\"[undoAction] Current historyIndex:\", historyIndex, \"History length:\", history.length);\n        if (historyIndex > 0) {\n            console.log(\"[undoAction] Performing undo to index:\", historyIndex - 1);\n            // Set all flags to prevent interference\n            isUndoRedoInProgressRef.current = true;\n            isUndoRedoActionRef.current = true;\n            skipHistoryTrackingRef.current = true;\n            const prevConfig = history[historyIndex - 1];\n            restoreConfig(prevConfig);\n            setHistoryIndex(historyIndex - 1);\n            // Reset flags after operation completes\n            setTimeout(()=>{\n                isUndoRedoActionRef.current = false;\n                skipHistoryTrackingRef.current = false;\n                isUndoRedoInProgressRef.current = false;\n                console.log(\"[undoAction] Undo completed, all flags reset\");\n            }, 1000);\n        } else {\n            console.log(\"[undoAction] Cannot undo - at beginning of history\");\n        }\n    }, [\n        historyIndex,\n        history,\n        restoreConfig\n    ]);\n    // Redo action\n    const redoAction = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        // Prevent rapid redo operations\n        if (isUndoRedoInProgressRef.current) {\n            console.log(\"[redoAction] Skipping - undo/redo already in progress\");\n            return;\n        }\n        debugHistory();\n        console.log(\"[redoAction] Current historyIndex:\", historyIndex, \"History length:\", history.length);\n        if (historyIndex < history.length - 1) {\n            console.log(\"[redoAction] Performing redo to index:\", historyIndex + 1);\n            // Set all flags to prevent interference\n            isUndoRedoInProgressRef.current = true;\n            isUndoRedoActionRef.current = true;\n            skipHistoryTrackingRef.current = true;\n            const nextConfig = history[historyIndex + 1];\n            console.log(\"[redoAction] Next config:\", nextConfig);\n            restoreConfig(nextConfig);\n            setHistoryIndex(historyIndex + 1);\n            // Reset flags after operation completes\n            setTimeout(()=>{\n                isUndoRedoActionRef.current = false;\n                skipHistoryTrackingRef.current = false;\n                isUndoRedoInProgressRef.current = false;\n                console.log(\"[redoAction] Redo completed, all flags reset\");\n            }, 1000);\n        } else {\n            console.log(\"[redoAction] Cannot redo - at end of history\");\n        }\n    }, [\n        historyIndex,\n        history,\n        restoreConfig,\n        debugHistory\n    ]);\n    // Track changes to objects and lights for history\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const trackChanges = ()=>{\n            if (selectedObjects.length > 0 || lights.length > 0) {\n                addToHistory({\n                    selectedObjects,\n                    lights\n                });\n            }\n        };\n        // Debounce to avoid too many history entries\n        const timeoutId = setTimeout(trackChanges, 500);\n        return ()=>clearTimeout(timeoutId);\n    }, [\n        selectedObjects,\n        lights,\n        addToHistory\n    ]);\n    const handleObjectClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e)=>{\n        e.stopPropagation();\n        // If placing decal, let the decal placement handler do its job (triggered by model's onClick)\n        if (isPlacingDecal) {\n            return;\n        }\n        console.log(\"Object clicked:\", e.object.name, e.object);\n        if (e.shiftKey) {\n            setSelectedObjects((prev)=>{\n                // Ensure prev is always an array\n                const prevArray = Array.isArray(prev) ? prev : [\n                    prev\n                ].filter(Boolean);\n                if (prevArray.includes(e.object)) {\n                    return prevArray.filter((obj)=>obj !== e.object);\n                }\n                return [\n                    ...prevArray,\n                    e.object\n                ];\n            });\n        } else {\n            setSelectedObjects([\n                e.object\n            ]);\n        }\n    }, [\n        isPlacingDecal,\n        setSelectedObjects\n    ]);\n    // Add debugging for selectedObjects changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"ExperienceNew - selectedObjects changed:\", selectedObjects);\n        console.log(\"ExperienceNew - selectedObjects length:\", selectedObjects === null || selectedObjects === void 0 ? void 0 : selectedObjects.length);\n    }, [\n        selectedObjects\n    ]);\n    // Add debugging for sceneObjects changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"ExperienceNew - sceneObjects changed:\", sceneObjects);\n        console.log(\"ExperienceNew - sceneObjects length:\", sceneObjects === null || sceneObjects === void 0 ? void 0 : sceneObjects.length);\n        console.log(\"ExperienceNew - sceneObjects details:\", sceneObjects === null || sceneObjects === void 0 ? void 0 : sceneObjects.map((obj)=>({\n                name: obj === null || obj === void 0 ? void 0 : obj.name,\n                type: obj === null || obj === void 0 ? void 0 : obj.type,\n                userData: obj === null || obj === void 0 ? void 0 : obj.userData\n            })));\n    }, [\n        sceneObjects\n    ]);\n    const handlePointerMissed = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        // If in decal placement mode, a \"missed\" click (on the canvas background)\n        // should probably cancel placement mode, or do nothing to allow model click-through.\n        // For now, let's prevent it from deselecting objects if placing decal.\n        if (isPlacingDecal) {\n            // setIsPlacingDecal(false); // Optionally, cancel placement on missed click\n            return;\n        }\n        setSelectedObjects([]);\n        setSelectedLight(null);\n    // Do not close the rightPanelTab here, as that's usually handled by clicking outside the panel itself.\n    }, [\n        isPlacingDecal,\n        setIsPlacingDecal\n    ]);\n    // Add the selection group to the scene\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // This effect runs once to add the selection group to the scene\n        return ()=>{\n            // Clean up on unmount\n            if (selectionGroupRef.current && selectionGroupRef.current.parent) {\n                selectionGroupRef.current.parent.remove(selectionGroupRef.current);\n            }\n        };\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Ensure selectedObjects is always an array\n        const objectsArray = Array.isArray(selectedObjects) ? selectedObjects : [\n            selectedObjects\n        ].filter(Boolean);\n        while(selectionGroupRef.current.children.length > 0){\n            selectionGroupRef.current.remove(selectionGroupRef.current.children[0]);\n        }\n        if (objectsArray.length > 0) {\n            const center = new three__WEBPACK_IMPORTED_MODULE_27__.Vector3();\n            objectsArray.forEach((obj)=>{\n                center.add(obj.position);\n            });\n            center.divideScalar(objectsArray.length);\n            selectionGroupRef.current.position.copy(center);\n            objectsArray.forEach((obj)=>{\n                const clone = obj.clone();\n                // Make the clone invisible while keeping its geometry for transforms\n                clone.traverse((child)=>{\n                    if (child.isMesh) {\n                        child.visible = false;\n                    }\n                });\n                clone.position.sub(center);\n                selectionGroupRef.current.add(clone);\n            });\n        }\n    }, [\n        selectedObjects\n    ]);\n    const updateObjectsFromGroup = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        // Ensure selectedObjects is always an array\n        const objectsArray = Array.isArray(selectedObjects) ? selectedObjects : [\n            selectedObjects\n        ].filter(Boolean);\n        if (objectsArray.length > 0 && selectionGroupRef.current) {\n            objectsArray.forEach((obj, index)=>{\n                if (selectionGroupRef.current.children[index]) {\n                    const worldPos = new three__WEBPACK_IMPORTED_MODULE_27__.Vector3();\n                    selectionGroupRef.current.children[index].getWorldPosition(worldPos);\n                    obj.position.copy(worldPos);\n                    obj.rotation.copy(selectionGroupRef.current.rotation);\n                    obj.scale.copy(selectionGroupRef.current.scale);\n                }\n            });\n        }\n    }, [\n        selectedObjects\n    ]);\n    const handleSceneUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((scene)=>{\n        var _scene_children;\n        console.log(\"handleSceneUpdate called with scene:\", scene);\n        console.log(\"Scene type:\", scene.type);\n        console.log(\"Scene children count:\", (_scene_children = scene.children) === null || _scene_children === void 0 ? void 0 : _scene_children.length);\n        setSceneObjects([]);\n        setSelectedObjects([]);\n        const stats = (0,_utils_ArtistTool_sceneUtils__WEBPACK_IMPORTED_MODULE_16__.calculateModelStats)(scene);\n        setModelStats(stats);\n        (0,_utils_ArtistTool_sceneUtils__WEBPACK_IMPORTED_MODULE_16__.setShadowsOnModel)(scene);\n        const objects = [];\n        const initialObjects = [];\n        scene.traverse((object)=>{\n            console.log(\"Traversing object:\", object.name, object.type, \"isMesh:\", object.isMesh, \"userData:\", object.userData);\n            if (object.isMesh) {\n                if (!object.name) {\n                    object.name = \"Mesh_\".concat(object.id);\n                }\n                console.log(\"Adding mesh to objects:\", object.name, \"userData:\", object.userData);\n                objects.push(object);\n                initialObjects.push({\n                    id: object.uuid,\n                    name: object.name,\n                    position: object.position.clone(),\n                    rotation: object.rotation.clone(),\n                    scale: object.scale.clone(),\n                    material: object.material ? {\n                        color: object.material.color ? object.material.color.getHex() : 0xffffff,\n                        roughness: object.material.roughness,\n                        metalness: object.material.metalness,\n                        wireframe: object.material.wireframe,\n                        transparent: object.material.transparent,\n                        opacity: object.material.opacity\n                    } : null\n                });\n            }\n        });\n        console.log(\"Found\", objects.length, \"mesh objects:\", objects.map((obj)=>({\n                name: obj.name,\n                userData: obj.userData\n            })));\n        modelInitialStateRef.current.objects = initialObjects;\n        setSceneObjects(objects);\n    }, []);\n    // Add this effect to detect model changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedModel !== lastSelectedModel) {\n            // Clear the scene objects when model changes\n            setSceneObjects([]);\n            setSelectedObjects([]);\n            setLastSelectedModel(selectedModel);\n        }\n    }, [\n        selectedModel,\n        lastSelectedModel\n    ]);\n    const handleLightMoved = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((lightId, newPosition)=>{\n        setLightPositionHistory((prev)=>({\n                ...prev,\n                [lightId]: [\n                    ...prev[lightId] || [],\n                    newPosition\n                ]\n            }));\n    }, []);\n    const undoLightMove = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((lightId)=>{\n        setLightPositionHistory((prev)=>{\n            const history = prev[lightId] || [];\n            if (history.length <= 1) return prev;\n            const newHistory = history.slice(0, -1);\n            const lastPosition = newHistory[newHistory.length - 1];\n            if (lightRefs.current[lightId]) {\n                lightRefs.current[lightId].position.copy(lastPosition);\n            }\n            return {\n                ...prev,\n                [lightId]: newHistory\n            };\n        });\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleKeyDown = (event)=>{\n            // Handle undo/redo keyboard shortcuts\n            if (event.ctrlKey) {\n                if (event.key === \"z\" && !event.shiftKey) {\n                    event.preventDefault();\n                    undoAction();\n                    return;\n                }\n                if (event.key === \"y\" || event.key === \"z\" && event.shiftKey) {\n                    event.preventDefault();\n                    redoAction();\n                    return;\n                }\n            }\n            // Handle Escape key for decal placement\n            if (event.key === \"Escape\" && isPlacingDecal) {\n                setIsPlacingDecal(false);\n                return;\n            }\n            // Handle light undo\n            if (event.ctrlKey && event.key === \"z\" && selectedLight) {\n                undoLightMove(selectedLight);\n            }\n            // Handle transform mode shortcuts\n            if (selectedObjects.length > 0) {\n                switch(event.key){\n                    case \"g\":\n                        setTransformMode(\"translate\");\n                        break;\n                    case \"r\":\n                        setTransformMode(\"rotate\");\n                        break;\n                    case \"s\":\n                        setTransformMode(\"scale\");\n                        break;\n                    case \"Escape\":\n                        setSelectedObjects([]);\n                        break;\n                }\n            }\n            // Handle select all\n            if (event.ctrlKey && event.key === \"a\") {\n                event.preventDefault();\n                if (sceneObjects.length > 0) {\n                    setSelectedObjects([\n                        ...sceneObjects\n                    ]);\n                }\n            }\n        };\n        window.addEventListener(\"keydown\", handleKeyDown);\n        return ()=>window.removeEventListener(\"keydown\", handleKeyDown);\n    }, [\n        selectedLight,\n        undoLightMove,\n        sceneObjects,\n        selectedObjects.length,\n        undoAction,\n        redoAction,\n        isPlacingDecal,\n        setIsPlacingDecal\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        return ()=>{\n            if (uploadedModel) {\n                URL.revokeObjectURL(uploadedModel);\n            }\n            if (customHdri) {\n                URL.revokeObjectURL(customHdri);\n            }\n        };\n    }, [\n        uploadedModel,\n        customHdri\n    ]);\n    const handleCameraView = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((view)=>{\n        (0,_utils_ArtistTool_cameraUtils__WEBPACK_IMPORTED_MODULE_14__.setCameraView)(orbitControlsRef.current, view);\n    }, []);\n    // Add the panelRef to ExperienceNew instead of in Toolbar\n    const panelRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [activeAnimation, setActiveAnimation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [animationStartTime, setAnimationStartTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const requestRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    // Animation loop handler\n    const animateCamera = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((timestamp)=>{\n        if (!activeAnimation || !orbitControlsRef.current) {\n            return;\n        }\n        // Calculate animation progress (0 to 1)\n        const animation = _animations_CameraAnimations__WEBPACK_IMPORTED_MODULE_17__.predefinedAnimations.find((a)=>a.id === activeAnimation);\n        if (!animation) return;\n        if (animationStartTime === 0) {\n            setAnimationStartTime(timestamp);\n        }\n        const elapsed = timestamp - animationStartTime;\n        const progress = elapsed % animation.duration / animation.duration;\n        // Get the target position (center of selected objects or scene center)\n        const target = new three__WEBPACK_IMPORTED_MODULE_27__.Vector3(0, 0, 0);\n        if (selectedObjects.length > 0) {\n            // Calculate center of selected objects\n            const objectsArray = Array.isArray(selectedObjects) ? selectedObjects : [\n                selectedObjects\n            ].filter(Boolean);\n            if (objectsArray.length > 0) {\n                const center = new three__WEBPACK_IMPORTED_MODULE_27__.Vector3();\n                objectsArray.forEach((obj)=>{\n                    center.add(obj.position);\n                });\n                center.divideScalar(objectsArray.length);\n                target.copy(center);\n            }\n        }\n        // Animate camera using the animation function\n        animation.animate(orbitControlsRef.current.object, orbitControlsRef.current, progress, target);\n        requestRef.current = requestAnimationFrame(animateCamera);\n    }, [\n        activeAnimation,\n        orbitControlsRef,\n        animationStartTime,\n        selectedObjects\n    ]);\n    // Setup animation loop\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (activeAnimation) {\n            setAnimationStartTime(0);\n            requestRef.current = requestAnimationFrame(animateCamera);\n            // Disable orbit controls during animation\n            if (orbitControlsRef.current) {\n                orbitControlsRef.current.enabled = false;\n            }\n        } else {\n            // Re-enable orbit controls when animation stops\n            if (orbitControlsRef.current && selectedObjects.length === 0) {\n                orbitControlsRef.current.enabled = true;\n            }\n        }\n        return ()=>{\n            if (requestRef.current) {\n                cancelAnimationFrame(requestRef.current);\n            }\n        };\n    }, [\n        activeAnimation,\n        animateCamera,\n        selectedObjects\n    ]);\n    // Handle animation playback from Outliner\n    const handlePlayAnimation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((animationId)=>{\n        setActiveAnimation(animationId);\n    }, []);\n    const getEnvironmentPreset = ()=>{\n        return envPreset;\n    };\n    // Reset function\n    const handleReset = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        // Check if there's a saved scene in localStorage\n        const savedScene = localStorage.getItem(\"agape_saved_scene\");\n        if (savedScene) {\n            const confirmReset = window.confirm(\"There is a saved scene in your browser storage. Resetting will clear this saved scene. Do you want to continue?\");\n            if (!confirmReset) {\n                return;\n            }\n            // Clear the saved scene from localStorage\n            localStorage.removeItem(\"agape_saved_scene\");\n        }\n        console.log(\"Resetting scene to initial state\");\n        setSceneObjects([]);\n        setSelectedObjects([]);\n        // Reset lights to default configuration\n        setLights([\n            {\n                id: \"defaultDirectional\",\n                type: \"directional\",\n                position: [\n                    2.5,\n                    5.5,\n                    5\n                ],\n                intensity: 1.0,\n                color: \"#ffffff\",\n                name: \"Main Directional Light\",\n                castShadow: true,\n                helperVisible: false\n            },\n            {\n                id: \"keyLight\",\n                type: \"point\",\n                position: [\n                    5,\n                    5,\n                    5\n                ],\n                intensity: 8.0,\n                color: \"#ffffff\",\n                name: \"Key Light\"\n            },\n            {\n                id: \"fillLight\",\n                type: \"point\",\n                position: [\n                    -5,\n                    5,\n                    -5\n                ],\n                intensity: 4.0,\n                color: \"#ffffff\",\n                name: \"Fill Light\"\n            },\n            {\n                id: \"backLight\",\n                type: \"point\",\n                position: [\n                    0,\n                    5,\n                    -5\n                ],\n                intensity: 2.0,\n                color: \"#ffffff\",\n                name: \"Back Light\"\n            }\n        ]);\n        // Reset environment settings\n        setEnvPreset(\"hdri_15\");\n        setBgColor(\"#202020\");\n        setShowEnvironment(false);\n        setShowLightSpheres(true);\n        setCustomHdri(null);\n        setEnvIntensity(1.5);\n        setEnvBlur(0.35);\n        setEnvRotation(0);\n        // Reset post-processing settings\n        setPostProcessingEnabled(false);\n        setPostProcessingSettings({\n            autofocus: {\n                enabled: false,\n                bokehScale: 18\n            },\n            bloom: {\n                enabled: true,\n                intensity: 0.12,\n                threshold: 0.8,\n                radius: 0.4\n            },\n            dof: {\n                enabled: false,\n                focusDistance: 0,\n                aperture: 0.01,\n                bokehScale: 3\n            },\n            vignette: {\n                enabled: true,\n                darkness: 0.5,\n                offset: 0.1\n            }\n        });\n        // Reset scene controls\n        setShowGrid(true);\n        setWireframe(false);\n        setGroundType(\"none\");\n        // Reset camera position\n        if (orbitControlsRef.current) {\n            orbitControlsRef.current.object.position.set(5, 2, 5);\n            orbitControlsRef.current.target.set(0, 1, 0);\n            orbitControlsRef.current.update();\n        }\n        // Reset transform state\n        setTransformMode(\"translate\");\n        setSelectedLight(null);\n        // Reset material shaders\n        setUseDiamondShader(false);\n        setUsePremiumWhiteGold(false);\n        setUsePremiumRoseGold(false);\n        // Clear history\n        setHistory([]);\n        setHistoryIndex(-1);\n        setLightPositionHistory({});\n        // Reset model stats\n        setModelStats({\n            vertices: 0,\n            triangles: 0,\n            materials: 0,\n            animations: 0\n        });\n        // Reset right panel tab\n        setRightPanelTab(null);\n        // Reset active animation\n        setActiveAnimation(null);\n        setAnimationStartTime(0);\n        if (requestRef.current) {\n            cancelAnimationFrame(requestRef.current);\n        }\n        // Clear any custom HDRI or uploaded model URLs\n        if (uploadedModel) {\n            URL.revokeObjectURL(uploadedModel);\n        }\n        if (customHdri) {\n            URL.revokeObjectURL(customHdri);\n        }\n        // Force a model reload by setting selectedModel to null first\n        setSelectedModel(null);\n        // Then set it back to uploaded after a small delay\n        setTimeout(()=>{\n            setSelectedModel(\"uploaded\");\n        }, 100);\n    }, []);\n    const screenshotRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const captureScreenshot = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (screenshotRef.current) {\n            console.log(\"Taking screenshot\");\n            screenshotRef.current();\n        } else {\n            console.error(\"Screenshot function not available yet\");\n        }\n    }, []);\n    // Load saved scene from localStorage on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadSavedScene = ()=>{\n            const savedScene = localStorage.getItem(\"agape_saved_scene\");\n            if (savedScene) {\n                try {\n                    const sceneConfig = JSON.parse(savedScene);\n                    console.log(\"Loading saved scene:\", sceneConfig);\n                    // Only load the saved configuration on initial load\n                    if (!lastSelectedModel) {\n                        console.log(\"Initial load - setting saved model:\", sceneConfig.model);\n                        setSelectedModel(\"uploaded\"); // Always use uploaded for S3 models\n                        // Load the rest of the saved scene configuration\n                        setLights(sceneConfig.lights);\n                        setEnvPreset(sceneConfig.environment.preset);\n                        setBgColor(sceneConfig.environment.bgColor);\n                        setShowEnvironment(sceneConfig.environment.showEnvironment);\n                        setShowLightSpheres(sceneConfig.environment.showLightSpheres);\n                        setEnvIntensity(sceneConfig.environment.intensity);\n                        setEnvBlur(sceneConfig.environment.blur);\n                        setEnvRotation(sceneConfig.environment.rotation);\n                        setPostProcessingEnabled(sceneConfig.postProcessing.enabled);\n                        setPostProcessingSettings(sceneConfig.postProcessing.settings);\n                        setShowGrid(sceneConfig.sceneControls.showGrid);\n                        setWireframe(sceneConfig.sceneControls.wireframe);\n                        setGroundType(sceneConfig.sceneControls.groundType);\n                        // Restore camera position after a short delay to ensure scene is loaded\n                        setTimeout(()=>{\n                            if (orbitControlsRef.current && sceneConfig.camera) {\n                                orbitControlsRef.current.object.position.fromArray(sceneConfig.camera.position);\n                                orbitControlsRef.current.target.fromArray(sceneConfig.camera.target);\n                                orbitControlsRef.current.update();\n                            }\n                        }, 1000);\n                    } else if (configRef.current) {\n                        // For explicit loads with configRef, do a complete load\n                        console.log(\"Explicit load with configRef\");\n                        handleLoadScene(sceneConfig);\n                    }\n                    // Create a function to restore materials\n                    const restoreMaterials = ()=>{\n                        if (!sceneConfig.materials || !sceneObjects.length) {\n                            console.log(\"No materials to restore or scene objects not ready\");\n                            return;\n                        }\n                        console.log(\"Attempting to restore materials for objects:\", sceneObjects);\n                        console.log(\"Saved materials:\", sceneConfig.materials);\n                        const savedMaterialsMap = new Map(sceneConfig.materials.map((m)=>[\n                                m.name,\n                                m.material\n                            ]));\n                        sceneObjects.forEach((obj)=>{\n                            if (obj.material && savedMaterialsMap.has(obj.name)) {\n                                const savedMaterial = savedMaterialsMap.get(obj.name);\n                                console.log(\"Restoring material for object:\", obj.name, savedMaterial);\n                                (0,_utils_ArtistTool_materialUtils__WEBPACK_IMPORTED_MODULE_15__.applyMaterialToObject)(obj, savedMaterial);\n                            } else {\n                                console.log(\"Could not find saved material for object:\", obj.name);\n                            }\n                        });\n                    };\n                    // Set up an interval to check for scene objects\n                    const checkInterval = setInterval(()=>{\n                        if (sceneObjects.length > 0) {\n                            clearInterval(checkInterval);\n                            // Add a small delay to ensure all materials are properly initialized\n                            setTimeout(restoreMaterials, 500);\n                        }\n                    }, 100);\n                    // Clean up interval after 5 seconds if scene objects never become available\n                    setTimeout(()=>{\n                        clearInterval(checkInterval);\n                    }, 5000);\n                    // Restore camera position after a short delay to ensure scene is loaded\n                    setTimeout(()=>{\n                        if (orbitControlsRef.current && sceneConfig.camera) {\n                            orbitControlsRef.current.object.position.fromArray(sceneConfig.camera.position);\n                            orbitControlsRef.current.target.fromArray(sceneConfig.camera.target);\n                            orbitControlsRef.current.update();\n                        }\n                    }, 1000);\n                } catch (error) {\n                    console.error(\"Error loading saved scene:\", error);\n                }\n            }\n        };\n        // Load saved scene immediately on mount\n        loadSavedScene();\n        // Also set up an interval to check for scene readiness\n        const checkSceneReady = setInterval(()=>{\n            if (sceneRef.current && orbitControlsRef.current) {\n                clearInterval(checkSceneReady);\n                loadSavedScene();\n            }\n        }, 100);\n        return ()=>clearInterval(checkSceneReady);\n    }, [\n        sceneObjects,\n        selectedModel\n    ]);\n    const [configState, setConfigState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (sceneObjects.length > 0 && configState && configState.materials) {\n            const applyMaterials = ()=>{\n                console.log(\"Applying materials to scene objects:\", sceneObjects);\n                console.log(\"Saved materials configuration:\", configState.materials);\n                sceneObjects.forEach((obj)=>{\n                    const materialConfig = configState.materials.find((m)=>m.name === obj.name || m.id === obj.uuid);\n                    if (materialConfig && materialConfig.material) {\n                        console.log(\"Found material config for object:\", obj.name, materialConfig.material);\n                        (0,_utils_ArtistTool_materialUtils__WEBPACK_IMPORTED_MODULE_15__.applyMaterialToObject)(obj, materialConfig.material);\n                    } else {\n                        console.log(\"No material config found for object:\", obj.name);\n                    }\n                });\n            };\n            // Add a small delay to ensure all materials are properly initialized\n            const timeoutId = setTimeout(applyMaterials, 500);\n            return ()=>clearTimeout(timeoutId);\n        }\n    }, [\n        sceneObjects,\n        configState\n    ]);\n    const handleLoadScene = (config)=>{\n        try {\n            console.log(\"Loading scene configuration:\", config);\n            // Store the config in the ref\n            configRef.current = config;\n            workingConfigRef.current = config; // Always keep this in sync with last applied config\n            setConfigState(config); // <-- keep in sync for effect\n            // Clear scene objects and selected objects first\n            setSceneObjects([]);\n            setSelectedObjects([]);\n            setLastSelectedModel(null);\n            // Restore scene settings\n            setSelectedModel(\"uploaded\");\n            setLights(config.lights);\n            setEnvPreset(config.environment.preset);\n            setBgColor(config.environment.bgColor);\n            setShowEnvironment(config.environment.showEnvironment);\n            setShowLightSpheres(config.environment.showLightSpheres);\n            setEnvIntensity(config.environment.intensity);\n            setEnvBlur(config.environment.blur);\n            setEnvRotation(config.environment.rotation);\n            setPostProcessingEnabled(config.postProcessing.enabled);\n            setPostProcessingSettings(config.postProcessing.settings);\n            setShowGrid(config.sceneControls.showGrid);\n            setWireframe(config.sceneControls.wireframe);\n            setGroundType(config.sceneControls.groundType);\n            // Wait for the scene to update before applying camera position\n            setTimeout(()=>{\n                if (orbitControlsRef.current && config.camera) {\n                    orbitControlsRef.current.object.position.fromArray(config.camera.position);\n                    orbitControlsRef.current.target.fromArray(config.camera.target);\n                    orbitControlsRef.current.update();\n                }\n            }, 1000);\n            // Restore materials after sceneObjects are ready\n            const restoreMaterials = ()=>{\n                if (!config.materials || !sceneObjects.length) {\n                    console.log(\"No materials to restore or scene objects not ready\");\n                    return;\n                }\n                console.log(\"Restoring materials for objects:\", sceneObjects);\n                const savedMaterialsMap = new Map(config.materials.map((m)=>[\n                        m.name,\n                        m.material\n                    ]));\n                sceneObjects.forEach((obj)=>{\n                    if (obj.material && savedMaterialsMap.has(obj.name)) {\n                        const savedMaterial = savedMaterialsMap.get(obj.name);\n                        console.log(\"Restoring material for object:\", obj.name, savedMaterial);\n                        (0,_utils_ArtistTool_materialUtils__WEBPACK_IMPORTED_MODULE_15__.applyMaterialToObject)(obj, savedMaterial);\n                    } else {\n                        console.log(\"Could not find saved material for object:\", obj.name);\n                    }\n                });\n            };\n            if (sceneObjects.length > 0) {\n                setTimeout(restoreMaterials, 500);\n            } else {\n                const checkInterval = setInterval(()=>{\n                    if (sceneObjects.length > 0) {\n                        clearInterval(checkInterval);\n                        setTimeout(restoreMaterials, 500);\n                    }\n                }, 100);\n                setTimeout(()=>{\n                    clearInterval(checkInterval);\n                }, 5000);\n            }\n        } catch (error) {\n            console.error(\"Error loading scene configuration:\", error);\n        }\n    };\n    const handleSaveScene = async (sceneName, description, saveType)=>{\n        // Use workingConfigRef if it exists and matches the current state, otherwise build from state\n        let sceneConfig;\n        if (workingConfigRef.current) {\n            // Use a shallow compare for top-level keys (or always use workingConfigRef for now)\n            sceneConfig = {\n                ...workingConfigRef.current,\n                name: sceneName,\n                timestamp: new Date().toISOString()\n            };\n        } else {\n            var _orbitControlsRef_current, _orbitControlsRef_current1;\n            // fallback to building from state\n            sceneConfig = {\n                name: sceneName,\n                timestamp: new Date().toISOString(),\n                model: selectedModel,\n                lights: lights.map((light)=>{\n                    var _lightRefs_current_light_id;\n                    return {\n                        ...light,\n                        position: ((_lightRefs_current_light_id = lightRefs.current[light.id]) === null || _lightRefs_current_light_id === void 0 ? void 0 : _lightRefs_current_light_id.position.toArray()) || light.position\n                    };\n                }),\n                environment: {\n                    preset: envPreset,\n                    intensity: envIntensity,\n                    blur: envBlur,\n                    rotation: envRotation,\n                    showEnvironment,\n                    bgColor,\n                    customHdri\n                },\n                postProcessing: {\n                    enabled: postProcessingEnabled,\n                    settings: postProcessingSettings\n                },\n                sceneControls: {\n                    showGrid,\n                    wireframe,\n                    groundType,\n                    showLightSpheres\n                },\n                camera: {\n                    position: (_orbitControlsRef_current = orbitControlsRef.current) === null || _orbitControlsRef_current === void 0 ? void 0 : _orbitControlsRef_current.object.position.toArray(),\n                    target: (_orbitControlsRef_current1 = orbitControlsRef.current) === null || _orbitControlsRef_current1 === void 0 ? void 0 : _orbitControlsRef_current1.target.toArray()\n                },\n                materials: sceneObjects.map((obj)=>{\n                    const material = obj.material;\n                    if (!material) return {\n                        id: obj.uuid,\n                        name: obj.name,\n                        material: null\n                    };\n                    return {\n                        id: obj.uuid,\n                        name: obj.name,\n                        material: {\n                            type: material.type,\n                            color: material.color ? material.color.getHexString() : \"ffffff\",\n                            roughness: material.roughness,\n                            metalness: material.metalness,\n                            wireframe: material.wireframe,\n                            transparent: material.transparent,\n                            opacity: material.opacity,\n                            envMapIntensity: material.envMapIntensity,\n                            ...material.type === \"MeshRefractionMaterial\" && {\n                                aberrationStrength: material.aberrationStrength,\n                                toneMapped: material.toneMapped,\n                                ior: material.ior,\n                                colorEnvMapRotY: material.colorEnvMapRotY\n                            }\n                        }\n                    };\n                })\n            };\n        }\n        if (saveType === \"local\") {\n            // Save to localStorage\n            localStorage.setItem(\"agape_saved_scene\", JSON.stringify(sceneConfig));\n        } else if (saveType === \"db\") {\n            // Save to DB (cloud)\n            try {\n                const version = await (0,_lib_actions_version_actions__WEBPACK_IMPORTED_MODULE_23__.createVersion)({\n                    userId: user.id,\n                    workspaceId: project._id,\n                    name: sceneName,\n                    config: sceneConfig,\n                    description: description\n                });\n                console.log(\"version\", version);\n                if (version) {\n                    addVersion(project._id, version);\n                }\n                sonner__WEBPACK_IMPORTED_MODULE_24__.toast.success(\"Scene saved to cloud!\");\n            } catch (err) {\n                sonner__WEBPACK_IMPORTED_MODULE_24__.toast.error(\"Failed to save scene to cloud\");\n            }\n        } else {\n            // Export as file\n            const blob = new Blob([\n                JSON.stringify(sceneConfig, null, 2)\n            ], {\n                type: \"application/json\"\n            });\n            const url = URL.createObjectURL(blob);\n            const a = document.createElement(\"a\");\n            a.href = url;\n            a.download = \"\".concat(sceneName.replace(/\\s+/g, \"_\"), \".json\");\n            document.body.appendChild(a);\n            a.click();\n            document.body.removeChild(a);\n            URL.revokeObjectURL(url);\n        }\n    };\n    // Engraving state\n    const [engravingText, setEngravingText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [decalPosition, setDecalPosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        0,\n        1.68,\n        0\n    ]);\n    const [decalScale, setDecalScale] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        2.8,\n        0.9,\n        1.5\n    ]);\n    const [decalRotation, setDecalRotation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        -Math.PI / 2,\n        0,\n        0\n    ]);\n    const [decalColor, setDecalColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"#cbcbcb\");\n    const [selectedFont, setSelectedFont] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"/fonts/ttf/RobotoSerif-Regular.ttf\");\n    // Diamond shader state\n    const [useDiamondShader, setUseDiamondShader] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [usePremiumWhiteGold, setUsePremiumWhiteGold] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [usePremiumRoseGold, setUsePremiumRoseGold] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleEngraveText = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((text)=>{\n        setEngravingText(text);\n        // Store in localStorage to make it persist across panel changes and page reloads\n        localStorage.setItem(\"engravingText\", text);\n    }, []);\n    const handleFontChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((font)=>{\n        setSelectedFont(font);\n        localStorage.setItem(\"engravingFont\", font);\n    }, []);\n    // Load saved engraving text on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const savedEngravingText = localStorage.getItem(\"engravingText\");\n        if (savedEngravingText) {\n            setEngravingText(savedEngravingText);\n        }\n    }, []);\n    const handleDecalPlacement = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((event)=>{\n        handleDecalPlacement(event, setIsPlacingDecal, setDecalPosition, setDecalRotation);\n    }, [\n        setDecalPosition,\n        setDecalRotation,\n        setIsPlacingDecal\n    ]);\n    // Get predefined decal positions based on selected model\n    const getDefaultDecalPosition = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((modelId)=>{\n        const modelConfigs = {\n            chosenring: [\n                0,\n                1.68,\n                0\n            ],\n            testring: [\n                0,\n                1.68,\n                0\n            ],\n            agapering1: [\n                0,\n                1.2,\n                0\n            ],\n            agapering2: [\n                0,\n                1.2,\n                0\n            ],\n            ringa: [\n                0,\n                0.26,\n                0\n            ],\n            ringb: [\n                0,\n                0.5,\n                0\n            ],\n            ringc: [\n                0,\n                0.26,\n                0\n            ]\n        };\n        return modelConfigs[modelId] || [\n            0,\n            1.68,\n            0\n        ];\n    }, []);\n    const getDefaultDecalRotation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((modelId)=>{\n        const modelConfigs = {\n            chosenring: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            testring: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            agapering1: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            agapering2: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            ringa: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            ringb: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            ringc: [\n                -Math.PI / 2,\n                0,\n                0\n            ]\n        };\n        return modelConfigs[modelId] || [\n            -Math.PI / 2,\n            0,\n            0\n        ];\n    }, []);\n    const getDefaultDecalScale = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((modelId)=>{\n        const modelConfigs = {\n            chosenring: [\n                6.0,\n                2.0,\n                1.5\n            ],\n            testring: [\n                6.0,\n                2.0,\n                1.5\n            ],\n            agapering1: [\n                3.2,\n                1.8,\n                1.0\n            ],\n            agapering2: [\n                3.2,\n                1.8,\n                1.0\n            ],\n            ringa: [\n                1.8,\n                2.0,\n                0.8\n            ],\n            ringb: [\n                1.8,\n                2.0,\n                0.8\n            ],\n            ringc: [\n                1.8,\n                2.0,\n                0.8\n            ]\n        };\n        return modelConfigs[modelId] || [\n            4.2,\n            2.0,\n            1.5\n        ];\n    }, []);\n    // Calculate dynamic scale based on text length and model type\n    const calculateDynamicScale = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((baseScale, text, modelId)=>{\n        if (!text || text.length === 0) return baseScale;\n        const textLength = text.length;\n        let [scaleX, scaleY, scaleZ] = baseScale;\n        // Calculate scale multiplier based on text length\n        let scaleMultiplier = 1.0;\n        if (textLength > 10) {\n            // For very long text, scale up more aggressively\n            scaleMultiplier = 1.3 + (textLength - 10) * 0.08;\n        } else if (textLength > 6) {\n            // For medium length text, scale up moderately\n            scaleMultiplier = 1.0 + (textLength - 6) * 0.08;\n        } else if (textLength < 4) {\n            // For short text, scale down slightly to prevent oversized appearance\n            scaleMultiplier = 0.8 + textLength * 0.05;\n        }\n        // Apply text length scaling to X-axis (width) primarily\n        scaleX = scaleX * scaleMultiplier;\n        // For very long text, also adjust the height slightly\n        if (textLength > 8) {\n            scaleY = scaleY * Math.min(1.2, 1.0 + (textLength - 8) * 0.02);\n        }\n        // Cap maximum scale to prevent excessive sizing (updated for larger Y values)\n        const maxScale = {\n            chosenring: [\n                12.0,\n                2.5,\n                2.0\n            ],\n            testring: [\n                12.0,\n                2.5,\n                2.0\n            ],\n            agapering1: [\n                6.5,\n                2.2,\n                1.5\n            ],\n            agapering2: [\n                6.5,\n                2.2,\n                1.5\n            ],\n            ringa: [\n                4.0,\n                2.5,\n                1.2\n            ],\n            ringb: [\n                4.0,\n                2.5,\n                1.2\n            ],\n            ringc: [\n                4.0,\n                2.5,\n                1.2\n            ]\n        };\n        const limits = maxScale[modelId] || [\n            8.5,\n            2.5,\n            2.0\n        ];\n        scaleX = Math.min(scaleX, limits[0]);\n        scaleY = Math.min(scaleY, limits[1]);\n        scaleZ = Math.min(scaleZ, limits[2]);\n        return [\n            scaleX,\n            scaleY,\n            scaleZ\n        ];\n    }, []);\n    // Update scale when text changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedModel && engravingText) {\n            const baseScale = getDefaultDecalScale(selectedModel);\n            const dynamicScale = calculateDynamicScale(baseScale, engravingText, selectedModel);\n            setDecalScale(dynamicScale);\n        }\n    }, [\n        engravingText,\n        selectedModel,\n        getDefaultDecalScale,\n        calculateDynamicScale\n    ]);\n    // Initialize decal position based on selected model\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedModel) {\n            setDecalPosition(getDefaultDecalPosition(selectedModel));\n            setDecalRotation(getDefaultDecalRotation(selectedModel));\n            // Only set initial scale if there's no engraving text yet\n            if (!engravingText) {\n                setDecalScale(getDefaultDecalScale(selectedModel));\n            }\n        }\n    }, [\n        selectedModel,\n        getDefaultDecalPosition,\n        getDefaultDecalRotation,\n        getDefaultDecalScale,\n        engravingText\n    ]);\n    const [isFullscreen, setIsFullscreen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Get camera position based on device type\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const checkMobile = ()=>{\n            setIsMobile(window.innerWidth < 768);\n        };\n        checkMobile();\n        window.addEventListener(\"resize\", checkMobile);\n        return ()=>window.removeEventListener(\"resize\", checkMobile);\n    }, []);\n    // Add state to control outliner collapsed state - initialize based on device type\n    const [outlinerCollapsed, setOutlinerCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        // Initialize based on device type - closed on mobile, open on desktop\n        if (true) {\n            return window.innerWidth < 768;\n        }\n        return false; // Default to open on server-side rendering\n    });\n    // Effect to close outliner when toolbar panels are opened on mobile\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isMobile && rightPanelTab) {\n            setOutlinerCollapsed(true);\n        }\n    }, [\n        isMobile,\n        rightPanelTab\n    ]);\n    // Effect to update outliner collapsed state when mobile state changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Update outliner state based on device type - closed on mobile, open on desktop\n        setOutlinerCollapsed(isMobile);\n    }, [\n        isMobile\n    ]);\n    const getCameraPosition = ()=>{\n        if (isMobile) {\n            return [\n                -1.85,\n                3.25,\n                4.5\n            ];\n        }\n        return [\n            -1.85,\n            1.55,\n            3.5\n        ];\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // On initial mount, fetch and apply the current version from DB\n        const fetchAndApplyCurrentVersion = async ()=>{\n            try {\n                const currentVersion = await (0,_lib_actions_version_actions__WEBPACK_IMPORTED_MODULE_23__.getCurrentVersion)({\n                    userId: user.id,\n                    workspaceId: project._id\n                });\n                if (currentVersion && currentVersion.config) {\n                    handleLoadScene(currentVersion.config);\n                }\n            } catch (err) {\n                console.error(\"Failed to load current version from DB\", err);\n            }\n        };\n        fetchAndApplyCurrentVersion();\n    // Only run on mount\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_toastify__WEBPACK_IMPORTED_MODULE_2__.ToastContainer, {}, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1769,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Header__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                onReset: handleReset,\n                onScreenshot: captureScreenshot,\n                onSaveScene: handleSaveScene,\n                onLoadScene: handleLoadScene,\n                isFullscreen: isFullscreen,\n                onToggleFullscreen: ()=>setIsFullscreen(!isFullscreen),\n                project: project,\n                user: user,\n                onUndo: undoAction,\n                onRedo: redoAction\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1770,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_InitialOverlayPanel__WEBPACK_IMPORTED_MODULE_19__.InitialOverlayPanel, {}, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1783,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed left-5 top-1/2 transform -translate-y-1/2 z-[1000] transition-all duration-300 \".concat(isFullscreen ? \"opacity-0 pointer-events-none\" : \"opacity-100\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Toolbar__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    transformMode: transformMode,\n                    setTransformMode: setTransformMode,\n                    undoAction: undoAction,\n                    redoAction: redoAction,\n                    rightPanelTab: rightPanelTab,\n                    setRightPanelTab: setRightPanelTab,\n                    orientation: \"vertical\",\n                    sceneObjects: sceneObjects,\n                    setSelectedObjects: setSelectedObjects,\n                    selectedObjects: selectedObjects,\n                    panelRef: panelRef,\n                    isMobile: isMobile,\n                    setOutlinerCollapsed: setOutlinerCollapsed\n                }, void 0, false, {\n                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                    lineNumber: 1791,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1786,\n                columnNumber: 7\n            }, this),\n            rightPanelTab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: panelRef,\n                className: \"fixed left-20 top-1/2 transform -translate-y-1/2 bg-gradient-to-tl from-[#32343D] to-[#14192D] p-4 rounded-lg shadow-lg z-[1000] max-h-[80vh] overflow-y-auto w-[250px] md:w-[300px] border border-[#A3A3A3]/30 scrollbar-none\",\n                children: [\n                    rightPanelTab === \"transform\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TransformPanel__WEBPACK_IMPORTED_MODULE_13__.TransformPanel, {\n                        selectedObjects: selectedObjects,\n                        transformMode: transformMode,\n                        setTransformMode: setTransformMode,\n                        undoAction: undoAction,\n                        redoAction: redoAction,\n                        addToHistory: addToHistory,\n                        invalidate: _react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.m\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1815,\n                        columnNumber: 13\n                    }, this),\n                    rightPanelTab === \"material\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MaterialPanel__WEBPACK_IMPORTED_MODULE_7__.MaterialPanel, {\n                        selectedObjects: selectedObjects,\n                        useDiamondShader: useDiamondShader,\n                        setUseDiamondShader: setUseDiamondShader,\n                        usePremiumWhiteGold: usePremiumWhiteGold,\n                        setUsePremiumWhiteGold: setUsePremiumWhiteGold,\n                        usePremiumRoseGold: usePremiumRoseGold,\n                        setUsePremiumRoseGold: setUsePremiumRoseGold,\n                        renderer: renderer,\n                        forceUpdate: forceUpdate,\n                        setMaterialVersion: setMaterialVersion,\n                        invalidate: _react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.m,\n                        addToHistory: addToHistory\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1827,\n                        columnNumber: 13\n                    }, this),\n                    rightPanelTab === \"postprocessing\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PostProcessingPanel__WEBPACK_IMPORTED_MODULE_10__.PostProcessingPanel, {\n                        postProcessingEnabled: postProcessingEnabled,\n                        setPostProcessingEnabled: setPostProcessingEnabled,\n                        postProcessingSettings: postProcessingSettings,\n                        setPostProcessingSettings: setPostProcessingSettings\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1844,\n                        columnNumber: 13\n                    }, this),\n                    rightPanelTab === \"environment\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EnvironmentPanel__WEBPACK_IMPORTED_MODULE_4__.EnvironmentPanel, {\n                        envPreset: envPreset,\n                        setEnvPreset: setEnvPreset,\n                        bgColor: bgColor,\n                        setBgColor: setBgColor,\n                        showEnvironment: showEnvironment,\n                        setShowEnvironment: setShowEnvironment,\n                        customHdri: customHdri,\n                        setCustomHdri: setCustomHdri,\n                        envIntensity: envIntensity,\n                        setEnvIntensity: setEnvIntensity,\n                        showModelStats: showModelStats,\n                        setShowModelStats: setShowModelStats,\n                        envBlur: envBlur,\n                        setEnvBlur: setEnvBlur,\n                        envRotation: envRotation,\n                        setEnvRotation: setEnvRotation\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1853,\n                        columnNumber: 13\n                    }, this),\n                    rightPanelTab === \"lights\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LightPanel__WEBPACK_IMPORTED_MODULE_6__.LightPanel, {\n                        lights: lights,\n                        setLights: setLights,\n                        lightRefs: lightRefs,\n                        showGrid: showGrid,\n                        setShowGrid: setShowGrid,\n                        wireframe: wireframe,\n                        setWireframe: setWireframe,\n                        showLightSpheres: showLightSpheres,\n                        setShowLightSpheres: setShowLightSpheres,\n                        onResetLights: ()=>{\n                            // Reset lights to default configuration\n                            const defaultLights = [\n                                {\n                                    id: \"defaultDirectional\",\n                                    type: \"directional\",\n                                    position: [\n                                        2.5,\n                                        5.5,\n                                        5\n                                    ],\n                                    intensity: 1.0,\n                                    color: \"#ffffff\",\n                                    name: \"Main Directional Light\",\n                                    castShadow: true,\n                                    helperVisible: false\n                                },\n                                {\n                                    id: \"keyLight\",\n                                    type: \"point\",\n                                    position: [\n                                        5,\n                                        5,\n                                        5\n                                    ],\n                                    intensity: 8.0,\n                                    color: \"#ffffff\",\n                                    name: \"Key Light\"\n                                },\n                                {\n                                    id: \"fillLight\",\n                                    type: \"point\",\n                                    position: [\n                                        -5,\n                                        5,\n                                        -5\n                                    ],\n                                    intensity: 4.0,\n                                    color: \"#ffffff\",\n                                    name: \"Fill Light\"\n                                },\n                                {\n                                    id: \"backLight\",\n                                    type: \"point\",\n                                    position: [\n                                        0,\n                                        5,\n                                        -5\n                                    ],\n                                    intensity: 2.0,\n                                    color: \"#ffffff\",\n                                    name: \"Back Light\"\n                                }\n                            ];\n                            setLights(defaultLights);\n                            // Clear selected light\n                            setSelectedLight(null);\n                            // Clear light position history\n                            setLightPositionHistory({});\n                        }\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1874,\n                        columnNumber: 13\n                    }, this),\n                    rightPanelTab === \"engraving\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EngravingPanel__WEBPACK_IMPORTED_MODULE_18__.EngravingPanel, {\n                        onEngraveText: handleEngraveText,\n                        onDecalPositionChange: setDecalPosition,\n                        onDecalScaleChange: setDecalScale,\n                        onDecalRotationChange: setDecalRotation,\n                        decalPosition: decalPosition,\n                        decalScale: decalScale,\n                        decalRotation: decalRotation,\n                        decalColor: decalColor,\n                        onDecalColorChange: setDecalColor,\n                        onFontChange: handleFontChange,\n                        selectedFont: selectedFont,\n                        isPlacingDecal: isPlacingDecal,\n                        setIsPlacingDecal: setIsPlacingDecal,\n                        showDebug: showDecalDebug,\n                        onShowDebugChange: setShowDecalDebug\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1932,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1810,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed right-5 top-24 z-[999] transition-all duration-300 \".concat(isFullscreen ? \"opacity-0 pointer-events-none\" : \"opacity-100\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-tl from-[#32343D] to-[#14192D] shadow-lg rounded-lg overflow-visible scrollbar-none\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Outliner__WEBPACK_IMPORTED_MODULE_9__.Outliner, {\n                        sceneObjects: sceneObjects,\n                        selectedObjects: selectedObjects,\n                        onSelectObject: setSelectedObjects,\n                        onCameraView: handleCameraView,\n                        onPlayAnimation: handlePlayAnimation,\n                        onSaveScene: handleSaveScene,\n                        groundType: groundType,\n                        setGroundType: setGroundType,\n                        collapsed: outlinerCollapsed,\n                        setCollapsed: setOutlinerCollapsed\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1960,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                    lineNumber: 1959,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1954,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-full\",\n                style: {\n                    background: \"linear-gradient(to bottom right, #363643, #5C5C6E)\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_fiber__WEBPACK_IMPORTED_MODULE_28__.Canvas, {\n                    className: \"w-full h-full\",\n                    camera: {\n                        position: getCameraPosition(),\n                        fov: 45,\n                        near: 0.1,\n                        far: 50\n                    },\n                    shadows: true,\n                    ref: sceneRef,\n                    gl: {\n                        antialias: true,\n                        preserveDrawingBuffer: true\n                    },\n                    onPointerMissed: handlePointerMissed,\n                    raycaster: {\n                        computeOffsets: (e)=>({\n                                offsetX: e.clientX,\n                                offsetY: e.clientY\n                            })\n                    },\n                    frameloop: \"demand\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n                        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_29__.Html, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_30__.Loader, {}, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2005,\n                                columnNumber: 17\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                            lineNumber: 2004,\n                            columnNumber: 15\n                        }, void 0),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ScreenshotTaker, {\n                                onScreenshotRef: (fn)=>{\n                                    screenshotRef.current = fn;\n                                }\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2011,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RendererProvider, {\n                                onRendererReady: (gl)=>{\n                                    setRenderer(gl);\n                                }\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2017,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SceneLights__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                lights: lights,\n                                lightRefs: lightRefs,\n                                selectedLight: selectedLight,\n                                onLightSelect: setSelectedLight,\n                                onLightMoved: handleLightMoved,\n                                showLightSpheres: showLightSpheres,\n                                threePointLighting: true\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2023,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Environment__WEBPACK_IMPORTED_MODULE_3__.Environment, {\n                                preset: getEnvironmentPreset(),\n                                background: showEnvironment,\n                                bgColor: bgColor,\n                                customHdri: customHdri,\n                                intensity: envIntensity,\n                                blur: envBlur,\n                                envRotation: envRotation\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2032,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SelectionOutline__WEBPACK_IMPORTED_MODULE_22__.SelectionOutline, {\n                                selectedObjects: selectedObjects,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ModelStage__WEBPACK_IMPORTED_MODULE_20__.ModelStage, {\n                                    selectedModel: selectedModel,\n                                    wireframe: wireframe,\n                                    handleSceneUpdate: handleSceneUpdate,\n                                    handleObjectClick: handleObjectClick,\n                                    uploadedModel: uploadedModel,\n                                    groundType: groundType,\n                                    engravingText: engravingText,\n                                    activeAnimation: activeAnimation,\n                                    decalPosition: decalPosition,\n                                    decalScale: decalScale,\n                                    decalRotation: decalRotation,\n                                    decalColor: decalColor,\n                                    font: selectedFont,\n                                    isPlacingDecal: isPlacingDecal,\n                                    onDecalPlacement: handleDecalPlacement,\n                                    showDecalDebug: showDecalDebug,\n                                    sceneObjects: sceneObjects,\n                                    useDiamondShader: useDiamondShader,\n                                    usePremiumWhiteGold: usePremiumWhiteGold,\n                                    usePremiumRoseGold: usePremiumRoseGold\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                    lineNumber: 2043,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2042,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_31__.OrbitControls, {\n                                ref: orbitControlsRef,\n                                target: [\n                                    0,\n                                    0.5,\n                                    0\n                                ],\n                                makeDefault: true,\n                                enableDamping: true,\n                                // minPolarAngle={0}\n                                // maxPolarAngle={Math.PI / 2.25}\n                                // minDistance={2.5}\n                                maxDistance: 20\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2067,\n                                columnNumber: 13\n                            }, this),\n                            selectedObjects.length > 0 && rightPanelTab === \"transform\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_32__.TransformControls, {\n                                object: selectedObjects.length === 1 ? selectedObjects[0] : selectionGroupRef.current,\n                                mode: transformMode,\n                                onMouseDown: ()=>{\n                                    if (orbitControlsRef.current) orbitControlsRef.current.enabled = false;\n                                },\n                                onMouseUp: ()=>{\n                                    if (orbitControlsRef.current) orbitControlsRef.current.enabled = true;\n                                    if (selectedObjects.length > 1) updateObjectsFromGroup();\n                                    forceUpdate(); // Ensure immediate update after transform\n                                    (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.m)(); // Force R3F to re-render after transform\n                                },\n                                onObjectChange: ()=>{\n                                    if (selectedObjects.length > 1) updateObjectsFromGroup();\n                                    forceUpdate(); // Ensure immediate update after transform\n                                    (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.m)(); // Force R3F to re-render after transform\n                                }\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2079,\n                                columnNumber: 15\n                            }, this),\n                            groundType === \"grid\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"gridHelper\", {\n                                args: [\n                                    50,\n                                    50,\n                                    \"#666666\",\n                                    \"#333333\"\n                                ],\n                                receiveShadow: true\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2105,\n                                columnNumber: 15\n                            }, this),\n                            groundType === \"solid\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n                                \"rotation-x\": -Math.PI / 2,\n                                receiveShadow: true,\n                                position: [\n                                    0,\n                                    -0.01,\n                                    0\n                                ],\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"planeGeometry\", {\n                                        args: [\n                                            200,\n                                            200\n                                        ]\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2113,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meshStandardMaterial\", {\n                                        color: \"#5d5d5d\",\n                                        wireframe: wireframe,\n                                        transparent: true,\n                                        opacity: 0.7\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2114,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2108,\n                                columnNumber: 15\n                            }, this),\n                            groundType === \"reflective\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n                                receiveShadow: true,\n                                rotation: [\n                                    -Math.PI / 2,\n                                    0,\n                                    0\n                                ],\n                                position: [\n                                    0,\n                                    -0.01,\n                                    0\n                                ],\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"planeGeometry\", {\n                                        args: [\n                                            80,\n                                            80\n                                        ]\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2128,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_33__.MeshReflectorMaterial, {\n                                        blur: [\n                                            800,\n                                            800\n                                        ],\n                                        resolution: 2048,\n                                        mixBlur: 0.5,\n                                        mixStrength: 10,\n                                        roughness: 1,\n                                        depthScale: 1,\n                                        minDepthThreshold: 0.4,\n                                        maxDepthThreshold: 1.2,\n                                        color: \"#363643\",\n                                        metalness: 0.01,\n                                        mirror: 0.3,\n                                        transparent: true,\n                                        opacity: 0.5\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2129,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2123,\n                                columnNumber: 15\n                            }, this),\n                            groundType === \"none\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n                                receiveShadow: true,\n                                rotation: [\n                                    -Math.PI / 2,\n                                    0,\n                                    0\n                                ],\n                                position: [\n                                    0,\n                                    -0.01,\n                                    0\n                                ],\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"planeGeometry\", {\n                                        args: [\n                                            200,\n                                            200\n                                        ]\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2152,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"primitive\", {\n                                        object: new three__WEBPACK_IMPORTED_MODULE_27__.ShadowMaterial({\n                                            opacity: 0.1,\n                                            color: new three__WEBPACK_IMPORTED_MODULE_27__.Color(0x000000),\n                                            transparent: true\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2153,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2147,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_34__.GizmoHelper, {\n                                alignment: \"bottom-right\",\n                                margin: [\n                                    80,\n                                    80\n                                ],\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_35__.GizmoViewport, {\n                                    labelColor: \"white\",\n                                    axisColors: [\n                                        \"red\",\n                                        \"green\",\n                                        \"blue\"\n                                    ]\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                    lineNumber: 2165,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2164,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"primitive\", {\n                                object: selectionGroupRef.current\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2171,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PostProcessingEffects__WEBPACK_IMPORTED_MODULE_21__.PostProcessingEffects, {\n                                enabled: postProcessingEnabled,\n                                settings: postProcessingSettings\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2173,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 2002,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                    lineNumber: 1982,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1976,\n                columnNumber: 7\n            }, this),\n            showModelStats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ModelStats__WEBPACK_IMPORTED_MODULE_8__.ModelStats, {\n                stats: modelStats\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 2181,\n                columnNumber: 26\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s2(EditorExperience, \"ZLQhfvycWEpm2Lwr19azPv9+kpY=\", false, function() {\n    return [\n        _lib_states__WEBPACK_IMPORTED_MODULE_25__.useVersionState\n    ];\n});\n_c3 = EditorExperience;\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"ScreenshotTaker\");\n$RefreshReg$(_c1, \"MaterialPanelWrapper\");\n$RefreshReg$(_c2, \"RendererProvider\");\n$RefreshReg$(_c3, \"EditorExperience\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ArtistTool/EditorExperience.jsx\n"));

/***/ })

});