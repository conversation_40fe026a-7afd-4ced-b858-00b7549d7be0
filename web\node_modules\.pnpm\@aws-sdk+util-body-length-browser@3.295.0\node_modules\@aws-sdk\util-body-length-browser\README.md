# @aws-sdk/util-body-length-browser

[![NPM version](https://img.shields.io/npm/v/@aws-sdk/util-body-length-browser/latest.svg)](https://www.npmjs.com/package/@aws-sdk/util-body-length-browser)
[![NPM downloads](https://img.shields.io/npm/dm/@aws-sdk/util-body-length-browser.svg)](https://www.npmjs.com/package/@aws-sdk/util-body-length-browser)

Determines the length of a request body in browsers

> An internal package

## Usage

You probably shouldn't, at least directly.
