import { LoadedConfigSelectors } from "@aws-sdk/node-config-provider";
export declare const NODE_DISABLE_MULTIREGION_ACCESS_POINT_ENV_NAME =
  "AWS_S3_DISABLE_MULTIREGION_ACCESS_POINTS";
export declare const NODE_DISABLE_MULTIREGION_ACCESS_POINT_INI_NAME =
  "s3_disable_multiregion_access_points";
export declare const NODE_DISABLE_MULTIREGION_ACCESS_POINT_CONFIG_OPTIONS: LoadedConfigSelectors<boolean>;
