"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/projects/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/ArtistTool/EditorExperience.jsx":
/*!********************************************************!*\
  !*** ./src/components/ArtistTool/EditorExperience.jsx ***!
  \********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ EditorExperience; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/web/Html.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/web/Loader.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/OrbitControls.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/TransformControls.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/MeshReflectorMaterial.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/GizmoHelper.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/GizmoViewport.js\");\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @react-three/fiber */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_three@0.167.1/node_modules/@react-three/fiber/dist/events-776716bd.esm.js\");\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @react-three/fiber */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_three@0.167.1/node_modules/@react-three/fiber/dist/react-three-fiber.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/.pnpm/react-toastify@11.0.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/react-toastify/dist/index.mjs\");\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! three */ \"(app-pages-browser)/./node_modules/.pnpm/three@0.167.1/node_modules/three/build/three.module.js\");\n/* harmony import */ var _Environment__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Environment */ \"(app-pages-browser)/./src/components/ArtistTool/Environment.tsx\");\n/* harmony import */ var _EnvironmentPanel__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./EnvironmentPanel */ \"(app-pages-browser)/./src/components/ArtistTool/EnvironmentPanel.jsx\");\n/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Header */ \"(app-pages-browser)/./src/components/ArtistTool/Header.jsx\");\n/* harmony import */ var _LightPanel__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./LightPanel */ \"(app-pages-browser)/./src/components/ArtistTool/LightPanel.jsx\");\n/* harmony import */ var _MaterialPanel__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./MaterialPanel */ \"(app-pages-browser)/./src/components/ArtistTool/MaterialPanel.jsx\");\n/* harmony import */ var _ModelStats__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./ModelStats */ \"(app-pages-browser)/./src/components/ArtistTool/ModelStats.jsx\");\n/* harmony import */ var _Outliner__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./Outliner */ \"(app-pages-browser)/./src/components/ArtistTool/Outliner.jsx\");\n/* harmony import */ var _PostProcessingPanel__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./PostProcessingPanel */ \"(app-pages-browser)/./src/components/ArtistTool/PostProcessingPanel.jsx\");\n/* harmony import */ var _SceneLights__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./SceneLights */ \"(app-pages-browser)/./src/components/ArtistTool/SceneLights.jsx\");\n/* harmony import */ var _Toolbar__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./Toolbar */ \"(app-pages-browser)/./src/components/ArtistTool/Toolbar.jsx\");\n/* harmony import */ var _TransformPanel__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./TransformPanel */ \"(app-pages-browser)/./src/components/ArtistTool/TransformPanel.jsx\");\n/* harmony import */ var _utils_ArtistTool_cameraUtils__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../../utils/ArtistTool/cameraUtils */ \"(app-pages-browser)/./src/utils/ArtistTool/cameraUtils.js\");\n/* harmony import */ var _utils_ArtistTool_historyUtils__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../../utils/ArtistTool/historyUtils */ \"(app-pages-browser)/./src/utils/ArtistTool/historyUtils.js\");\n/* harmony import */ var _utils_ArtistTool_materialUtils__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../../utils/ArtistTool/materialUtils */ \"(app-pages-browser)/./src/utils/ArtistTool/materialUtils.js\");\n/* harmony import */ var _utils_ArtistTool_sceneUtils__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ../../utils/ArtistTool/sceneUtils */ \"(app-pages-browser)/./src/utils/ArtistTool/sceneUtils.js\");\n/* harmony import */ var _animations_CameraAnimations__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./animations/CameraAnimations */ \"(app-pages-browser)/./src/components/ArtistTool/animations/CameraAnimations.js\");\n/* harmony import */ var _EngravingPanel__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./EngravingPanel */ \"(app-pages-browser)/./src/components/ArtistTool/EngravingPanel.jsx\");\n/* harmony import */ var _InitialOverlayPanel__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./InitialOverlayPanel */ \"(app-pages-browser)/./src/components/ArtistTool/InitialOverlayPanel.jsx\");\n/* harmony import */ var _ModelStage__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./ModelStage */ \"(app-pages-browser)/./src/components/ArtistTool/ModelStage.jsx\");\n/* harmony import */ var _PostProcessingEffects__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ./PostProcessingEffects */ \"(app-pages-browser)/./src/components/ArtistTool/PostProcessingEffects.jsx\");\n/* harmony import */ var _SelectionOutline__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! ./SelectionOutline */ \"(app-pages-browser)/./src/components/ArtistTool/SelectionOutline.jsx\");\n/* harmony import */ var _lib_actions_version_actions__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/lib/actions/version.actions */ \"(app-pages-browser)/./src/lib/actions/version.actions.ts\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/.pnpm/sonner@1.5.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _lib_states__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @/lib/states */ \"(app-pages-browser)/./src/lib/states.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst ScreenshotTaker = (param)=>{\n    let { onScreenshotRef } = param;\n    _s();\n    const { gl, scene, camera } = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_27__.D)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (typeof onScreenshotRef === \"function\") {\n            onScreenshotRef(()=>{\n                try {\n                    // Render the scene\n                    gl.render(scene, camera);\n                    // Get the canvas data URL\n                    const dataURL = gl.domElement.toDataURL(\"image/png\");\n                    // Create and trigger a download\n                    const link = document.createElement(\"a\");\n                    const timestamp = new Date().toISOString().replace(/:/g, \"-\").substring(0, 19);\n                    link.download = \"agape-scene-\".concat(timestamp, \".png\");\n                    link.href = dataURL;\n                    document.body.appendChild(link);\n                    link.click();\n                    // Clean up\n                    setTimeout(()=>{\n                        document.body.removeChild(link);\n                    }, 100);\n                    console.log(\"Screenshot captured successfully\");\n                    return true;\n                } catch (error) {\n                    console.error(\"Error capturing screenshot:\", error);\n                    return false;\n                }\n            });\n        }\n    }, [\n        gl,\n        scene,\n        camera,\n        onScreenshotRef\n    ]);\n    return null;\n};\n_s(ScreenshotTaker, \"RVTFHuczdlyvGFUsZJPSpjurI/Y=\", false, function() {\n    return [\n        _react_three_fiber__WEBPACK_IMPORTED_MODULE_27__.D\n    ];\n});\n_c = ScreenshotTaker;\n// Component to provide renderer to MaterialPanel\nconst MaterialPanelWrapper = (param)=>{\n    let { renderer, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MaterialPanel__WEBPACK_IMPORTED_MODULE_7__.MaterialPanel, {\n        renderer: renderer,\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n        lineNumber: 96,\n        columnNumber: 10\n    }, undefined);\n};\n_c1 = MaterialPanelWrapper;\n// Component to get renderer from Canvas context\nconst RendererProvider = (param)=>{\n    let { children, onRendererReady } = param;\n    _s1();\n    const { gl } = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_27__.D)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (onRendererReady) {\n            onRendererReady(gl);\n        }\n    }, [\n        gl,\n        onRendererReady\n    ]);\n    return null;\n};\n_s1(RendererProvider, \"iTWbOa64ZxlA5nuyNExgWK+vjCI=\", false, function() {\n    return [\n        _react_three_fiber__WEBPACK_IMPORTED_MODULE_27__.D\n    ];\n});\n_c2 = RendererProvider;\nfunction EditorExperience(param) {\n    let { modelUrl, project, user } = param;\n    _s2();\n    const { addVersion } = (0,_lib_states__WEBPACK_IMPORTED_MODULE_26__.useVersionState)();\n    const configRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const workingConfigRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [selectedModel, setSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"uploaded\");\n    const [lastSelectedModel, setLastSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [uploadedModel, setUploadedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(modelUrl || null);\n    const [modelStats, setModelStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        vertices: 0,\n        triangles: 0,\n        materials: 0,\n        animations: 0\n    });\n    const [lights, setLights] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: \"defaultDirectional\",\n            type: \"directional\",\n            position: [\n                2.5,\n                5.5,\n                5\n            ],\n            intensity: 1.0,\n            color: \"#ffffff\",\n            name: \"Main Directional Light\",\n            castShadow: true,\n            helperVisible: false\n        },\n        {\n            id: \"keyLight\",\n            type: \"point\",\n            position: [\n                5,\n                5,\n                5\n            ],\n            intensity: 8.0,\n            color: \"#ffffff\",\n            name: \"Key Light\"\n        },\n        {\n            id: \"fillLight\",\n            type: \"point\",\n            position: [\n                -5,\n                5,\n                -5\n            ],\n            intensity: 4.0,\n            color: \"#ffffff\",\n            name: \"Fill Light\"\n        },\n        {\n            id: \"backLight\",\n            type: \"point\",\n            position: [\n                0,\n                5,\n                -5\n            ],\n            intensity: 2.0,\n            color: \"#ffffff\",\n            name: \"Back Light\"\n        }\n    ]);\n    const lightRefs = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({});\n    const [selectedObjects, setSelectedObjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedLight, setSelectedLight] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [lightPositionHistory, setLightPositionHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [sceneObjects, setSceneObjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [transformMode, setTransformMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"translate\");\n    const selectionGroupRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(new three__WEBPACK_IMPORTED_MODULE_28__.Group());\n    console.log(\"renderingggg editor experience\");\n    const [rightPanelTab, setRightPanelTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Decal placement state\n    const [isPlacingDecal, setIsPlacingDecal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showDecalDebug, setShowDecalDebug] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Scene controls state\n    const [showGrid, setShowGrid] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [wireframe, setWireframe] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [groundType, setGroundType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"none\");\n    const [envPreset, setEnvPreset] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"hdri_15\");\n    const [bgColor, setBgColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"#272631\");\n    const [showEnvironment, setShowEnvironment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showLightSpheres, setShowLightSpheres] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [customHdri, setCustomHdri] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [envIntensity, setEnvIntensity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1.85);\n    const [envBlur, setEnvBlur] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0.35);\n    const [envRotation, setEnvRotation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [postProcessingEnabled, setPostProcessingEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [postProcessingSettings, setPostProcessingSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        autofocus: {\n            enabled: false,\n            bokehScale: 18\n        },\n        bloom: {\n            enabled: true,\n            intensity: 0.22,\n            threshold: 1,\n            radius: 0.5\n        },\n        dof: {\n            enabled: false,\n            focusDistance: 0,\n            aperture: 0.01,\n            bokehScale: 3\n        },\n        vignette: {\n            enabled: true,\n            darkness: 0.5,\n            offset: 0.1\n        }\n    });\n    // Store initial state\n    const initialStateRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        selectedModel: null,\n        lights: [],\n        envPreset: null,\n        bgColor: null,\n        postProcessingSettings: null,\n        showGrid: null,\n        wireframe: null,\n        groundType: null,\n        showEnvironment: null,\n        showLightSpheres: null,\n        envIntensity: null,\n        envBlur: null,\n        cameraPosition: null,\n        cameraTarget: null\n    });\n    // Add a ref to store the initial state of model objects\n    const modelInitialStateRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        objects: []\n    });\n    // Capture initial state when component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        initialStateRef.current = {\n            selectedModel,\n            lights: [\n                ...lights\n            ],\n            envPreset,\n            bgColor,\n            postProcessingSettings: {\n                ...postProcessingSettings\n            },\n            showGrid,\n            wireframe,\n            groundType,\n            showEnvironment,\n            showLightSpheres,\n            envIntensity,\n            envBlur,\n            cameraPosition: new three__WEBPACK_IMPORTED_MODULE_28__.Vector3(5, 2, 5),\n            cameraTarget: new three__WEBPACK_IMPORTED_MODULE_28__.Vector3(0, 1, 0)\n        };\n    }, []);\n    const orbitControlsRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const sceneRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const [renderer, setRenderer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // History state for undo/redo\n    const [history, setHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [historyIndex, setHistoryIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(-1);\n    // --- UNDO/REDO SYSTEM REPLACEMENT START ---\n    // Remove old undo/redo/addToHistory logic and state\n    // Add new refs for undo/redo flags\n    const isUndoRedoActionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const skipHistoryTrackingRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const isUndoRedoInProgressRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const pendingSelectedObjectsUUIDs = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [_, setForceUpdate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const forceUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>setForceUpdate((f)=>f + 1), []);\n    const [transformVersion, setTransformVersion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [materialVersion, setMaterialVersion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const lastStateRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [showModelStats, setShowModelStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // getFullConfig: serializes all relevant state for history\n    function getFullConfig() {\n        return JSON.parse(JSON.stringify({\n            selectedModel,\n            uploadedModel,\n            lights,\n            envPreset,\n            bgColor,\n            showEnvironment,\n            showLightSpheres,\n            customHdri,\n            envIntensity,\n            envBlur,\n            envRotation,\n            postProcessingEnabled,\n            postProcessingSettings,\n            showGrid,\n            wireframe,\n            groundType,\n            selectedObjects: selectedObjects.map((obj)=>obj.uuid),\n            objectStates: sceneObjects.map((obj)=>({\n                    uuid: obj.uuid,\n                    name: obj.name,\n                    position: obj.position.toArray(),\n                    rotation: obj.rotation.toArray(),\n                    scale: obj.scale.toArray(),\n                    material: obj.material ? {\n                        type: obj.material.type,\n                        color: obj.material.color ? obj.material.color.getHexString() : \"ffffff\",\n                        roughness: obj.material.roughness,\n                        metalness: obj.material.metalness,\n                        clearcoat: obj.material.clearcoat,\n                        clearcoatRoughness: obj.material.clearcoatRoughness,\n                        wireframe: obj.material.wireframe,\n                        transparent: obj.material.transparent,\n                        opacity: obj.material.opacity,\n                        envMapIntensity: obj.material.envMapIntensity\n                    } : null\n                }))\n        }));\n    }\n    // Debug function for history\n    const debugHistory = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        console.log(\"=== HISTORY DEBUG ===\");\n        console.log(\"Current historyIndex:\", historyIndex);\n        console.log(\"History length:\", history.length);\n        console.log(\"Can undo:\", historyIndex > 0);\n        console.log(\"Can redo:\", historyIndex < history.length - 1);\n        console.log(\"isUndoRedoActionRef.current:\", isUndoRedoActionRef.current);\n        console.log(\"skipHistoryTrackingRef.current:\", skipHistoryTrackingRef.current);\n        console.log(\"isUndoRedoInProgressRef.current:\", isUndoRedoInProgressRef.current);\n        console.log(\"History entries:\", history.map((_, i)=>\"\".concat(i).concat(i === historyIndex ? \" (current)\" : \"\")));\n        console.log(\"==================\");\n    }, [\n        historyIndex,\n        history\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        window.debugHistory = debugHistory;\n        return ()=>{\n            delete window.debugHistory;\n        };\n    }, [\n        debugHistory\n    ]);\n    // Add initial state to history on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (history.length === 0) {\n            const initialState = getFullConfig();\n            setHistory([\n                initialState\n            ]);\n            setHistoryIndex(0);\n        }\n    }, [\n        history.length\n    ]);\n    // Add a new state to the history\n    const addToHistory = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (isUndoRedoActionRef.current || skipHistoryTrackingRef.current) {\n            return;\n        }\n        const newState = getFullConfig();\n        if (history.length > 0 && historyIndex >= 0) {\n            const currentState = history[historyIndex];\n            if (JSON.stringify(currentState) === JSON.stringify(newState)) {\n                return;\n            }\n        }\n        const newHistory = history.slice(0, historyIndex + 1);\n        setHistory([\n            ...newHistory,\n            newState\n        ]);\n        const newIndex = historyIndex + 1;\n        setHistoryIndex(newIndex);\n    }, [\n        history,\n        historyIndex,\n        getFullConfig\n    ]);\n    const restoreConfig = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((config)=>{\n        setSelectedModel(config.selectedModel);\n        setUploadedModel(config.uploadedModel);\n        setLights(config.lights);\n        setEnvPreset(config.envPreset);\n        setBgColor(config.bgColor);\n        setShowEnvironment(config.showEnvironment);\n        setShowLightSpheres(config.showLightSpheres);\n        setCustomHdri(config.customHdri);\n        setEnvIntensity(config.envIntensity);\n        setEnvBlur(config.envBlur);\n        setEnvRotation(config.envRotation);\n        setPostProcessingEnabled(config.postProcessingEnabled);\n        setPostProcessingSettings(config.postProcessingSettings);\n        setShowGrid(config.showGrid);\n        setWireframe(config.wireframe);\n        setGroundType(config.groundType);\n        // Restore object states (transforms and materials)\n        if (config.objectStates && sceneObjects.length > 0) {\n            config.objectStates.forEach((savedState)=>{\n                const obj = sceneObjects.find((o)=>o.uuid === savedState.uuid);\n                if (obj) {\n                    obj.position.fromArray(savedState.position);\n                    obj.rotation.fromArray(savedState.rotation);\n                    obj.scale.fromArray(savedState.scale);\n                    if (savedState.material && obj.material) {\n                        const material = obj.material;\n                        if (material.color) {\n                            material.color.setHex(parseInt(savedState.material.color, 16));\n                        }\n                        if (savedState.material.roughness !== undefined) {\n                            material.roughness = savedState.material.roughness;\n                        }\n                        if (savedState.material.metalness !== undefined) {\n                            material.metalness = savedState.material.metalness;\n                        }\n                        if (savedState.material.clearcoat !== undefined) {\n                            material.clearcoat = savedState.material.clearcoat;\n                        }\n                        if (savedState.material.clearcoatRoughness !== undefined) {\n                            material.clearcoatRoughness = savedState.material.clearcoatRoughness;\n                        }\n                        if (savedState.material.wireframe !== undefined) {\n                            material.wireframe = savedState.material.wireframe;\n                        }\n                        if (savedState.material.transparent !== undefined) {\n                            material.transparent = savedState.material.transparent;\n                        }\n                        if (savedState.material.opacity !== undefined) {\n                            material.opacity = savedState.material.opacity;\n                        }\n                        if (savedState.material.envMapIntensity !== undefined) {\n                            material.envMapIntensity = savedState.material.envMapIntensity;\n                        }\n                        material.needsUpdate = true;\n                    }\n                }\n            });\n            forceUpdate();\n            if (!isUndoRedoActionRef.current) {\n                setMaterialVersion((v)=>v + 1);\n                setTransformVersion((v)=>v + 1);\n            }\n        }\n        pendingSelectedObjectsUUIDs.current = config.selectedObjects || [];\n    }, [\n        sceneObjects,\n        forceUpdate,\n        setMaterialVersion,\n        setTransformVersion\n    ]);\n    // After sceneObjects update, restore selectedObjects from UUIDs\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (pendingSelectedObjectsUUIDs.current && sceneObjects.length > 0) {\n            const uuids = pendingSelectedObjectsUUIDs.current;\n            const newSelected = sceneObjects.filter((obj)=>uuids.includes(obj.uuid));\n            setSelectedObjects(newSelected);\n            pendingSelectedObjectsUUIDs.current = null;\n        }\n    }, [\n        sceneObjects\n    ]);\n    const undoAction = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (isUndoRedoInProgressRef.current) {\n            return;\n        }\n        if (historyIndex > 0) {\n            isUndoRedoInProgressRef.current = true;\n            isUndoRedoActionRef.current = true;\n            skipHistoryTrackingRef.current = true;\n            const prevConfig = history[historyIndex - 1];\n            restoreConfig(prevConfig);\n            setHistoryIndex(historyIndex - 1);\n            setTimeout(()=>{\n                isUndoRedoActionRef.current = false;\n                skipHistoryTrackingRef.current = false;\n                isUndoRedoInProgressRef.current = false;\n            }, 1000);\n        }\n    }, [\n        historyIndex,\n        history,\n        restoreConfig\n    ]);\n    const redoAction = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (isUndoRedoInProgressRef.current) {\n            return;\n        }\n        if (historyIndex < history.length - 1) {\n            isUndoRedoInProgressRef.current = true;\n            isUndoRedoActionRef.current = true;\n            skipHistoryTrackingRef.current = true;\n            const nextConfig = history[historyIndex + 1];\n            restoreConfig(nextConfig);\n            setHistoryIndex(historyIndex + 1);\n            setTimeout(()=>{\n                isUndoRedoActionRef.current = false;\n                skipHistoryTrackingRef.current = false;\n                isUndoRedoInProgressRef.current = false;\n            }, 1000);\n        }\n    }, [\n        historyIndex,\n        history,\n        restoreConfig\n    ]);\n    // Debounced, flag-protected history tracking\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handler = setTimeout(()=>{\n            if (!isUndoRedoActionRef.current && !skipHistoryTrackingRef.current) {\n                const currentState = JSON.stringify({\n                    selectedModel,\n                    envPreset,\n                    bgColor,\n                    showEnvironment,\n                    showLightSpheres,\n                    envIntensity,\n                    envBlur,\n                    envRotation,\n                    postProcessingEnabled,\n                    showGrid,\n                    wireframe,\n                    groundType,\n                    transformVersion,\n                    materialVersion,\n                    lightsLength: lights.length\n                });\n                if (lastStateRef.current !== currentState) {\n                    lastStateRef.current = currentState;\n                    addToHistory();\n                }\n            }\n        }, 300);\n        return ()=>clearTimeout(handler);\n    }, [\n        selectedModel,\n        envPreset,\n        bgColor,\n        showEnvironment,\n        showLightSpheres,\n        envIntensity,\n        envBlur,\n        envRotation,\n        postProcessingEnabled,\n        showGrid,\n        wireframe,\n        groundType,\n        transformVersion,\n        materialVersion,\n        lights.length\n    ]);\n    // --- UNDO/REDO SYSTEM REPLACEMENT END ---\n    const handleObjectClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e)=>{\n        e.stopPropagation();\n        // If placing decal, let the decal placement handler do its job (triggered by model's onClick)\n        if (isPlacingDecal) {\n            return;\n        }\n        console.log(\"Object clicked:\", e.object.name, e.object);\n        if (e.shiftKey) {\n            setSelectedObjects((prev)=>{\n                // Ensure prev is always an array\n                const prevArray = Array.isArray(prev) ? prev : [\n                    prev\n                ].filter(Boolean);\n                if (prevArray.includes(e.object)) {\n                    return prevArray.filter((obj)=>obj !== e.object);\n                }\n                return [\n                    ...prevArray,\n                    e.object\n                ];\n            });\n        } else {\n            setSelectedObjects([\n                e.object\n            ]);\n        }\n    }, [\n        isPlacingDecal,\n        setSelectedObjects\n    ]);\n    // Add debugging for selectedObjects changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"ExperienceNew - selectedObjects changed:\", selectedObjects);\n        console.log(\"ExperienceNew - selectedObjects length:\", selectedObjects === null || selectedObjects === void 0 ? void 0 : selectedObjects.length);\n    }, [\n        selectedObjects\n    ]);\n    // Add debugging for sceneObjects changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"ExperienceNew - sceneObjects changed:\", sceneObjects);\n        console.log(\"ExperienceNew - sceneObjects length:\", sceneObjects === null || sceneObjects === void 0 ? void 0 : sceneObjects.length);\n        console.log(\"ExperienceNew - sceneObjects details:\", sceneObjects === null || sceneObjects === void 0 ? void 0 : sceneObjects.map((obj)=>({\n                name: obj === null || obj === void 0 ? void 0 : obj.name,\n                type: obj === null || obj === void 0 ? void 0 : obj.type,\n                userData: obj === null || obj === void 0 ? void 0 : obj.userData\n            })));\n    }, [\n        sceneObjects\n    ]);\n    const handlePointerMissed = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        // If in decal placement mode, a \"missed\" click (on the canvas background)\n        // should probably cancel placement mode, or do nothing to allow model click-through.\n        // For now, let's prevent it from deselecting objects if placing decal.\n        if (isPlacingDecal) {\n            // setIsPlacingDecal(false); // Optionally, cancel placement on missed click\n            return;\n        }\n        setSelectedObjects([]);\n        setSelectedLight(null);\n    // Do not close the rightPanelTab here, as that's usually handled by clicking outside the panel itself.\n    }, [\n        isPlacingDecal,\n        setIsPlacingDecal\n    ]);\n    // Add the selection group to the scene\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // This effect runs once to add the selection group to the scene\n        return ()=>{\n            // Clean up on unmount\n            if (selectionGroupRef.current && selectionGroupRef.current.parent) {\n                selectionGroupRef.current.parent.remove(selectionGroupRef.current);\n            }\n        };\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Ensure selectedObjects is always an array\n        const objectsArray = Array.isArray(selectedObjects) ? selectedObjects : [\n            selectedObjects\n        ].filter(Boolean);\n        while(selectionGroupRef.current.children.length > 0){\n            selectionGroupRef.current.remove(selectionGroupRef.current.children[0]);\n        }\n        if (objectsArray.length > 0) {\n            const center = new three__WEBPACK_IMPORTED_MODULE_28__.Vector3();\n            objectsArray.forEach((obj)=>{\n                center.add(obj.position);\n            });\n            center.divideScalar(objectsArray.length);\n            selectionGroupRef.current.position.copy(center);\n            objectsArray.forEach((obj)=>{\n                const clone = obj.clone();\n                // Make the clone invisible while keeping its geometry for transforms\n                clone.traverse((child)=>{\n                    if (child.isMesh) {\n                        child.visible = false;\n                    }\n                });\n                clone.position.sub(center);\n                selectionGroupRef.current.add(clone);\n            });\n        }\n    }, [\n        selectedObjects\n    ]);\n    const updateObjectsFromGroup = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        // Ensure selectedObjects is always an array\n        const objectsArray = Array.isArray(selectedObjects) ? selectedObjects : [\n            selectedObjects\n        ].filter(Boolean);\n        if (objectsArray.length > 0 && selectionGroupRef.current) {\n            objectsArray.forEach((obj, index)=>{\n                if (selectionGroupRef.current.children[index]) {\n                    const worldPos = new three__WEBPACK_IMPORTED_MODULE_28__.Vector3();\n                    selectionGroupRef.current.children[index].getWorldPosition(worldPos);\n                    obj.position.copy(worldPos);\n                    obj.rotation.copy(selectionGroupRef.current.rotation);\n                    obj.scale.copy(selectionGroupRef.current.scale);\n                }\n            });\n        }\n    }, [\n        selectedObjects\n    ]);\n    const handleSceneUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((scene)=>{\n        var _scene_children;\n        console.log(\"handleSceneUpdate called with scene:\", scene);\n        console.log(\"Scene type:\", scene.type);\n        console.log(\"Scene children count:\", (_scene_children = scene.children) === null || _scene_children === void 0 ? void 0 : _scene_children.length);\n        setSceneObjects([]);\n        setSelectedObjects([]);\n        const stats = (0,_utils_ArtistTool_sceneUtils__WEBPACK_IMPORTED_MODULE_17__.calculateModelStats)(scene);\n        setModelStats(stats);\n        (0,_utils_ArtistTool_sceneUtils__WEBPACK_IMPORTED_MODULE_17__.setShadowsOnModel)(scene);\n        const objects = [];\n        const initialObjects = [];\n        scene.traverse((object)=>{\n            console.log(\"Traversing object:\", object.name, object.type, \"isMesh:\", object.isMesh, \"userData:\", object.userData);\n            if (object.isMesh) {\n                if (!object.name) {\n                    object.name = \"Mesh_\".concat(object.id);\n                }\n                console.log(\"Adding mesh to objects:\", object.name, \"userData:\", object.userData);\n                objects.push(object);\n                initialObjects.push({\n                    id: object.uuid,\n                    name: object.name,\n                    position: object.position.clone(),\n                    rotation: object.rotation.clone(),\n                    scale: object.scale.clone(),\n                    material: object.material ? {\n                        color: object.material.color ? object.material.color.getHex() : 0xffffff,\n                        roughness: object.material.roughness,\n                        metalness: object.material.metalness,\n                        wireframe: object.material.wireframe,\n                        transparent: object.material.transparent,\n                        opacity: object.material.opacity\n                    } : null\n                });\n            }\n        });\n        console.log(\"Found\", objects.length, \"mesh objects:\", objects.map((obj)=>({\n                name: obj.name,\n                userData: obj.userData\n            })));\n        modelInitialStateRef.current.objects = initialObjects;\n        setSceneObjects(objects);\n    }, []);\n    // Add this effect to detect model changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedModel !== lastSelectedModel) {\n            // Clear the scene objects when model changes\n            setSceneObjects([]);\n            setSelectedObjects([]);\n            setLastSelectedModel(selectedModel);\n        }\n    }, [\n        selectedModel,\n        lastSelectedModel\n    ]);\n    const handleLightMoved = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((lightId, newPosition)=>{\n        setLightPositionHistory((prev)=>({\n                ...prev,\n                [lightId]: [\n                    ...prev[lightId] || [],\n                    newPosition\n                ]\n            }));\n    }, []);\n    const undoLightMove = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((lightId)=>{\n        setLightPositionHistory((prev)=>{\n            const history = prev[lightId] || [];\n            if (history.length <= 1) return prev;\n            const newHistory = history.slice(0, -1);\n            const lastPosition = newHistory[newHistory.length - 1];\n            if (lightRefs.current[lightId]) {\n                lightRefs.current[lightId].position.copy(lastPosition);\n            }\n            return {\n                ...prev,\n                [lightId]: newHistory\n            };\n        });\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleKeyDown = (event)=>{\n            // Handle undo/redo keyboard shortcuts\n            if (event.ctrlKey) {\n                if (event.key === \"z\") {\n                    event.preventDefault();\n                    undoAction();\n                    return;\n                }\n                if (event.key === \"y\") {\n                    event.preventDefault();\n                    redoAction();\n                    return;\n                }\n            }\n            // Handle Escape key for decal placement\n            if (event.key === \"Escape\" && isPlacingDecal) {\n                setIsPlacingDecal(false);\n                return;\n            }\n            // Handle light undo\n            if (event.ctrlKey && event.key === \"z\" && selectedLight) {\n                undoLightMove(selectedLight);\n            }\n            // Handle transform mode shortcuts\n            if (selectedObjects.length > 0) {\n                switch(event.key){\n                    case \"g\":\n                        setTransformMode(\"translate\");\n                        break;\n                    case \"r\":\n                        setTransformMode(\"rotate\");\n                        break;\n                    case \"s\":\n                        setTransformMode(\"scale\");\n                        break;\n                    case \"Escape\":\n                        setSelectedObjects([]);\n                        break;\n                }\n            }\n            // Handle select all\n            if (event.ctrlKey && event.key === \"a\") {\n                event.preventDefault();\n                if (sceneObjects.length > 0) {\n                    setSelectedObjects([\n                        ...sceneObjects\n                    ]);\n                }\n            }\n        };\n        window.addEventListener(\"keydown\", handleKeyDown);\n        return ()=>window.removeEventListener(\"keydown\", handleKeyDown);\n    }, [\n        selectedLight,\n        undoLightMove,\n        sceneObjects,\n        selectedObjects.length,\n        undoAction,\n        redoAction,\n        isPlacingDecal,\n        setIsPlacingDecal\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        return ()=>{\n            if (uploadedModel) {\n                URL.revokeObjectURL(uploadedModel);\n            }\n            if (customHdri) {\n                URL.revokeObjectURL(customHdri);\n            }\n        };\n    }, [\n        uploadedModel,\n        customHdri\n    ]);\n    const handleCameraView = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((view)=>{\n        (0,_utils_ArtistTool_cameraUtils__WEBPACK_IMPORTED_MODULE_14__.setCameraView)(orbitControlsRef.current, view);\n    }, []);\n    // Add the panelRef to ExperienceNew instead of in Toolbar\n    const panelRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [activeAnimation, setActiveAnimation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [animationStartTime, setAnimationStartTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const requestRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    // Animation loop handler\n    const animateCamera = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((timestamp)=>{\n        if (!activeAnimation || !orbitControlsRef.current) {\n            return;\n        }\n        // Calculate animation progress (0 to 1)\n        const animation = _animations_CameraAnimations__WEBPACK_IMPORTED_MODULE_18__.predefinedAnimations.find((a)=>a.id === activeAnimation);\n        if (!animation) return;\n        if (animationStartTime === 0) {\n            setAnimationStartTime(timestamp);\n        }\n        const elapsed = timestamp - animationStartTime;\n        const progress = elapsed % animation.duration / animation.duration;\n        // Get the target position (center of selected objects or scene center)\n        const target = new three__WEBPACK_IMPORTED_MODULE_28__.Vector3(0, 0, 0);\n        if (selectedObjects.length > 0) {\n            // Calculate center of selected objects\n            const objectsArray = Array.isArray(selectedObjects) ? selectedObjects : [\n                selectedObjects\n            ].filter(Boolean);\n            if (objectsArray.length > 0) {\n                const center = new three__WEBPACK_IMPORTED_MODULE_28__.Vector3();\n                objectsArray.forEach((obj)=>{\n                    center.add(obj.position);\n                });\n                center.divideScalar(objectsArray.length);\n                target.copy(center);\n            }\n        }\n        // Animate camera using the animation function\n        animation.animate(orbitControlsRef.current.object, orbitControlsRef.current, progress, target);\n        requestRef.current = requestAnimationFrame(animateCamera);\n    }, [\n        activeAnimation,\n        orbitControlsRef,\n        animationStartTime,\n        selectedObjects\n    ]);\n    // Setup animation loop\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (activeAnimation) {\n            setAnimationStartTime(0);\n            requestRef.current = requestAnimationFrame(animateCamera);\n            // Disable orbit controls during animation\n            if (orbitControlsRef.current) {\n                orbitControlsRef.current.enabled = false;\n            }\n        } else {\n            // Re-enable orbit controls when animation stops\n            if (orbitControlsRef.current && selectedObjects.length === 0) {\n                orbitControlsRef.current.enabled = true;\n            }\n        }\n        return ()=>{\n            if (requestRef.current) {\n                cancelAnimationFrame(requestRef.current);\n            }\n        };\n    }, [\n        activeAnimation,\n        animateCamera,\n        selectedObjects\n    ]);\n    // Handle animation playback from Outliner\n    const handlePlayAnimation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((animationId)=>{\n        setActiveAnimation(animationId);\n    }, []);\n    const getEnvironmentPreset = ()=>{\n        return envPreset;\n    };\n    // Reset function\n    const handleReset = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        // Check if there's a saved scene in localStorage\n        const savedScene = localStorage.getItem(\"agape_saved_scene\");\n        if (savedScene) {\n            const confirmReset = window.confirm(\"There is a saved scene in your browser storage. Resetting will clear this saved scene. Do you want to continue?\");\n            if (!confirmReset) {\n                return;\n            }\n            // Clear the saved scene from localStorage\n            localStorage.removeItem(\"agape_saved_scene\");\n        }\n        console.log(\"Resetting scene to initial state\");\n        setSceneObjects([]);\n        setSelectedObjects([]);\n        // Reset lights to default configuration\n        setLights([\n            {\n                id: \"defaultDirectional\",\n                type: \"directional\",\n                position: [\n                    2.5,\n                    5.5,\n                    5\n                ],\n                intensity: 1.0,\n                color: \"#ffffff\",\n                name: \"Main Directional Light\",\n                castShadow: true,\n                helperVisible: false\n            },\n            {\n                id: \"keyLight\",\n                type: \"point\",\n                position: [\n                    5,\n                    5,\n                    5\n                ],\n                intensity: 8.0,\n                color: \"#ffffff\",\n                name: \"Key Light\"\n            },\n            {\n                id: \"fillLight\",\n                type: \"point\",\n                position: [\n                    -5,\n                    5,\n                    -5\n                ],\n                intensity: 4.0,\n                color: \"#ffffff\",\n                name: \"Fill Light\"\n            },\n            {\n                id: \"backLight\",\n                type: \"point\",\n                position: [\n                    0,\n                    5,\n                    -5\n                ],\n                intensity: 2.0,\n                color: \"#ffffff\",\n                name: \"Back Light\"\n            }\n        ]);\n        // Reset environment settings\n        setEnvPreset(\"hdri_15\");\n        setBgColor(\"#202020\");\n        setShowEnvironment(false);\n        setShowLightSpheres(true);\n        setCustomHdri(null);\n        setEnvIntensity(1.5);\n        setEnvBlur(0.35);\n        setEnvRotation(0);\n        // Reset post-processing settings\n        setPostProcessingEnabled(false);\n        setPostProcessingSettings({\n            autofocus: {\n                enabled: false,\n                bokehScale: 18\n            },\n            bloom: {\n                enabled: true,\n                intensity: 0.12,\n                threshold: 0.8,\n                radius: 0.4\n            },\n            dof: {\n                enabled: false,\n                focusDistance: 0,\n                aperture: 0.01,\n                bokehScale: 3\n            },\n            vignette: {\n                enabled: true,\n                darkness: 0.5,\n                offset: 0.1\n            }\n        });\n        // Reset scene controls\n        setShowGrid(true);\n        setWireframe(false);\n        setGroundType(\"none\");\n        // Reset camera position\n        if (orbitControlsRef.current) {\n            orbitControlsRef.current.object.position.set(5, 2, 5);\n            orbitControlsRef.current.target.set(0, 1, 0);\n            orbitControlsRef.current.update();\n        }\n        // Reset transform state\n        setTransformMode(\"translate\");\n        setSelectedLight(null);\n        // Reset material shaders\n        setUseDiamondShader(false);\n        setUsePremiumWhiteGold(false);\n        setUsePremiumRoseGold(false);\n        // Clear history\n        setHistory([]);\n        setHistoryIndex(-1);\n        setLightPositionHistory({});\n        // Reset model stats\n        setModelStats({\n            vertices: 0,\n            triangles: 0,\n            materials: 0,\n            animations: 0\n        });\n        // Reset right panel tab\n        setRightPanelTab(null);\n        // Reset active animation\n        setActiveAnimation(null);\n        setAnimationStartTime(0);\n        if (requestRef.current) {\n            cancelAnimationFrame(requestRef.current);\n        }\n        // Clear any custom HDRI or uploaded model URLs\n        if (uploadedModel) {\n            URL.revokeObjectURL(uploadedModel);\n        }\n        if (customHdri) {\n            URL.revokeObjectURL(customHdri);\n        }\n        // Force a model reload by setting selectedModel to null first\n        setSelectedModel(null);\n        // Then set it back to uploaded after a small delay\n        setTimeout(()=>{\n            setSelectedModel(\"uploaded\");\n        }, 100);\n    }, []);\n    const screenshotRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const captureScreenshot = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (screenshotRef.current) {\n            console.log(\"Taking screenshot\");\n            screenshotRef.current();\n        } else {\n            console.error(\"Screenshot function not available yet\");\n        }\n    }, []);\n    // Load saved scene from localStorage on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadSavedScene = ()=>{\n            const savedScene = localStorage.getItem(\"agape_saved_scene\");\n            if (savedScene) {\n                try {\n                    const sceneConfig = JSON.parse(savedScene);\n                    console.log(\"Loading saved scene:\", sceneConfig);\n                    // Only load the saved configuration on initial load\n                    if (!lastSelectedModel) {\n                        console.log(\"Initial load - setting saved model:\", sceneConfig.model);\n                        setSelectedModel(\"uploaded\"); // Always use uploaded for S3 models\n                        // Load the rest of the saved scene configuration\n                        setLights(sceneConfig.lights);\n                        setEnvPreset(sceneConfig.environment.preset);\n                        setBgColor(sceneConfig.environment.bgColor);\n                        setShowEnvironment(sceneConfig.environment.showEnvironment);\n                        setShowLightSpheres(sceneConfig.environment.showLightSpheres);\n                        setEnvIntensity(sceneConfig.environment.intensity);\n                        setEnvBlur(sceneConfig.environment.blur);\n                        setEnvRotation(sceneConfig.environment.rotation);\n                        setPostProcessingEnabled(sceneConfig.postProcessing.enabled);\n                        setPostProcessingSettings(sceneConfig.postProcessing.settings);\n                        setShowGrid(sceneConfig.sceneControls.showGrid);\n                        setWireframe(sceneConfig.sceneControls.wireframe);\n                        setGroundType(sceneConfig.sceneControls.groundType);\n                        // Restore camera position after a short delay to ensure scene is loaded\n                        setTimeout(()=>{\n                            if (orbitControlsRef.current && sceneConfig.camera) {\n                                orbitControlsRef.current.object.position.fromArray(sceneConfig.camera.position);\n                                orbitControlsRef.current.target.fromArray(sceneConfig.camera.target);\n                                orbitControlsRef.current.update();\n                            }\n                        }, 1000);\n                    } else if (configRef.current) {\n                        // For explicit loads with configRef, do a complete load\n                        console.log(\"Explicit load with configRef\");\n                        handleLoadScene(sceneConfig);\n                    }\n                    // Create a function to restore materials\n                    const restoreMaterials = ()=>{\n                        if (!sceneConfig.materials || !sceneObjects.length) {\n                            console.log(\"No materials to restore or scene objects not ready\");\n                            return;\n                        }\n                        console.log(\"Attempting to restore materials for objects:\", sceneObjects);\n                        console.log(\"Saved materials:\", sceneConfig.materials);\n                        const savedMaterialsMap = new Map(sceneConfig.materials.map((m)=>[\n                                m.name,\n                                m.material\n                            ]));\n                        sceneObjects.forEach((obj)=>{\n                            if (obj.material && savedMaterialsMap.has(obj.name)) {\n                                const savedMaterial = savedMaterialsMap.get(obj.name);\n                                console.log(\"Restoring material for object:\", obj.name, savedMaterial);\n                                (0,_utils_ArtistTool_materialUtils__WEBPACK_IMPORTED_MODULE_16__.applyMaterialToObject)(obj, savedMaterial);\n                            } else {\n                                console.log(\"Could not find saved material for object:\", obj.name);\n                            }\n                        });\n                    };\n                    // Set up an interval to check for scene objects\n                    const checkInterval = setInterval(()=>{\n                        if (sceneObjects.length > 0) {\n                            clearInterval(checkInterval);\n                            // Add a small delay to ensure all materials are properly initialized\n                            setTimeout(restoreMaterials, 500);\n                        }\n                    }, 100);\n                    // Clean up interval after 5 seconds if scene objects never become available\n                    setTimeout(()=>{\n                        clearInterval(checkInterval);\n                    }, 5000);\n                    // Restore camera position after a short delay to ensure scene is loaded\n                    setTimeout(()=>{\n                        if (orbitControlsRef.current && sceneConfig.camera) {\n                            orbitControlsRef.current.object.position.fromArray(sceneConfig.camera.position);\n                            orbitControlsRef.current.target.fromArray(sceneConfig.camera.target);\n                            orbitControlsRef.current.update();\n                        }\n                    }, 1000);\n                } catch (error) {\n                    console.error(\"Error loading saved scene:\", error);\n                }\n            }\n        };\n        // Load saved scene immediately on mount\n        loadSavedScene();\n        // Also set up an interval to check for scene readiness\n        const checkSceneReady = setInterval(()=>{\n            if (sceneRef.current && orbitControlsRef.current) {\n                clearInterval(checkSceneReady);\n                loadSavedScene();\n            }\n        }, 100);\n        return ()=>clearInterval(checkSceneReady);\n    }, [\n        sceneObjects,\n        selectedModel\n    ]);\n    const [configState, setConfigState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (sceneObjects.length > 0 && configState && configState.materials) {\n            const applyMaterials = ()=>{\n                console.log(\"Applying materials to scene objects:\", sceneObjects);\n                console.log(\"Saved materials configuration:\", configState.materials);\n                sceneObjects.forEach((obj)=>{\n                    const materialConfig = configState.materials.find((m)=>m.name === obj.name || m.id === obj.uuid);\n                    if (materialConfig && materialConfig.material) {\n                        console.log(\"Found material config for object:\", obj.name, materialConfig.material);\n                        (0,_utils_ArtistTool_materialUtils__WEBPACK_IMPORTED_MODULE_16__.applyMaterialToObject)(obj, materialConfig.material);\n                    } else {\n                        console.log(\"No material config found for object:\", obj.name);\n                    }\n                });\n            };\n            // Add a small delay to ensure all materials are properly initialized\n            const timeoutId = setTimeout(applyMaterials, 500);\n            return ()=>clearTimeout(timeoutId);\n        }\n    }, [\n        sceneObjects,\n        configState\n    ]);\n    const handleLoadScene = (config)=>{\n        try {\n            console.log(\"Loading scene configuration:\", config);\n            // Store the config in the ref\n            configRef.current = config;\n            workingConfigRef.current = config; // Always keep this in sync with last applied config\n            setConfigState(config); // <-- keep in sync for effect\n            // Clear scene objects and selected objects first\n            setSceneObjects([]);\n            setSelectedObjects([]);\n            setLastSelectedModel(null);\n            // Restore scene settings\n            setSelectedModel(\"uploaded\");\n            setLights(config.lights);\n            setEnvPreset(config.environment.preset);\n            setBgColor(config.environment.bgColor);\n            setShowEnvironment(config.environment.showEnvironment);\n            setShowLightSpheres(config.environment.showLightSpheres);\n            setEnvIntensity(config.environment.intensity);\n            setEnvBlur(config.environment.blur);\n            setEnvRotation(config.environment.rotation);\n            setPostProcessingEnabled(config.postProcessing.enabled);\n            setPostProcessingSettings(config.postProcessing.settings);\n            setShowGrid(config.sceneControls.showGrid);\n            setWireframe(config.sceneControls.wireframe);\n            setGroundType(config.sceneControls.groundType);\n            // Wait for the scene to update before applying camera position\n            setTimeout(()=>{\n                if (orbitControlsRef.current && config.camera) {\n                    orbitControlsRef.current.object.position.fromArray(config.camera.position);\n                    orbitControlsRef.current.target.fromArray(config.camera.target);\n                    orbitControlsRef.current.update();\n                }\n            }, 1000);\n            // Restore materials after sceneObjects are ready\n            const restoreMaterials = ()=>{\n                if (!config.materials || !sceneObjects.length) {\n                    console.log(\"No materials to restore or scene objects not ready\");\n                    return;\n                }\n                console.log(\"Restoring materials for objects:\", sceneObjects);\n                const savedMaterialsMap = new Map(config.materials.map((m)=>[\n                        m.name,\n                        m.material\n                    ]));\n                sceneObjects.forEach((obj)=>{\n                    if (obj.material && savedMaterialsMap.has(obj.name)) {\n                        const savedMaterial = savedMaterialsMap.get(obj.name);\n                        console.log(\"Restoring material for object:\", obj.name, savedMaterial);\n                        (0,_utils_ArtistTool_materialUtils__WEBPACK_IMPORTED_MODULE_16__.applyMaterialToObject)(obj, savedMaterial);\n                    } else {\n                        console.log(\"Could not find saved material for object:\", obj.name);\n                    }\n                });\n            };\n            if (sceneObjects.length > 0) {\n                setTimeout(restoreMaterials, 500);\n            } else {\n                const checkInterval = setInterval(()=>{\n                    if (sceneObjects.length > 0) {\n                        clearInterval(checkInterval);\n                        setTimeout(restoreMaterials, 500);\n                    }\n                }, 100);\n                setTimeout(()=>{\n                    clearInterval(checkInterval);\n                }, 5000);\n            }\n        } catch (error) {\n            console.error(\"Error loading scene configuration:\", error);\n        }\n    };\n    const handleSaveScene = async (sceneName, description, saveType)=>{\n        // Use workingConfigRef if it exists and matches the current state, otherwise build from state\n        let sceneConfig;\n        if (workingConfigRef.current) {\n            // Use a shallow compare for top-level keys (or always use workingConfigRef for now)\n            sceneConfig = {\n                ...workingConfigRef.current,\n                name: sceneName,\n                timestamp: new Date().toISOString()\n            };\n        } else {\n            var _orbitControlsRef_current, _orbitControlsRef_current1;\n            // fallback to building from state\n            sceneConfig = {\n                name: sceneName,\n                timestamp: new Date().toISOString(),\n                model: selectedModel,\n                lights: lights.map((light)=>{\n                    var _lightRefs_current_light_id;\n                    return {\n                        ...light,\n                        position: ((_lightRefs_current_light_id = lightRefs.current[light.id]) === null || _lightRefs_current_light_id === void 0 ? void 0 : _lightRefs_current_light_id.position.toArray()) || light.position\n                    };\n                }),\n                environment: {\n                    preset: envPreset,\n                    intensity: envIntensity,\n                    blur: envBlur,\n                    rotation: envRotation,\n                    showEnvironment,\n                    bgColor,\n                    customHdri\n                },\n                postProcessing: {\n                    enabled: postProcessingEnabled,\n                    settings: postProcessingSettings\n                },\n                sceneControls: {\n                    showGrid,\n                    wireframe,\n                    groundType,\n                    showLightSpheres\n                },\n                camera: {\n                    position: (_orbitControlsRef_current = orbitControlsRef.current) === null || _orbitControlsRef_current === void 0 ? void 0 : _orbitControlsRef_current.object.position.toArray(),\n                    target: (_orbitControlsRef_current1 = orbitControlsRef.current) === null || _orbitControlsRef_current1 === void 0 ? void 0 : _orbitControlsRef_current1.target.toArray()\n                },\n                materials: sceneObjects.map((obj)=>{\n                    const material = obj.material;\n                    if (!material) return {\n                        id: obj.uuid,\n                        name: obj.name,\n                        material: null\n                    };\n                    return {\n                        id: obj.uuid,\n                        name: obj.name,\n                        material: {\n                            type: material.type,\n                            color: material.color ? material.color.getHexString() : \"ffffff\",\n                            roughness: material.roughness,\n                            metalness: material.metalness,\n                            wireframe: material.wireframe,\n                            transparent: material.transparent,\n                            opacity: material.opacity,\n                            envMapIntensity: material.envMapIntensity,\n                            ...material.type === \"MeshRefractionMaterial\" && {\n                                aberrationStrength: material.aberrationStrength,\n                                toneMapped: material.toneMapped,\n                                ior: material.ior,\n                                colorEnvMapRotY: material.colorEnvMapRotY\n                            }\n                        }\n                    };\n                })\n            };\n        }\n        if (saveType === \"local\") {\n            // Save to localStorage\n            localStorage.setItem(\"agape_saved_scene\", JSON.stringify(sceneConfig));\n        } else if (saveType === \"db\") {\n            // Save to DB (cloud)\n            try {\n                const version = await (0,_lib_actions_version_actions__WEBPACK_IMPORTED_MODULE_24__.createVersion)({\n                    userId: user.id,\n                    workspaceId: project._id,\n                    name: sceneName,\n                    config: sceneConfig,\n                    description: description\n                });\n                console.log(\"version\", version);\n                if (version) {\n                    addVersion(project._id, version);\n                }\n                sonner__WEBPACK_IMPORTED_MODULE_25__.toast.success(\"Scene saved to cloud!\");\n            } catch (err) {\n                sonner__WEBPACK_IMPORTED_MODULE_25__.toast.error(\"Failed to save scene to cloud\");\n            }\n        } else {\n            // Export as file\n            const blob = new Blob([\n                JSON.stringify(sceneConfig, null, 2)\n            ], {\n                type: \"application/json\"\n            });\n            const url = URL.createObjectURL(blob);\n            const a = document.createElement(\"a\");\n            a.href = url;\n            a.download = \"\".concat(sceneName.replace(/\\s+/g, \"_\"), \".json\");\n            document.body.appendChild(a);\n            a.click();\n            document.body.removeChild(a);\n            URL.revokeObjectURL(url);\n        }\n    };\n    // Engraving state\n    const [engravingText, setEngravingText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [decalPosition, setDecalPosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        0,\n        1.68,\n        0\n    ]);\n    const [decalScale, setDecalScale] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        2.8,\n        0.9,\n        1.5\n    ]);\n    const [decalRotation, setDecalRotation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        -Math.PI / 2,\n        0,\n        0\n    ]);\n    const [decalColor, setDecalColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"#cbcbcb\");\n    const [selectedFont, setSelectedFont] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"/fonts/ttf/RobotoSerif-Regular.ttf\");\n    // Diamond shader state\n    const [useDiamondShader, setUseDiamondShader] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [usePremiumWhiteGold, setUsePremiumWhiteGold] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [usePremiumRoseGold, setUsePremiumRoseGold] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleEngraveText = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((text)=>{\n        setEngravingText(text);\n        // Store in localStorage to make it persist across panel changes and page reloads\n        localStorage.setItem(\"engravingText\", text);\n    }, []);\n    const handleFontChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((font)=>{\n        setSelectedFont(font);\n        localStorage.setItem(\"engravingFont\", font);\n    }, []);\n    // Load saved engraving text on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const savedEngravingText = localStorage.getItem(\"engravingText\");\n        if (savedEngravingText) {\n            setEngravingText(savedEngravingText);\n        }\n    }, []);\n    const handleDecalPlacement = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((event)=>{\n        handleDecalPlacement(event, setIsPlacingDecal, setDecalPosition, setDecalRotation);\n    }, [\n        setDecalPosition,\n        setDecalRotation,\n        setIsPlacingDecal\n    ]);\n    // Get predefined decal positions based on selected model\n    const getDefaultDecalPosition = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((modelId)=>{\n        const modelConfigs = {\n            chosenring: [\n                0,\n                1.68,\n                0\n            ],\n            testring: [\n                0,\n                1.68,\n                0\n            ],\n            agapering1: [\n                0,\n                1.2,\n                0\n            ],\n            agapering2: [\n                0,\n                1.2,\n                0\n            ],\n            ringa: [\n                0,\n                0.26,\n                0\n            ],\n            ringb: [\n                0,\n                0.5,\n                0\n            ],\n            ringc: [\n                0,\n                0.26,\n                0\n            ]\n        };\n        return modelConfigs[modelId] || [\n            0,\n            1.68,\n            0\n        ];\n    }, []);\n    const getDefaultDecalRotation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((modelId)=>{\n        const modelConfigs = {\n            chosenring: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            testring: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            agapering1: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            agapering2: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            ringa: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            ringb: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            ringc: [\n                -Math.PI / 2,\n                0,\n                0\n            ]\n        };\n        return modelConfigs[modelId] || [\n            -Math.PI / 2,\n            0,\n            0\n        ];\n    }, []);\n    const getDefaultDecalScale = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((modelId)=>{\n        const modelConfigs = {\n            chosenring: [\n                6.0,\n                2.0,\n                1.5\n            ],\n            testring: [\n                6.0,\n                2.0,\n                1.5\n            ],\n            agapering1: [\n                3.2,\n                1.8,\n                1.0\n            ],\n            agapering2: [\n                3.2,\n                1.8,\n                1.0\n            ],\n            ringa: [\n                1.8,\n                2.0,\n                0.8\n            ],\n            ringb: [\n                1.8,\n                2.0,\n                0.8\n            ],\n            ringc: [\n                1.8,\n                2.0,\n                0.8\n            ]\n        };\n        return modelConfigs[modelId] || [\n            4.2,\n            2.0,\n            1.5\n        ];\n    }, []);\n    // Calculate dynamic scale based on text length and model type\n    const calculateDynamicScale = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((baseScale, text, modelId)=>{\n        if (!text || text.length === 0) return baseScale;\n        const textLength = text.length;\n        let [scaleX, scaleY, scaleZ] = baseScale;\n        // Calculate scale multiplier based on text length\n        let scaleMultiplier = 1.0;\n        if (textLength > 10) {\n            // For very long text, scale up more aggressively\n            scaleMultiplier = 1.3 + (textLength - 10) * 0.08;\n        } else if (textLength > 6) {\n            // For medium length text, scale up moderately\n            scaleMultiplier = 1.0 + (textLength - 6) * 0.08;\n        } else if (textLength < 4) {\n            // For short text, scale down slightly to prevent oversized appearance\n            scaleMultiplier = 0.8 + textLength * 0.05;\n        }\n        // Apply text length scaling to X-axis (width) primarily\n        scaleX = scaleX * scaleMultiplier;\n        // For very long text, also adjust the height slightly\n        if (textLength > 8) {\n            scaleY = scaleY * Math.min(1.2, 1.0 + (textLength - 8) * 0.02);\n        }\n        // Cap maximum scale to prevent excessive sizing (updated for larger Y values)\n        const maxScale = {\n            chosenring: [\n                12.0,\n                2.5,\n                2.0\n            ],\n            testring: [\n                12.0,\n                2.5,\n                2.0\n            ],\n            agapering1: [\n                6.5,\n                2.2,\n                1.5\n            ],\n            agapering2: [\n                6.5,\n                2.2,\n                1.5\n            ],\n            ringa: [\n                4.0,\n                2.5,\n                1.2\n            ],\n            ringb: [\n                4.0,\n                2.5,\n                1.2\n            ],\n            ringc: [\n                4.0,\n                2.5,\n                1.2\n            ]\n        };\n        const limits = maxScale[modelId] || [\n            8.5,\n            2.5,\n            2.0\n        ];\n        scaleX = Math.min(scaleX, limits[0]);\n        scaleY = Math.min(scaleY, limits[1]);\n        scaleZ = Math.min(scaleZ, limits[2]);\n        return [\n            scaleX,\n            scaleY,\n            scaleZ\n        ];\n    }, []);\n    // Update scale when text changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedModel && engravingText) {\n            const baseScale = getDefaultDecalScale(selectedModel);\n            const dynamicScale = calculateDynamicScale(baseScale, engravingText, selectedModel);\n            setDecalScale(dynamicScale);\n        }\n    }, [\n        engravingText,\n        selectedModel,\n        getDefaultDecalScale,\n        calculateDynamicScale\n    ]);\n    // Initialize decal position based on selected model\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedModel) {\n            setDecalPosition(getDefaultDecalPosition(selectedModel));\n            setDecalRotation(getDefaultDecalRotation(selectedModel));\n            // Only set initial scale if there's no engraving text yet\n            if (!engravingText) {\n                setDecalScale(getDefaultDecalScale(selectedModel));\n            }\n        }\n    }, [\n        selectedModel,\n        getDefaultDecalPosition,\n        getDefaultDecalRotation,\n        getDefaultDecalScale,\n        engravingText\n    ]);\n    const [isFullscreen, setIsFullscreen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Get camera position based on device type\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const checkMobile = ()=>{\n            setIsMobile(window.innerWidth < 768);\n        };\n        checkMobile();\n        window.addEventListener(\"resize\", checkMobile);\n        return ()=>window.removeEventListener(\"resize\", checkMobile);\n    }, []);\n    // Add state to control outliner collapsed state - initialize based on device type\n    const [outlinerCollapsed, setOutlinerCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        // Initialize based on device type - closed on mobile, open on desktop\n        if (true) {\n            return window.innerWidth < 768;\n        }\n        return false; // Default to open on server-side rendering\n    });\n    // Effect to close outliner when toolbar panels are opened on mobile\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isMobile && rightPanelTab) {\n            setOutlinerCollapsed(true);\n        }\n    }, [\n        isMobile,\n        rightPanelTab\n    ]);\n    // Effect to update outliner collapsed state when mobile state changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Update outliner state based on device type - closed on mobile, open on desktop\n        setOutlinerCollapsed(isMobile);\n    }, [\n        isMobile\n    ]);\n    const getCameraPosition = ()=>{\n        if (isMobile) {\n            return [\n                -1.85,\n                3.25,\n                4.5\n            ];\n        }\n        return [\n            -1.85,\n            1.55,\n            3.5\n        ];\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // On initial mount, fetch and apply the current version from DB\n        const fetchAndApplyCurrentVersion = async ()=>{\n            try {\n                const currentVersion = await (0,_lib_actions_version_actions__WEBPACK_IMPORTED_MODULE_24__.getCurrentVersion)({\n                    userId: user.id,\n                    workspaceId: project._id\n                });\n                if (currentVersion && currentVersion.config) {\n                    handleLoadScene(currentVersion.config);\n                }\n            } catch (err) {\n                console.error(\"Failed to load current version from DB\", err);\n            }\n        };\n        fetchAndApplyCurrentVersion();\n    // Only run on mount\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_toastify__WEBPACK_IMPORTED_MODULE_2__.ToastContainer, {}, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1714,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Header__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                onReset: handleReset,\n                onScreenshot: captureScreenshot,\n                onSaveScene: handleSaveScene,\n                onLoadScene: handleLoadScene,\n                isFullscreen: isFullscreen,\n                onToggleFullscreen: ()=>setIsFullscreen(!isFullscreen),\n                project: project,\n                user: user\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1715,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_InitialOverlayPanel__WEBPACK_IMPORTED_MODULE_20__.InitialOverlayPanel, {}, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1726,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed left-5 top-1/2 transform -translate-y-1/2 z-[1000] transition-all duration-300 \".concat(isFullscreen ? \"opacity-0 pointer-events-none\" : \"opacity-100\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Toolbar__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    transformMode: transformMode,\n                    setTransformMode: setTransformMode,\n                    undoAction: undoAction,\n                    redoAction: redoAction,\n                    rightPanelTab: rightPanelTab,\n                    setRightPanelTab: setRightPanelTab,\n                    orientation: \"vertical\",\n                    sceneObjects: sceneObjects,\n                    setSelectedObjects: setSelectedObjects,\n                    selectedObjects: selectedObjects,\n                    panelRef: panelRef,\n                    isMobile: isMobile,\n                    setOutlinerCollapsed: setOutlinerCollapsed\n                }, void 0, false, {\n                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                    lineNumber: 1734,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1729,\n                columnNumber: 7\n            }, this),\n            rightPanelTab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: panelRef,\n                className: \"fixed left-20 top-1/2 transform -translate-y-1/2 bg-gradient-to-tl from-[#32343D] to-[#14192D] p-4 rounded-lg shadow-lg z-[1000] max-h-[80vh] overflow-y-auto w-[250px] md:w-[300px] border border-[#A3A3A3]/30 scrollbar-none\",\n                children: [\n                    rightPanelTab === \"transform\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TransformPanel__WEBPACK_IMPORTED_MODULE_13__.TransformPanel, {\n                        selectedObjects: selectedObjects,\n                        transformMode: transformMode,\n                        setTransformMode: setTransformMode,\n                        undoAction: undoAction,\n                        redoAction: redoAction\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1758,\n                        columnNumber: 13\n                    }, this),\n                    rightPanelTab === \"material\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MaterialPanel__WEBPACK_IMPORTED_MODULE_7__.MaterialPanel, {\n                        selectedObjects: selectedObjects,\n                        useDiamondShader: useDiamondShader,\n                        setUseDiamondShader: setUseDiamondShader,\n                        usePremiumWhiteGold: usePremiumWhiteGold,\n                        setUsePremiumWhiteGold: setUsePremiumWhiteGold,\n                        usePremiumRoseGold: usePremiumRoseGold,\n                        setUsePremiumRoseGold: setUsePremiumRoseGold,\n                        renderer: renderer\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1768,\n                        columnNumber: 13\n                    }, this),\n                    rightPanelTab === \"postprocessing\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PostProcessingPanel__WEBPACK_IMPORTED_MODULE_10__.PostProcessingPanel, {\n                        postProcessingEnabled: postProcessingEnabled,\n                        setPostProcessingEnabled: setPostProcessingEnabled,\n                        postProcessingSettings: postProcessingSettings,\n                        setPostProcessingSettings: setPostProcessingSettings\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1781,\n                        columnNumber: 13\n                    }, this),\n                    rightPanelTab === \"environment\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EnvironmentPanel__WEBPACK_IMPORTED_MODULE_4__.EnvironmentPanel, {\n                        envPreset: envPreset,\n                        setEnvPreset: setEnvPreset,\n                        bgColor: bgColor,\n                        setBgColor: setBgColor,\n                        showEnvironment: showEnvironment,\n                        setShowEnvironment: setShowEnvironment,\n                        customHdri: customHdri,\n                        setCustomHdri: setCustomHdri,\n                        envIntensity: envIntensity,\n                        setEnvIntensity: setEnvIntensity,\n                        showModelStats: showModelStats,\n                        setShowModelStats: setShowModelStats,\n                        envBlur: envBlur,\n                        setEnvBlur: setEnvBlur,\n                        envRotation: envRotation,\n                        setEnvRotation: setEnvRotation\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1790,\n                        columnNumber: 13\n                    }, this),\n                    rightPanelTab === \"lights\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LightPanel__WEBPACK_IMPORTED_MODULE_6__.LightPanel, {\n                        lights: lights,\n                        setLights: setLights,\n                        lightRefs: lightRefs,\n                        showGrid: showGrid,\n                        setShowGrid: setShowGrid,\n                        wireframe: wireframe,\n                        setWireframe: setWireframe,\n                        showLightSpheres: showLightSpheres,\n                        setShowLightSpheres: setShowLightSpheres,\n                        onResetLights: ()=>{\n                            // Reset lights to default configuration\n                            const defaultLights = [\n                                {\n                                    id: \"defaultDirectional\",\n                                    type: \"directional\",\n                                    position: [\n                                        2.5,\n                                        5.5,\n                                        5\n                                    ],\n                                    intensity: 1.0,\n                                    color: \"#ffffff\",\n                                    name: \"Main Directional Light\",\n                                    castShadow: true,\n                                    helperVisible: false\n                                },\n                                {\n                                    id: \"keyLight\",\n                                    type: \"point\",\n                                    position: [\n                                        5,\n                                        5,\n                                        5\n                                    ],\n                                    intensity: 8.0,\n                                    color: \"#ffffff\",\n                                    name: \"Key Light\"\n                                },\n                                {\n                                    id: \"fillLight\",\n                                    type: \"point\",\n                                    position: [\n                                        -5,\n                                        5,\n                                        -5\n                                    ],\n                                    intensity: 4.0,\n                                    color: \"#ffffff\",\n                                    name: \"Fill Light\"\n                                },\n                                {\n                                    id: \"backLight\",\n                                    type: \"point\",\n                                    position: [\n                                        0,\n                                        5,\n                                        -5\n                                    ],\n                                    intensity: 2.0,\n                                    color: \"#ffffff\",\n                                    name: \"Back Light\"\n                                }\n                            ];\n                            setLights(defaultLights);\n                            // Clear selected light\n                            setSelectedLight(null);\n                            // Clear light position history\n                            setLightPositionHistory({});\n                        }\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1811,\n                        columnNumber: 13\n                    }, this),\n                    rightPanelTab === \"engraving\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EngravingPanel__WEBPACK_IMPORTED_MODULE_19__.EngravingPanel, {\n                        onEngraveText: handleEngraveText,\n                        onDecalPositionChange: setDecalPosition,\n                        onDecalScaleChange: setDecalScale,\n                        onDecalRotationChange: setDecalRotation,\n                        decalPosition: decalPosition,\n                        decalScale: decalScale,\n                        decalRotation: decalRotation,\n                        decalColor: decalColor,\n                        onDecalColorChange: setDecalColor,\n                        onFontChange: handleFontChange,\n                        selectedFont: selectedFont,\n                        isPlacingDecal: isPlacingDecal,\n                        setIsPlacingDecal: setIsPlacingDecal,\n                        showDebug: showDecalDebug,\n                        onShowDebugChange: setShowDecalDebug\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1869,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1753,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed right-5 top-24 z-[999] transition-all duration-300 \".concat(isFullscreen ? \"opacity-0 pointer-events-none\" : \"opacity-100\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-tl from-[#32343D] to-[#14192D] shadow-lg rounded-lg overflow-visible scrollbar-none\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Outliner__WEBPACK_IMPORTED_MODULE_9__.Outliner, {\n                        sceneObjects: sceneObjects,\n                        selectedObjects: selectedObjects,\n                        onSelectObject: setSelectedObjects,\n                        onCameraView: handleCameraView,\n                        onPlayAnimation: handlePlayAnimation,\n                        onSaveScene: handleSaveScene,\n                        groundType: groundType,\n                        setGroundType: setGroundType,\n                        collapsed: outlinerCollapsed,\n                        setCollapsed: setOutlinerCollapsed\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1897,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                    lineNumber: 1896,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1891,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-full\",\n                style: {\n                    background: \"linear-gradient(to bottom right, #363643, #5C5C6E)\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_fiber__WEBPACK_IMPORTED_MODULE_29__.Canvas, {\n                    className: \"w-full h-full\",\n                    camera: {\n                        position: getCameraPosition(),\n                        fov: 45,\n                        near: 0.1,\n                        far: 50\n                    },\n                    shadows: true,\n                    ref: sceneRef,\n                    gl: {\n                        antialias: true,\n                        preserveDrawingBuffer: true\n                    },\n                    onPointerMissed: handlePointerMissed,\n                    raycaster: {\n                        computeOffsets: (e)=>({\n                                offsetX: e.clientX,\n                                offsetY: e.clientY\n                            })\n                    },\n                    frameloop: \"demand\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n                        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_30__.Html, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_31__.Loader, {}, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 1942,\n                                columnNumber: 17\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                            lineNumber: 1941,\n                            columnNumber: 15\n                        }, void 0),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ScreenshotTaker, {\n                                onScreenshotRef: (fn)=>{\n                                    screenshotRef.current = fn;\n                                }\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 1948,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RendererProvider, {\n                                onRendererReady: (gl)=>{\n                                    setRenderer(gl);\n                                }\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 1954,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SceneLights__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                lights: lights,\n                                lightRefs: lightRefs,\n                                selectedLight: selectedLight,\n                                onLightSelect: setSelectedLight,\n                                onLightMoved: handleLightMoved,\n                                showLightSpheres: showLightSpheres,\n                                threePointLighting: true\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 1960,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Environment__WEBPACK_IMPORTED_MODULE_3__.Environment, {\n                                preset: getEnvironmentPreset(),\n                                background: showEnvironment,\n                                bgColor: bgColor,\n                                customHdri: customHdri,\n                                intensity: envIntensity,\n                                blur: envBlur,\n                                envRotation: envRotation\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 1969,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SelectionOutline__WEBPACK_IMPORTED_MODULE_23__.SelectionOutline, {\n                                selectedObjects: selectedObjects,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ModelStage__WEBPACK_IMPORTED_MODULE_21__.ModelStage, {\n                                    selectedModel: selectedModel,\n                                    wireframe: wireframe,\n                                    handleSceneUpdate: handleSceneUpdate,\n                                    handleObjectClick: handleObjectClick,\n                                    uploadedModel: uploadedModel,\n                                    groundType: groundType,\n                                    engravingText: engravingText,\n                                    activeAnimation: activeAnimation,\n                                    decalPosition: decalPosition,\n                                    decalScale: decalScale,\n                                    decalRotation: decalRotation,\n                                    decalColor: decalColor,\n                                    font: selectedFont,\n                                    isPlacingDecal: isPlacingDecal,\n                                    onDecalPlacement: handleDecalPlacement,\n                                    showDecalDebug: showDecalDebug,\n                                    sceneObjects: sceneObjects,\n                                    useDiamondShader: useDiamondShader,\n                                    usePremiumWhiteGold: usePremiumWhiteGold,\n                                    usePremiumRoseGold: usePremiumRoseGold\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                    lineNumber: 1980,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 1979,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_32__.OrbitControls, {\n                                ref: orbitControlsRef,\n                                target: [\n                                    0,\n                                    0.5,\n                                    0\n                                ],\n                                makeDefault: true,\n                                enableDamping: true,\n                                // minPolarAngle={0}\n                                // maxPolarAngle={Math.PI / 2.25}\n                                // minDistance={2.5}\n                                maxDistance: 20\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2004,\n                                columnNumber: 13\n                            }, this),\n                            selectedObjects.length > 0 && rightPanelTab === \"transform\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_33__.TransformControls, {\n                                object: selectedObjects.length === 1 ? selectedObjects[0] : selectionGroupRef.current,\n                                mode: transformMode,\n                                onMouseDown: ()=>{\n                                    if (orbitControlsRef.current) orbitControlsRef.current.enabled = false;\n                                },\n                                onMouseUp: ()=>{\n                                    if (orbitControlsRef.current) orbitControlsRef.current.enabled = true;\n                                    if (selectedObjects.length > 1) updateObjectsFromGroup();\n                                },\n                                onObjectChange: ()=>{\n                                    if (selectedObjects.length > 1) updateObjectsFromGroup();\n                                }\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2016,\n                                columnNumber: 15\n                            }, this),\n                            groundType === \"grid\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"gridHelper\", {\n                                args: [\n                                    50,\n                                    50,\n                                    \"#666666\",\n                                    \"#333333\"\n                                ],\n                                receiveShadow: true\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2038,\n                                columnNumber: 15\n                            }, this),\n                            groundType === \"solid\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n                                \"rotation-x\": -Math.PI / 2,\n                                receiveShadow: true,\n                                position: [\n                                    0,\n                                    -0.01,\n                                    0\n                                ],\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"planeGeometry\", {\n                                        args: [\n                                            200,\n                                            200\n                                        ]\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2046,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meshStandardMaterial\", {\n                                        color: \"#5d5d5d\",\n                                        wireframe: wireframe,\n                                        transparent: true,\n                                        opacity: 0.7\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2047,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2041,\n                                columnNumber: 15\n                            }, this),\n                            groundType === \"reflective\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n                                receiveShadow: true,\n                                rotation: [\n                                    -Math.PI / 2,\n                                    0,\n                                    0\n                                ],\n                                position: [\n                                    0,\n                                    -0.01,\n                                    0\n                                ],\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"planeGeometry\", {\n                                        args: [\n                                            80,\n                                            80\n                                        ]\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2061,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_34__.MeshReflectorMaterial, {\n                                        blur: [\n                                            800,\n                                            800\n                                        ],\n                                        resolution: 2048,\n                                        mixBlur: 0.5,\n                                        mixStrength: 10,\n                                        roughness: 1,\n                                        depthScale: 1,\n                                        minDepthThreshold: 0.4,\n                                        maxDepthThreshold: 1.2,\n                                        color: \"#363643\",\n                                        metalness: 0.01,\n                                        mirror: 0.3,\n                                        transparent: true,\n                                        opacity: 0.5\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2062,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2056,\n                                columnNumber: 15\n                            }, this),\n                            groundType === \"none\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n                                receiveShadow: true,\n                                rotation: [\n                                    -Math.PI / 2,\n                                    0,\n                                    0\n                                ],\n                                position: [\n                                    0,\n                                    -0.01,\n                                    0\n                                ],\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"planeGeometry\", {\n                                        args: [\n                                            200,\n                                            200\n                                        ]\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2085,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"primitive\", {\n                                        object: new three__WEBPACK_IMPORTED_MODULE_28__.ShadowMaterial({\n                                            opacity: 0.1,\n                                            color: new three__WEBPACK_IMPORTED_MODULE_28__.Color(0x000000),\n                                            transparent: true\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2086,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2080,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_35__.GizmoHelper, {\n                                alignment: \"bottom-right\",\n                                margin: [\n                                    80,\n                                    80\n                                ],\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_36__.GizmoViewport, {\n                                    labelColor: \"white\",\n                                    axisColors: [\n                                        \"red\",\n                                        \"green\",\n                                        \"blue\"\n                                    ]\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                    lineNumber: 2098,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2097,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"primitive\", {\n                                object: selectionGroupRef.current\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2104,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PostProcessingEffects__WEBPACK_IMPORTED_MODULE_22__.PostProcessingEffects, {\n                                enabled: postProcessingEnabled,\n                                settings: postProcessingSettings\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2106,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1939,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                    lineNumber: 1919,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1913,\n                columnNumber: 7\n            }, this),\n            showModelStats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ModelStats__WEBPACK_IMPORTED_MODULE_8__.ModelStats, {\n                stats: modelStats\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 2114,\n                columnNumber: 26\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s2(EditorExperience, \"FEC5NjLFHAxsG5r5KACAtSGCyE0=\", false, function() {\n    return [\n        _lib_states__WEBPACK_IMPORTED_MODULE_26__.useVersionState\n    ];\n});\n_c3 = EditorExperience;\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"ScreenshotTaker\");\n$RefreshReg$(_c1, \"MaterialPanelWrapper\");\n$RefreshReg$(_c2, \"RendererProvider\");\n$RefreshReg$(_c3, \"EditorExperience\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ArtistTool/EditorExperience.jsx\n"));

/***/ })

});