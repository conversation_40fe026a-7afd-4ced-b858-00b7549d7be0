"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/projects/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/ArtistTool/EditorExperience.jsx":
/*!********************************************************!*\
  !*** ./src/components/ArtistTool/EditorExperience.jsx ***!
  \********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ EditorExperience; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/web/Html.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/web/Loader.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/OrbitControls.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/TransformControls.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/MeshReflectorMaterial.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/GizmoHelper.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/GizmoViewport.js\");\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @react-three/fiber */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_three@0.167.1/node_modules/@react-three/fiber/dist/events-776716bd.esm.js\");\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @react-three/fiber */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_three@0.167.1/node_modules/@react-three/fiber/dist/react-three-fiber.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/.pnpm/react-toastify@11.0.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/react-toastify/dist/index.mjs\");\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! three */ \"(app-pages-browser)/./node_modules/.pnpm/three@0.167.1/node_modules/three/build/three.module.js\");\n/* harmony import */ var _Environment__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Environment */ \"(app-pages-browser)/./src/components/ArtistTool/Environment.tsx\");\n/* harmony import */ var _EnvironmentPanel__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./EnvironmentPanel */ \"(app-pages-browser)/./src/components/ArtistTool/EnvironmentPanel.jsx\");\n/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Header */ \"(app-pages-browser)/./src/components/ArtistTool/Header.jsx\");\n/* harmony import */ var _LightPanel__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./LightPanel */ \"(app-pages-browser)/./src/components/ArtistTool/LightPanel.jsx\");\n/* harmony import */ var _MaterialPanel__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./MaterialPanel */ \"(app-pages-browser)/./src/components/ArtistTool/MaterialPanel.jsx\");\n/* harmony import */ var _ModelStats__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./ModelStats */ \"(app-pages-browser)/./src/components/ArtistTool/ModelStats.jsx\");\n/* harmony import */ var _Outliner__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./Outliner */ \"(app-pages-browser)/./src/components/ArtistTool/Outliner.jsx\");\n/* harmony import */ var _PostProcessingPanel__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./PostProcessingPanel */ \"(app-pages-browser)/./src/components/ArtistTool/PostProcessingPanel.jsx\");\n/* harmony import */ var _SceneLights__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./SceneLights */ \"(app-pages-browser)/./src/components/ArtistTool/SceneLights.jsx\");\n/* harmony import */ var _Toolbar__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./Toolbar */ \"(app-pages-browser)/./src/components/ArtistTool/Toolbar.jsx\");\n/* harmony import */ var _TransformPanel__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./TransformPanel */ \"(app-pages-browser)/./src/components/ArtistTool/TransformPanel.jsx\");\n/* harmony import */ var _utils_ArtistTool_cameraUtils__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../../utils/ArtistTool/cameraUtils */ \"(app-pages-browser)/./src/utils/ArtistTool/cameraUtils.js\");\n/* harmony import */ var _utils_ArtistTool_materialUtils__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../../utils/ArtistTool/materialUtils */ \"(app-pages-browser)/./src/utils/ArtistTool/materialUtils.js\");\n/* harmony import */ var _utils_ArtistTool_sceneUtils__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../../utils/ArtistTool/sceneUtils */ \"(app-pages-browser)/./src/utils/ArtistTool/sceneUtils.js\");\n/* harmony import */ var _animations_CameraAnimations__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./animations/CameraAnimations */ \"(app-pages-browser)/./src/components/ArtistTool/animations/CameraAnimations.js\");\n/* harmony import */ var _EngravingPanel__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./EngravingPanel */ \"(app-pages-browser)/./src/components/ArtistTool/EngravingPanel.jsx\");\n/* harmony import */ var _InitialOverlayPanel__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./InitialOverlayPanel */ \"(app-pages-browser)/./src/components/ArtistTool/InitialOverlayPanel.jsx\");\n/* harmony import */ var _ModelStage__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./ModelStage */ \"(app-pages-browser)/./src/components/ArtistTool/ModelStage.jsx\");\n/* harmony import */ var _PostProcessingEffects__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./PostProcessingEffects */ \"(app-pages-browser)/./src/components/ArtistTool/PostProcessingEffects.jsx\");\n/* harmony import */ var _SelectionOutline__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ./SelectionOutline */ \"(app-pages-browser)/./src/components/ArtistTool/SelectionOutline.jsx\");\n/* harmony import */ var _lib_actions_version_actions__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/lib/actions/version.actions */ \"(app-pages-browser)/./src/lib/actions/version.actions.ts\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/.pnpm/sonner@1.5.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _lib_states__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @/lib/states */ \"(app-pages-browser)/./src/lib/states.ts\");\n/* harmony import */ var three_examples_jsm_loaders_RGBELoader_js__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! three/examples/jsm/loaders/RGBELoader.js */ \"(app-pages-browser)/./node_modules/.pnpm/three@0.167.1/node_modules/three/examples/jsm/loaders/RGBELoader.js\");\n/* harmony import */ var three_examples_jsm_loaders_EXRLoader_js__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! three/examples/jsm/loaders/EXRLoader.js */ \"(app-pages-browser)/./node_modules/.pnpm/three@0.167.1/node_modules/three/examples/jsm/loaders/EXRLoader.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst ScreenshotTaker = (param)=>{\n    let { onScreenshotRef } = param;\n    _s();\n    const { gl, scene, camera } = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.D)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (typeof onScreenshotRef === \"function\") {\n            onScreenshotRef(()=>{\n                try {\n                    // Render the scene\n                    gl.render(scene, camera);\n                    // Get the canvas data URL\n                    const dataURL = gl.domElement.toDataURL(\"image/png\");\n                    // Create and trigger a download\n                    const link = document.createElement(\"a\");\n                    const timestamp = new Date().toISOString().replace(/:/g, \"-\").substring(0, 19);\n                    link.download = \"agape-scene-\".concat(timestamp, \".png\");\n                    link.href = dataURL;\n                    document.body.appendChild(link);\n                    link.click();\n                    // Clean up\n                    setTimeout(()=>{\n                        document.body.removeChild(link);\n                    }, 100);\n                    console.log(\"Screenshot captured successfully\");\n                    return true;\n                } catch (error) {\n                    console.error(\"Error capturing screenshot:\", error);\n                    return false;\n                }\n            });\n        }\n    }, [\n        gl,\n        scene,\n        camera,\n        onScreenshotRef\n    ]);\n    return null;\n};\n_s(ScreenshotTaker, \"RVTFHuczdlyvGFUsZJPSpjurI/Y=\", false, function() {\n    return [\n        _react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.D\n    ];\n});\n_c = ScreenshotTaker;\n// Component to provide renderer to MaterialPanel\nconst MaterialPanelWrapper = (param)=>{\n    let { renderer, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MaterialPanel__WEBPACK_IMPORTED_MODULE_7__.MaterialPanel, {\n        renderer: renderer,\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n        lineNumber: 96,\n        columnNumber: 10\n    }, undefined);\n};\n_c1 = MaterialPanelWrapper;\n// Component to get renderer from Canvas context\nconst RendererProvider = (param)=>{\n    let { children, onRendererReady } = param;\n    _s1();\n    const { gl } = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.D)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (onRendererReady) {\n            onRendererReady(gl);\n        }\n    }, [\n        gl,\n        onRendererReady\n    ]);\n    return null;\n};\n_s1(RendererProvider, \"iTWbOa64ZxlA5nuyNExgWK+vjCI=\", false, function() {\n    return [\n        _react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.D\n    ];\n});\n_c2 = RendererProvider;\nfunction EditorExperience(param) {\n    let { modelUrl, project, user } = param;\n    _s2();\n    const { addVersion } = (0,_lib_states__WEBPACK_IMPORTED_MODULE_25__.useVersionState)();\n    const configRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const workingConfigRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [selectedModel, setSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"uploaded\");\n    const [lastSelectedModel, setLastSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [uploadedModel, setUploadedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(modelUrl || null);\n    const [modelStats, setModelStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        vertices: 0,\n        triangles: 0,\n        materials: 0,\n        animations: 0\n    });\n    const [lights, setLights] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: \"defaultDirectional\",\n            type: \"directional\",\n            position: [\n                2.5,\n                5.5,\n                5\n            ],\n            intensity: 1.0,\n            color: \"#ffffff\",\n            name: \"Main Directional Light\",\n            castShadow: true,\n            helperVisible: false\n        },\n        {\n            id: \"keyLight\",\n            type: \"point\",\n            position: [\n                5,\n                5,\n                5\n            ],\n            intensity: 8.0,\n            color: \"#ffffff\",\n            name: \"Key Light\"\n        },\n        {\n            id: \"fillLight\",\n            type: \"point\",\n            position: [\n                -5,\n                5,\n                -5\n            ],\n            intensity: 4.0,\n            color: \"#ffffff\",\n            name: \"Fill Light\"\n        },\n        {\n            id: \"backLight\",\n            type: \"point\",\n            position: [\n                0,\n                5,\n                -5\n            ],\n            intensity: 2.0,\n            color: \"#ffffff\",\n            name: \"Back Light\"\n        }\n    ]);\n    const lightRefs = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({});\n    const [selectedObjects, setSelectedObjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedLight, setSelectedLight] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [lightPositionHistory, setLightPositionHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [sceneObjects, setSceneObjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [transformMode, setTransformMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"translate\");\n    const selectionGroupRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(new three__WEBPACK_IMPORTED_MODULE_27__.Group());\n    console.log(\"renderingggg editor experience\");\n    const [rightPanelTab, setRightPanelTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Decal placement state\n    const [isPlacingDecal, setIsPlacingDecal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showDecalDebug, setShowDecalDebug] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Scene controls state\n    const [showGrid, setShowGrid] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [wireframe, setWireframe] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [groundType, setGroundType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"none\");\n    const [envPreset, setEnvPreset] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"hdri_15\");\n    const [bgColor, setBgColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"#272631\");\n    const [showEnvironment, setShowEnvironment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showLightSpheres, setShowLightSpheres] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [customHdri, setCustomHdri] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [envIntensity, setEnvIntensity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1.85);\n    const [envBlur, setEnvBlur] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0.35);\n    const [envRotation, setEnvRotation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [postProcessingEnabled, setPostProcessingEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [postProcessingSettings, setPostProcessingSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        autofocus: {\n            enabled: false,\n            bokehScale: 18\n        },\n        bloom: {\n            enabled: true,\n            intensity: 0.22,\n            threshold: 1,\n            radius: 0.5\n        },\n        dof: {\n            enabled: false,\n            focusDistance: 0,\n            aperture: 0.01,\n            bokehScale: 3\n        },\n        vignette: {\n            enabled: true,\n            darkness: 0.5,\n            offset: 0.1\n        }\n    });\n    // Store initial state\n    const initialStateRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        selectedModel: null,\n        lights: [],\n        envPreset: null,\n        bgColor: null,\n        postProcessingSettings: null,\n        showGrid: null,\n        wireframe: null,\n        groundType: null,\n        showEnvironment: null,\n        showLightSpheres: null,\n        envIntensity: null,\n        envBlur: null,\n        cameraPosition: null,\n        cameraTarget: null\n    });\n    // Add a ref to store the initial state of model objects\n    const modelInitialStateRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        objects: []\n    });\n    // Capture initial state when component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        initialStateRef.current = {\n            selectedModel,\n            lights: [\n                ...lights\n            ],\n            envPreset,\n            bgColor,\n            postProcessingSettings: {\n                ...postProcessingSettings\n            },\n            showGrid,\n            wireframe,\n            groundType,\n            showEnvironment,\n            showLightSpheres,\n            envIntensity,\n            envBlur,\n            cameraPosition: new three__WEBPACK_IMPORTED_MODULE_27__.Vector3(5, 2, 5),\n            cameraTarget: new three__WEBPACK_IMPORTED_MODULE_27__.Vector3(0, 1, 0)\n        };\n    }, []);\n    const orbitControlsRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const sceneRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const [renderer, setRenderer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // History state for undo/redo\n    const [history, setHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [historyIndex, setHistoryIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(-1);\n    const isUndoRedoActionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const skipHistoryTrackingRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const isUndoRedoInProgressRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const [showModelStats, setShowModelStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Debug function to inspect history state\n    const debugHistory = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        console.log(\"=== HISTORY DEBUG ===\");\n        console.log(\"Current historyIndex:\", historyIndex);\n        console.log(\"History length:\", history.length);\n        console.log(\"Can undo:\", historyIndex > 0);\n        console.log(\"Can redo:\", historyIndex < history.length - 1);\n        console.log(\"isUndoRedoActionRef.current:\", isUndoRedoActionRef.current);\n        console.log(\"skipHistoryTrackingRef.current:\", skipHistoryTrackingRef.current);\n        console.log(\"isUndoRedoInProgressRef.current:\", isUndoRedoInProgressRef.current);\n        console.log(\"History entries:\", history.map((_, i)=>\"\".concat(i).concat(i === historyIndex ? \" (current)\" : \"\")));\n        console.log(\"==================\");\n    }, [\n        historyIndex,\n        history\n    ]);\n    // Expose debug function to window for testing\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        window.debugHistory = debugHistory;\n        return ()=>{\n            delete window.debugHistory;\n        };\n    }, [\n        debugHistory\n    ]);\n    // Get full configuration for history\n    const getFullConfig = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        return JSON.parse(JSON.stringify({\n            selectedModel,\n            uploadedModel,\n            lights,\n            envPreset,\n            bgColor,\n            showEnvironment,\n            showLightSpheres,\n            customHdri,\n            envIntensity,\n            envBlur,\n            envRotation,\n            postProcessingEnabled,\n            postProcessingSettings,\n            showGrid,\n            wireframe,\n            groundType,\n            selectedObjects: selectedObjects.map((obj)=>obj.uuid),\n            // Store object states for transforms and materials\n            objectStates: sceneObjects.map((obj)=>({\n                    uuid: obj.uuid,\n                    name: obj.name,\n                    position: obj.position.toArray(),\n                    rotation: obj.rotation.toArray(),\n                    scale: obj.scale.toArray(),\n                    material: obj.material ? {\n                        type: obj.material.type,\n                        color: obj.material.color ? obj.material.color.getHexString() : \"ffffff\",\n                        roughness: obj.material.roughness,\n                        metalness: obj.material.metalness,\n                        clearcoat: obj.material.clearcoat,\n                        clearcoatRoughness: obj.material.clearcoatRoughness,\n                        wireframe: obj.material.wireframe,\n                        transparent: obj.material.transparent,\n                        opacity: obj.material.opacity,\n                        envMapIntensity: obj.material.envMapIntensity\n                    } : null\n                }))\n        }));\n    }, [\n        selectedModel,\n        uploadedModel,\n        lights,\n        envPreset,\n        bgColor,\n        showEnvironment,\n        showLightSpheres,\n        customHdri,\n        envIntensity,\n        envBlur,\n        envRotation,\n        postProcessingEnabled,\n        postProcessingSettings,\n        showGrid,\n        wireframe,\n        groundType,\n        selectedObjects,\n        sceneObjects\n    ]);\n    // Add initial state to history when component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (history.length === 0) {\n            console.log(\"[Initial] Adding initial state to history\");\n            const initialState = getFullConfig();\n            setHistory([\n                initialState\n            ]);\n            setHistoryIndex(0);\n        }\n    }, [\n        history.length,\n        getFullConfig\n    ]);\n    // Add a new state to the history\n    const addToHistory = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (isUndoRedoActionRef.current || skipHistoryTrackingRef.current) {\n            console.log(\"[addToHistory] Skipping - undo/redo action in progress\");\n            return;\n        }\n        const newState = getFullConfig();\n        // Check if the new state is different from the current state\n        if (history.length > 0 && historyIndex >= 0) {\n            const currentState = history[historyIndex];\n            if (JSON.stringify(currentState) === JSON.stringify(newState)) {\n                console.log(\"[addToHistory] Skipping - state unchanged\");\n                return;\n            }\n        }\n        const newHistory = history.slice(0, historyIndex + 1);\n        setHistory([\n            ...newHistory,\n            newState\n        ]);\n        const newIndex = historyIndex + 1;\n        setHistoryIndex(newIndex);\n        console.log(\"[addToHistory] Added state. New historyIndex:\", newIndex, \"History length:\", newHistory.length + 1, \"Can redo:\", newIndex < newHistory.length);\n    }, [\n        history,\n        historyIndex,\n        getFullConfig\n    ]);\n    const pendingSelectedObjectsUUIDs = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Define forceUpdate and version setters early so they can be used in restoreConfig\n    const [_, setForceUpdate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const forceUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>setForceUpdate((f)=>f + 1), []);\n    const [transformVersion, setTransformVersion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [materialVersion, setMaterialVersion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const restoreConfig = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((config)=>{\n        setSelectedModel(config.selectedModel);\n        setUploadedModel(config.uploadedModel);\n        setLights(config.lights);\n        setEnvPreset(config.envPreset);\n        setBgColor(config.bgColor);\n        setShowEnvironment(config.showEnvironment);\n        setShowLightSpheres(config.showLightSpheres);\n        setCustomHdri(config.customHdri);\n        setEnvIntensity(config.envIntensity);\n        setEnvBlur(config.envBlur);\n        setEnvRotation(config.envRotation);\n        setPostProcessingEnabled(config.postProcessingEnabled);\n        setPostProcessingSettings(config.postProcessingSettings);\n        setShowGrid(config.showGrid);\n        setWireframe(config.wireframe);\n        setGroundType(config.groundType);\n        // Restore object states (transforms and materials)\n        if (config.objectStates && sceneObjects.length > 0) {\n            config.objectStates.forEach((savedState)=>{\n                const obj = sceneObjects.find((o)=>o.uuid === savedState.uuid);\n                if (obj) {\n                    // Restore transform\n                    obj.position.fromArray(savedState.position);\n                    obj.rotation.fromArray(savedState.rotation);\n                    obj.scale.fromArray(savedState.scale);\n                    // Restore material properties\n                    if (savedState.material && obj.material) {\n                        const material = obj.material;\n                        if (material.color) {\n                            material.color.setHex(parseInt(savedState.material.color, 16));\n                        }\n                        if (savedState.material.roughness !== undefined) {\n                            material.roughness = savedState.material.roughness;\n                        }\n                        if (savedState.material.metalness !== undefined) {\n                            material.metalness = savedState.material.metalness;\n                        }\n                        if (savedState.material.clearcoat !== undefined) {\n                            material.clearcoat = savedState.material.clearcoat;\n                        }\n                        if (savedState.material.clearcoatRoughness !== undefined) {\n                            material.clearcoatRoughness = savedState.material.clearcoatRoughness;\n                        }\n                        if (savedState.material.wireframe !== undefined) {\n                            material.wireframe = savedState.material.wireframe;\n                        }\n                        if (savedState.material.transparent !== undefined) {\n                            material.transparent = savedState.material.transparent;\n                        }\n                        if (savedState.material.opacity !== undefined) {\n                            material.opacity = savedState.material.opacity;\n                        }\n                        if (savedState.material.envMapIntensity !== undefined) {\n                            material.envMapIntensity = savedState.material.envMapIntensity;\n                        }\n                        material.needsUpdate = true;\n                    }\n                }\n            });\n            // Force update to reflect changes\n            forceUpdate();\n            // Don't increment versions during restore to avoid triggering history loop\n            if (!isUndoRedoActionRef.current) {\n                setMaterialVersion((v)=>v + 1);\n                setTransformVersion((v)=>v + 1);\n            }\n        }\n        // Store selectedObjects UUIDs to be restored after sceneObjects update\n        pendingSelectedObjectsUUIDs.current = config.selectedObjects || [];\n        console.log(\"[restoreConfig] Restoring config\", config);\n    }, [\n        sceneObjects,\n        forceUpdate,\n        setMaterialVersion,\n        setTransformVersion\n    ]);\n    // After sceneObjects update, restore selectedObjects from UUIDs\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (pendingSelectedObjectsUUIDs.current && sceneObjects.length > 0) {\n            const uuids = pendingSelectedObjectsUUIDs.current;\n            const newSelected = sceneObjects.filter((obj)=>uuids.includes(obj.uuid));\n            setSelectedObjects(newSelected);\n            pendingSelectedObjectsUUIDs.current = null;\n            console.log(\"[restoreConfig] Restored selectedObjects\", newSelected);\n        }\n    }, [\n        sceneObjects\n    ]);\n    // Undo action\n    const undoAction = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        // Prevent rapid undo operations\n        if (isUndoRedoInProgressRef.current) {\n            console.log(\"[undoAction] Skipping - undo/redo already in progress\");\n            return;\n        }\n        console.log(\"[undoAction] Current historyIndex:\", historyIndex, \"History length:\", history.length);\n        if (historyIndex > 0) {\n            console.log(\"[undoAction] Performing undo to index:\", historyIndex - 1);\n            // Set all flags to prevent interference\n            isUndoRedoInProgressRef.current = true;\n            isUndoRedoActionRef.current = true;\n            skipHistoryTrackingRef.current = true;\n            const prevConfig = history[historyIndex - 1];\n            restoreConfig(prevConfig);\n            setHistoryIndex(historyIndex - 1);\n            // Reset flags after operation completes\n            setTimeout(()=>{\n                isUndoRedoActionRef.current = false;\n                skipHistoryTrackingRef.current = false;\n                isUndoRedoInProgressRef.current = false;\n                console.log(\"[undoAction] Undo completed, all flags reset\");\n            }, 1000);\n        } else {\n            console.log(\"[undoAction] Cannot undo - at beginning of history\");\n        }\n    }, [\n        historyIndex,\n        history,\n        restoreConfig\n    ]);\n    // Redo action\n    const redoAction = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        // Prevent rapid redo operations\n        if (isUndoRedoInProgressRef.current) {\n            console.log(\"[redoAction] Skipping - undo/redo already in progress\");\n            return;\n        }\n        debugHistory();\n        console.log(\"[redoAction] Current historyIndex:\", historyIndex, \"History length:\", history.length);\n        if (historyIndex < history.length - 1) {\n            console.log(\"[redoAction] Performing redo to index:\", historyIndex + 1);\n            // Set all flags to prevent interference\n            isUndoRedoInProgressRef.current = true;\n            isUndoRedoActionRef.current = true;\n            skipHistoryTrackingRef.current = true;\n            const nextConfig = history[historyIndex + 1];\n            console.log(\"[redoAction] Next config:\", nextConfig);\n            restoreConfig(nextConfig);\n            setHistoryIndex(historyIndex + 1);\n            // Reset flags after operation completes\n            setTimeout(()=>{\n                isUndoRedoActionRef.current = false;\n                skipHistoryTrackingRef.current = false;\n                isUndoRedoInProgressRef.current = false;\n                console.log(\"[redoAction] Redo completed, all flags reset\");\n            }, 1000);\n        } else {\n            console.log(\"[redoAction] Cannot redo - at end of history\");\n        }\n    }, [\n        historyIndex,\n        history,\n        restoreConfig,\n        debugHistory\n    ]);\n    // Track changes to objects and lights for history\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const trackChanges = ()=>{\n            if (selectedObjects.length > 0 || lights.length > 0) {\n                addToHistory({\n                    selectedObjects,\n                    lights\n                });\n            }\n        };\n        // Debounce to avoid too many history entries\n        const timeoutId = setTimeout(trackChanges, 500);\n        return ()=>clearTimeout(timeoutId);\n    }, [\n        selectedObjects,\n        lights,\n        addToHistory\n    ]);\n    const handleObjectClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e)=>{\n        e.stopPropagation();\n        // If placing decal, let the decal placement handler do its job (triggered by model's onClick)\n        if (isPlacingDecal) {\n            return;\n        }\n        console.log(\"Object clicked:\", e.object.name, e.object);\n        if (e.shiftKey) {\n            setSelectedObjects((prev)=>{\n                // Ensure prev is always an array\n                const prevArray = Array.isArray(prev) ? prev : [\n                    prev\n                ].filter(Boolean);\n                if (prevArray.includes(e.object)) {\n                    return prevArray.filter((obj)=>obj !== e.object);\n                }\n                return [\n                    ...prevArray,\n                    e.object\n                ];\n            });\n        } else {\n            setSelectedObjects([\n                e.object\n            ]);\n        }\n    }, [\n        isPlacingDecal,\n        setSelectedObjects\n    ]);\n    // Add debugging for selectedObjects changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"ExperienceNew - selectedObjects changed:\", selectedObjects);\n        console.log(\"ExperienceNew - selectedObjects length:\", selectedObjects === null || selectedObjects === void 0 ? void 0 : selectedObjects.length);\n    }, [\n        selectedObjects\n    ]);\n    // Add debugging for sceneObjects changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"ExperienceNew - sceneObjects changed:\", sceneObjects);\n        console.log(\"ExperienceNew - sceneObjects length:\", sceneObjects === null || sceneObjects === void 0 ? void 0 : sceneObjects.length);\n        console.log(\"ExperienceNew - sceneObjects details:\", sceneObjects === null || sceneObjects === void 0 ? void 0 : sceneObjects.map((obj)=>({\n                name: obj === null || obj === void 0 ? void 0 : obj.name,\n                type: obj === null || obj === void 0 ? void 0 : obj.type,\n                userData: obj === null || obj === void 0 ? void 0 : obj.userData\n            })));\n    }, [\n        sceneObjects\n    ]);\n    const handlePointerMissed = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        // If in decal placement mode, a \"missed\" click (on the canvas background)\n        // should probably cancel placement mode, or do nothing to allow model click-through.\n        // For now, let's prevent it from deselecting objects if placing decal.\n        if (isPlacingDecal) {\n            // setIsPlacingDecal(false); // Optionally, cancel placement on missed click\n            return;\n        }\n        setSelectedObjects([]);\n        setSelectedLight(null);\n    // Do not close the rightPanelTab here, as that's usually handled by clicking outside the panel itself.\n    }, [\n        isPlacingDecal,\n        setIsPlacingDecal\n    ]);\n    // Add the selection group to the scene\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // This effect runs once to add the selection group to the scene\n        return ()=>{\n            // Clean up on unmount\n            if (selectionGroupRef.current && selectionGroupRef.current.parent) {\n                selectionGroupRef.current.parent.remove(selectionGroupRef.current);\n            }\n        };\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Ensure selectedObjects is always an array\n        const objectsArray = Array.isArray(selectedObjects) ? selectedObjects : [\n            selectedObjects\n        ].filter(Boolean);\n        while(selectionGroupRef.current.children.length > 0){\n            selectionGroupRef.current.remove(selectionGroupRef.current.children[0]);\n        }\n        if (objectsArray.length > 0) {\n            const center = new three__WEBPACK_IMPORTED_MODULE_27__.Vector3();\n            objectsArray.forEach((obj)=>{\n                center.add(obj.position);\n            });\n            center.divideScalar(objectsArray.length);\n            selectionGroupRef.current.position.copy(center);\n            objectsArray.forEach((obj)=>{\n                const clone = obj.clone();\n                // Make the clone invisible while keeping its geometry for transforms\n                clone.traverse((child)=>{\n                    if (child.isMesh) {\n                        child.visible = false;\n                    }\n                });\n                clone.position.sub(center);\n                selectionGroupRef.current.add(clone);\n            });\n        }\n    }, [\n        selectedObjects\n    ]);\n    const updateObjectsFromGroup = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        // Ensure selectedObjects is always an array\n        const objectsArray = Array.isArray(selectedObjects) ? selectedObjects : [\n            selectedObjects\n        ].filter(Boolean);\n        if (objectsArray.length > 0 && selectionGroupRef.current) {\n            objectsArray.forEach((obj, index)=>{\n                if (selectionGroupRef.current.children[index]) {\n                    const worldPos = new three__WEBPACK_IMPORTED_MODULE_27__.Vector3();\n                    selectionGroupRef.current.children[index].getWorldPosition(worldPos);\n                    obj.position.copy(worldPos);\n                    obj.rotation.copy(selectionGroupRef.current.rotation);\n                    obj.scale.copy(selectionGroupRef.current.scale);\n                }\n            });\n        }\n    }, [\n        selectedObjects\n    ]);\n    const handleSceneUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((scene)=>{\n        var _scene_children;\n        console.log(\"handleSceneUpdate called with scene:\", scene);\n        console.log(\"Scene type:\", scene.type);\n        console.log(\"Scene children count:\", (_scene_children = scene.children) === null || _scene_children === void 0 ? void 0 : _scene_children.length);\n        setSceneObjects([]);\n        setSelectedObjects([]);\n        const stats = (0,_utils_ArtistTool_sceneUtils__WEBPACK_IMPORTED_MODULE_16__.calculateModelStats)(scene);\n        setModelStats(stats);\n        (0,_utils_ArtistTool_sceneUtils__WEBPACK_IMPORTED_MODULE_16__.setShadowsOnModel)(scene);\n        const objects = [];\n        const initialObjects = [];\n        scene.traverse((object)=>{\n            console.log(\"Traversing object:\", object.name, object.type, \"isMesh:\", object.isMesh, \"userData:\", object.userData);\n            if (object.isMesh) {\n                if (!object.name) {\n                    object.name = \"Mesh_\".concat(object.id);\n                }\n                console.log(\"Adding mesh to objects:\", object.name, \"userData:\", object.userData);\n                objects.push(object);\n                initialObjects.push({\n                    id: object.uuid,\n                    name: object.name,\n                    position: object.position.clone(),\n                    rotation: object.rotation.clone(),\n                    scale: object.scale.clone(),\n                    material: object.material ? {\n                        color: object.material.color ? object.material.color.getHex() : 0xffffff,\n                        roughness: object.material.roughness,\n                        metalness: object.material.metalness,\n                        wireframe: object.material.wireframe,\n                        transparent: object.material.transparent,\n                        opacity: object.material.opacity\n                    } : null\n                });\n            }\n        });\n        console.log(\"Found\", objects.length, \"mesh objects:\", objects.map((obj)=>({\n                name: obj.name,\n                userData: obj.userData\n            })));\n        modelInitialStateRef.current.objects = initialObjects;\n        setSceneObjects(objects);\n    }, []);\n    // Add this effect to detect model changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedModel !== lastSelectedModel) {\n            // Clear the scene objects when model changes\n            setSceneObjects([]);\n            setSelectedObjects([]);\n            setLastSelectedModel(selectedModel);\n        }\n    }, [\n        selectedModel,\n        lastSelectedModel\n    ]);\n    const handleLightMoved = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((lightId, newPosition)=>{\n        setLightPositionHistory((prev)=>({\n                ...prev,\n                [lightId]: [\n                    ...prev[lightId] || [],\n                    newPosition\n                ]\n            }));\n    }, []);\n    const undoLightMove = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((lightId)=>{\n        setLightPositionHistory((prev)=>{\n            const history = prev[lightId] || [];\n            if (history.length <= 1) return prev;\n            const newHistory = history.slice(0, -1);\n            const lastPosition = newHistory[newHistory.length - 1];\n            if (lightRefs.current[lightId]) {\n                lightRefs.current[lightId].position.copy(lastPosition);\n            }\n            return {\n                ...prev,\n                [lightId]: newHistory\n            };\n        });\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleKeyDown = (event)=>{\n            // Handle undo/redo keyboard shortcuts\n            if (event.ctrlKey) {\n                if (event.key === \"z\" && !event.shiftKey) {\n                    event.preventDefault();\n                    undoAction();\n                    return;\n                }\n                if (event.key === \"y\" || event.key === \"z\" && event.shiftKey) {\n                    event.preventDefault();\n                    redoAction();\n                    return;\n                }\n            }\n            // Handle Escape key for decal placement\n            if (event.key === \"Escape\" && isPlacingDecal) {\n                setIsPlacingDecal(false);\n                return;\n            }\n            // Handle light undo\n            if (event.ctrlKey && event.key === \"z\" && selectedLight) {\n                undoLightMove(selectedLight);\n            }\n            // Handle transform mode shortcuts\n            if (selectedObjects.length > 0) {\n                switch(event.key){\n                    case \"g\":\n                        setTransformMode(\"translate\");\n                        break;\n                    case \"r\":\n                        setTransformMode(\"rotate\");\n                        break;\n                    case \"s\":\n                        setTransformMode(\"scale\");\n                        break;\n                    case \"Escape\":\n                        setSelectedObjects([]);\n                        break;\n                }\n            }\n            // Handle select all\n            if (event.ctrlKey && event.key === \"a\") {\n                event.preventDefault();\n                if (sceneObjects.length > 0) {\n                    setSelectedObjects([\n                        ...sceneObjects\n                    ]);\n                }\n            }\n        };\n        window.addEventListener(\"keydown\", handleKeyDown);\n        return ()=>window.removeEventListener(\"keydown\", handleKeyDown);\n    }, [\n        selectedLight,\n        undoLightMove,\n        sceneObjects,\n        selectedObjects.length,\n        undoAction,\n        redoAction,\n        isPlacingDecal,\n        setIsPlacingDecal\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        return ()=>{\n            if (uploadedModel) {\n                URL.revokeObjectURL(uploadedModel);\n            }\n            if (customHdri) {\n                URL.revokeObjectURL(customHdri);\n            }\n        };\n    }, [\n        uploadedModel,\n        customHdri\n    ]);\n    const handleCameraView = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((view)=>{\n        (0,_utils_ArtistTool_cameraUtils__WEBPACK_IMPORTED_MODULE_14__.setCameraView)(orbitControlsRef.current, view);\n    }, []);\n    // Add the panelRef to ExperienceNew instead of in Toolbar\n    const panelRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [activeAnimation, setActiveAnimation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [animationStartTime, setAnimationStartTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const requestRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    // Animation loop handler\n    const animateCamera = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((timestamp)=>{\n        if (!activeAnimation || !orbitControlsRef.current) {\n            return;\n        }\n        // Calculate animation progress (0 to 1)\n        const animation = _animations_CameraAnimations__WEBPACK_IMPORTED_MODULE_17__.predefinedAnimations.find((a)=>a.id === activeAnimation);\n        if (!animation) return;\n        if (animationStartTime === 0) {\n            setAnimationStartTime(timestamp);\n        }\n        const elapsed = timestamp - animationStartTime;\n        const progress = elapsed % animation.duration / animation.duration;\n        // Get the target position (center of selected objects or scene center)\n        const target = new three__WEBPACK_IMPORTED_MODULE_27__.Vector3(0, 0, 0);\n        if (selectedObjects.length > 0) {\n            // Calculate center of selected objects\n            const objectsArray = Array.isArray(selectedObjects) ? selectedObjects : [\n                selectedObjects\n            ].filter(Boolean);\n            if (objectsArray.length > 0) {\n                const center = new three__WEBPACK_IMPORTED_MODULE_27__.Vector3();\n                objectsArray.forEach((obj)=>{\n                    center.add(obj.position);\n                });\n                center.divideScalar(objectsArray.length);\n                target.copy(center);\n            }\n        }\n        // Animate camera using the animation function\n        animation.animate(orbitControlsRef.current.object, orbitControlsRef.current, progress, target);\n        requestRef.current = requestAnimationFrame(animateCamera);\n    }, [\n        activeAnimation,\n        orbitControlsRef,\n        animationStartTime,\n        selectedObjects\n    ]);\n    // Setup animation loop\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (activeAnimation) {\n            setAnimationStartTime(0);\n            requestRef.current = requestAnimationFrame(animateCamera);\n            // Disable orbit controls during animation\n            if (orbitControlsRef.current) {\n                orbitControlsRef.current.enabled = false;\n            }\n        } else {\n            // Re-enable orbit controls when animation stops\n            if (orbitControlsRef.current && selectedObjects.length === 0) {\n                orbitControlsRef.current.enabled = true;\n            }\n        }\n        return ()=>{\n            if (requestRef.current) {\n                cancelAnimationFrame(requestRef.current);\n            }\n        };\n    }, [\n        activeAnimation,\n        animateCamera,\n        selectedObjects\n    ]);\n    // Handle animation playback from Outliner\n    const handlePlayAnimation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((animationId)=>{\n        setActiveAnimation(animationId);\n    }, []);\n    const getEnvironmentPreset = ()=>{\n        return envPreset;\n    };\n    // Reset function\n    const handleReset = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        // Check if there's a saved scene in localStorage\n        const savedScene = localStorage.getItem(\"agape_saved_scene\");\n        if (savedScene) {\n            const confirmReset = window.confirm(\"There is a saved scene in your browser storage. Resetting will clear this saved scene. Do you want to continue?\");\n            if (!confirmReset) {\n                return;\n            }\n            // Clear the saved scene from localStorage\n            localStorage.removeItem(\"agape_saved_scene\");\n        }\n        console.log(\"Resetting scene to initial state\");\n        setSceneObjects([]);\n        setSelectedObjects([]);\n        // Reset lights to default configuration\n        setLights([\n            {\n                id: \"defaultDirectional\",\n                type: \"directional\",\n                position: [\n                    2.5,\n                    5.5,\n                    5\n                ],\n                intensity: 1.0,\n                color: \"#ffffff\",\n                name: \"Main Directional Light\",\n                castShadow: true,\n                helperVisible: false\n            },\n            {\n                id: \"keyLight\",\n                type: \"point\",\n                position: [\n                    5,\n                    5,\n                    5\n                ],\n                intensity: 8.0,\n                color: \"#ffffff\",\n                name: \"Key Light\"\n            },\n            {\n                id: \"fillLight\",\n                type: \"point\",\n                position: [\n                    -5,\n                    5,\n                    -5\n                ],\n                intensity: 4.0,\n                color: \"#ffffff\",\n                name: \"Fill Light\"\n            },\n            {\n                id: \"backLight\",\n                type: \"point\",\n                position: [\n                    0,\n                    5,\n                    -5\n                ],\n                intensity: 2.0,\n                color: \"#ffffff\",\n                name: \"Back Light\"\n            }\n        ]);\n        // Reset environment settings\n        setEnvPreset(\"hdri_15\");\n        setBgColor(\"#202020\");\n        setShowEnvironment(false);\n        setShowLightSpheres(true);\n        setCustomHdri(null);\n        setEnvIntensity(1.5);\n        setEnvBlur(0.35);\n        setEnvRotation(0);\n        // Reset post-processing settings\n        setPostProcessingEnabled(false);\n        setPostProcessingSettings({\n            autofocus: {\n                enabled: false,\n                bokehScale: 18\n            },\n            bloom: {\n                enabled: true,\n                intensity: 0.12,\n                threshold: 0.8,\n                radius: 0.4\n            },\n            dof: {\n                enabled: false,\n                focusDistance: 0,\n                aperture: 0.01,\n                bokehScale: 3\n            },\n            vignette: {\n                enabled: true,\n                darkness: 0.5,\n                offset: 0.1\n            }\n        });\n        // Reset scene controls\n        setShowGrid(true);\n        setWireframe(false);\n        setGroundType(\"none\");\n        // Reset camera position\n        if (orbitControlsRef.current) {\n            orbitControlsRef.current.object.position.set(5, 2, 5);\n            orbitControlsRef.current.target.set(0, 1, 0);\n            orbitControlsRef.current.update();\n        }\n        // Reset transform state\n        setTransformMode(\"translate\");\n        setSelectedLight(null);\n        // Reset material shaders\n        setUseDiamondShader(false);\n        setUsePremiumWhiteGold(false);\n        setUsePremiumRoseGold(false);\n        // Clear history\n        setHistory([]);\n        setHistoryIndex(-1);\n        setLightPositionHistory({});\n        // Reset model stats\n        setModelStats({\n            vertices: 0,\n            triangles: 0,\n            materials: 0,\n            animations: 0\n        });\n        // Reset right panel tab\n        setRightPanelTab(null);\n        // Reset active animation\n        setActiveAnimation(null);\n        setAnimationStartTime(0);\n        if (requestRef.current) {\n            cancelAnimationFrame(requestRef.current);\n        }\n        // Clear any custom HDRI or uploaded model URLs\n        if (uploadedModel) {\n            URL.revokeObjectURL(uploadedModel);\n        }\n        if (customHdri) {\n            URL.revokeObjectURL(customHdri);\n        }\n        // Force a model reload by setting selectedModel to null first\n        setSelectedModel(null);\n        // Then set it back to uploaded after a small delay\n        setTimeout(()=>{\n            setSelectedModel(\"uploaded\");\n        }, 100);\n    }, []);\n    const screenshotRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const captureScreenshot = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (screenshotRef.current) {\n            console.log(\"Taking screenshot\");\n            screenshotRef.current();\n        } else {\n            console.error(\"Screenshot function not available yet\");\n        }\n    }, []);\n    // Load saved scene from localStorage on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadSavedScene = ()=>{\n            const savedScene = localStorage.getItem(\"agape_saved_scene\");\n            if (savedScene) {\n                try {\n                    const sceneConfig = JSON.parse(savedScene);\n                    console.log(\"Loading saved scene:\", sceneConfig);\n                    // Only load the saved configuration on initial load\n                    if (!lastSelectedModel) {\n                        console.log(\"Initial load - setting saved model:\", sceneConfig.model);\n                        setSelectedModel(\"uploaded\"); // Always use uploaded for S3 models\n                        // Load the rest of the saved scene configuration\n                        setLights(sceneConfig.lights);\n                        setEnvPreset(sceneConfig.environment.preset);\n                        setBgColor(sceneConfig.environment.bgColor);\n                        setShowEnvironment(sceneConfig.environment.showEnvironment);\n                        setShowLightSpheres(sceneConfig.environment.showLightSpheres);\n                        setEnvIntensity(sceneConfig.environment.intensity);\n                        setEnvBlur(sceneConfig.environment.blur);\n                        setEnvRotation(sceneConfig.environment.rotation);\n                        setPostProcessingEnabled(sceneConfig.postProcessing.enabled);\n                        setPostProcessingSettings(sceneConfig.postProcessing.settings);\n                        setShowGrid(sceneConfig.sceneControls.showGrid);\n                        setWireframe(sceneConfig.sceneControls.wireframe);\n                        setGroundType(sceneConfig.sceneControls.groundType);\n                        // Restore camera position after a short delay to ensure scene is loaded\n                        setTimeout(()=>{\n                            if (orbitControlsRef.current && sceneConfig.camera) {\n                                orbitControlsRef.current.object.position.fromArray(sceneConfig.camera.position);\n                                orbitControlsRef.current.target.fromArray(sceneConfig.camera.target);\n                                orbitControlsRef.current.update();\n                            }\n                        }, 1000);\n                    } else if (configRef.current) {\n                        // For explicit loads with configRef, do a complete load\n                        console.log(\"Explicit load with configRef\");\n                        handleLoadScene(sceneConfig);\n                    }\n                    // Create a function to restore materials\n                    const restoreMaterials = ()=>{\n                        if (!sceneConfig.materials || !sceneObjects.length) {\n                            console.log(\"No materials to restore or scene objects not ready\");\n                            return;\n                        }\n                        console.log(\"Attempting to restore materials for objects:\", sceneObjects);\n                        console.log(\"Saved materials:\", sceneConfig.materials);\n                        const savedMaterialsMap = new Map(sceneConfig.materials.map((m)=>[\n                                m.name,\n                                m.material\n                            ]));\n                        sceneObjects.forEach((obj)=>{\n                            if (obj.material && savedMaterialsMap.has(obj.name)) {\n                                const savedMaterial = savedMaterialsMap.get(obj.name);\n                                console.log(\"Restoring material for object:\", obj.name, savedMaterial);\n                                (0,_utils_ArtistTool_materialUtils__WEBPACK_IMPORTED_MODULE_15__.applyMaterialToObject)(obj, savedMaterial);\n                            } else {\n                                console.log(\"Could not find saved material for object:\", obj.name);\n                            }\n                        });\n                    };\n                    // Set up an interval to check for scene objects\n                    const checkInterval = setInterval(()=>{\n                        if (sceneObjects.length > 0) {\n                            clearInterval(checkInterval);\n                            // Add a small delay to ensure all materials are properly initialized\n                            setTimeout(restoreMaterials, 500);\n                        }\n                    }, 100);\n                    // Clean up interval after 5 seconds if scene objects never become available\n                    setTimeout(()=>{\n                        clearInterval(checkInterval);\n                    }, 5000);\n                    // Restore camera position after a short delay to ensure scene is loaded\n                    setTimeout(()=>{\n                        if (orbitControlsRef.current && sceneConfig.camera) {\n                            orbitControlsRef.current.object.position.fromArray(sceneConfig.camera.position);\n                            orbitControlsRef.current.target.fromArray(sceneConfig.camera.target);\n                            orbitControlsRef.current.update();\n                        }\n                    }, 1000);\n                } catch (error) {\n                    console.error(\"Error loading saved scene:\", error);\n                }\n            }\n        };\n        // Load saved scene immediately on mount\n        loadSavedScene();\n        // Also set up an interval to check for scene readiness\n        const checkSceneReady = setInterval(()=>{\n            if (sceneRef.current && orbitControlsRef.current) {\n                clearInterval(checkSceneReady);\n                loadSavedScene();\n            }\n        }, 100);\n        return ()=>clearInterval(checkSceneReady);\n    }, [\n        sceneObjects,\n        selectedModel\n    ]);\n    const [configState, setConfigState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (sceneObjects.length > 0 && configState && configState.materials) {\n            const applyMaterials = ()=>{\n                console.log(\"Applying materials to scene objects:\", sceneObjects);\n                console.log(\"Saved materials configuration:\", configState.materials);\n                sceneObjects.forEach((obj)=>{\n                    const materialConfig = configState.materials.find((m)=>m.name === obj.name || m.id === obj.uuid);\n                    if (materialConfig && materialConfig.material) {\n                        console.log(\"Found material config for object:\", obj.name, materialConfig.material);\n                        (0,_utils_ArtistTool_materialUtils__WEBPACK_IMPORTED_MODULE_15__.applyMaterialToObject)(obj, materialConfig.material);\n                    } else {\n                        console.log(\"No material config found for object:\", obj.name);\n                    }\n                });\n            };\n            // Add a small delay to ensure all materials are properly initialized\n            const timeoutId = setTimeout(applyMaterials, 500);\n            return ()=>clearTimeout(timeoutId);\n        }\n    }, [\n        sceneObjects,\n        configState\n    ]);\n    const handleLoadScene = (config)=>{\n        try {\n            console.log(\"Loading scene configuration:\", config);\n            // Store the config in the ref\n            configRef.current = config;\n            workingConfigRef.current = config; // Always keep this in sync with last applied config\n            setConfigState(config); // <-- keep in sync for effect\n            // Clear scene objects and selected objects first\n            setSceneObjects([]);\n            setSelectedObjects([]);\n            setLastSelectedModel(null);\n            // Restore scene settings\n            setSelectedModel(\"uploaded\");\n            setLights(config.lights);\n            setEnvPreset(config.environment.preset);\n            setBgColor(config.environment.bgColor);\n            setShowEnvironment(config.environment.showEnvironment);\n            setShowLightSpheres(config.environment.showLightSpheres);\n            setEnvIntensity(config.environment.intensity);\n            setEnvBlur(config.environment.blur);\n            setEnvRotation(config.environment.rotation);\n            setPostProcessingEnabled(config.postProcessing.enabled);\n            setPostProcessingSettings(config.postProcessing.settings);\n            setShowGrid(config.sceneControls.showGrid);\n            setWireframe(config.sceneControls.wireframe);\n            setGroundType(config.sceneControls.groundType);\n            // Wait for the scene to update before applying camera position\n            setTimeout(()=>{\n                if (orbitControlsRef.current && config.camera) {\n                    orbitControlsRef.current.object.position.fromArray(config.camera.position);\n                    orbitControlsRef.current.target.fromArray(config.camera.target);\n                    orbitControlsRef.current.update();\n                }\n            }, 1000);\n            // Restore materials after sceneObjects are ready\n            const restoreMaterials = ()=>{\n                if (!config.materials || !sceneObjects.length) {\n                    console.log(\"No materials to restore or scene objects not ready\");\n                    return;\n                }\n                console.log(\"Restoring materials for objects:\", sceneObjects);\n                const savedMaterialsMap = new Map(config.materials.map((m)=>[\n                        m.name,\n                        m.material\n                    ]));\n                sceneObjects.forEach((obj)=>{\n                    if (obj.material && savedMaterialsMap.has(obj.name)) {\n                        const savedMaterial = savedMaterialsMap.get(obj.name);\n                        console.log(\"Restoring material for object:\", obj.name, savedMaterial);\n                        (0,_utils_ArtistTool_materialUtils__WEBPACK_IMPORTED_MODULE_15__.applyMaterialToObject)(obj, savedMaterial);\n                    } else {\n                        console.log(\"Could not find saved material for object:\", obj.name);\n                    }\n                });\n            };\n            if (sceneObjects.length > 0) {\n                setTimeout(restoreMaterials, 500);\n            } else {\n                const checkInterval = setInterval(()=>{\n                    if (sceneObjects.length > 0) {\n                        clearInterval(checkInterval);\n                        setTimeout(restoreMaterials, 500);\n                    }\n                }, 100);\n                setTimeout(()=>{\n                    clearInterval(checkInterval);\n                }, 5000);\n            }\n        } catch (error) {\n            console.error(\"Error loading scene configuration:\", error);\n        }\n    };\n    const handleSaveScene = async (sceneName, description, saveType)=>{\n        // Use workingConfigRef if it exists and matches the current state, otherwise build from state\n        let sceneConfig;\n        if (workingConfigRef.current) {\n            // Use a shallow compare for top-level keys (or always use workingConfigRef for now)\n            sceneConfig = {\n                ...workingConfigRef.current,\n                name: sceneName,\n                timestamp: new Date().toISOString()\n            };\n        } else {\n            var _orbitControlsRef_current, _orbitControlsRef_current1;\n            // fallback to building from state\n            sceneConfig = {\n                name: sceneName,\n                timestamp: new Date().toISOString(),\n                model: selectedModel,\n                lights: lights.map((light)=>{\n                    var _lightRefs_current_light_id;\n                    return {\n                        ...light,\n                        position: ((_lightRefs_current_light_id = lightRefs.current[light.id]) === null || _lightRefs_current_light_id === void 0 ? void 0 : _lightRefs_current_light_id.position.toArray()) || light.position\n                    };\n                }),\n                environment: {\n                    preset: envPreset,\n                    intensity: envIntensity,\n                    blur: envBlur,\n                    rotation: envRotation,\n                    showEnvironment,\n                    bgColor,\n                    customHdri\n                },\n                postProcessing: {\n                    enabled: postProcessingEnabled,\n                    settings: postProcessingSettings\n                },\n                sceneControls: {\n                    showGrid,\n                    wireframe,\n                    groundType,\n                    showLightSpheres\n                },\n                camera: {\n                    position: (_orbitControlsRef_current = orbitControlsRef.current) === null || _orbitControlsRef_current === void 0 ? void 0 : _orbitControlsRef_current.object.position.toArray(),\n                    target: (_orbitControlsRef_current1 = orbitControlsRef.current) === null || _orbitControlsRef_current1 === void 0 ? void 0 : _orbitControlsRef_current1.target.toArray()\n                },\n                materials: sceneObjects.map((obj)=>{\n                    const material = obj.material;\n                    if (!material) return {\n                        id: obj.uuid,\n                        name: obj.name,\n                        material: null\n                    };\n                    return {\n                        id: obj.uuid,\n                        name: obj.name,\n                        material: {\n                            type: material.type,\n                            color: material.color ? material.color.getHexString() : \"ffffff\",\n                            roughness: material.roughness,\n                            metalness: material.metalness,\n                            wireframe: material.wireframe,\n                            transparent: material.transparent,\n                            opacity: material.opacity,\n                            envMapIntensity: material.envMapIntensity,\n                            ...material.type === \"MeshRefractionMaterial\" && {\n                                aberrationStrength: material.aberrationStrength,\n                                toneMapped: material.toneMapped,\n                                ior: material.ior,\n                                colorEnvMapRotY: material.colorEnvMapRotY\n                            }\n                        }\n                    };\n                })\n            };\n        }\n        if (saveType === \"local\") {\n            // Save to localStorage\n            localStorage.setItem(\"agape_saved_scene\", JSON.stringify(sceneConfig));\n        } else if (saveType === \"db\") {\n            // Save to DB (cloud)\n            try {\n                const version = await (0,_lib_actions_version_actions__WEBPACK_IMPORTED_MODULE_23__.createVersion)({\n                    userId: user.id,\n                    workspaceId: project._id,\n                    name: sceneName,\n                    config: sceneConfig,\n                    description: description\n                });\n                console.log(\"version\", version);\n                if (version) {\n                    addVersion(project._id, version);\n                }\n                sonner__WEBPACK_IMPORTED_MODULE_24__.toast.success(\"Scene saved to cloud!\");\n            } catch (err) {\n                sonner__WEBPACK_IMPORTED_MODULE_24__.toast.error(\"Failed to save scene to cloud\");\n            }\n        } else {\n            // Export as file\n            const blob = new Blob([\n                JSON.stringify(sceneConfig, null, 2)\n            ], {\n                type: \"application/json\"\n            });\n            const url = URL.createObjectURL(blob);\n            const a = document.createElement(\"a\");\n            a.href = url;\n            a.download = \"\".concat(sceneName.replace(/\\s+/g, \"_\"), \".json\");\n            document.body.appendChild(a);\n            a.click();\n            document.body.removeChild(a);\n            URL.revokeObjectURL(url);\n        }\n    };\n    // Engraving state\n    const [engravingText, setEngravingText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [decalPosition, setDecalPosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        0,\n        1.68,\n        0\n    ]);\n    const [decalScale, setDecalScale] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        2.8,\n        0.9,\n        1.5\n    ]);\n    const [decalRotation, setDecalRotation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        -Math.PI / 2,\n        0,\n        0\n    ]);\n    const [decalColor, setDecalColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"#cbcbcb\");\n    const [selectedFont, setSelectedFont] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"/fonts/ttf/RobotoSerif-Regular.ttf\");\n    // Diamond shader state\n    const [useDiamondShader, setUseDiamondShader] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [usePremiumWhiteGold, setUsePremiumWhiteGold] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [usePremiumRoseGold, setUsePremiumRoseGold] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleEngraveText = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((text)=>{\n        setEngravingText(text);\n        // Store in localStorage to make it persist across panel changes and page reloads\n        localStorage.setItem(\"engravingText\", text);\n    }, []);\n    const handleFontChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((font)=>{\n        setSelectedFont(font);\n        localStorage.setItem(\"engravingFont\", font);\n    }, []);\n    // Load saved engraving text on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const savedEngravingText = localStorage.getItem(\"engravingText\");\n        if (savedEngravingText) {\n            setEngravingText(savedEngravingText);\n        }\n    }, []);\n    const handleDecalPlacement = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((event)=>{\n        handleDecalPlacement(event, setIsPlacingDecal, setDecalPosition, setDecalRotation);\n    }, [\n        setDecalPosition,\n        setDecalRotation,\n        setIsPlacingDecal\n    ]);\n    // Get predefined decal positions based on selected model\n    const getDefaultDecalPosition = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((modelId)=>{\n        const modelConfigs = {\n            chosenring: [\n                0,\n                1.68,\n                0\n            ],\n            testring: [\n                0,\n                1.68,\n                0\n            ],\n            agapering1: [\n                0,\n                1.2,\n                0\n            ],\n            agapering2: [\n                0,\n                1.2,\n                0\n            ],\n            ringa: [\n                0,\n                0.26,\n                0\n            ],\n            ringb: [\n                0,\n                0.5,\n                0\n            ],\n            ringc: [\n                0,\n                0.26,\n                0\n            ]\n        };\n        return modelConfigs[modelId] || [\n            0,\n            1.68,\n            0\n        ];\n    }, []);\n    const getDefaultDecalRotation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((modelId)=>{\n        const modelConfigs = {\n            chosenring: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            testring: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            agapering1: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            agapering2: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            ringa: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            ringb: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            ringc: [\n                -Math.PI / 2,\n                0,\n                0\n            ]\n        };\n        return modelConfigs[modelId] || [\n            -Math.PI / 2,\n            0,\n            0\n        ];\n    }, []);\n    const getDefaultDecalScale = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((modelId)=>{\n        const modelConfigs = {\n            chosenring: [\n                6.0,\n                2.0,\n                1.5\n            ],\n            testring: [\n                6.0,\n                2.0,\n                1.5\n            ],\n            agapering1: [\n                3.2,\n                1.8,\n                1.0\n            ],\n            agapering2: [\n                3.2,\n                1.8,\n                1.0\n            ],\n            ringa: [\n                1.8,\n                2.0,\n                0.8\n            ],\n            ringb: [\n                1.8,\n                2.0,\n                0.8\n            ],\n            ringc: [\n                1.8,\n                2.0,\n                0.8\n            ]\n        };\n        return modelConfigs[modelId] || [\n            4.2,\n            2.0,\n            1.5\n        ];\n    }, []);\n    // Calculate dynamic scale based on text length and model type\n    const calculateDynamicScale = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((baseScale, text, modelId)=>{\n        if (!text || text.length === 0) return baseScale;\n        const textLength = text.length;\n        let [scaleX, scaleY, scaleZ] = baseScale;\n        // Calculate scale multiplier based on text length\n        let scaleMultiplier = 1.0;\n        if (textLength > 10) {\n            // For very long text, scale up more aggressively\n            scaleMultiplier = 1.3 + (textLength - 10) * 0.08;\n        } else if (textLength > 6) {\n            // For medium length text, scale up moderately\n            scaleMultiplier = 1.0 + (textLength - 6) * 0.08;\n        } else if (textLength < 4) {\n            // For short text, scale down slightly to prevent oversized appearance\n            scaleMultiplier = 0.8 + textLength * 0.05;\n        }\n        // Apply text length scaling to X-axis (width) primarily\n        scaleX = scaleX * scaleMultiplier;\n        // For very long text, also adjust the height slightly\n        if (textLength > 8) {\n            scaleY = scaleY * Math.min(1.2, 1.0 + (textLength - 8) * 0.02);\n        }\n        // Cap maximum scale to prevent excessive sizing (updated for larger Y values)\n        const maxScale = {\n            chosenring: [\n                12.0,\n                2.5,\n                2.0\n            ],\n            testring: [\n                12.0,\n                2.5,\n                2.0\n            ],\n            agapering1: [\n                6.5,\n                2.2,\n                1.5\n            ],\n            agapering2: [\n                6.5,\n                2.2,\n                1.5\n            ],\n            ringa: [\n                4.0,\n                2.5,\n                1.2\n            ],\n            ringb: [\n                4.0,\n                2.5,\n                1.2\n            ],\n            ringc: [\n                4.0,\n                2.5,\n                1.2\n            ]\n        };\n        const limits = maxScale[modelId] || [\n            8.5,\n            2.5,\n            2.0\n        ];\n        scaleX = Math.min(scaleX, limits[0]);\n        scaleY = Math.min(scaleY, limits[1]);\n        scaleZ = Math.min(scaleZ, limits[2]);\n        return [\n            scaleX,\n            scaleY,\n            scaleZ\n        ];\n    }, []);\n    // Update scale when text changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedModel && engravingText) {\n            const baseScale = getDefaultDecalScale(selectedModel);\n            const dynamicScale = calculateDynamicScale(baseScale, engravingText, selectedModel);\n            setDecalScale(dynamicScale);\n        }\n    }, [\n        engravingText,\n        selectedModel,\n        getDefaultDecalScale,\n        calculateDynamicScale\n    ]);\n    // Initialize decal position based on selected model\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedModel) {\n            setDecalPosition(getDefaultDecalPosition(selectedModel));\n            setDecalRotation(getDefaultDecalRotation(selectedModel));\n            // Only set initial scale if there's no engraving text yet\n            if (!engravingText) {\n                setDecalScale(getDefaultDecalScale(selectedModel));\n            }\n        }\n    }, [\n        selectedModel,\n        getDefaultDecalPosition,\n        getDefaultDecalRotation,\n        getDefaultDecalScale,\n        engravingText\n    ]);\n    const [isFullscreen, setIsFullscreen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Get camera position based on device type\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const checkMobile = ()=>{\n            setIsMobile(window.innerWidth < 768);\n        };\n        checkMobile();\n        window.addEventListener(\"resize\", checkMobile);\n        return ()=>window.removeEventListener(\"resize\", checkMobile);\n    }, []);\n    // Add state to control outliner collapsed state - initialize based on device type\n    const [outlinerCollapsed, setOutlinerCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        // Initialize based on device type - closed on mobile, open on desktop\n        if (true) {\n            return window.innerWidth < 768;\n        }\n        return false; // Default to open on server-side rendering\n    });\n    // Effect to close outliner when toolbar panels are opened on mobile\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isMobile && rightPanelTab) {\n            setOutlinerCollapsed(true);\n        }\n    }, [\n        isMobile,\n        rightPanelTab\n    ]);\n    // Effect to update outliner collapsed state when mobile state changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Update outliner state based on device type - closed on mobile, open on desktop\n        setOutlinerCollapsed(isMobile);\n    }, [\n        isMobile\n    ]);\n    const getCameraPosition = ()=>{\n        if (isMobile) {\n            return [\n                -1.85,\n                3.25,\n                4.5\n            ];\n        }\n        return [\n            -1.85,\n            1.55,\n            3.5\n        ];\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // On initial mount, fetch and apply the current version from DB\n        const fetchAndApplyCurrentVersion = async ()=>{\n            try {\n                const currentVersion = await (0,_lib_actions_version_actions__WEBPACK_IMPORTED_MODULE_23__.getCurrentVersion)({\n                    userId: user.id,\n                    workspaceId: project._id\n                });\n                if (currentVersion && currentVersion.config) {\n                    handleLoadScene(currentVersion.config);\n                }\n            } catch (err) {\n                console.error(\"Failed to load current version from DB\", err);\n            }\n        };\n        fetchAndApplyCurrentVersion();\n    // Only run on mount\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, []);\n    // Preload all HDRI/EXR environment textures\n    const envTextureCache = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({});\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const hdriFiles = [\n            {\n                key: \"hdri_15\",\n                path: \"/hdris/hdri_15.hdr\",\n                loader: three_examples_jsm_loaders_RGBELoader_js__WEBPACK_IMPORTED_MODULE_28__.RGBELoader\n            },\n            {\n                key: \"hdri_metal\",\n                path: \"/hdris/hdri_metal.exr\",\n                loader: three_examples_jsm_loaders_EXRLoader_js__WEBPACK_IMPORTED_MODULE_29__.EXRLoader\n            },\n            {\n                key: \"hdri_metal2\",\n                path: \"/hdris/metal_hdri2.hdr\",\n                loader: three_examples_jsm_loaders_RGBELoader_js__WEBPACK_IMPORTED_MODULE_28__.RGBELoader\n            },\n            {\n                key: \"hdri_metal3\",\n                path: \"/hdris/metal_hdri3.hdr\",\n                loader: three_examples_jsm_loaders_RGBELoader_js__WEBPACK_IMPORTED_MODULE_28__.RGBELoader\n            },\n            {\n                key: \"hdri_gem\",\n                path: \"/hdris/env_gem_002_30251392af.exr\",\n                loader: three_examples_jsm_loaders_EXRLoader_js__WEBPACK_IMPORTED_MODULE_29__.EXRLoader\n            },\n            {\n                key: \"studio_small\",\n                path: \"/hdris/studio_small_02_2k.hdr\",\n                loader: three_examples_jsm_loaders_RGBELoader_js__WEBPACK_IMPORTED_MODULE_28__.RGBELoader\n            }\n        ];\n        hdriFiles.forEach((param)=>{\n            let { key, path, loader } = param;\n            if (!envTextureCache.current[key]) {\n                new loader().load(path, (texture)=>{\n                    envTextureCache.current[key] = texture;\n                });\n            }\n        });\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_toastify__WEBPACK_IMPORTED_MODULE_2__.ToastContainer, {}, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1809,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Header__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                onReset: handleReset,\n                onScreenshot: captureScreenshot,\n                onSaveScene: handleSaveScene,\n                onLoadScene: handleLoadScene,\n                isFullscreen: isFullscreen,\n                onToggleFullscreen: ()=>setIsFullscreen(!isFullscreen),\n                project: project,\n                user: user,\n                onUndo: undoAction,\n                onRedo: redoAction\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1810,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_InitialOverlayPanel__WEBPACK_IMPORTED_MODULE_19__.InitialOverlayPanel, {}, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1823,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed left-5 top-1/2 transform -translate-y-1/2 z-[1000] transition-all duration-300 \".concat(isFullscreen ? \"opacity-0 pointer-events-none\" : \"opacity-100\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Toolbar__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    transformMode: transformMode,\n                    setTransformMode: setTransformMode,\n                    undoAction: undoAction,\n                    redoAction: redoAction,\n                    rightPanelTab: rightPanelTab,\n                    setRightPanelTab: setRightPanelTab,\n                    orientation: \"vertical\",\n                    sceneObjects: sceneObjects,\n                    setSelectedObjects: setSelectedObjects,\n                    selectedObjects: selectedObjects,\n                    panelRef: panelRef,\n                    isMobile: isMobile,\n                    setOutlinerCollapsed: setOutlinerCollapsed\n                }, void 0, false, {\n                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                    lineNumber: 1831,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1826,\n                columnNumber: 7\n            }, this),\n            rightPanelTab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: panelRef,\n                className: \"fixed left-20 top-1/2 transform -translate-y-1/2 bg-gradient-to-tl from-[#32343D] to-[#14192D] p-4 rounded-lg shadow-lg z-[1000] max-h-[80vh] overflow-y-auto w-[250px] md:w-[300px] border border-[#A3A3A3]/30 scrollbar-none\",\n                children: [\n                    rightPanelTab === \"transform\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TransformPanel__WEBPACK_IMPORTED_MODULE_13__.TransformPanel, {\n                        selectedObjects: selectedObjects,\n                        transformMode: transformMode,\n                        setTransformMode: setTransformMode,\n                        undoAction: undoAction,\n                        redoAction: redoAction,\n                        addToHistory: addToHistory,\n                        invalidate: _react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.m\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1855,\n                        columnNumber: 13\n                    }, this),\n                    rightPanelTab === \"material\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MaterialPanel__WEBPACK_IMPORTED_MODULE_7__.MaterialPanel, {\n                        selectedObjects: selectedObjects,\n                        useDiamondShader: useDiamondShader,\n                        setUseDiamondShader: setUseDiamondShader,\n                        usePremiumWhiteGold: usePremiumWhiteGold,\n                        setUsePremiumWhiteGold: setUsePremiumWhiteGold,\n                        usePremiumRoseGold: usePremiumRoseGold,\n                        setUsePremiumRoseGold: setUsePremiumRoseGold,\n                        renderer: renderer,\n                        forceUpdate: forceUpdate,\n                        setMaterialVersion: setMaterialVersion,\n                        invalidate: _react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.m,\n                        addToHistory: addToHistory\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1867,\n                        columnNumber: 13\n                    }, this),\n                    rightPanelTab === \"postprocessing\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PostProcessingPanel__WEBPACK_IMPORTED_MODULE_10__.PostProcessingPanel, {\n                        postProcessingEnabled: postProcessingEnabled,\n                        setPostProcessingEnabled: setPostProcessingEnabled,\n                        postProcessingSettings: postProcessingSettings,\n                        setPostProcessingSettings: setPostProcessingSettings\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1884,\n                        columnNumber: 13\n                    }, this),\n                    rightPanelTab === \"environment\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EnvironmentPanel__WEBPACK_IMPORTED_MODULE_4__.EnvironmentPanel, {\n                        envPreset: envPreset,\n                        setEnvPreset: setEnvPreset,\n                        bgColor: bgColor,\n                        setBgColor: setBgColor,\n                        showEnvironment: showEnvironment,\n                        setShowEnvironment: setShowEnvironment,\n                        customHdri: customHdri,\n                        setCustomHdri: setCustomHdri,\n                        envIntensity: envIntensity,\n                        setEnvIntensity: setEnvIntensity,\n                        showModelStats: showModelStats,\n                        setShowModelStats: setShowModelStats,\n                        envBlur: envBlur,\n                        setEnvBlur: setEnvBlur,\n                        envRotation: envRotation,\n                        setEnvRotation: setEnvRotation,\n                        envTextureCache: envTextureCache.current\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1893,\n                        columnNumber: 13\n                    }, this),\n                    rightPanelTab === \"lights\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LightPanel__WEBPACK_IMPORTED_MODULE_6__.LightPanel, {\n                        lights: lights,\n                        setLights: setLights,\n                        lightRefs: lightRefs,\n                        showGrid: showGrid,\n                        setShowGrid: setShowGrid,\n                        wireframe: wireframe,\n                        setWireframe: setWireframe,\n                        showLightSpheres: showLightSpheres,\n                        setShowLightSpheres: setShowLightSpheres,\n                        onResetLights: ()=>{\n                            // Reset lights to default configuration\n                            const defaultLights = [\n                                {\n                                    id: \"defaultDirectional\",\n                                    type: \"directional\",\n                                    position: [\n                                        2.5,\n                                        5.5,\n                                        5\n                                    ],\n                                    intensity: 1.0,\n                                    color: \"#ffffff\",\n                                    name: \"Main Directional Light\",\n                                    castShadow: true,\n                                    helperVisible: false\n                                },\n                                {\n                                    id: \"keyLight\",\n                                    type: \"point\",\n                                    position: [\n                                        5,\n                                        5,\n                                        5\n                                    ],\n                                    intensity: 8.0,\n                                    color: \"#ffffff\",\n                                    name: \"Key Light\"\n                                },\n                                {\n                                    id: \"fillLight\",\n                                    type: \"point\",\n                                    position: [\n                                        -5,\n                                        5,\n                                        -5\n                                    ],\n                                    intensity: 4.0,\n                                    color: \"#ffffff\",\n                                    name: \"Fill Light\"\n                                },\n                                {\n                                    id: \"backLight\",\n                                    type: \"point\",\n                                    position: [\n                                        0,\n                                        5,\n                                        -5\n                                    ],\n                                    intensity: 2.0,\n                                    color: \"#ffffff\",\n                                    name: \"Back Light\"\n                                }\n                            ];\n                            setLights(defaultLights);\n                            // Clear selected light\n                            setSelectedLight(null);\n                            // Clear light position history\n                            setLightPositionHistory({});\n                        }\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1915,\n                        columnNumber: 13\n                    }, this),\n                    rightPanelTab === \"engraving\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EngravingPanel__WEBPACK_IMPORTED_MODULE_18__.EngravingPanel, {\n                        onEngraveText: handleEngraveText,\n                        onDecalPositionChange: setDecalPosition,\n                        onDecalScaleChange: setDecalScale,\n                        onDecalRotationChange: setDecalRotation,\n                        decalPosition: decalPosition,\n                        decalScale: decalScale,\n                        decalRotation: decalRotation,\n                        decalColor: decalColor,\n                        onDecalColorChange: setDecalColor,\n                        onFontChange: handleFontChange,\n                        selectedFont: selectedFont,\n                        isPlacingDecal: isPlacingDecal,\n                        setIsPlacingDecal: setIsPlacingDecal,\n                        showDebug: showDecalDebug,\n                        onShowDebugChange: setShowDecalDebug\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1973,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1850,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed right-5 top-24 z-[999] transition-all duration-300 \".concat(isFullscreen ? \"opacity-0 pointer-events-none\" : \"opacity-100\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-tl from-[#32343D] to-[#14192D] shadow-lg rounded-lg overflow-visible scrollbar-none\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Outliner__WEBPACK_IMPORTED_MODULE_9__.Outliner, {\n                        sceneObjects: sceneObjects,\n                        selectedObjects: selectedObjects,\n                        onSelectObject: setSelectedObjects,\n                        onCameraView: handleCameraView,\n                        onPlayAnimation: handlePlayAnimation,\n                        onSaveScene: handleSaveScene,\n                        groundType: groundType,\n                        setGroundType: setGroundType,\n                        collapsed: outlinerCollapsed,\n                        setCollapsed: setOutlinerCollapsed\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 2001,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                    lineNumber: 2000,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1995,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-full\",\n                style: {\n                    background: \"linear-gradient(to bottom right, #363643, #5C5C6E)\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_fiber__WEBPACK_IMPORTED_MODULE_30__.Canvas, {\n                    className: \"w-full h-full\",\n                    camera: {\n                        position: getCameraPosition(),\n                        fov: 45,\n                        near: 0.1,\n                        far: 50\n                    },\n                    shadows: true,\n                    ref: sceneRef,\n                    gl: {\n                        antialias: true,\n                        preserveDrawingBuffer: true\n                    },\n                    onPointerMissed: handlePointerMissed,\n                    raycaster: {\n                        computeOffsets: (e)=>({\n                                offsetX: e.clientX,\n                                offsetY: e.clientY\n                            })\n                    },\n                    frameloop: \"demand\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n                        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_31__.Html, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_32__.Loader, {}, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2046,\n                                columnNumber: 17\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                            lineNumber: 2045,\n                            columnNumber: 15\n                        }, void 0),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ScreenshotTaker, {\n                                onScreenshotRef: (fn)=>{\n                                    screenshotRef.current = fn;\n                                }\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2052,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RendererProvider, {\n                                onRendererReady: (gl)=>{\n                                    setRenderer(gl);\n                                }\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2058,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SceneLights__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                lights: lights,\n                                lightRefs: lightRefs,\n                                selectedLight: selectedLight,\n                                onLightSelect: setSelectedLight,\n                                onLightMoved: handleLightMoved,\n                                showLightSpheres: showLightSpheres,\n                                threePointLighting: true\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2064,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Environment__WEBPACK_IMPORTED_MODULE_3__.Environment, {\n                                preset: getEnvironmentPreset(),\n                                background: showEnvironment,\n                                bgColor: bgColor,\n                                customHdri: customHdri,\n                                intensity: envIntensity,\n                                blur: envBlur,\n                                envRotation: envRotation,\n                                envTextureCache: envTextureCache.current\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2073,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SelectionOutline__WEBPACK_IMPORTED_MODULE_22__.SelectionOutline, {\n                                selectedObjects: selectedObjects,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ModelStage__WEBPACK_IMPORTED_MODULE_20__.ModelStage, {\n                                    selectedModel: selectedModel,\n                                    wireframe: wireframe,\n                                    handleSceneUpdate: handleSceneUpdate,\n                                    handleObjectClick: handleObjectClick,\n                                    uploadedModel: uploadedModel,\n                                    groundType: groundType,\n                                    engravingText: engravingText,\n                                    activeAnimation: activeAnimation,\n                                    decalPosition: decalPosition,\n                                    decalScale: decalScale,\n                                    decalRotation: decalRotation,\n                                    decalColor: decalColor,\n                                    font: selectedFont,\n                                    isPlacingDecal: isPlacingDecal,\n                                    onDecalPlacement: handleDecalPlacement,\n                                    showDecalDebug: showDecalDebug,\n                                    sceneObjects: sceneObjects,\n                                    useDiamondShader: useDiamondShader,\n                                    usePremiumWhiteGold: usePremiumWhiteGold,\n                                    usePremiumRoseGold: usePremiumRoseGold\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                    lineNumber: 2085,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2084,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_33__.OrbitControls, {\n                                ref: orbitControlsRef,\n                                target: [\n                                    0,\n                                    0.5,\n                                    0\n                                ],\n                                makeDefault: true,\n                                enableDamping: true,\n                                // minPolarAngle={0}\n                                // maxPolarAngle={Math.PI / 2.25}\n                                // minDistance={2.5}\n                                maxDistance: 20\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2109,\n                                columnNumber: 13\n                            }, this),\n                            selectedObjects.length > 0 && rightPanelTab === \"transform\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_34__.TransformControls, {\n                                object: selectedObjects.length === 1 ? selectedObjects[0] : selectionGroupRef.current,\n                                mode: transformMode,\n                                onMouseDown: ()=>{\n                                    if (orbitControlsRef.current) orbitControlsRef.current.enabled = false;\n                                },\n                                onMouseUp: ()=>{\n                                    if (orbitControlsRef.current) orbitControlsRef.current.enabled = true;\n                                    if (selectedObjects.length > 1) updateObjectsFromGroup();\n                                    forceUpdate(); // Ensure immediate update after transform\n                                    (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.m)(); // Force R3F to re-render after transform\n                                },\n                                onObjectChange: ()=>{\n                                    if (selectedObjects.length > 1) updateObjectsFromGroup();\n                                    forceUpdate(); // Ensure immediate update after transform\n                                    (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_26__.m)(); // Force R3F to re-render after transform\n                                }\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2121,\n                                columnNumber: 15\n                            }, this),\n                            groundType === \"grid\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"gridHelper\", {\n                                args: [\n                                    50,\n                                    50,\n                                    \"#666666\",\n                                    \"#333333\"\n                                ],\n                                receiveShadow: true\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2147,\n                                columnNumber: 15\n                            }, this),\n                            groundType === \"solid\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n                                \"rotation-x\": -Math.PI / 2,\n                                receiveShadow: true,\n                                position: [\n                                    0,\n                                    -0.01,\n                                    0\n                                ],\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"planeGeometry\", {\n                                        args: [\n                                            200,\n                                            200\n                                        ]\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2155,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meshStandardMaterial\", {\n                                        color: \"#5d5d5d\",\n                                        wireframe: wireframe,\n                                        transparent: true,\n                                        opacity: 0.7\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2156,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2150,\n                                columnNumber: 15\n                            }, this),\n                            groundType === \"reflective\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n                                receiveShadow: true,\n                                rotation: [\n                                    -Math.PI / 2,\n                                    0,\n                                    0\n                                ],\n                                position: [\n                                    0,\n                                    -0.01,\n                                    0\n                                ],\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"planeGeometry\", {\n                                        args: [\n                                            80,\n                                            80\n                                        ]\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2170,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_35__.MeshReflectorMaterial, {\n                                        blur: [\n                                            800,\n                                            800\n                                        ],\n                                        resolution: 2048,\n                                        mixBlur: 0.5,\n                                        mixStrength: 10,\n                                        roughness: 1,\n                                        depthScale: 1,\n                                        minDepthThreshold: 0.4,\n                                        maxDepthThreshold: 1.2,\n                                        color: \"#363643\",\n                                        metalness: 0.01,\n                                        mirror: 0.3,\n                                        transparent: true,\n                                        opacity: 0.5\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2171,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2165,\n                                columnNumber: 15\n                            }, this),\n                            groundType === \"none\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n                                receiveShadow: true,\n                                rotation: [\n                                    -Math.PI / 2,\n                                    0,\n                                    0\n                                ],\n                                position: [\n                                    0,\n                                    -0.01,\n                                    0\n                                ],\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"planeGeometry\", {\n                                        args: [\n                                            200,\n                                            200\n                                        ]\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2194,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"primitive\", {\n                                        object: new three__WEBPACK_IMPORTED_MODULE_27__.ShadowMaterial({\n                                            opacity: 0.1,\n                                            color: new three__WEBPACK_IMPORTED_MODULE_27__.Color(0x000000),\n                                            transparent: true\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2195,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2189,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_36__.GizmoHelper, {\n                                alignment: \"bottom-right\",\n                                margin: [\n                                    80,\n                                    80\n                                ],\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_37__.GizmoViewport, {\n                                    labelColor: \"white\",\n                                    axisColors: [\n                                        \"red\",\n                                        \"green\",\n                                        \"blue\"\n                                    ]\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                    lineNumber: 2207,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2206,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"primitive\", {\n                                object: selectionGroupRef.current\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2213,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PostProcessingEffects__WEBPACK_IMPORTED_MODULE_21__.PostProcessingEffects, {\n                                enabled: postProcessingEnabled,\n                                settings: postProcessingSettings\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2215,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 2043,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                    lineNumber: 2023,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 2017,\n                columnNumber: 7\n            }, this),\n            showModelStats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ModelStats__WEBPACK_IMPORTED_MODULE_8__.ModelStats, {\n                stats: modelStats\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 2223,\n                columnNumber: 26\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s2(EditorExperience, \"JxrURUqzlmQjBBZjlm5WfzcQTGM=\", false, function() {\n    return [\n        _lib_states__WEBPACK_IMPORTED_MODULE_25__.useVersionState\n    ];\n});\n_c3 = EditorExperience;\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"ScreenshotTaker\");\n$RefreshReg$(_c1, \"MaterialPanelWrapper\");\n$RefreshReg$(_c2, \"RendererProvider\");\n$RefreshReg$(_c3, \"EditorExperience\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ArtistTool/EditorExperience.jsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/three@0.167.1/node_modules/three/examples/jsm/loaders/RGBELoader.js":
/*!************************************************************************************************!*\
  !*** ./node_modules/.pnpm/three@0.167.1/node_modules/three/examples/jsm/loaders/RGBELoader.js ***!
  \************************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RGBELoader: function() { return /* binding */ RGBELoader; }\n/* harmony export */ });\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! three */ \"(app-pages-browser)/./node_modules/.pnpm/three@0.167.1/node_modules/three/build/three.module.js\");\n\n\n// https://github.com/mrdoob/three.js/issues/5552\n// http://en.wikipedia.org/wiki/RGBE_image_format\n\nclass RGBELoader extends three__WEBPACK_IMPORTED_MODULE_0__.DataTextureLoader {\n\n\tconstructor( manager ) {\n\n\t\tsuper( manager );\n\n\t\tthis.type = three__WEBPACK_IMPORTED_MODULE_0__.HalfFloatType;\n\n\t}\n\n\t// adapted from http://www.graphics.cornell.edu/~bjw/rgbe.html\n\n\tparse( buffer ) {\n\n\t\tconst\n\t\t\t/* default error routine.  change this to change error handling */\n\t\t\trgbe_read_error = 1,\n\t\t\trgbe_write_error = 2,\n\t\t\trgbe_format_error = 3,\n\t\t\trgbe_memory_error = 4,\n\t\t\trgbe_error = function ( rgbe_error_code, msg ) {\n\n\t\t\t\tswitch ( rgbe_error_code ) {\n\n\t\t\t\t\tcase rgbe_read_error: throw new Error( 'THREE.RGBELoader: Read Error: ' + ( msg || '' ) );\n\t\t\t\t\tcase rgbe_write_error: throw new Error( 'THREE.RGBELoader: Write Error: ' + ( msg || '' ) );\n\t\t\t\t\tcase rgbe_format_error: throw new Error( 'THREE.RGBELoader: Bad File Format: ' + ( msg || '' ) );\n\t\t\t\t\tdefault:\n\t\t\t\t\tcase rgbe_memory_error: throw new Error( 'THREE.RGBELoader: Memory Error: ' + ( msg || '' ) );\n\n\t\t\t\t}\n\n\t\t\t},\n\n\t\t\t/* offsets to red, green, and blue components in a data (float) pixel */\n\t\t\t//RGBE_DATA_RED = 0,\n\t\t\t//RGBE_DATA_GREEN = 1,\n\t\t\t//RGBE_DATA_BLUE = 2,\n\n\t\t\t/* number of floats per pixel, use 4 since stored in rgba image format */\n\t\t\t//RGBE_DATA_SIZE = 4,\n\n\t\t\t/* flags indicating which fields in an rgbe_header_info are valid */\n\t\t\tRGBE_VALID_PROGRAMTYPE = 1,\n\t\t\tRGBE_VALID_FORMAT = 2,\n\t\t\tRGBE_VALID_DIMENSIONS = 4,\n\n\t\t\tNEWLINE = '\\n',\n\n\t\t\tfgets = function ( buffer, lineLimit, consume ) {\n\n\t\t\t\tconst chunkSize = 128;\n\n\t\t\t\tlineLimit = ! lineLimit ? 1024 : lineLimit;\n\t\t\t\tlet p = buffer.pos,\n\t\t\t\t\ti = - 1, len = 0, s = '',\n\t\t\t\t\tchunk = String.fromCharCode.apply( null, new Uint16Array( buffer.subarray( p, p + chunkSize ) ) );\n\n\t\t\t\twhile ( ( 0 > ( i = chunk.indexOf( NEWLINE ) ) ) && ( len < lineLimit ) && ( p < buffer.byteLength ) ) {\n\n\t\t\t\t\ts += chunk; len += chunk.length;\n\t\t\t\t\tp += chunkSize;\n\t\t\t\t\tchunk += String.fromCharCode.apply( null, new Uint16Array( buffer.subarray( p, p + chunkSize ) ) );\n\n\t\t\t\t}\n\n\t\t\t\tif ( - 1 < i ) {\n\n\t\t\t\t\t/*for (i=l-1; i>=0; i--) {\n\t\t\t\t\t\tbyteCode = m.charCodeAt(i);\n\t\t\t\t\t\tif (byteCode > 0x7f && byteCode <= 0x7ff) byteLen++;\n\t\t\t\t\t\telse if (byteCode > 0x7ff && byteCode <= 0xffff) byteLen += 2;\n\t\t\t\t\t\tif (byteCode >= 0xDC00 && byteCode <= 0xDFFF) i--; //trail surrogate\n\t\t\t\t\t}*/\n\t\t\t\t\tif ( false !== consume ) buffer.pos += len + i + 1;\n\t\t\t\t\treturn s + chunk.slice( 0, i );\n\n\t\t\t\t}\n\n\t\t\t\treturn false;\n\n\t\t\t},\n\n\t\t\t/* minimal header reading.  modify if you want to parse more information */\n\t\t\tRGBE_ReadHeader = function ( buffer ) {\n\n\n\t\t\t\t// regexes to parse header info fields\n\t\t\t\tconst magic_token_re = /^#\\?(\\S+)/,\n\t\t\t\t\tgamma_re = /^\\s*GAMMA\\s*=\\s*(\\d+(\\.\\d+)?)\\s*$/,\n\t\t\t\t\texposure_re = /^\\s*EXPOSURE\\s*=\\s*(\\d+(\\.\\d+)?)\\s*$/,\n\t\t\t\t\tformat_re = /^\\s*FORMAT=(\\S+)\\s*$/,\n\t\t\t\t\tdimensions_re = /^\\s*\\-Y\\s+(\\d+)\\s+\\+X\\s+(\\d+)\\s*$/,\n\n\t\t\t\t\t// RGBE format header struct\n\t\t\t\t\theader = {\n\n\t\t\t\t\t\tvalid: 0, /* indicate which fields are valid */\n\n\t\t\t\t\t\tstring: '', /* the actual header string */\n\n\t\t\t\t\t\tcomments: '', /* comments found in header */\n\n\t\t\t\t\t\tprogramtype: 'RGBE', /* listed at beginning of file to identify it after \"#?\". defaults to \"RGBE\" */\n\n\t\t\t\t\t\tformat: '', /* RGBE format, default 32-bit_rle_rgbe */\n\n\t\t\t\t\t\tgamma: 1.0, /* image has already been gamma corrected with given gamma. defaults to 1.0 (no correction) */\n\n\t\t\t\t\t\texposure: 1.0, /* a value of 1.0 in an image corresponds to <exposure> watts/steradian/m^2. defaults to 1.0 */\n\n\t\t\t\t\t\twidth: 0, height: 0 /* image dimensions, width/height */\n\n\t\t\t\t\t};\n\n\t\t\t\tlet line, match;\n\n\t\t\t\tif ( buffer.pos >= buffer.byteLength || ! ( line = fgets( buffer ) ) ) {\n\n\t\t\t\t\trgbe_error( rgbe_read_error, 'no header found' );\n\n\t\t\t\t}\n\n\t\t\t\t/* if you want to require the magic token then uncomment the next line */\n\t\t\t\tif ( ! ( match = line.match( magic_token_re ) ) ) {\n\n\t\t\t\t\trgbe_error( rgbe_format_error, 'bad initial token' );\n\n\t\t\t\t}\n\n\t\t\t\theader.valid |= RGBE_VALID_PROGRAMTYPE;\n\t\t\t\theader.programtype = match[ 1 ];\n\t\t\t\theader.string += line + '\\n';\n\n\t\t\t\twhile ( true ) {\n\n\t\t\t\t\tline = fgets( buffer );\n\t\t\t\t\tif ( false === line ) break;\n\t\t\t\t\theader.string += line + '\\n';\n\n\t\t\t\t\tif ( '#' === line.charAt( 0 ) ) {\n\n\t\t\t\t\t\theader.comments += line + '\\n';\n\t\t\t\t\t\tcontinue; // comment line\n\n\t\t\t\t\t}\n\n\t\t\t\t\tif ( match = line.match( gamma_re ) ) {\n\n\t\t\t\t\t\theader.gamma = parseFloat( match[ 1 ] );\n\n\t\t\t\t\t}\n\n\t\t\t\t\tif ( match = line.match( exposure_re ) ) {\n\n\t\t\t\t\t\theader.exposure = parseFloat( match[ 1 ] );\n\n\t\t\t\t\t}\n\n\t\t\t\t\tif ( match = line.match( format_re ) ) {\n\n\t\t\t\t\t\theader.valid |= RGBE_VALID_FORMAT;\n\t\t\t\t\t\theader.format = match[ 1 ];//'32-bit_rle_rgbe';\n\n\t\t\t\t\t}\n\n\t\t\t\t\tif ( match = line.match( dimensions_re ) ) {\n\n\t\t\t\t\t\theader.valid |= RGBE_VALID_DIMENSIONS;\n\t\t\t\t\t\theader.height = parseInt( match[ 1 ], 10 );\n\t\t\t\t\t\theader.width = parseInt( match[ 2 ], 10 );\n\n\t\t\t\t\t}\n\n\t\t\t\t\tif ( ( header.valid & RGBE_VALID_FORMAT ) && ( header.valid & RGBE_VALID_DIMENSIONS ) ) break;\n\n\t\t\t\t}\n\n\t\t\t\tif ( ! ( header.valid & RGBE_VALID_FORMAT ) ) {\n\n\t\t\t\t\trgbe_error( rgbe_format_error, 'missing format specifier' );\n\n\t\t\t\t}\n\n\t\t\t\tif ( ! ( header.valid & RGBE_VALID_DIMENSIONS ) ) {\n\n\t\t\t\t\trgbe_error( rgbe_format_error, 'missing image size specifier' );\n\n\t\t\t\t}\n\n\t\t\t\treturn header;\n\n\t\t\t},\n\n\t\t\tRGBE_ReadPixels_RLE = function ( buffer, w, h ) {\n\n\t\t\t\tconst scanline_width = w;\n\n\t\t\t\tif (\n\t\t\t\t\t// run length encoding is not allowed so read flat\n\t\t\t\t\t( ( scanline_width < 8 ) || ( scanline_width > 0x7fff ) ) ||\n\t\t\t\t\t// this file is not run length encoded\n\t\t\t\t\t( ( 2 !== buffer[ 0 ] ) || ( 2 !== buffer[ 1 ] ) || ( buffer[ 2 ] & 0x80 ) )\n\t\t\t\t) {\n\n\t\t\t\t\t// return the flat buffer\n\t\t\t\t\treturn new Uint8Array( buffer );\n\n\t\t\t\t}\n\n\t\t\t\tif ( scanline_width !== ( ( buffer[ 2 ] << 8 ) | buffer[ 3 ] ) ) {\n\n\t\t\t\t\trgbe_error( rgbe_format_error, 'wrong scanline width' );\n\n\t\t\t\t}\n\n\t\t\t\tconst data_rgba = new Uint8Array( 4 * w * h );\n\n\t\t\t\tif ( ! data_rgba.length ) {\n\n\t\t\t\t\trgbe_error( rgbe_memory_error, 'unable to allocate buffer space' );\n\n\t\t\t\t}\n\n\t\t\t\tlet offset = 0, pos = 0;\n\n\t\t\t\tconst ptr_end = 4 * scanline_width;\n\t\t\t\tconst rgbeStart = new Uint8Array( 4 );\n\t\t\t\tconst scanline_buffer = new Uint8Array( ptr_end );\n\t\t\t\tlet num_scanlines = h;\n\n\t\t\t\t// read in each successive scanline\n\t\t\t\twhile ( ( num_scanlines > 0 ) && ( pos < buffer.byteLength ) ) {\n\n\t\t\t\t\tif ( pos + 4 > buffer.byteLength ) {\n\n\t\t\t\t\t\trgbe_error( rgbe_read_error );\n\n\t\t\t\t\t}\n\n\t\t\t\t\trgbeStart[ 0 ] = buffer[ pos ++ ];\n\t\t\t\t\trgbeStart[ 1 ] = buffer[ pos ++ ];\n\t\t\t\t\trgbeStart[ 2 ] = buffer[ pos ++ ];\n\t\t\t\t\trgbeStart[ 3 ] = buffer[ pos ++ ];\n\n\t\t\t\t\tif ( ( 2 != rgbeStart[ 0 ] ) || ( 2 != rgbeStart[ 1 ] ) || ( ( ( rgbeStart[ 2 ] << 8 ) | rgbeStart[ 3 ] ) != scanline_width ) ) {\n\n\t\t\t\t\t\trgbe_error( rgbe_format_error, 'bad rgbe scanline format' );\n\n\t\t\t\t\t}\n\n\t\t\t\t\t// read each of the four channels for the scanline into the buffer\n\t\t\t\t\t// first red, then green, then blue, then exponent\n\t\t\t\t\tlet ptr = 0, count;\n\n\t\t\t\t\twhile ( ( ptr < ptr_end ) && ( pos < buffer.byteLength ) ) {\n\n\t\t\t\t\t\tcount = buffer[ pos ++ ];\n\t\t\t\t\t\tconst isEncodedRun = count > 128;\n\t\t\t\t\t\tif ( isEncodedRun ) count -= 128;\n\n\t\t\t\t\t\tif ( ( 0 === count ) || ( ptr + count > ptr_end ) ) {\n\n\t\t\t\t\t\t\trgbe_error( rgbe_format_error, 'bad scanline data' );\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif ( isEncodedRun ) {\n\n\t\t\t\t\t\t\t// a (encoded) run of the same value\n\t\t\t\t\t\t\tconst byteValue = buffer[ pos ++ ];\n\t\t\t\t\t\t\tfor ( let i = 0; i < count; i ++ ) {\n\n\t\t\t\t\t\t\t\tscanline_buffer[ ptr ++ ] = byteValue;\n\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t//ptr += count;\n\n\t\t\t\t\t\t} else {\n\n\t\t\t\t\t\t\t// a literal-run\n\t\t\t\t\t\t\tscanline_buffer.set( buffer.subarray( pos, pos + count ), ptr );\n\t\t\t\t\t\t\tptr += count; pos += count;\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t}\n\n\n\t\t\t\t\t// now convert data from buffer into rgba\n\t\t\t\t\t// first red, then green, then blue, then exponent (alpha)\n\t\t\t\t\tconst l = scanline_width; //scanline_buffer.byteLength;\n\t\t\t\t\tfor ( let i = 0; i < l; i ++ ) {\n\n\t\t\t\t\t\tlet off = 0;\n\t\t\t\t\t\tdata_rgba[ offset ] = scanline_buffer[ i + off ];\n\t\t\t\t\t\toff += scanline_width; //1;\n\t\t\t\t\t\tdata_rgba[ offset + 1 ] = scanline_buffer[ i + off ];\n\t\t\t\t\t\toff += scanline_width; //1;\n\t\t\t\t\t\tdata_rgba[ offset + 2 ] = scanline_buffer[ i + off ];\n\t\t\t\t\t\toff += scanline_width; //1;\n\t\t\t\t\t\tdata_rgba[ offset + 3 ] = scanline_buffer[ i + off ];\n\t\t\t\t\t\toffset += 4;\n\n\t\t\t\t\t}\n\n\t\t\t\t\tnum_scanlines --;\n\n\t\t\t\t}\n\n\t\t\t\treturn data_rgba;\n\n\t\t\t};\n\n\t\tconst RGBEByteToRGBFloat = function ( sourceArray, sourceOffset, destArray, destOffset ) {\n\n\t\t\tconst e = sourceArray[ sourceOffset + 3 ];\n\t\t\tconst scale = Math.pow( 2.0, e - 128.0 ) / 255.0;\n\n\t\t\tdestArray[ destOffset + 0 ] = sourceArray[ sourceOffset + 0 ] * scale;\n\t\t\tdestArray[ destOffset + 1 ] = sourceArray[ sourceOffset + 1 ] * scale;\n\t\t\tdestArray[ destOffset + 2 ] = sourceArray[ sourceOffset + 2 ] * scale;\n\t\t\tdestArray[ destOffset + 3 ] = 1;\n\n\t\t};\n\n\t\tconst RGBEByteToRGBHalf = function ( sourceArray, sourceOffset, destArray, destOffset ) {\n\n\t\t\tconst e = sourceArray[ sourceOffset + 3 ];\n\t\t\tconst scale = Math.pow( 2.0, e - 128.0 ) / 255.0;\n\n\t\t\t// clamping to 65504, the maximum representable value in float16\n\t\t\tdestArray[ destOffset + 0 ] = three__WEBPACK_IMPORTED_MODULE_0__.DataUtils.toHalfFloat( Math.min( sourceArray[ sourceOffset + 0 ] * scale, 65504 ) );\n\t\t\tdestArray[ destOffset + 1 ] = three__WEBPACK_IMPORTED_MODULE_0__.DataUtils.toHalfFloat( Math.min( sourceArray[ sourceOffset + 1 ] * scale, 65504 ) );\n\t\t\tdestArray[ destOffset + 2 ] = three__WEBPACK_IMPORTED_MODULE_0__.DataUtils.toHalfFloat( Math.min( sourceArray[ sourceOffset + 2 ] * scale, 65504 ) );\n\t\t\tdestArray[ destOffset + 3 ] = three__WEBPACK_IMPORTED_MODULE_0__.DataUtils.toHalfFloat( 1 );\n\n\t\t};\n\n\t\tconst byteArray = new Uint8Array( buffer );\n\t\tbyteArray.pos = 0;\n\t\tconst rgbe_header_info = RGBE_ReadHeader( byteArray );\n\n\t\tconst w = rgbe_header_info.width,\n\t\t\th = rgbe_header_info.height,\n\t\t\timage_rgba_data = RGBE_ReadPixels_RLE( byteArray.subarray( byteArray.pos ), w, h );\n\n\n\t\tlet data, type;\n\t\tlet numElements;\n\n\t\tswitch ( this.type ) {\n\n\t\t\tcase three__WEBPACK_IMPORTED_MODULE_0__.FloatType:\n\n\t\t\t\tnumElements = image_rgba_data.length / 4;\n\t\t\t\tconst floatArray = new Float32Array( numElements * 4 );\n\n\t\t\t\tfor ( let j = 0; j < numElements; j ++ ) {\n\n\t\t\t\t\tRGBEByteToRGBFloat( image_rgba_data, j * 4, floatArray, j * 4 );\n\n\t\t\t\t}\n\n\t\t\t\tdata = floatArray;\n\t\t\t\ttype = three__WEBPACK_IMPORTED_MODULE_0__.FloatType;\n\t\t\t\tbreak;\n\n\t\t\tcase three__WEBPACK_IMPORTED_MODULE_0__.HalfFloatType:\n\n\t\t\t\tnumElements = image_rgba_data.length / 4;\n\t\t\t\tconst halfArray = new Uint16Array( numElements * 4 );\n\n\t\t\t\tfor ( let j = 0; j < numElements; j ++ ) {\n\n\t\t\t\t\tRGBEByteToRGBHalf( image_rgba_data, j * 4, halfArray, j * 4 );\n\n\t\t\t\t}\n\n\t\t\t\tdata = halfArray;\n\t\t\t\ttype = three__WEBPACK_IMPORTED_MODULE_0__.HalfFloatType;\n\t\t\t\tbreak;\n\n\t\t\tdefault:\n\n\t\t\t\tthrow new Error( 'THREE.RGBELoader: Unsupported type: ' + this.type );\n\t\t\t\tbreak;\n\n\t\t}\n\n\t\treturn {\n\t\t\twidth: w, height: h,\n\t\t\tdata: data,\n\t\t\theader: rgbe_header_info.string,\n\t\t\tgamma: rgbe_header_info.gamma,\n\t\t\texposure: rgbe_header_info.exposure,\n\t\t\ttype: type\n\t\t};\n\n\t}\n\n\tsetDataType( value ) {\n\n\t\tthis.type = value;\n\t\treturn this;\n\n\t}\n\n\tload( url, onLoad, onProgress, onError ) {\n\n\t\tfunction onLoadCallback( texture, texData ) {\n\n\t\t\tswitch ( texture.type ) {\n\n\t\t\t\tcase three__WEBPACK_IMPORTED_MODULE_0__.FloatType:\n\t\t\t\tcase three__WEBPACK_IMPORTED_MODULE_0__.HalfFloatType:\n\n\t\t\t\t\ttexture.colorSpace = three__WEBPACK_IMPORTED_MODULE_0__.LinearSRGBColorSpace;\n\t\t\t\t\ttexture.minFilter = three__WEBPACK_IMPORTED_MODULE_0__.LinearFilter;\n\t\t\t\t\ttexture.magFilter = three__WEBPACK_IMPORTED_MODULE_0__.LinearFilter;\n\t\t\t\t\ttexture.generateMipmaps = false;\n\t\t\t\t\ttexture.flipY = true;\n\n\t\t\t\t\tbreak;\n\n\t\t\t}\n\n\t\t\tif ( onLoad ) onLoad( texture, texData );\n\n\t\t}\n\n\t\treturn super.load( url, onLoadCallback, onProgress, onError );\n\n\t}\n\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/three@0.167.1/node_modules/three/examples/jsm/loaders/RGBELoader.js\n"));

/***/ })

});