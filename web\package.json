{"name": "agape-new-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@auth/mongodb-adapter": "^3.4.2", "@aws-sdk/client-s3": "3.300.0", "@aws-sdk/s3-request-presigner": "3.300.0", "@gltf-transform/core": "^4.1.2", "@gltf-transform/extensions": "^4.1.2", "@gltf-transform/functions": "^4.1.2", "@gsap/react": "^2.1.1", "@hookform/resolvers": "^3.10.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.0", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.1", "@react-three/drei": "^9.111.2", "@react-three/fiber": "^8.17.5", "@react-three/postprocessing": "^2.16.3", "@spaceymonk/react-radial-menu": "^2.0.6", "@types/lodash": "^4.17.13", "@types/react-color": "^3.0.12", "@types/uuid": "^10.0.0", "base64-arraybuffer": "^1.0.2", "bull": "^4.16.5", "bullmq": "^5.34.10", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "emoji-picker-react": "^4.11.1", "gsap": "^3.12.5", "ioredis": "^5.4.2", "lodash": "^4.17.21", "lucide-react": "^0.396.0", "moment": "^2.30.1", "mongodb": "^6.8.1", "mongoose": "^8.6.1", "next": "14.2.4", "next-auth": "^4.24.5", "next-themes": "^0.3.0", "next-transition-router": "^0.2.1", "nodemailer": "^6.9.16", "react": "^18", "react-color": "^2.19.3", "react-colorful": "^5.6.1", "react-dom": "^18", "react-draggable": "^4.4.6", "react-dropzone": "^14.2.3", "react-hook-form": "^7.54.2", "react-joyride": "^2.9.3", "react-toastify": "^11.0.5", "sharp": "^0.33.5", "socket.io": "^4.8.0", "socket.io-client": "^4.8.0", "sonner": "^1.5.0", "tailwind-merge": "^2.3.0", "tailwind-scrollbar": "^3.1.0", "tailwindcss-animate": "^1.0.7", "three": "^0.167.1", "three-stdlib": "^2.36.0", "uuid": "^10.0.0", "vaul": "^0.9.1", "zod": "^3.24.1", "zustand": "^4.5.3"}, "devDependencies": {"@tailwindcss/forms": "^0.5.10", "@types/node": "^20", "@types/nodemailer": "^6.4.17", "@types/react": "^18", "@types/react-dom": "^18", "@types/three": "^0.168.0", "eslint": "^8", "eslint-config-next": "14.2.4", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}