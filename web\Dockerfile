
FROM node:20.10.0-alpine


# Set the working directory inside the container
WORKDIR /app

RUN npm install -g pnpm

COPY package.json package.json
COPY pnpm-lock.yaml pnpm-lock.yaml
RUN pnpm install

# Copy all files from the current directory to the /app directory in the container
COPY . /app


# Ignore the node_modules folder


# Run the command to build the application
RUN pnpm run build

EXPOSE 3000

# Run the command to start the application
CMD ["pnpm", "run", "start"]