#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/g/agape/agape-newFrontend/web/node_modules/.pnpm/which@2.0.2/node_modules/which/bin/node_modules:/mnt/g/agape/agape-newFrontend/web/node_modules/.pnpm/which@2.0.2/node_modules/which/node_modules:/mnt/g/agape/agape-newFrontend/web/node_modules/.pnpm/which@2.0.2/node_modules:/mnt/g/agape/agape-newFrontend/web/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/g/agape/agape-newFrontend/web/node_modules/.pnpm/which@2.0.2/node_modules/which/bin/node_modules:/mnt/g/agape/agape-newFrontend/web/node_modules/.pnpm/which@2.0.2/node_modules/which/node_modules:/mnt/g/agape/agape-newFrontend/web/node_modules/.pnpm/which@2.0.2/node_modules:/mnt/g/agape/agape-newFrontend/web/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../which@2.0.2/node_modules/which/bin/node-which" "$@"
else
  exec node  "$basedir/../../../../../which@2.0.2/node_modules/which/bin/node-which" "$@"
fi
