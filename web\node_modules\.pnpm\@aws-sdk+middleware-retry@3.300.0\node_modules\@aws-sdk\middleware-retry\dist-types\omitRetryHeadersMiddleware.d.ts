import { FinalizeHandler, MetadataBearer, Pluggable, RelativeMiddlewareOptions } from "@aws-sdk/types";
export declare const omitRetryHeadersMiddleware: () => <Output extends MetadataBearer = MetadataBearer>(next: FinalizeHandler<any, Output>) => FinalizeHandler<any, Output>;
export declare const omitRetryHeadersMiddlewareOptions: RelativeMiddlewareOptions;
export declare const getOmitRetryHeadersPlugin: (options: unknown) => Pluggable<any, any>;
