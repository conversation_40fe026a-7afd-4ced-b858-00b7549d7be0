"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getSsoSessionData = void 0;
const ssoSessionKeyRegex = /^sso-session\s(["'])?([^\1]+)\1$/;
const getSsoSessionData = (data) => Object.entries(data)
    .filter(([key]) => ssoSessionKeyRegex.test(key))
    .reduce((acc, [key, value]) => ({ ...acc, [ssoSessionKeyRegex.exec(key)[2]]: value }), {});
exports.getSsoSessionData = getSsoSessionData;
