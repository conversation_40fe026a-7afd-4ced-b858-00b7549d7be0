import { Logger } from "@aws-sdk/types";
export declare const DEFAULT_TIMEOUT = 1000;
export declare const DEFAULT_MAX_RETRIES = 0;
export interface RemoteProviderConfig {
  timeout: number;
  maxRetries: number;
}
export interface RemoteProviderInit extends Partial<RemoteProviderConfig> {
  logger?: Logger;
}
export declare const providerConfigFromInit: ({
  maxRetries,
  timeout,
}: RemoteProviderInit) => RemoteProviderConfig;
