"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/projects/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/ArtistTool/EditorExperience.jsx":
/*!********************************************************!*\
  !*** ./src/components/ArtistTool/EditorExperience.jsx ***!
  \********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ EditorExperience; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/web/Html.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/web/Loader.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/OrbitControls.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/TransformControls.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/MeshReflectorMaterial.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/GizmoHelper.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/GizmoViewport.js\");\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @react-three/fiber */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_three@0.167.1/node_modules/@react-three/fiber/dist/events-776716bd.esm.js\");\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @react-three/fiber */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3.1_three@0.167.1/node_modules/@react-three/fiber/dist/react-three-fiber.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/.pnpm/react-toastify@11.0.5_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/react-toastify/dist/index.mjs\");\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! three */ \"(app-pages-browser)/./node_modules/.pnpm/three@0.167.1/node_modules/three/build/three.module.js\");\n/* harmony import */ var _Environment__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Environment */ \"(app-pages-browser)/./src/components/ArtistTool/Environment.tsx\");\n/* harmony import */ var _EnvironmentPanel__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./EnvironmentPanel */ \"(app-pages-browser)/./src/components/ArtistTool/EnvironmentPanel.jsx\");\n/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Header */ \"(app-pages-browser)/./src/components/ArtistTool/Header.jsx\");\n/* harmony import */ var _LightPanel__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./LightPanel */ \"(app-pages-browser)/./src/components/ArtistTool/LightPanel.jsx\");\n/* harmony import */ var _MaterialPanel__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./MaterialPanel */ \"(app-pages-browser)/./src/components/ArtistTool/MaterialPanel.jsx\");\n/* harmony import */ var _ModelStats__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./ModelStats */ \"(app-pages-browser)/./src/components/ArtistTool/ModelStats.jsx\");\n/* harmony import */ var _Outliner__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./Outliner */ \"(app-pages-browser)/./src/components/ArtistTool/Outliner.jsx\");\n/* harmony import */ var _PostProcessingPanel__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./PostProcessingPanel */ \"(app-pages-browser)/./src/components/ArtistTool/PostProcessingPanel.jsx\");\n/* harmony import */ var _SceneLights__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./SceneLights */ \"(app-pages-browser)/./src/components/ArtistTool/SceneLights.jsx\");\n/* harmony import */ var _Toolbar__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./Toolbar */ \"(app-pages-browser)/./src/components/ArtistTool/Toolbar.jsx\");\n/* harmony import */ var _TransformPanel__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./TransformPanel */ \"(app-pages-browser)/./src/components/ArtistTool/TransformPanel.jsx\");\n/* harmony import */ var _utils_ArtistTool_cameraUtils__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../../utils/ArtistTool/cameraUtils */ \"(app-pages-browser)/./src/utils/ArtistTool/cameraUtils.js\");\n/* harmony import */ var _utils_ArtistTool_historyUtils__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../../utils/ArtistTool/historyUtils */ \"(app-pages-browser)/./src/utils/ArtistTool/historyUtils.js\");\n/* harmony import */ var _utils_ArtistTool_materialUtils__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../../utils/ArtistTool/materialUtils */ \"(app-pages-browser)/./src/utils/ArtistTool/materialUtils.js\");\n/* harmony import */ var _utils_ArtistTool_sceneUtils__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ../../utils/ArtistTool/sceneUtils */ \"(app-pages-browser)/./src/utils/ArtistTool/sceneUtils.js\");\n/* harmony import */ var _animations_CameraAnimations__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./animations/CameraAnimations */ \"(app-pages-browser)/./src/components/ArtistTool/animations/CameraAnimations.js\");\n/* harmony import */ var _EngravingPanel__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./EngravingPanel */ \"(app-pages-browser)/./src/components/ArtistTool/EngravingPanel.jsx\");\n/* harmony import */ var _InitialOverlayPanel__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./InitialOverlayPanel */ \"(app-pages-browser)/./src/components/ArtistTool/InitialOverlayPanel.jsx\");\n/* harmony import */ var _ModelStage__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./ModelStage */ \"(app-pages-browser)/./src/components/ArtistTool/ModelStage.jsx\");\n/* harmony import */ var _PostProcessingEffects__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ./PostProcessingEffects */ \"(app-pages-browser)/./src/components/ArtistTool/PostProcessingEffects.jsx\");\n/* harmony import */ var _SelectionOutline__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! ./SelectionOutline */ \"(app-pages-browser)/./src/components/ArtistTool/SelectionOutline.jsx\");\n/* harmony import */ var _lib_actions_version_actions__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/lib/actions/version.actions */ \"(app-pages-browser)/./src/lib/actions/version.actions.ts\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/.pnpm/sonner@1.5.0_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _lib_states__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @/lib/states */ \"(app-pages-browser)/./src/lib/states.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst ScreenshotTaker = (param)=>{\n    let { onScreenshotRef } = param;\n    _s();\n    const { gl, scene, camera } = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_27__.D)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (typeof onScreenshotRef === \"function\") {\n            onScreenshotRef(()=>{\n                try {\n                    // Render the scene\n                    gl.render(scene, camera);\n                    // Get the canvas data URL\n                    const dataURL = gl.domElement.toDataURL(\"image/png\");\n                    // Create and trigger a download\n                    const link = document.createElement(\"a\");\n                    const timestamp = new Date().toISOString().replace(/:/g, \"-\").substring(0, 19);\n                    link.download = \"agape-scene-\".concat(timestamp, \".png\");\n                    link.href = dataURL;\n                    document.body.appendChild(link);\n                    link.click();\n                    // Clean up\n                    setTimeout(()=>{\n                        document.body.removeChild(link);\n                    }, 100);\n                    console.log(\"Screenshot captured successfully\");\n                    return true;\n                } catch (error) {\n                    console.error(\"Error capturing screenshot:\", error);\n                    return false;\n                }\n            });\n        }\n    }, [\n        gl,\n        scene,\n        camera,\n        onScreenshotRef\n    ]);\n    return null;\n};\n_s(ScreenshotTaker, \"RVTFHuczdlyvGFUsZJPSpjurI/Y=\", false, function() {\n    return [\n        _react_three_fiber__WEBPACK_IMPORTED_MODULE_27__.D\n    ];\n});\n_c = ScreenshotTaker;\n// Component to provide renderer to MaterialPanel\nconst MaterialPanelWrapper = (param)=>{\n    let { renderer, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MaterialPanel__WEBPACK_IMPORTED_MODULE_7__.MaterialPanel, {\n        renderer: renderer,\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n        lineNumber: 96,\n        columnNumber: 10\n    }, undefined);\n};\n_c1 = MaterialPanelWrapper;\n// Component to get renderer from Canvas context\nconst RendererProvider = (param)=>{\n    let { children, onRendererReady } = param;\n    _s1();\n    const { gl } = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_27__.D)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (onRendererReady) {\n            onRendererReady(gl);\n        }\n    }, [\n        gl,\n        onRendererReady\n    ]);\n    return null;\n};\n_s1(RendererProvider, \"iTWbOa64ZxlA5nuyNExgWK+vjCI=\", false, function() {\n    return [\n        _react_three_fiber__WEBPACK_IMPORTED_MODULE_27__.D\n    ];\n});\n_c2 = RendererProvider;\nfunction EditorExperience(param) {\n    let { modelUrl, project, user } = param;\n    _s2();\n    const { addVersion } = (0,_lib_states__WEBPACK_IMPORTED_MODULE_26__.useVersionState)();\n    const configRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const workingConfigRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [selectedModel, setSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"uploaded\");\n    const [lastSelectedModel, setLastSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [uploadedModel, setUploadedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(modelUrl || null);\n    const [modelStats, setModelStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        vertices: 0,\n        triangles: 0,\n        materials: 0,\n        animations: 0\n    });\n    const [lights, setLights] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: \"defaultDirectional\",\n            type: \"directional\",\n            position: [\n                2.5,\n                5.5,\n                5\n            ],\n            intensity: 1.0,\n            color: \"#ffffff\",\n            name: \"Main Directional Light\",\n            castShadow: true,\n            helperVisible: false\n        },\n        {\n            id: \"keyLight\",\n            type: \"point\",\n            position: [\n                5,\n                5,\n                5\n            ],\n            intensity: 8.0,\n            color: \"#ffffff\",\n            name: \"Key Light\"\n        },\n        {\n            id: \"fillLight\",\n            type: \"point\",\n            position: [\n                -5,\n                5,\n                -5\n            ],\n            intensity: 4.0,\n            color: \"#ffffff\",\n            name: \"Fill Light\"\n        },\n        {\n            id: \"backLight\",\n            type: \"point\",\n            position: [\n                0,\n                5,\n                -5\n            ],\n            intensity: 2.0,\n            color: \"#ffffff\",\n            name: \"Back Light\"\n        }\n    ]);\n    const lightRefs = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({});\n    const [selectedObjects, setSelectedObjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedLight, setSelectedLight] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [lightPositionHistory, setLightPositionHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [sceneObjects, setSceneObjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [transformMode, setTransformMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"translate\");\n    const selectionGroupRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(new three__WEBPACK_IMPORTED_MODULE_28__.Group());\n    console.log(\"renderingggg editor experience\");\n    const [rightPanelTab, setRightPanelTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Decal placement state\n    const [isPlacingDecal, setIsPlacingDecal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showDecalDebug, setShowDecalDebug] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Scene controls state\n    const [showGrid, setShowGrid] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [wireframe, setWireframe] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [groundType, setGroundType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"none\");\n    const [envPreset, setEnvPreset] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"hdri_15\");\n    const [bgColor, setBgColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"#272631\");\n    const [showEnvironment, setShowEnvironment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showLightSpheres, setShowLightSpheres] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [customHdri, setCustomHdri] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [envIntensity, setEnvIntensity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1.85);\n    const [envBlur, setEnvBlur] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0.35);\n    const [envRotation, setEnvRotation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [postProcessingEnabled, setPostProcessingEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [postProcessingSettings, setPostProcessingSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        autofocus: {\n            enabled: false,\n            bokehScale: 18\n        },\n        bloom: {\n            enabled: true,\n            intensity: 0.22,\n            threshold: 1,\n            radius: 0.5\n        },\n        dof: {\n            enabled: false,\n            focusDistance: 0,\n            aperture: 0.01,\n            bokehScale: 3\n        },\n        vignette: {\n            enabled: true,\n            darkness: 0.5,\n            offset: 0.1\n        }\n    });\n    // Store initial state\n    const initialStateRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        selectedModel: null,\n        lights: [],\n        envPreset: null,\n        bgColor: null,\n        postProcessingSettings: null,\n        showGrid: null,\n        wireframe: null,\n        groundType: null,\n        showEnvironment: null,\n        showLightSpheres: null,\n        envIntensity: null,\n        envBlur: null,\n        cameraPosition: null,\n        cameraTarget: null\n    });\n    // Add a ref to store the initial state of model objects\n    const modelInitialStateRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        objects: []\n    });\n    // Capture initial state when component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        initialStateRef.current = {\n            selectedModel,\n            lights: [\n                ...lights\n            ],\n            envPreset,\n            bgColor,\n            postProcessingSettings: {\n                ...postProcessingSettings\n            },\n            showGrid,\n            wireframe,\n            groundType,\n            showEnvironment,\n            showLightSpheres,\n            envIntensity,\n            envBlur,\n            cameraPosition: new three__WEBPACK_IMPORTED_MODULE_28__.Vector3(5, 2, 5),\n            cameraTarget: new three__WEBPACK_IMPORTED_MODULE_28__.Vector3(0, 1, 0)\n        };\n    }, []);\n    const orbitControlsRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const sceneRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const [renderer, setRenderer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // History state for undo/redo\n    const [history, setHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [historyIndex, setHistoryIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(-1);\n    const [showModelStats, setShowModelStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const isUndoRedoActionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const skipHistoryTrackingRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const isUndoRedoInProgressRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const [transformVersion, setTransformVersion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [materialVersion, setMaterialVersion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const lastStateRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const pendingSelectedObjectsUUIDs = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Add a new state to the history\n    function getFullConfig() {\n        return JSON.parse(JSON.stringify({\n            selectedModel,\n            uploadedModel,\n            lights,\n            envPreset,\n            bgColor,\n            showEnvironment,\n            showLightSpheres,\n            customHdri,\n            envIntensity,\n            envBlur,\n            envRotation,\n            postProcessingEnabled,\n            postProcessingSettings,\n            showGrid,\n            wireframe,\n            groundType,\n            selectedObjects: selectedObjects.map((obj)=>obj.uuid),\n            // Store object states for transforms and materials\n            objectStates: sceneObjects.map((obj)=>({\n                    uuid: obj.uuid,\n                    name: obj.name,\n                    position: obj.position.toArray(),\n                    rotation: obj.rotation.toArray(),\n                    scale: obj.scale.toArray(),\n                    material: obj.material ? {\n                        type: obj.material.type,\n                        color: obj.material.color ? obj.material.color.getHexString() : \"ffffff\",\n                        roughness: obj.material.roughness,\n                        metalness: obj.material.metalness,\n                        clearcoat: obj.material.clearcoat,\n                        clearcoatRoughness: obj.material.clearcoatRoughness,\n                        wireframe: obj.material.wireframe,\n                        transparent: obj.material.transparent,\n                        opacity: obj.material.opacity,\n                        envMapIntensity: obj.material.envMapIntensity\n                    } : null\n                }))\n        }));\n    }\n    function restoreConfig(config) {\n        setSelectedModel(config.selectedModel);\n        setUploadedModel(config.uploadedModel);\n        setLights(config.lights);\n        setEnvPreset(config.envPreset);\n        setBgColor(config.bgColor);\n        setShowEnvironment(config.showEnvironment);\n        setShowLightSpheres(config.showLightSpheres);\n        setCustomHdri(config.customHdri);\n        setEnvIntensity(config.envIntensity);\n        setEnvBlur(config.envBlur);\n        setEnvRotation(config.envRotation);\n        setPostProcessingEnabled(config.postProcessingEnabled);\n        setPostProcessingSettings(config.postProcessingSettings);\n        setShowGrid(config.showGrid);\n        setWireframe(config.wireframe);\n        setGroundType(config.groundType);\n        // Restore object states (transforms and materials)\n        if (config.objectStates && sceneObjects.length > 0) {\n            config.objectStates.forEach((savedState)=>{\n                const obj = sceneObjects.find((o)=>o.uuid === savedState.uuid);\n                if (obj) {\n                    // Restore transform\n                    obj.position.fromArray(savedState.position);\n                    obj.rotation.fromArray(savedState.rotation);\n                    obj.scale.fromArray(savedState.scale);\n                    // Restore material properties\n                    if (savedState.material && obj.material) {\n                        const material = obj.material;\n                        if (material.color) {\n                            material.color.set(savedState.material.color);\n                        }\n                        if (savedState.material.roughness !== undefined) {\n                            material.roughness = savedState.material.roughness;\n                        }\n                        if (savedState.material.metalness !== undefined) {\n                            material.metalness = savedState.material.metalness;\n                        }\n                        if (savedState.material.clearcoat !== undefined) {\n                            material.clearcoat = savedState.material.clearcoat;\n                        }\n                        if (savedState.material.clearcoatRoughness !== undefined) {\n                            material.clearcoatRoughness = savedState.material.clearcoatRoughness;\n                        }\n                        if (savedState.material.wireframe !== undefined) {\n                            material.wireframe = savedState.material.wireframe;\n                        }\n                        if (savedState.material.transparent !== undefined) {\n                            material.transparent = savedState.material.transparent;\n                        }\n                        if (savedState.material.opacity !== undefined) {\n                            material.opacity = savedState.material.opacity;\n                        }\n                        if (savedState.material.envMapIntensity !== undefined) {\n                            material.envMapIntensity = savedState.material.envMapIntensity;\n                        }\n                        material.needsUpdate = true;\n                    }\n                }\n            });\n            // Force update to reflect changes\n            setTransformVersion((v)=>v + 1);\n            setMaterialVersion((v)=>v + 1);\n        }\n        // Store selectedObjects UUIDs to be restored after sceneObjects update\n        pendingSelectedObjectsUUIDs.current = config.selectedObjects || [];\n        console.log(\"[restoreConfig] Restoring config\", config);\n    }\n    // After sceneObjects update, restore selectedObjects from UUIDs\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (pendingSelectedObjectsUUIDs.current && sceneObjects.length > 0) {\n            const uuids = pendingSelectedObjectsUUIDs.current;\n            const newSelected = sceneObjects.filter((obj)=>uuids.includes(obj.uuid));\n            setSelectedObjects(newSelected);\n            pendingSelectedObjectsUUIDs.current = null;\n            console.log(\"[restoreConfig] Restored selectedObjects\", newSelected);\n        }\n    }, [\n        sceneObjects\n    ]);\n    // Undo action\n    const undoAction = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (isUndoRedoInProgressRef.current) {\n            console.log(\"[undoAction] Skipping - undo/redo already in progress\");\n            return;\n        }\n        console.log(\"[undoAction] Current historyIndex:\", historyIndex, \"History length:\", history.length);\n        if (historyIndex > 0) {\n            console.log(\"[undoAction] Performing undo to index:\", historyIndex - 1);\n            isUndoRedoInProgressRef.current = true;\n            isUndoRedoActionRef.current = true;\n            skipHistoryTrackingRef.current = true;\n            const prevConfig = history[historyIndex - 1];\n            restoreConfig(prevConfig);\n            setHistoryIndex(historyIndex - 1);\n            setTimeout(()=>{\n                isUndoRedoActionRef.current = false;\n                skipHistoryTrackingRef.current = false;\n                isUndoRedoInProgressRef.current = false;\n                console.log(\"[undoAction] Undo completed, all flags reset\");\n            }, 1000);\n        } else {\n            console.log(\"[undoAction] Cannot undo - at beginning of history\");\n        }\n    }, [\n        historyIndex,\n        history,\n        restoreConfig\n    ]);\n    // Redo action\n    const redoAction = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (isUndoRedoInProgressRef.current) {\n            console.log(\"[redoAction] Skipping - undo/redo already in progress\");\n            return;\n        }\n        console.log(\"[redoAction] Current historyIndex:\", historyIndex, \"History length:\", history.length);\n        if (historyIndex < history.length - 1) {\n            console.log(\"[redoAction] Performing redo to index:\", historyIndex + 1);\n            isUndoRedoInProgressRef.current = true;\n            isUndoRedoActionRef.current = true;\n            skipHistoryTrackingRef.current = true;\n            const nextConfig = history[historyIndex + 1];\n            restoreConfig(nextConfig);\n            setHistoryIndex(historyIndex + 1);\n            setTimeout(()=>{\n                isUndoRedoActionRef.current = false;\n                skipHistoryTrackingRef.current = false;\n                isUndoRedoInProgressRef.current = false;\n                console.log(\"[redoAction] Redo completed, all flags reset\");\n            }, 1000);\n        } else {\n            console.log(\"[redoAction] Cannot redo - at end of history\");\n        }\n    }, [\n        historyIndex,\n        history,\n        restoreConfig\n    ]);\n    // Track changes to objects and lights for history (debounced)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handler = setTimeout(()=>{\n            if (!isUndoRedoActionRef.current && !skipHistoryTrackingRef.current) {\n                const currentState = JSON.stringify({\n                    selectedModel,\n                    envPreset,\n                    bgColor,\n                    showEnvironment,\n                    showLightSpheres,\n                    envIntensity,\n                    envBlur,\n                    envRotation,\n                    postProcessingEnabled,\n                    showGrid,\n                    wireframe,\n                    groundType,\n                    transformVersion,\n                    materialVersion,\n                    lightsLength: lights.length\n                });\n                if (lastStateRef.current !== currentState) {\n                    lastStateRef.current = currentState;\n                    console.log(\"[useEffect] State changed, adding to history\");\n                // addToHistory(); // This line was removed from the new_code, so it's removed here.\n                } else {\n                    console.log(\"[useEffect] State unchanged, skipping history\");\n                }\n            } else {\n                console.log(\"[useEffect] Skipping - undo/redo in progress\");\n            }\n        }, 300);\n        return ()=>clearTimeout(handler);\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        selectedModel,\n        envPreset,\n        bgColor,\n        showEnvironment,\n        showLightSpheres,\n        envIntensity,\n        envBlur,\n        envRotation,\n        postProcessingEnabled,\n        showGrid,\n        wireframe,\n        groundType,\n        transformVersion,\n        materialVersion,\n        lights.length\n    ]);\n    const handleObjectClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e)=>{\n        e.stopPropagation();\n        // If placing decal, let the decal placement handler do its job (triggered by model's onClick)\n        if (isPlacingDecal) {\n            return;\n        }\n        console.log(\"Object clicked:\", e.object.name, e.object);\n        if (e.shiftKey) {\n            setSelectedObjects((prev)=>{\n                // Ensure prev is always an array\n                const prevArray = Array.isArray(prev) ? prev : [\n                    prev\n                ].filter(Boolean);\n                if (prevArray.includes(e.object)) {\n                    return prevArray.filter((obj)=>obj !== e.object);\n                }\n                return [\n                    ...prevArray,\n                    e.object\n                ];\n            });\n        } else {\n            setSelectedObjects([\n                e.object\n            ]);\n        }\n    }, [\n        isPlacingDecal,\n        setSelectedObjects\n    ]);\n    // Add debugging for selectedObjects changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"ExperienceNew - selectedObjects changed:\", selectedObjects);\n        console.log(\"ExperienceNew - selectedObjects length:\", selectedObjects === null || selectedObjects === void 0 ? void 0 : selectedObjects.length);\n    }, [\n        selectedObjects\n    ]);\n    // Add debugging for sceneObjects changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"ExperienceNew - sceneObjects changed:\", sceneObjects);\n        console.log(\"ExperienceNew - sceneObjects length:\", sceneObjects === null || sceneObjects === void 0 ? void 0 : sceneObjects.length);\n        console.log(\"ExperienceNew - sceneObjects details:\", sceneObjects === null || sceneObjects === void 0 ? void 0 : sceneObjects.map((obj)=>({\n                name: obj === null || obj === void 0 ? void 0 : obj.name,\n                type: obj === null || obj === void 0 ? void 0 : obj.type,\n                userData: obj === null || obj === void 0 ? void 0 : obj.userData\n            })));\n    }, [\n        sceneObjects\n    ]);\n    const handlePointerMissed = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        // If in decal placement mode, a \"missed\" click (on the canvas background)\n        // should probably cancel placement mode, or do nothing to allow model click-through.\n        // For now, let's prevent it from deselecting objects if placing decal.\n        if (isPlacingDecal) {\n            // setIsPlacingDecal(false); // Optionally, cancel placement on missed click\n            return;\n        }\n        setSelectedObjects([]);\n        setSelectedLight(null);\n    // Do not close the rightPanelTab here, as that's usually handled by clicking outside the panel itself.\n    }, [\n        isPlacingDecal,\n        setIsPlacingDecal\n    ]);\n    // Add the selection group to the scene\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // This effect runs once to add the selection group to the scene\n        return ()=>{\n            // Clean up on unmount\n            if (selectionGroupRef.current && selectionGroupRef.current.parent) {\n                selectionGroupRef.current.parent.remove(selectionGroupRef.current);\n            }\n        };\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Ensure selectedObjects is always an array\n        const objectsArray = Array.isArray(selectedObjects) ? selectedObjects : [\n            selectedObjects\n        ].filter(Boolean);\n        while(selectionGroupRef.current.children.length > 0){\n            selectionGroupRef.current.remove(selectionGroupRef.current.children[0]);\n        }\n        if (objectsArray.length > 0) {\n            const center = new three__WEBPACK_IMPORTED_MODULE_28__.Vector3();\n            objectsArray.forEach((obj)=>{\n                center.add(obj.position);\n            });\n            center.divideScalar(objectsArray.length);\n            selectionGroupRef.current.position.copy(center);\n            objectsArray.forEach((obj)=>{\n                const clone = obj.clone();\n                // Make the clone invisible while keeping its geometry for transforms\n                clone.traverse((child)=>{\n                    if (child.isMesh) {\n                        child.visible = false;\n                    }\n                });\n                clone.position.sub(center);\n                selectionGroupRef.current.add(clone);\n            });\n        }\n    }, [\n        selectedObjects\n    ]);\n    const updateObjectsFromGroup = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        // Ensure selectedObjects is always an array\n        const objectsArray = Array.isArray(selectedObjects) ? selectedObjects : [\n            selectedObjects\n        ].filter(Boolean);\n        if (objectsArray.length > 0 && selectionGroupRef.current) {\n            objectsArray.forEach((obj, index)=>{\n                if (selectionGroupRef.current.children[index]) {\n                    const worldPos = new three__WEBPACK_IMPORTED_MODULE_28__.Vector3();\n                    selectionGroupRef.current.children[index].getWorldPosition(worldPos);\n                    obj.position.copy(worldPos);\n                    obj.rotation.copy(selectionGroupRef.current.rotation);\n                    obj.scale.copy(selectionGroupRef.current.scale);\n                }\n            });\n        }\n    }, [\n        selectedObjects\n    ]);\n    const handleSceneUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((scene)=>{\n        var _scene_children;\n        console.log(\"handleSceneUpdate called with scene:\", scene);\n        console.log(\"Scene type:\", scene.type);\n        console.log(\"Scene children count:\", (_scene_children = scene.children) === null || _scene_children === void 0 ? void 0 : _scene_children.length);\n        setSceneObjects([]);\n        setSelectedObjects([]);\n        const stats = (0,_utils_ArtistTool_sceneUtils__WEBPACK_IMPORTED_MODULE_17__.calculateModelStats)(scene);\n        setModelStats(stats);\n        (0,_utils_ArtistTool_sceneUtils__WEBPACK_IMPORTED_MODULE_17__.setShadowsOnModel)(scene);\n        const objects = [];\n        const initialObjects = [];\n        scene.traverse((object)=>{\n            console.log(\"Traversing object:\", object.name, object.type, \"isMesh:\", object.isMesh, \"userData:\", object.userData);\n            if (object.isMesh) {\n                if (!object.name) {\n                    object.name = \"Mesh_\".concat(object.id);\n                }\n                console.log(\"Adding mesh to objects:\", object.name, \"userData:\", object.userData);\n                objects.push(object);\n                initialObjects.push({\n                    id: object.uuid,\n                    name: object.name,\n                    position: object.position.clone(),\n                    rotation: object.rotation.clone(),\n                    scale: object.scale.clone(),\n                    material: object.material ? {\n                        color: object.material.color ? object.material.color.getHex() : 0xffffff,\n                        roughness: object.material.roughness,\n                        metalness: object.material.metalness,\n                        wireframe: object.material.wireframe,\n                        transparent: object.material.transparent,\n                        opacity: object.material.opacity\n                    } : null\n                });\n            }\n        });\n        console.log(\"Found\", objects.length, \"mesh objects:\", objects.map((obj)=>({\n                name: obj.name,\n                userData: obj.userData\n            })));\n        modelInitialStateRef.current.objects = initialObjects;\n        setSceneObjects(objects);\n    }, []);\n    // Add this effect to detect model changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedModel !== lastSelectedModel) {\n            // Clear the scene objects when model changes\n            setSceneObjects([]);\n            setSelectedObjects([]);\n            setLastSelectedModel(selectedModel);\n        }\n    }, [\n        selectedModel,\n        lastSelectedModel\n    ]);\n    const handleLightMoved = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((lightId, newPosition)=>{\n        setLightPositionHistory((prev)=>({\n                ...prev,\n                [lightId]: [\n                    ...prev[lightId] || [],\n                    newPosition\n                ]\n            }));\n    }, []);\n    const undoLightMove = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((lightId)=>{\n        setLightPositionHistory((prev)=>{\n            const history = prev[lightId] || [];\n            if (history.length <= 1) return prev;\n            const newHistory = history.slice(0, -1);\n            const lastPosition = newHistory[newHistory.length - 1];\n            if (lightRefs.current[lightId]) {\n                lightRefs.current[lightId].position.copy(lastPosition);\n            }\n            return {\n                ...prev,\n                [lightId]: newHistory\n            };\n        });\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleKeyDown = (event)=>{\n            // Handle undo/redo keyboard shortcuts\n            if (event.ctrlKey) {\n                if (event.key === \"z\") {\n                    event.preventDefault();\n                    undoAction();\n                    return;\n                }\n                if (event.key === \"y\") {\n                    event.preventDefault();\n                    redoAction();\n                    return;\n                }\n            }\n            // Handle Escape key for decal placement\n            if (event.key === \"Escape\" && isPlacingDecal) {\n                setIsPlacingDecal(false);\n                return;\n            }\n            // Handle light undo\n            if (event.ctrlKey && event.key === \"z\" && selectedLight) {\n                undoLightMove(selectedLight);\n            }\n            // Handle transform mode shortcuts\n            if (selectedObjects.length > 0) {\n                switch(event.key){\n                    case \"g\":\n                        setTransformMode(\"translate\");\n                        break;\n                    case \"r\":\n                        setTransformMode(\"rotate\");\n                        break;\n                    case \"s\":\n                        setTransformMode(\"scale\");\n                        break;\n                    case \"Escape\":\n                        setSelectedObjects([]);\n                        break;\n                }\n            }\n            // Handle select all\n            if (event.ctrlKey && event.key === \"a\") {\n                event.preventDefault();\n                if (sceneObjects.length > 0) {\n                    setSelectedObjects([\n                        ...sceneObjects\n                    ]);\n                }\n            }\n        };\n        window.addEventListener(\"keydown\", handleKeyDown);\n        return ()=>window.removeEventListener(\"keydown\", handleKeyDown);\n    }, [\n        selectedLight,\n        undoLightMove,\n        sceneObjects,\n        selectedObjects.length,\n        undoAction,\n        redoAction,\n        isPlacingDecal,\n        setIsPlacingDecal\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        return ()=>{\n            if (uploadedModel) {\n                URL.revokeObjectURL(uploadedModel);\n            }\n            if (customHdri) {\n                URL.revokeObjectURL(customHdri);\n            }\n        };\n    }, [\n        uploadedModel,\n        customHdri\n    ]);\n    const handleCameraView = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((view)=>{\n        (0,_utils_ArtistTool_cameraUtils__WEBPACK_IMPORTED_MODULE_14__.setCameraView)(orbitControlsRef.current, view);\n    }, []);\n    // Add the panelRef to ExperienceNew instead of in Toolbar\n    const panelRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [activeAnimation, setActiveAnimation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [animationStartTime, setAnimationStartTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const requestRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    // Animation loop handler\n    const animateCamera = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((timestamp)=>{\n        if (!activeAnimation || !orbitControlsRef.current) {\n            return;\n        }\n        // Calculate animation progress (0 to 1)\n        const animation = _animations_CameraAnimations__WEBPACK_IMPORTED_MODULE_18__.predefinedAnimations.find((a)=>a.id === activeAnimation);\n        if (!animation) return;\n        if (animationStartTime === 0) {\n            setAnimationStartTime(timestamp);\n        }\n        const elapsed = timestamp - animationStartTime;\n        const progress = elapsed % animation.duration / animation.duration;\n        // Get the target position (center of selected objects or scene center)\n        const target = new three__WEBPACK_IMPORTED_MODULE_28__.Vector3(0, 0, 0);\n        if (selectedObjects.length > 0) {\n            // Calculate center of selected objects\n            const objectsArray = Array.isArray(selectedObjects) ? selectedObjects : [\n                selectedObjects\n            ].filter(Boolean);\n            if (objectsArray.length > 0) {\n                const center = new three__WEBPACK_IMPORTED_MODULE_28__.Vector3();\n                objectsArray.forEach((obj)=>{\n                    center.add(obj.position);\n                });\n                center.divideScalar(objectsArray.length);\n                target.copy(center);\n            }\n        }\n        // Animate camera using the animation function\n        animation.animate(orbitControlsRef.current.object, orbitControlsRef.current, progress, target);\n        requestRef.current = requestAnimationFrame(animateCamera);\n    }, [\n        activeAnimation,\n        orbitControlsRef,\n        animationStartTime,\n        selectedObjects\n    ]);\n    // Setup animation loop\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (activeAnimation) {\n            setAnimationStartTime(0);\n            requestRef.current = requestAnimationFrame(animateCamera);\n            // Disable orbit controls during animation\n            if (orbitControlsRef.current) {\n                orbitControlsRef.current.enabled = false;\n            }\n        } else {\n            // Re-enable orbit controls when animation stops\n            if (orbitControlsRef.current && selectedObjects.length === 0) {\n                orbitControlsRef.current.enabled = true;\n            }\n        }\n        return ()=>{\n            if (requestRef.current) {\n                cancelAnimationFrame(requestRef.current);\n            }\n        };\n    }, [\n        activeAnimation,\n        animateCamera,\n        selectedObjects\n    ]);\n    // Handle animation playback from Outliner\n    const handlePlayAnimation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((animationId)=>{\n        setActiveAnimation(animationId);\n    }, []);\n    const getEnvironmentPreset = ()=>{\n        return envPreset;\n    };\n    // Reset function\n    const handleReset = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        // Check if there's a saved scene in localStorage\n        const savedScene = localStorage.getItem(\"agape_saved_scene\");\n        if (savedScene) {\n            const confirmReset = window.confirm(\"There is a saved scene in your browser storage. Resetting will clear this saved scene. Do you want to continue?\");\n            if (!confirmReset) {\n                return;\n            }\n            // Clear the saved scene from localStorage\n            localStorage.removeItem(\"agape_saved_scene\");\n        }\n        console.log(\"Resetting scene to initial state\");\n        setSceneObjects([]);\n        setSelectedObjects([]);\n        // Reset lights to default configuration\n        setLights([\n            {\n                id: \"defaultDirectional\",\n                type: \"directional\",\n                position: [\n                    2.5,\n                    5.5,\n                    5\n                ],\n                intensity: 1.0,\n                color: \"#ffffff\",\n                name: \"Main Directional Light\",\n                castShadow: true,\n                helperVisible: false\n            },\n            {\n                id: \"keyLight\",\n                type: \"point\",\n                position: [\n                    5,\n                    5,\n                    5\n                ],\n                intensity: 8.0,\n                color: \"#ffffff\",\n                name: \"Key Light\"\n            },\n            {\n                id: \"fillLight\",\n                type: \"point\",\n                position: [\n                    -5,\n                    5,\n                    -5\n                ],\n                intensity: 4.0,\n                color: \"#ffffff\",\n                name: \"Fill Light\"\n            },\n            {\n                id: \"backLight\",\n                type: \"point\",\n                position: [\n                    0,\n                    5,\n                    -5\n                ],\n                intensity: 2.0,\n                color: \"#ffffff\",\n                name: \"Back Light\"\n            }\n        ]);\n        // Reset environment settings\n        setEnvPreset(\"hdri_15\");\n        setBgColor(\"#202020\");\n        setShowEnvironment(false);\n        setShowLightSpheres(true);\n        setCustomHdri(null);\n        setEnvIntensity(1.5);\n        setEnvBlur(0.35);\n        setEnvRotation(0);\n        // Reset post-processing settings\n        setPostProcessingEnabled(false);\n        setPostProcessingSettings({\n            autofocus: {\n                enabled: false,\n                bokehScale: 18\n            },\n            bloom: {\n                enabled: true,\n                intensity: 0.12,\n                threshold: 0.8,\n                radius: 0.4\n            },\n            dof: {\n                enabled: false,\n                focusDistance: 0,\n                aperture: 0.01,\n                bokehScale: 3\n            },\n            vignette: {\n                enabled: true,\n                darkness: 0.5,\n                offset: 0.1\n            }\n        });\n        // Reset scene controls\n        setShowGrid(true);\n        setWireframe(false);\n        setGroundType(\"none\");\n        // Reset camera position\n        if (orbitControlsRef.current) {\n            orbitControlsRef.current.object.position.set(5, 2, 5);\n            orbitControlsRef.current.target.set(0, 1, 0);\n            orbitControlsRef.current.update();\n        }\n        // Reset transform state\n        setTransformMode(\"translate\");\n        setSelectedLight(null);\n        // Reset material shaders\n        setUseDiamondShader(false);\n        setUsePremiumWhiteGold(false);\n        setUsePremiumRoseGold(false);\n        // Clear history\n        setHistory([]);\n        setHistoryIndex(-1);\n        setLightPositionHistory({});\n        // Reset model stats\n        setModelStats({\n            vertices: 0,\n            triangles: 0,\n            materials: 0,\n            animations: 0\n        });\n        // Reset right panel tab\n        setRightPanelTab(null);\n        // Reset active animation\n        setActiveAnimation(null);\n        setAnimationStartTime(0);\n        if (requestRef.current) {\n            cancelAnimationFrame(requestRef.current);\n        }\n        // Clear any custom HDRI or uploaded model URLs\n        if (uploadedModel) {\n            URL.revokeObjectURL(uploadedModel);\n        }\n        if (customHdri) {\n            URL.revokeObjectURL(customHdri);\n        }\n        // Force a model reload by setting selectedModel to null first\n        setSelectedModel(null);\n        // Then set it back to uploaded after a small delay\n        setTimeout(()=>{\n            setSelectedModel(\"uploaded\");\n        }, 100);\n    }, []);\n    const screenshotRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const captureScreenshot = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (screenshotRef.current) {\n            console.log(\"Taking screenshot\");\n            screenshotRef.current();\n        } else {\n            console.error(\"Screenshot function not available yet\");\n        }\n    }, []);\n    // Load saved scene from localStorage on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadSavedScene = ()=>{\n            const savedScene = localStorage.getItem(\"agape_saved_scene\");\n            if (savedScene) {\n                try {\n                    const sceneConfig = JSON.parse(savedScene);\n                    console.log(\"Loading saved scene:\", sceneConfig);\n                    // Only load the saved configuration on initial load\n                    if (!lastSelectedModel) {\n                        console.log(\"Initial load - setting saved model:\", sceneConfig.model);\n                        setSelectedModel(\"uploaded\"); // Always use uploaded for S3 models\n                        // Load the rest of the saved scene configuration\n                        setLights(sceneConfig.lights);\n                        setEnvPreset(sceneConfig.environment.preset);\n                        setBgColor(sceneConfig.environment.bgColor);\n                        setShowEnvironment(sceneConfig.environment.showEnvironment);\n                        setShowLightSpheres(sceneConfig.environment.showLightSpheres);\n                        setEnvIntensity(sceneConfig.environment.intensity);\n                        setEnvBlur(sceneConfig.environment.blur);\n                        setEnvRotation(sceneConfig.environment.rotation);\n                        setPostProcessingEnabled(sceneConfig.postProcessing.enabled);\n                        setPostProcessingSettings(sceneConfig.postProcessing.settings);\n                        setShowGrid(sceneConfig.sceneControls.showGrid);\n                        setWireframe(sceneConfig.sceneControls.wireframe);\n                        setGroundType(sceneConfig.sceneControls.groundType);\n                        // Restore camera position after a short delay to ensure scene is loaded\n                        setTimeout(()=>{\n                            if (orbitControlsRef.current && sceneConfig.camera) {\n                                orbitControlsRef.current.object.position.fromArray(sceneConfig.camera.position);\n                                orbitControlsRef.current.target.fromArray(sceneConfig.camera.target);\n                                orbitControlsRef.current.update();\n                            }\n                        }, 1000);\n                    } else if (configRef.current) {\n                        // For explicit loads with configRef, do a complete load\n                        console.log(\"Explicit load with configRef\");\n                        handleLoadScene(sceneConfig);\n                    }\n                    // Create a function to restore materials\n                    const restoreMaterials = ()=>{\n                        if (!sceneConfig.materials || !sceneObjects.length) {\n                            console.log(\"No materials to restore or scene objects not ready\");\n                            return;\n                        }\n                        console.log(\"Attempting to restore materials for objects:\", sceneObjects);\n                        console.log(\"Saved materials:\", sceneConfig.materials);\n                        const savedMaterialsMap = new Map(sceneConfig.materials.map((m)=>[\n                                m.name,\n                                m.material\n                            ]));\n                        sceneObjects.forEach((obj)=>{\n                            if (obj.material && savedMaterialsMap.has(obj.name)) {\n                                const savedMaterial = savedMaterialsMap.get(obj.name);\n                                console.log(\"Restoring material for object:\", obj.name, savedMaterial);\n                                (0,_utils_ArtistTool_materialUtils__WEBPACK_IMPORTED_MODULE_16__.applyMaterialToObject)(obj, savedMaterial);\n                            } else {\n                                console.log(\"Could not find saved material for object:\", obj.name);\n                            }\n                        });\n                    };\n                    // Set up an interval to check for scene objects\n                    const checkInterval = setInterval(()=>{\n                        if (sceneObjects.length > 0) {\n                            clearInterval(checkInterval);\n                            // Add a small delay to ensure all materials are properly initialized\n                            setTimeout(restoreMaterials, 500);\n                        }\n                    }, 100);\n                    // Clean up interval after 5 seconds if scene objects never become available\n                    setTimeout(()=>{\n                        clearInterval(checkInterval);\n                    }, 5000);\n                    // Restore camera position after a short delay to ensure scene is loaded\n                    setTimeout(()=>{\n                        if (orbitControlsRef.current && sceneConfig.camera) {\n                            orbitControlsRef.current.object.position.fromArray(sceneConfig.camera.position);\n                            orbitControlsRef.current.target.fromArray(sceneConfig.camera.target);\n                            orbitControlsRef.current.update();\n                        }\n                    }, 1000);\n                } catch (error) {\n                    console.error(\"Error loading saved scene:\", error);\n                }\n            }\n        };\n        // Load saved scene immediately on mount\n        loadSavedScene();\n        // Also set up an interval to check for scene readiness\n        const checkSceneReady = setInterval(()=>{\n            if (sceneRef.current && orbitControlsRef.current) {\n                clearInterval(checkSceneReady);\n                loadSavedScene();\n            }\n        }, 100);\n        return ()=>clearInterval(checkSceneReady);\n    }, [\n        sceneObjects,\n        selectedModel\n    ]);\n    const [configState, setConfigState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (sceneObjects.length > 0 && configState && configState.materials) {\n            const applyMaterials = ()=>{\n                console.log(\"Applying materials to scene objects:\", sceneObjects);\n                console.log(\"Saved materials configuration:\", configState.materials);\n                sceneObjects.forEach((obj)=>{\n                    const materialConfig = configState.materials.find((m)=>m.name === obj.name || m.id === obj.uuid);\n                    if (materialConfig && materialConfig.material) {\n                        console.log(\"Found material config for object:\", obj.name, materialConfig.material);\n                        (0,_utils_ArtistTool_materialUtils__WEBPACK_IMPORTED_MODULE_16__.applyMaterialToObject)(obj, materialConfig.material);\n                    } else {\n                        console.log(\"No material config found for object:\", obj.name);\n                    }\n                });\n            };\n            // Add a small delay to ensure all materials are properly initialized\n            const timeoutId = setTimeout(applyMaterials, 500);\n            return ()=>clearTimeout(timeoutId);\n        }\n    }, [\n        sceneObjects,\n        configState\n    ]);\n    const handleLoadScene = (config)=>{\n        try {\n            console.log(\"Loading scene configuration:\", config);\n            // Store the config in the ref\n            configRef.current = config;\n            workingConfigRef.current = config; // Always keep this in sync with last applied config\n            setConfigState(config); // <-- keep in sync for effect\n            // Clear scene objects and selected objects first\n            setSceneObjects([]);\n            setSelectedObjects([]);\n            setLastSelectedModel(null);\n            // Restore scene settings\n            setSelectedModel(\"uploaded\");\n            setLights(config.lights);\n            setEnvPreset(config.environment.preset);\n            setBgColor(config.environment.bgColor);\n            setShowEnvironment(config.environment.showEnvironment);\n            setShowLightSpheres(config.environment.showLightSpheres);\n            setEnvIntensity(config.environment.intensity);\n            setEnvBlur(config.environment.blur);\n            setEnvRotation(config.environment.rotation);\n            setPostProcessingEnabled(config.postProcessing.enabled);\n            setPostProcessingSettings(config.postProcessing.settings);\n            setShowGrid(config.sceneControls.showGrid);\n            setWireframe(config.sceneControls.wireframe);\n            setGroundType(config.sceneControls.groundType);\n            // Wait for the scene to update before applying camera position\n            setTimeout(()=>{\n                if (orbitControlsRef.current && config.camera) {\n                    orbitControlsRef.current.object.position.fromArray(config.camera.position);\n                    orbitControlsRef.current.target.fromArray(config.camera.target);\n                    orbitControlsRef.current.update();\n                }\n            }, 1000);\n            // Restore materials after sceneObjects are ready\n            const restoreMaterials = ()=>{\n                if (!config.materials || !sceneObjects.length) {\n                    console.log(\"No materials to restore or scene objects not ready\");\n                    return;\n                }\n                console.log(\"Restoring materials for objects:\", sceneObjects);\n                const savedMaterialsMap = new Map(config.materials.map((m)=>[\n                        m.name,\n                        m.material\n                    ]));\n                sceneObjects.forEach((obj)=>{\n                    if (obj.material && savedMaterialsMap.has(obj.name)) {\n                        const savedMaterial = savedMaterialsMap.get(obj.name);\n                        console.log(\"Restoring material for object:\", obj.name, savedMaterial);\n                        (0,_utils_ArtistTool_materialUtils__WEBPACK_IMPORTED_MODULE_16__.applyMaterialToObject)(obj, savedMaterial);\n                    } else {\n                        console.log(\"Could not find saved material for object:\", obj.name);\n                    }\n                });\n            };\n            if (sceneObjects.length > 0) {\n                setTimeout(restoreMaterials, 500);\n            } else {\n                const checkInterval = setInterval(()=>{\n                    if (sceneObjects.length > 0) {\n                        clearInterval(checkInterval);\n                        setTimeout(restoreMaterials, 500);\n                    }\n                }, 100);\n                setTimeout(()=>{\n                    clearInterval(checkInterval);\n                }, 5000);\n            }\n        } catch (error) {\n            console.error(\"Error loading scene configuration:\", error);\n        }\n    };\n    const handleSaveScene = async (sceneName, description, saveType)=>{\n        // Use workingConfigRef if it exists and matches the current state, otherwise build from state\n        let sceneConfig;\n        if (workingConfigRef.current) {\n            // Use a shallow compare for top-level keys (or always use workingConfigRef for now)\n            sceneConfig = {\n                ...workingConfigRef.current,\n                name: sceneName,\n                timestamp: new Date().toISOString()\n            };\n        } else {\n            var _orbitControlsRef_current, _orbitControlsRef_current1;\n            // fallback to building from state\n            sceneConfig = {\n                name: sceneName,\n                timestamp: new Date().toISOString(),\n                model: selectedModel,\n                lights: lights.map((light)=>{\n                    var _lightRefs_current_light_id;\n                    return {\n                        ...light,\n                        position: ((_lightRefs_current_light_id = lightRefs.current[light.id]) === null || _lightRefs_current_light_id === void 0 ? void 0 : _lightRefs_current_light_id.position.toArray()) || light.position\n                    };\n                }),\n                environment: {\n                    preset: envPreset,\n                    intensity: envIntensity,\n                    blur: envBlur,\n                    rotation: envRotation,\n                    showEnvironment,\n                    bgColor,\n                    customHdri\n                },\n                postProcessing: {\n                    enabled: postProcessingEnabled,\n                    settings: postProcessingSettings\n                },\n                sceneControls: {\n                    showGrid,\n                    wireframe,\n                    groundType,\n                    showLightSpheres\n                },\n                camera: {\n                    position: (_orbitControlsRef_current = orbitControlsRef.current) === null || _orbitControlsRef_current === void 0 ? void 0 : _orbitControlsRef_current.object.position.toArray(),\n                    target: (_orbitControlsRef_current1 = orbitControlsRef.current) === null || _orbitControlsRef_current1 === void 0 ? void 0 : _orbitControlsRef_current1.target.toArray()\n                },\n                materials: sceneObjects.map((obj)=>{\n                    const material = obj.material;\n                    if (!material) return {\n                        id: obj.uuid,\n                        name: obj.name,\n                        material: null\n                    };\n                    return {\n                        id: obj.uuid,\n                        name: obj.name,\n                        material: {\n                            type: material.type,\n                            color: material.color ? material.color.getHexString() : \"ffffff\",\n                            roughness: material.roughness,\n                            metalness: material.metalness,\n                            wireframe: material.wireframe,\n                            transparent: material.transparent,\n                            opacity: material.opacity,\n                            envMapIntensity: material.envMapIntensity,\n                            ...material.type === \"MeshRefractionMaterial\" && {\n                                aberrationStrength: material.aberrationStrength,\n                                toneMapped: material.toneMapped,\n                                ior: material.ior,\n                                colorEnvMapRotY: material.colorEnvMapRotY\n                            }\n                        }\n                    };\n                })\n            };\n        }\n        if (saveType === \"local\") {\n            // Save to localStorage\n            localStorage.setItem(\"agape_saved_scene\", JSON.stringify(sceneConfig));\n        } else if (saveType === \"db\") {\n            // Save to DB (cloud)\n            try {\n                const version = await (0,_lib_actions_version_actions__WEBPACK_IMPORTED_MODULE_24__.createVersion)({\n                    userId: user.id,\n                    workspaceId: project._id,\n                    name: sceneName,\n                    config: sceneConfig,\n                    description: description\n                });\n                console.log(\"version\", version);\n                if (version) {\n                    addVersion(project._id, version);\n                }\n                sonner__WEBPACK_IMPORTED_MODULE_25__.toast.success(\"Scene saved to cloud!\");\n            } catch (err) {\n                sonner__WEBPACK_IMPORTED_MODULE_25__.toast.error(\"Failed to save scene to cloud\");\n            }\n        } else {\n            // Export as file\n            const blob = new Blob([\n                JSON.stringify(sceneConfig, null, 2)\n            ], {\n                type: \"application/json\"\n            });\n            const url = URL.createObjectURL(blob);\n            const a = document.createElement(\"a\");\n            a.href = url;\n            a.download = \"\".concat(sceneName.replace(/\\s+/g, \"_\"), \".json\");\n            document.body.appendChild(a);\n            a.click();\n            document.body.removeChild(a);\n            URL.revokeObjectURL(url);\n        }\n    };\n    // Engraving state\n    const [engravingText, setEngravingText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [decalPosition, setDecalPosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        0,\n        1.68,\n        0\n    ]);\n    const [decalScale, setDecalScale] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        2.8,\n        0.9,\n        1.5\n    ]);\n    const [decalRotation, setDecalRotation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        -Math.PI / 2,\n        0,\n        0\n    ]);\n    const [decalColor, setDecalColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"#cbcbcb\");\n    const [selectedFont, setSelectedFont] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"/fonts/ttf/RobotoSerif-Regular.ttf\");\n    // Diamond shader state\n    const [useDiamondShader, setUseDiamondShader] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [usePremiumWhiteGold, setUsePremiumWhiteGold] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [usePremiumRoseGold, setUsePremiumRoseGold] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleEngraveText = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((text)=>{\n        setEngravingText(text);\n        // Store in localStorage to make it persist across panel changes and page reloads\n        localStorage.setItem(\"engravingText\", text);\n    }, []);\n    const handleFontChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((font)=>{\n        setSelectedFont(font);\n        localStorage.setItem(\"engravingFont\", font);\n    }, []);\n    // Load saved engraving text on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const savedEngravingText = localStorage.getItem(\"engravingText\");\n        if (savedEngravingText) {\n            setEngravingText(savedEngravingText);\n        }\n    }, []);\n    const handleDecalPlacement = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((event)=>{\n        handleDecalPlacement(event, setIsPlacingDecal, setDecalPosition, setDecalRotation);\n    }, [\n        setDecalPosition,\n        setDecalRotation,\n        setIsPlacingDecal\n    ]);\n    // Get predefined decal positions based on selected model\n    const getDefaultDecalPosition = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((modelId)=>{\n        const modelConfigs = {\n            chosenring: [\n                0,\n                1.68,\n                0\n            ],\n            testring: [\n                0,\n                1.68,\n                0\n            ],\n            agapering1: [\n                0,\n                1.2,\n                0\n            ],\n            agapering2: [\n                0,\n                1.2,\n                0\n            ],\n            ringa: [\n                0,\n                0.26,\n                0\n            ],\n            ringb: [\n                0,\n                0.5,\n                0\n            ],\n            ringc: [\n                0,\n                0.26,\n                0\n            ]\n        };\n        return modelConfigs[modelId] || [\n            0,\n            1.68,\n            0\n        ];\n    }, []);\n    const getDefaultDecalRotation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((modelId)=>{\n        const modelConfigs = {\n            chosenring: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            testring: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            agapering1: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            agapering2: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            ringa: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            ringb: [\n                -Math.PI / 2,\n                0,\n                0\n            ],\n            ringc: [\n                -Math.PI / 2,\n                0,\n                0\n            ]\n        };\n        return modelConfigs[modelId] || [\n            -Math.PI / 2,\n            0,\n            0\n        ];\n    }, []);\n    const getDefaultDecalScale = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((modelId)=>{\n        const modelConfigs = {\n            chosenring: [\n                6.0,\n                2.0,\n                1.5\n            ],\n            testring: [\n                6.0,\n                2.0,\n                1.5\n            ],\n            agapering1: [\n                3.2,\n                1.8,\n                1.0\n            ],\n            agapering2: [\n                3.2,\n                1.8,\n                1.0\n            ],\n            ringa: [\n                1.8,\n                2.0,\n                0.8\n            ],\n            ringb: [\n                1.8,\n                2.0,\n                0.8\n            ],\n            ringc: [\n                1.8,\n                2.0,\n                0.8\n            ]\n        };\n        return modelConfigs[modelId] || [\n            4.2,\n            2.0,\n            1.5\n        ];\n    }, []);\n    // Calculate dynamic scale based on text length and model type\n    const calculateDynamicScale = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((baseScale, text, modelId)=>{\n        if (!text || text.length === 0) return baseScale;\n        const textLength = text.length;\n        let [scaleX, scaleY, scaleZ] = baseScale;\n        // Calculate scale multiplier based on text length\n        let scaleMultiplier = 1.0;\n        if (textLength > 10) {\n            // For very long text, scale up more aggressively\n            scaleMultiplier = 1.3 + (textLength - 10) * 0.08;\n        } else if (textLength > 6) {\n            // For medium length text, scale up moderately\n            scaleMultiplier = 1.0 + (textLength - 6) * 0.08;\n        } else if (textLength < 4) {\n            // For short text, scale down slightly to prevent oversized appearance\n            scaleMultiplier = 0.8 + textLength * 0.05;\n        }\n        // Apply text length scaling to X-axis (width) primarily\n        scaleX = scaleX * scaleMultiplier;\n        // For very long text, also adjust the height slightly\n        if (textLength > 8) {\n            scaleY = scaleY * Math.min(1.2, 1.0 + (textLength - 8) * 0.02);\n        }\n        // Cap maximum scale to prevent excessive sizing (updated for larger Y values)\n        const maxScale = {\n            chosenring: [\n                12.0,\n                2.5,\n                2.0\n            ],\n            testring: [\n                12.0,\n                2.5,\n                2.0\n            ],\n            agapering1: [\n                6.5,\n                2.2,\n                1.5\n            ],\n            agapering2: [\n                6.5,\n                2.2,\n                1.5\n            ],\n            ringa: [\n                4.0,\n                2.5,\n                1.2\n            ],\n            ringb: [\n                4.0,\n                2.5,\n                1.2\n            ],\n            ringc: [\n                4.0,\n                2.5,\n                1.2\n            ]\n        };\n        const limits = maxScale[modelId] || [\n            8.5,\n            2.5,\n            2.0\n        ];\n        scaleX = Math.min(scaleX, limits[0]);\n        scaleY = Math.min(scaleY, limits[1]);\n        scaleZ = Math.min(scaleZ, limits[2]);\n        return [\n            scaleX,\n            scaleY,\n            scaleZ\n        ];\n    }, []);\n    // Update scale when text changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedModel && engravingText) {\n            const baseScale = getDefaultDecalScale(selectedModel);\n            const dynamicScale = calculateDynamicScale(baseScale, engravingText, selectedModel);\n            setDecalScale(dynamicScale);\n        }\n    }, [\n        engravingText,\n        selectedModel,\n        getDefaultDecalScale,\n        calculateDynamicScale\n    ]);\n    // Initialize decal position based on selected model\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedModel) {\n            setDecalPosition(getDefaultDecalPosition(selectedModel));\n            setDecalRotation(getDefaultDecalRotation(selectedModel));\n            // Only set initial scale if there's no engraving text yet\n            if (!engravingText) {\n                setDecalScale(getDefaultDecalScale(selectedModel));\n            }\n        }\n    }, [\n        selectedModel,\n        getDefaultDecalPosition,\n        getDefaultDecalRotation,\n        getDefaultDecalScale,\n        engravingText\n    ]);\n    const [isFullscreen, setIsFullscreen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Get camera position based on device type\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const checkMobile = ()=>{\n            setIsMobile(window.innerWidth < 768);\n        };\n        checkMobile();\n        window.addEventListener(\"resize\", checkMobile);\n        return ()=>window.removeEventListener(\"resize\", checkMobile);\n    }, []);\n    // Add state to control outliner collapsed state - initialize based on device type\n    const [outlinerCollapsed, setOutlinerCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        // Initialize based on device type - closed on mobile, open on desktop\n        if (true) {\n            return window.innerWidth < 768;\n        }\n        return false; // Default to open on server-side rendering\n    });\n    // Effect to close outliner when toolbar panels are opened on mobile\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isMobile && rightPanelTab) {\n            setOutlinerCollapsed(true);\n        }\n    }, [\n        isMobile,\n        rightPanelTab\n    ]);\n    // Effect to update outliner collapsed state when mobile state changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Update outliner state based on device type - closed on mobile, open on desktop\n        setOutlinerCollapsed(isMobile);\n    }, [\n        isMobile\n    ]);\n    const getCameraPosition = ()=>{\n        if (isMobile) {\n            return [\n                -1.85,\n                3.25,\n                4.5\n            ];\n        }\n        return [\n            -1.85,\n            1.55,\n            3.5\n        ];\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // On initial mount, fetch and apply the current version from DB\n        const fetchAndApplyCurrentVersion = async ()=>{\n            try {\n                const currentVersion = await (0,_lib_actions_version_actions__WEBPACK_IMPORTED_MODULE_24__.getCurrentVersion)({\n                    userId: user.id,\n                    workspaceId: project._id\n                });\n                if (currentVersion && currentVersion.config) {\n                    handleLoadScene(currentVersion.config);\n                }\n            } catch (err) {\n                console.error(\"Failed to load current version from DB\", err);\n            }\n        };\n        fetchAndApplyCurrentVersion();\n    // Only run on mount\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_toastify__WEBPACK_IMPORTED_MODULE_2__.ToastContainer, {}, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1686,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Header__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                onReset: handleReset,\n                onScreenshot: captureScreenshot,\n                onSaveScene: handleSaveScene,\n                onLoadScene: handleLoadScene,\n                isFullscreen: isFullscreen,\n                onToggleFullscreen: ()=>setIsFullscreen(!isFullscreen),\n                project: project,\n                user: user\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1687,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_InitialOverlayPanel__WEBPACK_IMPORTED_MODULE_20__.InitialOverlayPanel, {}, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1698,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed left-5 top-1/2 transform -translate-y-1/2 z-[1000] transition-all duration-300 \".concat(isFullscreen ? \"opacity-0 pointer-events-none\" : \"opacity-100\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Toolbar__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    transformMode: transformMode,\n                    setTransformMode: setTransformMode,\n                    undoAction: undoAction,\n                    redoAction: redoAction,\n                    rightPanelTab: rightPanelTab,\n                    setRightPanelTab: setRightPanelTab,\n                    orientation: \"vertical\",\n                    sceneObjects: sceneObjects,\n                    setSelectedObjects: setSelectedObjects,\n                    selectedObjects: selectedObjects,\n                    panelRef: panelRef,\n                    isMobile: isMobile,\n                    setOutlinerCollapsed: setOutlinerCollapsed\n                }, void 0, false, {\n                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                    lineNumber: 1706,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1701,\n                columnNumber: 7\n            }, this),\n            rightPanelTab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: panelRef,\n                className: \"fixed left-20 top-1/2 transform -translate-y-1/2 bg-gradient-to-tl from-[#32343D] to-[#14192D] p-4 rounded-lg shadow-lg z-[1000] max-h-[80vh] overflow-y-auto w-[250px] md:w-[300px] border border-[#A3A3A3]/30 scrollbar-none\",\n                children: [\n                    rightPanelTab === \"transform\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TransformPanel__WEBPACK_IMPORTED_MODULE_13__.TransformPanel, {\n                        selectedObjects: selectedObjects,\n                        transformMode: transformMode,\n                        setTransformMode: setTransformMode,\n                        undoAction: undoAction,\n                        redoAction: redoAction\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1730,\n                        columnNumber: 13\n                    }, this),\n                    rightPanelTab === \"material\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MaterialPanel__WEBPACK_IMPORTED_MODULE_7__.MaterialPanel, {\n                        selectedObjects: selectedObjects,\n                        useDiamondShader: useDiamondShader,\n                        setUseDiamondShader: setUseDiamondShader,\n                        usePremiumWhiteGold: usePremiumWhiteGold,\n                        setUsePremiumWhiteGold: setUsePremiumWhiteGold,\n                        usePremiumRoseGold: usePremiumRoseGold,\n                        setUsePremiumRoseGold: setUsePremiumRoseGold,\n                        renderer: renderer\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1740,\n                        columnNumber: 13\n                    }, this),\n                    rightPanelTab === \"postprocessing\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PostProcessingPanel__WEBPACK_IMPORTED_MODULE_10__.PostProcessingPanel, {\n                        postProcessingEnabled: postProcessingEnabled,\n                        setPostProcessingEnabled: setPostProcessingEnabled,\n                        postProcessingSettings: postProcessingSettings,\n                        setPostProcessingSettings: setPostProcessingSettings\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1753,\n                        columnNumber: 13\n                    }, this),\n                    rightPanelTab === \"environment\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EnvironmentPanel__WEBPACK_IMPORTED_MODULE_4__.EnvironmentPanel, {\n                        envPreset: envPreset,\n                        setEnvPreset: setEnvPreset,\n                        bgColor: bgColor,\n                        setBgColor: setBgColor,\n                        showEnvironment: showEnvironment,\n                        setShowEnvironment: setShowEnvironment,\n                        customHdri: customHdri,\n                        setCustomHdri: setCustomHdri,\n                        envIntensity: envIntensity,\n                        setEnvIntensity: setEnvIntensity,\n                        showModelStats: showModelStats,\n                        setShowModelStats: setShowModelStats,\n                        envBlur: envBlur,\n                        setEnvBlur: setEnvBlur,\n                        envRotation: envRotation,\n                        setEnvRotation: setEnvRotation\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1762,\n                        columnNumber: 13\n                    }, this),\n                    rightPanelTab === \"lights\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LightPanel__WEBPACK_IMPORTED_MODULE_6__.LightPanel, {\n                        lights: lights,\n                        setLights: setLights,\n                        lightRefs: lightRefs,\n                        showGrid: showGrid,\n                        setShowGrid: setShowGrid,\n                        wireframe: wireframe,\n                        setWireframe: setWireframe,\n                        showLightSpheres: showLightSpheres,\n                        setShowLightSpheres: setShowLightSpheres,\n                        onResetLights: ()=>{\n                            // Reset lights to default configuration\n                            const defaultLights = [\n                                {\n                                    id: \"defaultDirectional\",\n                                    type: \"directional\",\n                                    position: [\n                                        2.5,\n                                        5.5,\n                                        5\n                                    ],\n                                    intensity: 1.0,\n                                    color: \"#ffffff\",\n                                    name: \"Main Directional Light\",\n                                    castShadow: true,\n                                    helperVisible: false\n                                },\n                                {\n                                    id: \"keyLight\",\n                                    type: \"point\",\n                                    position: [\n                                        5,\n                                        5,\n                                        5\n                                    ],\n                                    intensity: 8.0,\n                                    color: \"#ffffff\",\n                                    name: \"Key Light\"\n                                },\n                                {\n                                    id: \"fillLight\",\n                                    type: \"point\",\n                                    position: [\n                                        -5,\n                                        5,\n                                        -5\n                                    ],\n                                    intensity: 4.0,\n                                    color: \"#ffffff\",\n                                    name: \"Fill Light\"\n                                },\n                                {\n                                    id: \"backLight\",\n                                    type: \"point\",\n                                    position: [\n                                        0,\n                                        5,\n                                        -5\n                                    ],\n                                    intensity: 2.0,\n                                    color: \"#ffffff\",\n                                    name: \"Back Light\"\n                                }\n                            ];\n                            setLights(defaultLights);\n                            // Clear selected light\n                            setSelectedLight(null);\n                            // Clear light position history\n                            setLightPositionHistory({});\n                        }\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1783,\n                        columnNumber: 13\n                    }, this),\n                    rightPanelTab === \"engraving\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EngravingPanel__WEBPACK_IMPORTED_MODULE_19__.EngravingPanel, {\n                        onEngraveText: handleEngraveText,\n                        onDecalPositionChange: setDecalPosition,\n                        onDecalScaleChange: setDecalScale,\n                        onDecalRotationChange: setDecalRotation,\n                        decalPosition: decalPosition,\n                        decalScale: decalScale,\n                        decalRotation: decalRotation,\n                        decalColor: decalColor,\n                        onDecalColorChange: setDecalColor,\n                        onFontChange: handleFontChange,\n                        selectedFont: selectedFont,\n                        isPlacingDecal: isPlacingDecal,\n                        setIsPlacingDecal: setIsPlacingDecal,\n                        showDebug: showDecalDebug,\n                        onShowDebugChange: setShowDecalDebug\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1841,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1725,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed right-5 top-24 z-[999] transition-all duration-300 \".concat(isFullscreen ? \"opacity-0 pointer-events-none\" : \"opacity-100\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-tl from-[#32343D] to-[#14192D] shadow-lg rounded-lg overflow-visible scrollbar-none\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Outliner__WEBPACK_IMPORTED_MODULE_9__.Outliner, {\n                        sceneObjects: sceneObjects,\n                        selectedObjects: selectedObjects,\n                        onSelectObject: setSelectedObjects,\n                        onCameraView: handleCameraView,\n                        onPlayAnimation: handlePlayAnimation,\n                        onSaveScene: handleSaveScene,\n                        groundType: groundType,\n                        setGroundType: setGroundType,\n                        collapsed: outlinerCollapsed,\n                        setCollapsed: setOutlinerCollapsed\n                    }, void 0, false, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1869,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                    lineNumber: 1868,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1863,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-full\",\n                style: {\n                    background: \"linear-gradient(to bottom right, #363643, #5C5C6E)\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_fiber__WEBPACK_IMPORTED_MODULE_29__.Canvas, {\n                    className: \"w-full h-full\",\n                    camera: {\n                        position: getCameraPosition(),\n                        fov: 45,\n                        near: 0.1,\n                        far: 50\n                    },\n                    shadows: true,\n                    ref: sceneRef,\n                    gl: {\n                        antialias: true,\n                        preserveDrawingBuffer: true\n                    },\n                    onPointerMissed: handlePointerMissed,\n                    raycaster: {\n                        computeOffsets: (e)=>({\n                                offsetX: e.clientX,\n                                offsetY: e.clientY\n                            })\n                    },\n                    frameloop: \"demand\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n                        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_30__.Html, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_31__.Loader, {}, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 1914,\n                                columnNumber: 17\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                            lineNumber: 1913,\n                            columnNumber: 15\n                        }, void 0),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ScreenshotTaker, {\n                                onScreenshotRef: (fn)=>{\n                                    screenshotRef.current = fn;\n                                }\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 1920,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RendererProvider, {\n                                onRendererReady: (gl)=>{\n                                    setRenderer(gl);\n                                }\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 1926,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SceneLights__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                lights: lights,\n                                lightRefs: lightRefs,\n                                selectedLight: selectedLight,\n                                onLightSelect: setSelectedLight,\n                                onLightMoved: handleLightMoved,\n                                showLightSpheres: showLightSpheres,\n                                threePointLighting: true\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 1932,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Environment__WEBPACK_IMPORTED_MODULE_3__.Environment, {\n                                preset: getEnvironmentPreset(),\n                                background: showEnvironment,\n                                bgColor: bgColor,\n                                customHdri: customHdri,\n                                intensity: envIntensity,\n                                blur: envBlur,\n                                envRotation: envRotation\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 1941,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SelectionOutline__WEBPACK_IMPORTED_MODULE_23__.SelectionOutline, {\n                                selectedObjects: selectedObjects,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ModelStage__WEBPACK_IMPORTED_MODULE_21__.ModelStage, {\n                                    selectedModel: selectedModel,\n                                    wireframe: wireframe,\n                                    handleSceneUpdate: handleSceneUpdate,\n                                    handleObjectClick: handleObjectClick,\n                                    uploadedModel: uploadedModel,\n                                    groundType: groundType,\n                                    engravingText: engravingText,\n                                    activeAnimation: activeAnimation,\n                                    decalPosition: decalPosition,\n                                    decalScale: decalScale,\n                                    decalRotation: decalRotation,\n                                    decalColor: decalColor,\n                                    font: selectedFont,\n                                    isPlacingDecal: isPlacingDecal,\n                                    onDecalPlacement: handleDecalPlacement,\n                                    showDecalDebug: showDecalDebug,\n                                    sceneObjects: sceneObjects,\n                                    useDiamondShader: useDiamondShader,\n                                    usePremiumWhiteGold: usePremiumWhiteGold,\n                                    usePremiumRoseGold: usePremiumRoseGold\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                    lineNumber: 1952,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 1951,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_32__.OrbitControls, {\n                                ref: orbitControlsRef,\n                                target: [\n                                    0,\n                                    0.5,\n                                    0\n                                ],\n                                makeDefault: true,\n                                enableDamping: true,\n                                // minPolarAngle={0}\n                                // maxPolarAngle={Math.PI / 2.25}\n                                // minDistance={2.5}\n                                maxDistance: 20\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 1976,\n                                columnNumber: 13\n                            }, this),\n                            selectedObjects.length > 0 && rightPanelTab === \"transform\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_33__.TransformControls, {\n                                object: selectedObjects.length === 1 ? selectedObjects[0] : selectionGroupRef.current,\n                                mode: transformMode,\n                                onMouseDown: ()=>{\n                                    if (orbitControlsRef.current) orbitControlsRef.current.enabled = false;\n                                },\n                                onMouseUp: ()=>{\n                                    if (orbitControlsRef.current) orbitControlsRef.current.enabled = true;\n                                    if (selectedObjects.length > 1) updateObjectsFromGroup();\n                                },\n                                onObjectChange: ()=>{\n                                    if (selectedObjects.length > 1) updateObjectsFromGroup();\n                                }\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 1988,\n                                columnNumber: 15\n                            }, this),\n                            groundType === \"grid\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"gridHelper\", {\n                                args: [\n                                    50,\n                                    50,\n                                    \"#666666\",\n                                    \"#333333\"\n                                ],\n                                receiveShadow: true\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2010,\n                                columnNumber: 15\n                            }, this),\n                            groundType === \"solid\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n                                \"rotation-x\": -Math.PI / 2,\n                                receiveShadow: true,\n                                position: [\n                                    0,\n                                    -0.01,\n                                    0\n                                ],\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"planeGeometry\", {\n                                        args: [\n                                            200,\n                                            200\n                                        ]\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2018,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meshStandardMaterial\", {\n                                        color: \"#5d5d5d\",\n                                        wireframe: wireframe,\n                                        transparent: true,\n                                        opacity: 0.7\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2019,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2013,\n                                columnNumber: 15\n                            }, this),\n                            groundType === \"reflective\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n                                receiveShadow: true,\n                                rotation: [\n                                    -Math.PI / 2,\n                                    0,\n                                    0\n                                ],\n                                position: [\n                                    0,\n                                    -0.01,\n                                    0\n                                ],\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"planeGeometry\", {\n                                        args: [\n                                            80,\n                                            80\n                                        ]\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2033,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_34__.MeshReflectorMaterial, {\n                                        blur: [\n                                            800,\n                                            800\n                                        ],\n                                        resolution: 2048,\n                                        mixBlur: 0.5,\n                                        mixStrength: 10,\n                                        roughness: 1,\n                                        depthScale: 1,\n                                        minDepthThreshold: 0.4,\n                                        maxDepthThreshold: 1.2,\n                                        color: \"#363643\",\n                                        metalness: 0.01,\n                                        mirror: 0.3,\n                                        transparent: true,\n                                        opacity: 0.5\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2034,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2028,\n                                columnNumber: 15\n                            }, this),\n                            groundType === \"none\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"mesh\", {\n                                receiveShadow: true,\n                                rotation: [\n                                    -Math.PI / 2,\n                                    0,\n                                    0\n                                ],\n                                position: [\n                                    0,\n                                    -0.01,\n                                    0\n                                ],\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"planeGeometry\", {\n                                        args: [\n                                            200,\n                                            200\n                                        ]\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2057,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"primitive\", {\n                                        object: new three__WEBPACK_IMPORTED_MODULE_28__.ShadowMaterial({\n                                            opacity: 0.1,\n                                            color: new three__WEBPACK_IMPORTED_MODULE_28__.Color(0x000000),\n                                            transparent: true\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                        lineNumber: 2058,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2052,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_35__.GizmoHelper, {\n                                alignment: \"bottom-right\",\n                                margin: [\n                                    80,\n                                    80\n                                ],\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_36__.GizmoViewport, {\n                                    labelColor: \"white\",\n                                    axisColors: [\n                                        \"red\",\n                                        \"green\",\n                                        \"blue\"\n                                    ]\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                    lineNumber: 2070,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2069,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"primitive\", {\n                                object: selectionGroupRef.current\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2076,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PostProcessingEffects__WEBPACK_IMPORTED_MODULE_22__.PostProcessingEffects, {\n                                enabled: postProcessingEnabled,\n                                settings: postProcessingSettings\n                            }, void 0, false, {\n                                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                                lineNumber: 2078,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                        lineNumber: 1911,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                    lineNumber: 1891,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 1885,\n                columnNumber: 7\n            }, this),\n            showModelStats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ModelStats__WEBPACK_IMPORTED_MODULE_8__.ModelStats, {\n                stats: modelStats\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\EditorExperience.jsx\",\n                lineNumber: 2086,\n                columnNumber: 26\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s2(EditorExperience, \"dCp3Z9qFCd3oKkequ/BGsCXXTB0=\", false, function() {\n    return [\n        _lib_states__WEBPACK_IMPORTED_MODULE_26__.useVersionState\n    ];\n});\n_c3 = EditorExperience;\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"ScreenshotTaker\");\n$RefreshReg$(_c1, \"MaterialPanelWrapper\");\n$RefreshReg$(_c2, \"RendererProvider\");\n$RefreshReg$(_c3, \"EditorExperience\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ArtistTool/EditorExperience.jsx\n"));

/***/ })

});