"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/projects/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/ArtistTool/Environment.tsx":
/*!***************************************************!*\
  !*** ./src/components/ArtistTool/Environment.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Environment: function() { return /* binding */ Environment; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _react_three_drei__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @react-three/drei */ \"(app-pages-browser)/./node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_react-dom@18.3.1_react@18.3.1__react@18.3_c576vgmee4hxmr3ajmr4dgczti/node_modules/@react-three/drei/core/Environment.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! three */ \"(app-pages-browser)/./node_modules/.pnpm/three@0.167.1/node_modules/three/build/three.module.js\");\n\nvar _s = $RefreshSig$();\n\n\n\nfunction Environment(param) {\n    let { preset = \"sunset\", background = false, bgColor = \"#000000\", customHdri = null, intensity = 1, envRotation = 0, blur = 0.1, envTextureCache = {} } = param;\n    _s();\n    const [backgroundColor, setBackgroundColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (bgColor) {\n            setBackgroundColor(new three__WEBPACK_IMPORTED_MODULE_2__.Color(bgColor));\n        }\n    }, [\n        bgColor\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            backgroundColor && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"color\", {\n                attach: \"background\",\n                args: [\n                    backgroundColor\n                ]\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Environment.tsx\",\n                lineNumber: 59,\n                columnNumber: 9\n            }, this),\n            preset !== \"none\" && (customHdri ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_3__.Environment, {\n                files: customHdri,\n                background: background,\n                environmentIntensity: intensity,\n                environmentRotation: [\n                    0,\n                    envRotation,\n                    0\n                ],\n                blur: background ? blur : 0\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Environment.tsx\",\n                lineNumber: 64,\n                columnNumber: 11\n            }, this) : preset === \"hdri_15\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_3__.Environment, {\n                files: envTextureCache[\"hdri_15\"] || \"/hdris/hdri_15.hdr\",\n                background: background,\n                environmentIntensity: intensity,\n                environmentRotation: [\n                    0,\n                    envRotation,\n                    0\n                ],\n                blur: background ? blur : 0\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Environment.tsx\",\n                lineNumber: 72,\n                columnNumber: 11\n            }, this) : preset === \"hdri_metal\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_3__.Environment, {\n                files: envTextureCache[\"hdri_metal\"] || \"/hdris/hdri_metal.exr\",\n                background: background,\n                environmentIntensity: intensity,\n                environmentRotation: [\n                    0,\n                    envRotation,\n                    0\n                ],\n                blur: background ? blur : 0\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Environment.tsx\",\n                lineNumber: 80,\n                columnNumber: 11\n            }, this) : preset === \"hdri_metal2\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_3__.Environment, {\n                files: envTextureCache[\"hdri_metal2\"] || \"/hdris/metal_hdri2.hdr\",\n                background: background,\n                environmentIntensity: intensity,\n                environmentRotation: [\n                    0,\n                    envRotation,\n                    0\n                ],\n                blur: background ? blur : 0\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Environment.tsx\",\n                lineNumber: 88,\n                columnNumber: 11\n            }, this) : preset === \"hdri_metal3\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_3__.Environment, {\n                files: envTextureCache[\"hdri_metal3\"] || \"/hdris/metal_hdri3.hdr\",\n                background: background,\n                environmentIntensity: intensity,\n                environmentRotation: [\n                    0,\n                    envRotation,\n                    0\n                ],\n                blur: background ? blur : 0\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Environment.tsx\",\n                lineNumber: 96,\n                columnNumber: 11\n            }, this) : preset === \"hdri_gem\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_3__.Environment, {\n                files: envTextureCache[\"hdri_gem\"] || \"/hdris/env_gem_002_30251392af.exr\",\n                background: background,\n                environmentIntensity: intensity,\n                environmentRotation: [\n                    0,\n                    envRotation,\n                    0\n                ],\n                blur: background ? blur : 0\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Environment.tsx\",\n                lineNumber: 104,\n                columnNumber: 11\n            }, this) : preset === \"studio_small\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_3__.Environment, {\n                files: envTextureCache[\"studio_small\"] || \"/hdris/studio_small_02_2k.hdr\",\n                background: background,\n                environmentIntensity: intensity,\n                environmentRotation: [\n                    0,\n                    envRotation,\n                    0\n                ],\n                blur: background ? blur : 0\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Environment.tsx\",\n                lineNumber: 114,\n                columnNumber: 11\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_three_drei__WEBPACK_IMPORTED_MODULE_3__.Environment, {\n                preset: preset,\n                background: background,\n                environmentIntensity: intensity,\n                environmentRotation: [\n                    0,\n                    envRotation,\n                    0\n                ],\n                blur: background ? blur : 0\n            }, void 0, false, {\n                fileName: \"G:\\\\agape\\\\agape-newFrontend\\\\web\\\\src\\\\components\\\\ArtistTool\\\\Environment.tsx\",\n                lineNumber: 124,\n                columnNumber: 11\n            }, this))\n        ]\n    }, void 0, true);\n}\n_s(Environment, \"8dfVRfhsIeo/uEzTOufHhYu5Oto=\");\n_c = Environment;\nvar _c;\n$RefreshReg$(_c, \"Environment\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ArtistTool/Environment.tsx\n"));

/***/ })

});